using System;

namespace PharmacyManagement.Models
{
    /// <summary>
    /// نموذج المستخدم - User Model
    /// يمثل بيانات المستخدمين في النظام
    /// </summary>
    public class User
    {
        #region Properties - الخصائص

        /// <summary>
        /// معرف المستخدم الفريد
        /// </summary>
        public int UserID { get; set; }

        /// <summary>
        /// اسم المستخدم للدخول
        /// </summary>
        public string Username { get; set; }

        /// <summary>
        /// كلمة المرور (مشفرة)
        /// </summary>
        public string Password { get; set; }

        /// <summary>
        /// الاسم الكامل للمستخدم
        /// </summary>
        public string FullName { get; set; }

        /// <summary>
        /// البريد الإلكتروني
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// رقم الهاتف
        /// </summary>
        public string Phone { get; set; }

        /// <summary>
        /// دور المستخدم في النظام
        /// Admin, Manager, Employee, Pharmacist
        /// </summary>
        public string Role { get; set; }

        /// <summary>
        /// حالة المستخدم (نشط/غير نشط)
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// صلاحية إضافة مستخدمين
        /// </summary>
        public bool CanAddUsers { get; set; }

        /// <summary>
        /// صلاحية تعديل مستخدمين
        /// </summary>
        public bool CanEditUsers { get; set; }

        /// <summary>
        /// صلاحية حذف مستخدمين
        /// </summary>
        public bool CanDeleteUsers { get; set; }

        /// <summary>
        /// صلاحية عرض التقارير
        /// </summary>
        public bool CanViewReports { get; set; }

        /// <summary>
        /// صلاحية إدارة الإعدادات
        /// </summary>
        public bool CanPrint { get; set; }

        /// <summary>
        /// تاريخ إنشاء الحساب
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// تاريخ آخر تعديل
        /// </summary>
        public DateTime? ModifiedDate { get; set; }

        /// <summary>
        /// آخر تسجيل دخول
        /// </summary>
        public DateTime? LastLogin { get; set; }

        /// <summary>
        /// تاريخ آخر تسجيل دخول
        /// </summary>
        public DateTime? LastLoginDate { get; set; }

        /// <summary>
        /// اسم الفرع
        /// </summary>
        public string BranchName { get; set; }

        /// <summary>
        /// معرف المستخدم الذي أنشأ هذا الحساب
        /// </summary>
        public int? CreatedBy { get; set; }

        /// <summary>
        /// معرف الفرع الذي ينتمي إليه المستخدم
        /// </summary>
        public int? BranchID { get; set; }

        #endregion

        #region Constructors - المنشئات

        /// <summary>
        /// منشئ افتراضي
        /// </summary>
        public User()
        {
            IsActive = true;
            CreatedDate = DateTime.Now;
            Role = "Employee";
        }

        /// <summary>
        /// منشئ مع المعاملات الأساسية
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <param name="password">كلمة المرور</param>
        /// <param name="fullName">الاسم الكامل</param>
        /// <param name="role">الدور</param>
        public User(string username, string password, string fullName, string role)
        {
            Username = username;
            Password = password;
            FullName = fullName;
            Role = role;
            IsActive = true;
            CreatedDate = DateTime.Now;
        }

        #endregion

        #region Methods - الطرق

        /// <summary>
        /// التحقق من صحة بيانات المستخدم
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(Username) &&
                   !string.IsNullOrWhiteSpace(Password) &&
                   !string.IsNullOrWhiteSpace(FullName) &&
                   !string.IsNullOrWhiteSpace(Role);
        }

        /// <summary>
        /// التحقق من صلاحيات المستخدم
        /// </summary>
        /// <param name="requiredRole">الدور المطلوب</param>
        /// <returns>true إذا كان المستخدم يملك الصلاحية</returns>
        public bool HasPermission(string requiredRole)
        {
            if (!IsActive) return false;

            switch (Role.ToLower())
            {
                case "admin":
                    return true; // المدير له جميع الصلاحيات
                case "manager":
                    return requiredRole.ToLower() != "admin";
                case "pharmacist":
                    return requiredRole.ToLower() == "pharmacist" || requiredRole.ToLower() == "employee";
                case "employee":
                    return requiredRole.ToLower() == "employee";
                default:
                    return false;
            }
        }

        /// <summary>
        /// إرجاع تمثيل نصي للمستخدم
        /// </summary>
        /// <returns>الاسم الكامل ودور المستخدم</returns>
        public override string ToString()
        {
            return FullName + " (" + Role + ")";
        }

        #endregion
    }
}
