using System;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using PharmacyManagement.Classes;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نافذة إدارة النسخ الاحتياطية - Backup Management Form
    /// </summary>
    public partial class BackupManagementForm : Form
    {
        #region Constructor - المنشئ

        public BackupManagementForm()
        {
            InitializeComponent();
            SetupForm();
            LoadData();
        }

        #endregion

        #region Form Setup - إعداد النافذة

        /// <summary>
        /// إعداد النافذة
        /// </summary>
        private void SetupForm()
        {
            // إعداد DataGridView
            SetupDataGridView();
            
            // إعداد التصميم المسطح
            ApplyFlatDesign();
            
            // تحميل الإعدادات
            LoadSettings();
        }

        /// <summary>
        /// إعداد DataGridView
        /// </summary>
        private void SetupDataGridView()
        {
            dgvBackups.AutoGenerateColumns = false;
            dgvBackups.AllowUserToAddRows = false;
            dgvBackups.AllowUserToDeleteRows = false;
            dgvBackups.ReadOnly = true;
            dgvBackups.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvBackups.MultiSelect = false;
            
            // تنسيق الألوان
            dgvBackups.BackgroundColor = Color.White;
            dgvBackups.GridColor = Color.FromArgb(189, 195, 199);
            dgvBackups.DefaultCellStyle.SelectionBackColor = Color.FromArgb(52, 152, 219);
            dgvBackups.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvBackups.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 73, 94);
            dgvBackups.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvBackups.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10, FontStyle.Bold);
        }

        /// <summary>
        /// تطبيق التصميم المسطح
        /// </summary>
        private void ApplyFlatDesign()
        {
            // إزالة حدود الأزرار
            foreach (Control control in this.Controls)
            {
                ApplyFlatStyleToControl(control);
            }
        }

        /// <summary>
        /// تطبيق التصميم المسطح على عنصر
        /// </summary>
        private void ApplyFlatStyleToControl(Control control)
        {
            if (control is Button button)
            {
                button.FlatAppearance.BorderSize = 0;
                button.Cursor = Cursors.Hand;
            }
            
            // تطبيق على العناصر الفرعية
            foreach (Control child in control.Controls)
            {
                ApplyFlatStyleToControl(child);
            }
        }

        /// <summary>
        /// تحميل الإعدادات
        /// </summary>
        private void LoadSettings()
        {
            try
            {
                chkAutoBackup.Checked = SettingsManager.GetBoolSetting("AutoBackup", true);
                numBackupInterval.Value = SettingsManager.GetIntSetting("BackupInterval", 24);
                numKeepBackups.Value = SettingsManager.GetIntSetting("KeepBackupsCount", 10);
                
                string backupPath = SettingsManager.GetSetting("BackupPath", 
                    Path.Combine(Application.StartupPath, "Backups"));
                txtBackupPath.Text = backupPath;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تحميل إعدادات النسخ الاحتياطي: {ex.Message}");
            }
        }

        #endregion

        #region Data Loading - تحميل البيانات

        /// <summary>
        /// تحميل بيانات النسخ الاحتياطية
        /// </summary>
        private void LoadData()
        {
            try
            {
                string backupFolder = txtBackupPath.Text;
                if (!Directory.Exists(backupFolder))
                {
                    Directory.CreateDirectory(backupFolder);
                }

                var backupFiles = Directory.GetFiles(backupFolder, "*.bak")
                    .Select(file => BackupManager.GetBackupInfo(file))
                    .Where(info => info != null)
                    .OrderByDescending(info => info.CreatedDate)
                    .ToList();

                dgvBackups.DataSource = backupFiles;
                
                // تحديث الإحصائيات
                UpdateStatistics(backupFiles);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحديث الإحصائيات
        /// </summary>
        private void UpdateStatistics(System.Collections.Generic.List<BackupInfo> backups)
        {
            try
            {
                lblTotalBackups.Text = $"إجمالي النسخ: {backups.Count}";
                
                long totalSize = backups.Sum(b => b.FileSize);
                lblTotalSize.Text = $"الحجم الإجمالي: {FormatFileSize(totalSize)}";
                
                var lastBackup = backups.FirstOrDefault();
                lblLastBackup.Text = lastBackup != null ? 
                    $"آخر نسخة: {lastBackup.CreatedDate:yyyy/MM/dd HH:mm}" : 
                    "لا توجد نسخ احتياطية";
                
                DateTime lastAutoBackup = SettingsManager.GetDateTimeSetting("LastBackupDate", DateTime.MinValue);
                lblNextBackup.Text = lastAutoBackup != DateTime.MinValue ? 
                    $"النسخة التالية: {lastAutoBackup.AddHours((double)numBackupInterval.Value):yyyy/MM/dd HH:mm}" : 
                    "غير محدد";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث الإحصائيات: {ex.Message}");
            }
        }

        /// <summary>
        /// تنسيق حجم الملف
        /// </summary>
        private string FormatFileSize(long bytes)
        {
            if (bytes < 1024) return $"{bytes} بايت";
            if (bytes < 1024 * 1024) return $"{bytes / 1024:F1} كيلوبايت";
            if (bytes < 1024 * 1024 * 1024) return $"{bytes / (1024 * 1024):F1} ميجابايت";
            return $"{bytes / (1024 * 1024 * 1024):F1} جيجابايت";
        }

        #endregion

        #region Event Handlers - معالجات الأحداث

        /// <summary>
        /// إنشاء نسخة احتياطية جديدة
        /// </summary>
        private void btnCreateBackup_Click(object sender, EventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "Backup Files|*.bak",
                    Title = "حفظ النسخة الاحتياطية",
                    FileName = $"ManualBackup_{DateTime.Now:yyyyMMdd_HHmmss}.bak",
                    InitialDirectory = txtBackupPath.Text
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    progressBar.Visible = true;
                    progressBar.Style = ProgressBarStyle.Marquee;
                    
                    // تشغيل العملية في خيط منفصل
                    var worker = new System.ComponentModel.BackgroundWorker();
                    worker.DoWork += (s, args) =>
                    {
                        args.Result = BackupManager.CreateBackup(saveDialog.FileName);
                    };
                    worker.RunWorkerCompleted += (s, args) =>
                    {
                        progressBar.Visible = false;
                        
                        if ((bool)args.Result)
                        {
                            MessageBox.Show("تم إنشاء النسخة الاحتياطية بنجاح", "نجح", 
                                          MessageBoxButtons.OK, MessageBoxIcon.Information);
                            LoadData();
                        }
                        else
                        {
                            MessageBox.Show("فشل في إنشاء النسخة الاحتياطية", "خطأ", 
                                          MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    };
                    worker.RunWorkerAsync();
                }
            }
            catch (Exception ex)
            {
                progressBar.Visible = false;
                MessageBox.Show($"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// استعادة نسخة احتياطية
        /// </summary>
        private void btnRestoreBackup_Click(object sender, EventArgs e)
        {
            if (dgvBackups.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار نسخة احتياطية للاستعادة", "تنبيه", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                var selectedBackup = dgvBackups.SelectedRows[0].DataBoundItem as BackupInfo;
                if (selectedBackup != null)
                {
                    var result = MessageBox.Show(
                        "تحذير: سيتم استبدال جميع البيانات الحالية بالنسخة الاحتياطية المحددة.\n\n" +
                        "هل أنت متأكد من المتابعة؟", 
                        "تأكيد الاستعادة", 
                        MessageBoxButtons.YesNo, 
                        MessageBoxIcon.Warning);
                    
                    if (result == DialogResult.Yes)
                    {
                        progressBar.Visible = true;
                        progressBar.Style = ProgressBarStyle.Marquee;
                        
                        // تشغيل العملية في خيط منفصل
                        var worker = new System.ComponentModel.BackgroundWorker();
                        worker.DoWork += (s, args) =>
                        {
                            args.Result = BackupManager.RestoreBackup(selectedBackup.FilePath);
                        };
                        worker.RunWorkerCompleted += (s, args) =>
                        {
                            progressBar.Visible = false;
                            
                            if ((bool)args.Result)
                            {
                                MessageBox.Show(
                                    "تم استعادة النسخة الاحتياطية بنجاح.\n\n" +
                                    "سيتم إعادة تشغيل النظام الآن.", 
                                    "نجح", 
                                    MessageBoxButtons.OK, 
                                    MessageBoxIcon.Information);
                                
                                Application.Restart();
                            }
                            else
                            {
                                MessageBox.Show("فشل في استعادة النسخة الاحتياطية", "خطأ", 
                                              MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        };
                        worker.RunWorkerAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                progressBar.Visible = false;
                MessageBox.Show($"خطأ في استعادة النسخة الاحتياطية: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حذف نسخة احتياطية
        /// </summary>
        private void btnDeleteBackup_Click(object sender, EventArgs e)
        {
            if (dgvBackups.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار نسخة احتياطية للحذف", "تنبيه", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                var selectedBackup = dgvBackups.SelectedRows[0].DataBoundItem as BackupInfo;
                if (selectedBackup != null)
                {
                    var result = MessageBox.Show("هل تريد حذف هذه النسخة الاحتياطية؟", "تأكيد الحذف", 
                                               MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    
                    if (result == DialogResult.Yes)
                    {
                        File.Delete(selectedBackup.FilePath);
                        MessageBox.Show("تم حذف النسخة الاحتياطية بنجاح", "نجح", 
                                      MessageBoxButtons.OK, MessageBoxIcon.Information);
                        LoadData();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف النسخة الاحتياطية: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// التحقق من صحة النسخة الاحتياطية
        /// </summary>
        private void btnVerifyBackup_Click(object sender, EventArgs e)
        {
            if (dgvBackups.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار نسخة احتياطية للتحقق", "تنبيه", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                var selectedBackup = dgvBackups.SelectedRows[0].DataBoundItem as BackupInfo;
                if (selectedBackup != null)
                {
                    progressBar.Visible = true;
                    progressBar.Style = ProgressBarStyle.Marquee;
                    
                    // تشغيل العملية في خيط منفصل
                    var worker = new System.ComponentModel.BackgroundWorker();
                    worker.DoWork += (s, args) =>
                    {
                        args.Result = BackupManager.ValidateBackup(selectedBackup.FilePath);
                    };
                    worker.RunWorkerCompleted += (s, args) =>
                    {
                        progressBar.Visible = false;
                        
                        if ((bool)args.Result)
                        {
                            MessageBox.Show("النسخة الاحتياطية صحيحة وقابلة للاستعادة", "نجح", 
                                          MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            MessageBox.Show("النسخة الاحتياطية تالفة أو غير صالحة", "خطأ", 
                                          MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    };
                    worker.RunWorkerAsync();
                }
            }
            catch (Exception ex)
            {
                progressBar.Visible = false;
                MessageBox.Show($"خطأ في التحقق من النسخة الاحتياطية: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تصدير النسخة الاحتياطية
        /// </summary>
        private void btnExportBackup_Click(object sender, EventArgs e)
        {
            if (dgvBackups.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار نسخة احتياطية للتصدير", "تنبيه", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                var selectedBackup = dgvBackups.SelectedRows[0].DataBoundItem as BackupInfo;
                if (selectedBackup != null)
                {
                    var saveDialog = new SaveFileDialog
                    {
                        Filter = "Backup Files|*.bak",
                        Title = "تصدير النسخة الاحتياطية",
                        FileName = selectedBackup.FileName
                    };

                    if (saveDialog.ShowDialog() == DialogResult.OK)
                    {
                        File.Copy(selectedBackup.FilePath, saveDialog.FileName, true);
                        MessageBox.Show("تم تصدير النسخة الاحتياطية بنجاح", "نجح", 
                                      MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير النسخة الاحتياطية: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحديث البيانات
        /// </summary>
        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadData();
        }

      

        /// <summary>
        /// حفظ الإعدادات
        /// </summary>
        private void btnSaveSettings_Click(object sender, EventArgs e)
        {
            try
            {
                SettingsManager.SetSetting("AutoBackup", chkAutoBackup.Checked.ToString());
                SettingsManager.SetSetting("BackupInterval", numBackupInterval.Value.ToString());
                SettingsManager.SetSetting("KeepBackupsCount", numKeepBackups.Value.ToString());
                SettingsManager.SetSetting("BackupPath", txtBackupPath.Text);
                
                MessageBox.Show("تم حفظ الإعدادات بنجاح", "نجح", 
                              MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تصفح مجلد النسخ الاحتياطية
        /// </summary>
        private void btnBrowseBackupPath_Click(object sender, EventArgs e)
        {
            try
            {
                using (var folderDialog = new FolderBrowserDialog())
                {
                    folderDialog.Description = "اختر مجلد النسخ الاحتياطية";
                    folderDialog.SelectedPath = txtBackupPath.Text;

                    if (folderDialog.ShowDialog() == DialogResult.OK)
                    {
                        txtBackupPath.Text = folderDialog.SelectedPath;
                        LoadData();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصفح المجلد: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// النقر المزدوج لعرض تفاصيل النسخة
        /// </summary>
        private void dgvBackups_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                var selectedBackup = dgvBackups.SelectedRows[0].DataBoundItem as BackupInfo;
                if (selectedBackup != null)
                {
                    string details = $"اسم الملف: {selectedBackup.FileName}\n" +
                                   $"المسار: {selectedBackup.FilePath}\n" +
                                   $"الحجم: {selectedBackup.FileSizeFormatted}\n" +
                                   $"تاريخ الإنشاء: {selectedBackup.CreatedDate:yyyy/MM/dd HH:mm:ss}\n" +
                                   $"تاريخ التعديل: {selectedBackup.ModifiedDate:yyyy/MM/dd HH:mm:ss}";
                    
                    MessageBox.Show(details, "تفاصيل النسخة الاحتياطية", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }

        /// <summary>
        /// تحديد صف في الجدول
        /// </summary>
        private void dgvBackups_SelectionChanged(object sender, EventArgs e)
        {
            bool hasSelection = dgvBackups.SelectedRows.Count > 0;
            btnRestoreBackup.Enabled = hasSelection;
            btnDeleteBackup.Enabled = hasSelection;
            btnVerifyBackup.Enabled = hasSelection;
            btnExportBackup.Enabled = hasSelection;
        }

        #endregion
    }
}
