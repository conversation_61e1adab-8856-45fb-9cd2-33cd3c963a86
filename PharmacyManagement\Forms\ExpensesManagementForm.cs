using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نافذة إدارة المصروفات - Expenses Management Form
    /// </summary>
    public partial class ExpensesManagementForm : Form
    {
        #region Fields - الحقول

        private List<Expense> _expenses;
        private DateTime _fromDate;
        private DateTime _toDate;

        #endregion

        #region Constructor - المنشئ

        /// <summary>
        /// منشئ نافذة إدارة المصروفات
        /// </summary>
        public ExpensesManagementForm()
        {
            InitializeComponent();
            SetupForm();
            LoadData();
        }

        #endregion

        #region Form Setup - إعداد النافذة

        /// <summary>
        /// إعداد النافذة
        /// </summary>
        private void SetupForm()
        {
            this.Size = new Size(1200, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.WindowState = FormWindowState.Maximized;

            SetupDataGridView();
            SetupDateFilters();
            ApplyFlatDesign();
        }

        /// <summary>
        /// إعداد جدول البيانات
        /// </summary>
        private void SetupDataGridView()
        {
            dgvExpenses.AutoGenerateColumns = false;
            dgvExpenses.AllowUserToAddRows = false;
            dgvExpenses.AllowUserToDeleteRows = false;
            dgvExpenses.ReadOnly = true;
            dgvExpenses.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvExpenses.MultiSelect = false;
        }

        /// <summary>
        /// إعداد فلاتر التاريخ
        /// </summary>
        private void SetupDateFilters()
        {
            _fromDate = DateTime.Now.AddMonths(-1);
            _toDate = DateTime.Now;
            
            dtpFromDate.Value = _fromDate;
            dtpToDate.Value = _toDate;
        }

        /// <summary>
        /// تطبيق التصميم المسطح
        /// </summary>
        private void ApplyFlatDesign()
        {
            this.BackColor = Color.White;
            
            foreach (Control control in this.Controls)
            {
                if (control is Button button)
                {
                    button.FlatStyle = FlatStyle.Flat;
                    button.FlatAppearance.BorderSize = 0;
                }
            }
        }

        #endregion

        #region Data Loading - تحميل البيانات

        /// <summary>
        /// تحميل البيانات
        /// </summary>
        private void LoadData()
        {
            try
            {
                _expenses = FinancialManager.GetExpenses(_fromDate, _toDate);
                dgvExpenses.DataSource = _expenses;
                UpdateSummary();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
                LogManager.LogError($"ExpensesManagementForm.LoadData: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث الملخص
        /// </summary>
        private void UpdateSummary()
        {
            if (_expenses != null && _expenses.Any())
            {
                var totalExpenses = _expenses.Sum(e => e.Amount);
                var expenseCount = _expenses.Count;
                
                lblSummary.Text = $"عدد المصروفات: {expenseCount} | إجمالي المصروفات: {totalExpenses:F2} ر.ي";
            }
            else
            {
                lblSummary.Text = "لا توجد مصروفات في الفترة المحددة";
            }
        }

        #endregion

        #region Event Handlers - معالجات الأحداث

        /// <summary>
        /// إضافة مصروف جديد
        /// </summary>
        private void btnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                var addForm = new ExpenseAddForm();
                if (addForm.ShowDialog() == DialogResult.OK)
                {
                    LoadData();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة المصروف: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
                LogManager.LogError($"ExpensesManagementForm.btnAdd_Click: {ex.Message}");
            }
        }

        /// <summary>
        /// تعديل مصروف
        /// </summary>
        private void btnEdit_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvExpenses.SelectedRows.Count == 0)
                {
                    MessageBox.Show("يرجى اختيار مصروف للتعديل", "تنبيه", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var selectedExpense = dgvExpenses.SelectedRows[0].DataBoundItem as Expense;
                if (selectedExpense != null)
                {
                    var editForm = new ExpenseAddForm(selectedExpense);
                    if (editForm.ShowDialog() == DialogResult.OK)
                    {
                        LoadData();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعديل المصروف: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
                LogManager.LogError($"ExpensesManagementForm.btnEdit_Click: {ex.Message}");
            }
        }

        /// <summary>
        /// حذف مصروف
        /// </summary>
        private void btnDelete_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvExpenses.SelectedRows.Count == 0)
                {
                    MessageBox.Show("يرجى اختيار مصروف للحذف", "تنبيه", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var selectedExpense = dgvExpenses.SelectedRows[0].DataBoundItem as Expense;
                if (selectedExpense != null)
                {
                    var result = MessageBox.Show($"هل تريد حذف المصروف '{selectedExpense.Description}'؟", 
                                               "تأكيد الحذف", 
                                               MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    
                    if (result == DialogResult.Yes)
                    {
                        if (FinancialManager.DeleteExpense(selectedExpense.ExpenseID))
                        {
                            MessageBox.Show("تم حذف المصروف بنجاح", "نجح", 
                                          MessageBoxButtons.OK, MessageBoxIcon.Information);
                            LoadData();
                        }
                        else
                        {
                            MessageBox.Show("فشل في حذف المصروف", "خطأ", 
                                          MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف المصروف: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
                LogManager.LogError($"ExpensesManagementForm.btnDelete_Click: {ex.Message}");
            }
        }

        /// <summary>
        /// فلترة البيانات
        /// </summary>
        private void btnFilter_Click(object sender, EventArgs e)
        {
            try
            {
                _fromDate = dtpFromDate.Value.Date;
                _toDate = dtpToDate.Value.Date.AddDays(1).AddSeconds(-1);
                
                LoadData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الفلترة: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
                LogManager.LogError($"ExpensesManagementForm.btnFilter_Click: {ex.Message}");
            }
        }

        /// <summary>
        /// إعادة تعيين الفلتر
        /// </summary>
        private void btnReset_Click(object sender, EventArgs e)
        {
            try
            {
                SetupDateFilters();
                LoadData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعادة التعيين: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
                LogManager.LogError($"ExpensesManagementForm.btnReset_Click: {ex.Message}");
            }
        }

        /// <summary>
        /// تصدير البيانات
        /// </summary>
        private void btnExport_Click(object sender, EventArgs e)
        {
            try
            {
                if (_expenses == null || !_expenses.Any())
                {
                    MessageBox.Show("لا توجد بيانات للتصدير", "تنبيه", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var saveDialog = new SaveFileDialog
                {
                    Filter = "CSV Files (*.csv)|*.csv",
                    FileName = $"المصروفات_{DateTime.Now:yyyyMMdd}.csv"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    ExportManager.ExportFinancialData(_expenses, saveDialog.FileName);
                    MessageBox.Show("تم تصدير البيانات بنجاح", "نجح", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
                LogManager.LogError($"ExpensesManagementForm.btnExport_Click: {ex.Message}");
            }
        }

        /// <summary>
        /// طباعة التقرير
        /// </summary>
        private void btnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                // TODO: تنفيذ الطباعة
                MessageBox.Show("سيتم تنفيذ الطباعة قريباً", "معلومات", 
                              MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
                LogManager.LogError($"ExpensesManagementForm.btnPrint_Click: {ex.Message}");
            }
        }

        /// <summary>
        /// إغلاق النافذة
        /// </summary>
        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// النقر المزدوج على الجدول
        /// </summary>
        private void dgvExpenses_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                btnEdit_Click(sender, e);
            }
        }

        #endregion
    }
}
