using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using PharmacyManagement.Models;

namespace PharmacyManagement.Classes
{
    /// <summary>
    /// مدير الأدوية - Drug Manager
    /// يوفر طرق إدارة الأدوية والمخزون
    /// </summary>
    public static class DrugManager
    {
        #region Drug CRUD Operations - عمليات إدارة الأدوية

        /// <summary>
        /// إضافة دواء جديد
        /// </summary>
        /// <param name="drug">بيانات الدواء</param>
        /// <returns>معرف الدواء الجديد أو -1 في حالة الفشل</returns>
        public static int AddDrug(Drug drug)
        {
            try
            {
                if (!drug.IsValid())
                    return -1;

                // التحقق من عدم وجود كود الدواء مسبقاً
                if (DrugCodeExists(drug.DrugCode))
                    return -1;

                string query = @"
                    INSERT INTO Drugs (DrugCode, DrugName, ScientificName, Barcode, CategoryID, ManufacturerID,
                                     Form, Strength, PackageSize, Unit, PurchasePrice, SalePrice,
                                     MinStock, MaxStock, RequiresPrescription,
                                     IsActive, CreatedDate, CreatedBy)
                    VALUES (@DrugCode, @DrugName, @ScientificName, @Barcode, @CategoryID, @ManufacturerID,
                           @Form, @Strength, @PackageSize, @Unit, @PurchasePrice, @SalePrice,
                           @MinStock, @MaxStock, @RequiresPrescription,
                           @IsActive, @CreatedDate, @CreatedBy);
                    SELECT SCOPE_IDENTITY();";

                var parameters = new[]
                {
                    DatabaseHelper.CreateParameter("@DrugCode", drug.DrugCode),
                    DatabaseHelper.CreateParameter("@DrugName", drug.DrugName), // استخدام TradeName كـ DrugName
                    DatabaseHelper.CreateParameter("@ScientificName", drug.ScientificName),
                    DatabaseHelper.CreateParameter("@Barcode", drug.Barcode),
                    DatabaseHelper.CreateParameter("@CategoryID", drug.CategoryID),
                    DatabaseHelper.CreateParameter("@ManufacturerID", drug.ManufacturerID),
                    DatabaseHelper.CreateParameter("@Form", drug.DosageForm),
                    DatabaseHelper.CreateParameter("@Strength", drug.Strength),
                    DatabaseHelper.CreateParameter("@PackageSize", drug.PackSize),
                    DatabaseHelper.CreateParameter("@Unit", drug.Unit),
                    DatabaseHelper.CreateParameter("@PurchasePrice", drug.PurchasePrice),
                    DatabaseHelper.CreateParameter("@SalePrice", drug.SalePrice),
                    DatabaseHelper.CreateParameter("@MinStock", drug.MinStockLevel),
                    DatabaseHelper.CreateParameter("@MaxStock", drug.MaxStockLevel),
                    DatabaseHelper.CreateParameter("@RequiresPrescription", drug.RequiresPrescription),
                    DatabaseHelper.CreateParameter("@IsActive", drug.IsActive),
                    DatabaseHelper.CreateParameter("@CreatedDate", DateTime.Now),
                    DatabaseHelper.CreateParameter("@CreatedBy", UserManager.CurrentUser?.UserID)
                };

                var result = DatabaseHelper.ExecuteScalar(query, parameters);
                int drugId = Convert.ToInt32(result);

                if (drugId > 0)
                {
                    LogStockMovement(drugId, null, "In", drug.CurrentStock, "Initial Stock", "إضافة مخزون أولي");
                }

                return drugId;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("خطأ في إضافة الدواء: " + ex.Message);
                return -1;
            }
        }

    
        /// <summary>
        /// تحديث بيانات دواء
        /// </summary>
        /// <param name="drug">بيانات الدواء</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public static bool UpdateDrug(Drug drug)
        {
            try
            {
                if (!drug.IsValid() || drug.DrugID <= 0)
                    return false;

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        UPDATE Drugs
                        SET DrugCode = @DrugCode,
                            DrugName = @DrugName,
                            ScientificName = @ScientificName,
                            CategoryID = @CategoryID,
                            ManufacturerID = @ManufacturerID,
                            Form = @Form,
                            Strength = @Strength,
                            Unit = @Unit,
                            Barcode = @Barcode,
                            PurchasePrice = @PurchasePrice,
                            SalePrice = @SalePrice,
                            MinStock = @MinStock,
                            MaxStock = @MaxStock,
                            IsActive = @IsActive,
                            ModifiedDate = @ModifiedDate,
                            ModifiedBy = @ModifiedBy
                        WHERE DrugID = @DrugID";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@DrugID", drug.DrugID);
                        command.Parameters.AddWithValue("@DrugCode", drug.DrugCode);
                        command.Parameters.AddWithValue("@DrugName", drug.DrugName);
                        command.Parameters.AddWithValue("@ScientificName", drug.ScientificName ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@CategoryID", drug.CategoryID ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@ManufacturerID", drug.ManufacturerID ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Form", drug.DosageForm ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Strength", drug.Strength ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Unit", drug.Unit ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Barcode", drug.Barcode ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@PurchasePrice", drug.PurchasePrice);
                        command.Parameters.AddWithValue("@SalePrice", drug.SalePrice);
                        command.Parameters.AddWithValue("@MinStock", drug.MinStockLevel);
                        command.Parameters.AddWithValue("@MaxStock", drug.MaxStockLevel);
                        command.Parameters.AddWithValue("@IsActive", drug.IsActive);
                        command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);
                        command.Parameters.AddWithValue("@ModifiedBy", UserManager.CurrentUser?.UserID ?? (object)DBNull.Value);

                        int rowsAffected = command.ExecuteNonQuery();
                        if (rowsAffected > 0)
                        {
                            LogManager.LogInfo("تم تحديث الدواء: " + drug.DrugName);
                            return true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في تحديث الدواء: " + ex.Message);
            }

            return false;
        }

        /// <summary>
        /// تحديث مخزون الدواء
        /// </summary>
        /// <param name="drugId">معرف الدواء</param>
        /// <param name="newStock">المخزون الجديد</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public static bool UpdateDrugStock(int drugId, decimal newStock)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        UPDATE Inventory
                        SET Quantity = @Quantity,
                            UpdatedDate = @UpdatedDate
                        WHERE DrugID = @DrugID";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@DrugID", drugId);
                        command.Parameters.AddWithValue("@Quantity", newStock);
                        command.Parameters.AddWithValue("@UpdatedDate", DateTime.Now);

                        int rowsAffected = command.ExecuteNonQuery();
                        if (rowsAffected > 0)
                        {
                            LogManager.LogInfo("تم تحديث مخزون الدواء رقم: " + drugId.ToString() + " إلى " + newStock.ToString());
                            return true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في تحديث مخزون الدواء: " + ex.Message);
            }

            return false;
        }

        /// <summary>
        /// حذف الدواء (إلغاء تفعيل)
        /// </summary>
        /// <param name="drugId">معرف الدواء</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public static bool DeleteDrug(int drugId)
        {
            try
            {
                if (drugId <= 0)
                    return false;

                string query = "UPDATE Drugs SET IsActive = 0 WHERE DrugID = @DrugID";
                var parameters = new[] { DatabaseHelper.CreateParameter("@DrugID", drugId) };

                return DatabaseHelper.ExecuteNonQuery(query, parameters) > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("خطأ في حذف الدواء: " + ex.Message);
                return false;
            }
        }

        /// <summary>
        /// الحصول على دواء بالمعرف
        /// </summary>
        /// <param name="drugId">معرف الدواء</param>
        /// <returns>بيانات الدواء أو null</returns>
        public static Drug GetDrugById(int drugId)
        {
            try
            {
                string query = @"
                    SELECT d.*, c.CategoryName, m.ManufacturerName
                    FROM Drugs d
                    LEFT JOIN DrugCategories c ON d.CategoryID = c.CategoryID
                    LEFT JOIN Manufacturers m ON d.ManufacturerID = m.ManufacturerID
                    WHERE d.DrugID = @DrugID";

                var parameters = new[] { DatabaseHelper.CreateParameter("@DrugID", drugId) };

                using (var reader = DatabaseHelper.ExecuteReader(query, parameters))
                {
                    if (reader.Read())
                    {
                        return MapReaderToDrug(reader);
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("خطأ في جلب الدواء: " + ex.Message);
                return null;
            }
        }

        /// <summary>
        /// البحث عن الأدوية
        /// </summary>
        /// <param name="searchTerm">كلمة البحث</param>
        /// <param name="categoryId">معرف الفئة (اختياري)</param>
        /// <param name="manufacturerId">معرف الشركة المصنعة (اختياري)</param>
        /// <param name="includeInactive">تضمين الأدوية غير النشطة</param>
        /// <returns>قائمة الأدوية</returns>
        public static List<Drug> SearchDrugs(string searchTerm = "", int? categoryId = null, 
                                           int? manufacturerId = null, bool includeInactive = false)
        {
            var drugs = new List<Drug>();

            try
            {
                var whereConditions = new List<string>();
                var parameters = new List<SqlParameter>();

                if (!includeInactive)
                {
                    whereConditions.Add("d.IsActive = 1");
                }

                if (!string.IsNullOrWhiteSpace(searchTerm))
                {
                    whereConditions.Add("(d.DrugName LIKE @SearchTerm OR d.ScientificName LIKE @SearchTerm OR d.DrugCode LIKE @SearchTerm OR d.Barcode LIKE @SearchTerm)");
                    parameters.Add(DatabaseHelper.CreateParameter("@SearchTerm", "%" + searchTerm + "%"));
                }

                if (categoryId.HasValue)
                {
                    whereConditions.Add("d.CategoryID = @CategoryID");
                    parameters.Add(DatabaseHelper.CreateParameter("@CategoryID", categoryId.Value));
                }

                if (manufacturerId.HasValue)
                {
                    whereConditions.Add("d.ManufacturerID = @ManufacturerID");
                    parameters.Add(DatabaseHelper.CreateParameter("@ManufacturerID", manufacturerId.Value));
                }

                string whereClause = whereConditions.Count > 0 ? "WHERE " + string.Join(" AND ", whereConditions) : "";

                string query = "SELECT d.*, c.CategoryName, m.ManufacturerName " +
                              "FROM Drugs d " +
                              "LEFT JOIN DrugCategories c ON d.CategoryID = c.CategoryID " +
                              "LEFT JOIN Manufacturers m ON d.ManufacturerID = m.ManufacturerID " +
                              whereClause + " " +
                              "ORDER BY d.DrugName";

                using (var reader = DatabaseHelper.ExecuteReader(query, parameters.ToArray()))
                {
                    while (reader.Read())
                    {
                        drugs.Add(MapReaderToDrug(reader));
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("خطأ في البحث عن الأدوية: " + ex.Message);
            }

            return drugs;
        }

        /// <summary>
        /// الحصول على الأدوية منخفضة المخزون
        /// </summary>
        /// <returns>قائمة الأدوية منخفضة المخزون</returns>
        public static List<Drug> GetLowStockDrugs()
        {
            var drugs = new List<Drug>();

            try
            {
                string query = @"
                    SELECT d.*, c.CategoryName, m.ManufacturerName
                    FROM Drugs d
                    LEFT JOIN DrugCategories c ON d.CategoryID = c.CategoryID
                    LEFT JOIN Manufacturers m ON d.ManufacturerID = m.ManufacturerID
                    LEFT JOIN (SELECT DrugID, SUM(Quantity) as TotalStock FROM Inventory GROUP BY DrugID) i ON d.DrugID = i.DrugID
                    WHERE d.IsActive = 1 AND ISNULL(i.TotalStock, 0) <= d.MinStock
                    ORDER BY ISNULL(i.TotalStock, 0), d.DrugName";

                using (var reader = DatabaseHelper.ExecuteReader(query))
                {
                    while (reader.Read())
                    {
                        drugs.Add(MapReaderToDrug(reader));
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("خطأ في جلب الأدوية منخفضة المخزون: " + ex.Message);
            }

            return drugs;
        }

        #endregion

        #region Stock Management - إدارة المخزون

        /// <summary>
        /// تحديث مخزون الدواء
        /// </summary>
        /// <param name="drugId">معرف الدواء</param>
        /// <param name="quantity">الكمية (موجبة للإضافة، سالبة للخصم)</param>
        /// <param name="movementType">نوع الحركة</param>
        /// <param name="referenceType">نوع المرجع</param>
        /// <param name="referenceId">معرف المرجع</param>
        /// <param name="notes">ملاحظات</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public static bool UpdateStock(int drugId, int quantity, string movementType, 
                                     string referenceType = null, int? referenceId = null, string notes = null)
        {
            try
            {
                if (drugId <= 0 || quantity == 0)
                    return false;

                // الحصول على المخزون الحالي
                var currentStock = GetCurrentStock(drugId);
                if (currentStock < 0)
                    return false;

                // التحقق من عدم السماح بالمخزون السالب
                if (currentStock + quantity < 0)
                    return false;

                var queries = new List<string>();
                var parameters = new List<SqlParameter[]>();

                // تحديث المخزون
                string updateStockQuery = "UPDATE Inventory SET Quantity = Quantity + @Quantity WHERE DrugID = @DrugID";
                var updateStockParams = new[]
                {
                    DatabaseHelper.CreateParameter("@DrugID", drugId),
                    DatabaseHelper.CreateParameter("@Quantity", quantity)
                };
                queries.Add(updateStockQuery);
                parameters.Add(updateStockParams);

                // تسجيل حركة المخزون
                string logMovementQuery = @"
                    INSERT INTO StockMovements (DrugID, MovementType, Quantity, ReferenceType, ReferenceID, MovementDate, Notes, CreatedBy)
                    VALUES (@DrugID, @MovementType, @Quantity, @ReferenceType, @ReferenceID, @MovementDate, @Notes, @CreatedBy)";
                
                var logMovementParams = new[]
                {
                    DatabaseHelper.CreateParameter("@DrugID", drugId),
                    DatabaseHelper.CreateParameter("@MovementType", movementType),
                    DatabaseHelper.CreateParameter("@Quantity", Math.Abs(quantity)),
                    DatabaseHelper.CreateParameter("@ReferenceType", referenceType),
                    DatabaseHelper.CreateParameter("@ReferenceID", referenceId),
                    DatabaseHelper.CreateParameter("@MovementDate", DateTime.Now),
                    DatabaseHelper.CreateParameter("@Notes", notes),
                    DatabaseHelper.CreateParameter("@CreatedBy", UserManager.CurrentUser != null ? UserManager.CurrentUser.UserID : (object)DBNull.Value)
                };
                queries.Add(logMovementQuery);
                parameters.Add(logMovementParams);

                return DatabaseHelper.ExecuteTransaction(queries.ToArray(), parameters.ToArray());
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("خطأ في تحديث المخزون: " + ex.Message);
                return false;
            }
        }

        /// <summary>
        /// الحصول على المخزون الحالي للدواء
        /// </summary>
        /// <param name="drugId">معرف الدواء</param>
        /// <returns>المخزون الحالي أو -1 في حالة الخطأ</returns>
        public static int GetCurrentStock(int drugId)
        {
            try
            {
                string query = "SELECT SUM(Quantity) FROM Inventory WHERE DrugID = @DrugID";
                var parameters = new[] { DatabaseHelper.CreateParameter("@DrugID", drugId) };

                var result = DatabaseHelper.ExecuteScalar(query, parameters);
                return result != null ? Convert.ToInt32(result) : -1;
            }
            catch
            {
                return -1;
            }
        }

        /// <summary>
        /// البحث عن الأدوية مع حساب المخزون الفعلي
        /// </summary>
        /// <param name="searchTerm">كلمة البحث</param>
        /// <param name="categoryId">معرف الفئة (اختياري)</param>
        /// <param name="manufacturerId">معرف الشركة المصنعة (اختياري)</param>
        /// <param name="includeInactive">تضمين الأدوية غير النشطة</param>
        /// <param name="onlyInStock">عرض الأدوية المتوفرة في المخزون فقط</param>
        /// <returns>قائمة الأدوية مع المخزون الفعلي</returns>
        public static List<Drug> SearchDrugsWithStock(string searchTerm = "", int? categoryId = null,
                                                    int? manufacturerId = null, bool includeInactive = false,
                                                    bool onlyInStock = false)
        {
            var drugs = new List<Drug>();

            try
            {
                var whereConditions = new List<string>();
                var parameters = new List<SqlParameter>();

                if (!includeInactive)
                {
                    whereConditions.Add("d.IsActive = 1");
                }

                if (!string.IsNullOrWhiteSpace(searchTerm))
                {
                    whereConditions.Add("(d.DrugName LIKE @SearchTerm OR d.ScientificName LIKE @SearchTerm OR d.DrugCode LIKE @SearchTerm OR d.Barcode LIKE @SearchTerm)");
                    parameters.Add(DatabaseHelper.CreateParameter("@SearchTerm", "%" + searchTerm + "%"));
                }

                if (categoryId.HasValue)
                {
                    whereConditions.Add("d.CategoryID = @CategoryID");
                    parameters.Add(DatabaseHelper.CreateParameter("@CategoryID", categoryId.Value));
                }

                if (manufacturerId.HasValue)
                {
                    whereConditions.Add("d.ManufacturerID = @ManufacturerID");
                    parameters.Add(DatabaseHelper.CreateParameter("@ManufacturerID", manufacturerId.Value));
                }

                if (onlyInStock)
                {
                    whereConditions.Add("ISNULL(i.TotalStock, 0) > 0");
                }

                string whereClause = whereConditions.Count > 0 ? "WHERE " + string.Join(" AND ", whereConditions) : "";

                string query = @"
                    SELECT d.DrugID, d.DrugCode, d.DrugName, d.ScientificName, d.Barcode,
                           d.CategoryID, d.ManufacturerID, d.Form, d.Strength, d.PackageSize,
                           d.Unit, d.PurchasePrice, d.SalePrice, d.MinStock, d.MaxStock,
                           d.RequiresPrescription, d.IsActive, d.CreatedDate, d.CreatedBy,
                           c.CategoryName, m.ManufacturerName,
                           ISNULL(i.TotalStock, 0) as CurrentStock
                    FROM Drugs d
                    LEFT JOIN DrugCategories c ON d.CategoryID = c.CategoryID
                    LEFT JOIN Manufacturers m ON d.ManufacturerID = m.ManufacturerID
                    LEFT JOIN (
                        SELECT DrugID, SUM(Quantity) as TotalStock
                        FROM Inventory
                        WHERE ExpiryDate > GETDATE() OR ExpiryDate IS NULL
                        GROUP BY DrugID
                    ) i ON d.DrugID = i.DrugID " +
                    whereClause + " " +
                    "ORDER BY d.DrugName";

                using (var reader = DatabaseHelper.ExecuteReader(query, parameters.ToArray()))
                {
                    while (reader.Read())
                    {
                        var drug = MapReaderToDrug(reader);
                        // تحديث المخزون الفعلي
                        drug.CurrentStock = HasColumn(reader, "CurrentStock") ?
                                          Convert.ToInt32(reader["CurrentStock"]) : 0;
                        drugs.Add(drug);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("خطأ في البحث مع المخزون: " + ex.Message);
            }

            return drugs;
        }

        /// <summary>
        /// دالة اختبار لعرض بيانات الأدوية الأساسية
        /// </summary>
        public static List<Drug> TestGetBasicDrugs()
        {
            var drugs = new List<Drug>();
            try
            {
                string query = "SELECT DrugID, DrugCode, DrugName FROM Drugs WHERE IsActive = 1 ORDER BY DrugName";

                using (var reader = DatabaseHelper.ExecuteReader(query))
                {
                    while (reader.Read())
                    {
                        drugs.Add(new Drug
                        {
                            DrugID = Convert.ToInt32(reader["DrugID"]),
                            DrugCode = reader["DrugCode"]?.ToString() ?? "",
                            DrugName = reader["DrugName"]?.ToString() ?? "",
                            IsActive = true,
                            CurrentStock = 0
                        });

                        System.Diagnostics.Debug.WriteLine($"دواء: {reader["DrugName"]} - كود: {reader["DrugCode"]}");
                    }
                }

                System.Diagnostics.Debug.WriteLine($"تم تحميل {drugs.Count} دواء");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في الاختبار: {ex.Message}");
            }

            return drugs;
        }

        #endregion

        #region Helper Methods - الطرق المساعدة

        /// <summary>
        /// التحقق من وجود كود الدواء
        /// </summary>
        /// <param name="drugCode">كود الدواء</param>
        /// <returns>true إذا كان موجوداً</returns>
        private static bool DrugCodeExists(string drugCode)
        {
            try
            {
                string query = "SELECT COUNT(*) FROM Drugs WHERE DrugCode = @DrugCode";
                var parameters = new[] { DatabaseHelper.CreateParameter("@DrugCode", drugCode) };

                var result = DatabaseHelper.ExecuteScalar(query, parameters);
                return Convert.ToInt32(result) > 0;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// تحويل SqlDataReader إلى كائن Drug
        /// </summary>
        /// <param name="reader">قارئ البيانات</param>
        /// <returns>كائن Drug</returns>
        private static Drug MapReaderToDrug(SqlDataReader reader)
        {
            try
            {
                var drug = new Drug
                {
                    DrugID = Convert.ToInt32(reader["DrugID"]),
                    DrugCode = reader["DrugCode"]?.ToString() ?? "",
                    DrugName = reader["DrugName"]?.ToString() ?? "",
                ScientificName = reader["ScientificName"] == DBNull.Value ? null : reader["ScientificName"].ToString(),
                Barcode = reader["Barcode"] == DBNull.Value ? null : reader["Barcode"].ToString(),
                CategoryID = reader["CategoryID"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["CategoryID"]),
                CategoryName = HasColumn(reader, "CategoryName") && reader["CategoryName"] != DBNull.Value ? reader["CategoryName"].ToString() : null,
                ManufacturerID = reader["ManufacturerID"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["ManufacturerID"]),
                ManufacturerName = HasColumn(reader, "ManufacturerName") && reader["ManufacturerName"] != DBNull.Value ? reader["ManufacturerName"].ToString() : null,
                DosageForm = reader["Form"] == DBNull.Value ? null : reader["Form"].ToString(), // تصحيح: Form بدلاً من DosageForm
                Strength = reader["Strength"] == DBNull.Value ? null : reader["Strength"].ToString(),
                PackSize = Convert.ToInt32(reader["PackageSize"]), // تصحيح: PackageSize بدلاً من PackSize
                Unit = reader["Unit"] == DBNull.Value ? null : reader["Unit"].ToString(),
                PurchasePrice = Convert.ToDecimal(reader["PurchasePrice"]),
                SalePrice = Convert.ToDecimal(reader["SalePrice"]),
                MinStockLevel = Convert.ToInt32(reader["MinStock"]), // تصحيح: MinStock بدلاً من MinStockLevel
                MaxStockLevel = Convert.ToInt32(reader["MaxStock"]), // تصحيح: MaxStock بدلاً من MaxStockLevel
                CurrentStock = 0, // المخزون سيتم حسابه من جدول Inventory منفصل
                Location = HasColumn(reader, "Location") && reader["Location"] != DBNull.Value ? reader["Location"].ToString() : null,
                RequiresPrescription = Convert.ToBoolean(reader["RequiresPrescription"]),
                IsActive = Convert.ToBoolean(reader["IsActive"]),
                Notes = HasColumn(reader, "Notes") && reader["Notes"] != DBNull.Value ? reader["Notes"].ToString() : null,
                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                    CreatedBy = reader["CreatedBy"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["CreatedBy"])
                };

                // تسجيل للتشخيص
                System.Diagnostics.Debug.WriteLine($"تم تحميل دواء: ID={drug.DrugID}, Code={drug.DrugCode}, Name='{drug.DrugName}', Stock={drug.CurrentStock}");

                return drug;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحويل بيانات الدواء: {ex.Message}");
                // إرجاع دواء بقيم افتراضية في حالة الخطأ
                return new Drug
                {
                    DrugID = Convert.ToInt32(reader["DrugID"]),
                    DrugCode = reader["DrugCode"]?.ToString() ?? "N/A",
                    DrugName = "خطأ في تحميل الاسم",
                    IsActive = true,
                    CurrentStock = 0
                };
            }
        }

        /// <summary>
        /// فحص وجود عمود في SqlDataReader
        /// </summary>
        private static bool HasColumn(SqlDataReader reader, string columnName)
        {
            for (int i = 0; i < reader.FieldCount; i++)
            {
                if (reader.GetName(i).Equals(columnName, StringComparison.OrdinalIgnoreCase))
                    return true;
            }
            return false;
        }

        /// <summary>
        /// تسجيل حركة المخزون
        /// </summary>
        /// <param name="drugId">معرف الدواء</param>
        /// <param name="batchId">معرف الدفعة</param>
        /// <param name="movementType">نوع الحركة</param>
        /// <param name="quantity">الكمية</param>
        /// <param name="referenceType">نوع المرجع</param>
        /// <param name="notes">ملاحظات</param>
        private static void LogStockMovement(int drugId, int? batchId, string movementType, 
                                           int quantity, string referenceType, string notes)
        {
            try
            {
                string query = @"
                    INSERT INTO StockMovements (DrugID, BatchID, MovementType, Quantity, ReferenceType, MovementDate, Notes, CreatedBy)
                    VALUES (@DrugID, @BatchID, @MovementType, @Quantity, @ReferenceType, @MovementDate, @Notes, @CreatedBy)";

                var parameters = new[]
                {
                    DatabaseHelper.CreateParameter("@DrugID", drugId),
                    DatabaseHelper.CreateParameter("@BatchID", batchId),
                    DatabaseHelper.CreateParameter("@MovementType", movementType),
                    DatabaseHelper.CreateParameter("@Quantity", quantity),
                    DatabaseHelper.CreateParameter("@ReferenceType", referenceType),
                    DatabaseHelper.CreateParameter("@MovementDate", DateTime.Now),
                    DatabaseHelper.CreateParameter("@Notes", notes),
                    DatabaseHelper.CreateParameter("@CreatedBy", UserManager.CurrentUser?.UserID)
                };

                DatabaseHelper.ExecuteNonQuery(query, parameters);
            }
            catch
            {
                // تجاهل أخطاء تسجيل الحركة
            }
        }

        /// <summary>
        /// الحصول على الأدوية منتهية الصلاحية
        /// </summary>
        /// <returns>قائمة الأدوية منتهية الصلاحية</returns>
        public static List<Drug> GetExpiredDrugs()
        {
            var drugs = new List<Drug>();
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT d.*, c.CategoryName, m.ManufacturerName
                        FROM Drugs d
                        LEFT JOIN Categories c ON d.CategoryID = c.CategoryID
                        LEFT JOIN Manufacturers m ON d.ManufacturerID = m.ManufacturerID
                        WHERE d.ExpiryDate < GETDATE() AND d.IsActive = 1
                        ORDER BY d.ExpiryDate";

                    using (var command = new SqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                drugs.Add(MapReaderToDrug(reader));
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في الحصول على الأدوية منتهية الصلاحية: " + ex.Message);
            }
            return drugs;
        }

        /// <summary>
        /// الحصول على إجمالي عدد الأدوية
        /// </summary>
        /// <returns>عدد الأدوية</returns>
        public static int GetTotalDrugsCount()
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = "SELECT COUNT(*) FROM Drugs WHERE IsActive = 1";

                    using (var command = new SqlCommand(query, connection))
                    {
                        var result = command.ExecuteScalar();
                        return result != null ? Convert.ToInt32(result) : 0;
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في حساب عدد الأدوية: " + ex.Message);
                return 0;
            }
        }

        /// <summary>
        /// الحصول على إجمالي قيمة المخزون
        /// </summary>
        /// <returns>إجمالي قيمة المخزون</returns>
        public static decimal GetTotalInventoryValue()
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT ISNULL(SUM(CurrentStock * PurchasePrice), 0)
                        FROM Drugs
                        WHERE IsActive = 1 AND CurrentStock > 0";

                    using (var command = new SqlCommand(query, connection))
                    {
                        var result = command.ExecuteScalar();
                        return result != null ? Convert.ToDecimal(result) : 0;
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في حساب إجمالي قيمة المخزون: " + ex.Message);
                return 0;
            }
        }

        #endregion
    }
}


