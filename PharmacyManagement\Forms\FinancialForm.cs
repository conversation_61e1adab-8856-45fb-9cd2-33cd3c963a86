using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PharmacyManagement.Classes;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نافذة الإدارة المالية - Financial Management Form
    /// </summary>
    public partial class FinancialForm : Form
    {
        #region Constructor - المنشئ

        public FinancialForm()
        {
            InitializeComponent();
            SetupForm();
            LoadData();
        }

        #endregion

        #region Form Setup - إعداد النافذة

        /// <summary>
        /// إعداد النافذة
        /// </summary>
        private void SetupForm()
        {
            // إعداد التواريخ الافتراضية
            dtpFromDate.Value = DateTime.Now.AddMonths(-1);
            dtpToDate.Value = DateTime.Now;
            
            // إعداد التصميم المسطح
            ApplyFlatDesign();
        }

        /// <summary>
        /// تطبيق التصميم المسطح
        /// </summary>
        private void ApplyFlatDesign()
        {
            // إزالة حدود الأزرار
            foreach (Control control in this.Controls)
            {
                ApplyFlatStyleToControl(control);
            }
        }

        /// <summary>
        /// تطبيق التصميم المسطح على عنصر
        /// </summary>
        private void ApplyFlatStyleToControl(Control control)
        {
            if (control is Button button)
            {
                button.FlatAppearance.BorderSize = 0;
                button.Cursor = Cursors.Hand;
            }
            
            // تطبيق على العناصر الفرعية
            foreach (Control child in control.Controls)
            {
                ApplyFlatStyleToControl(child);
            }
        }

        #endregion

        #region Data Loading - تحميل البيانات

        /// <summary>
        /// تحميل البيانات المالية
        /// </summary>
        private void LoadData()
        {
            try
            {
                DateTime fromDate = dtpFromDate.Value.Date;
                DateTime toDate = dtpToDate.Value.Date.AddDays(1).AddSeconds(-1);
                
                // تحميل الإحصائيات المالية
                LoadFinancialSummary(fromDate, toDate);
                
                // تحميل المعاملات المالية
                LoadTransactions(fromDate, toDate);
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تحميل البيانات: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحميل الملخص المالي
        /// </summary>
        private void LoadFinancialSummary(DateTime fromDate, DateTime toDate)
        {
            try
            {
                var summary = FinancialManager.GetFinancialSummary(fromDate, toDate);
                
                // تحديث البطاقات المالية
                lblTotalSales.Text = "إجمالي المبيعات: " + summary.TotalSales.ToString("N2") + " ر.ي";
                lblTotalPurchases.Text = "إجمالي المشتريات: " + summary.TotalPurchases.ToString("N2") + " ر.ي";
                lblTotalProfit.Text = "إجمالي الأرباح: " + summary.TotalProfit.ToString("N2") + " ر.ي";
                lblTotalExpenses.Text = "إجمالي المصروفات: " + summary.TotalExpenses.ToString("N2") + " ر.ي";
                lblNetProfit.Text = "صافي الربح: " + summary.NetProfit.ToString("N2") + " ر.ي";
                
                // تحديث لون صافي الربح
                if (summary.NetProfit >= 0)
                {
                    lblNetProfit.ForeColor = Color.Green;
                }
                else
                {
                    lblNetProfit.ForeColor = Color.Red;
                }
                
                // تحديث الرسم البياني
                UpdateChart(summary);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("خطأ في تحميل الملخص المالي: " + ex.Message);
            }
        }

        /// <summary>
        /// تحميل المعاملات المالية
        /// </summary>
        private void LoadTransactions(DateTime fromDate, DateTime toDate)
        {
            try
            {
                var transactions = FinancialManager.GetTransactions(fromDate, toDate);
                dgvTransactions.DataSource = transactions;
                
                // تحديث عدد السجلات
                lblRecordsCount.Text = "عدد المعاملات: " + transactions.Count.ToString();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("خطأ في تحميل المعاملات: " + ex.Message);
            }
        }

        /// <summary>
        /// تحديث الرسم البياني
        /// </summary>
        private void UpdateChart(FinancialSummary summary)
        {
            try
            {
                // مسح البيانات السابقة
                chartFinancial.Series.Clear();
                
                // إضافة سلسلة البيانات
                var series = chartFinancial.Series.Add("البيانات المالية");
                series.ChartType = System.Windows.Forms.DataVisualization.Charting.SeriesChartType.Column;
                
                // إضافة النقاط
                series.Points.AddXY("المبيعات", summary.TotalSales);
                series.Points.AddXY("المشتريات", summary.TotalPurchases);
                series.Points.AddXY("الأرباح", summary.TotalProfit);
                series.Points.AddXY("المصروفات", summary.TotalExpenses);
                
                // تنسيق الألوان
                series.Points[0].Color = Color.Green;
                series.Points[1].Color = Color.Red;
                series.Points[2].Color = Color.Blue;
                series.Points[3].Color = Color.Orange;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("خطأ في تحديث الرسم البياني: " + ex.Message);
            }
        }

        #endregion

        #region Event Handlers - معالجات الأحداث

        /// <summary>
        /// تحديث البيانات
        /// </summary>
        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadData();
        }

        /// <summary>
        /// إضافة مصروف
        /// </summary>
        private void btnAddExpense_Click(object sender, EventArgs e)
        {
            try
            {
                var expenseForm = new ExpenseAddForm();
                if (expenseForm.ShowDialog() == DialogResult.OK)
                {
                    LoadData();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في فتح نافذة المصروفات: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إدارة المصروفات
        /// </summary>
        private void btnManageExpenses_Click(object sender, EventArgs e)
        {
            try
            {
                var expensesForm = new ExpensesManagementForm();
                if (expensesForm.ShowDialog() == DialogResult.OK)
                {
                    LoadData();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في فتح إدارة المصروفات: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تقرير مالي مفصل
        /// </summary>
        private void btnDetailedReport_Click(object sender, EventArgs e)
        {
            try
            {
                // TODO: Create FinancialReportForm
                MessageBox.Show("تقرير مالي مفصل - قيد التطوير", "معلومات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                // var reportForm = new FinancialReportForm(dtpFromDate.Value, dtpToDate.Value);
                // reportForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في فتح التقرير المالي: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تصدير البيانات المالية
        /// </summary>
        private void btnExport_Click(object sender, EventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "Excel Files|*.xlsx|PDF Files|*.pdf",
                    Title = "تصدير البيانات المالية",
                    FileName = "Financial_Report_" + DateTime.Now.ToString("yyyyMMdd")
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    var transactions = dgvTransactions.DataSource as List<FinancialTransaction>;
                    if (transactions != null)
                    {
                        ExportManager.ExportFinancialData(dtpFromDate.Value.ToString("yyyy-MM-dd"), dtpToDate.Value.ToString("yyyy-MM-dd"));
                        MessageBox.Show("تم تصدير البيانات بنجاح", "نجح", 
                                      MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تصدير البيانات: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تغيير التاريخ من
        /// </summary>
        private void dtpFromDate_ValueChanged(object sender, EventArgs e)
        {
            if (dtpFromDate.Value > dtpToDate.Value)
            {
                dtpToDate.Value = dtpFromDate.Value;
            }
            LoadData();
        }

        /// <summary>
        /// تغيير التاريخ إلى
        /// </summary>
        private void dtpToDate_ValueChanged(object sender, EventArgs e)
        {
            if (dtpToDate.Value < dtpFromDate.Value)
            {
                dtpFromDate.Value = dtpToDate.Value;
            }
            LoadData();
        }

        /// <summary>
        /// فترات سريعة - اليوم
        /// </summary>
        private void btnToday_Click(object sender, EventArgs e)
        {
            dtpFromDate.Value = DateTime.Now.Date;
            dtpToDate.Value = DateTime.Now.Date;
            LoadData();
        }

        /// <summary>
        /// فترات سريعة - هذا الأسبوع
        /// </summary>
        private void btnThisWeek_Click(object sender, EventArgs e)
        {
            var startOfWeek = DateTime.Now.AddDays(-(int)DateTime.Now.DayOfWeek);
            dtpFromDate.Value = startOfWeek.Date;
            dtpToDate.Value = DateTime.Now.Date;
            LoadData();
        }

        /// <summary>
        /// فترات سريعة - هذا الشهر
        /// </summary>
        private void btnThisMonth_Click(object sender, EventArgs e)
        {
            dtpFromDate.Value = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
            dtpToDate.Value = DateTime.Now.Date;
            LoadData();
        }

        /// <summary>
        /// فترات سريعة - هذا العام
        /// </summary>
        private void btnThisYear_Click(object sender, EventArgs e)
        {
            dtpFromDate.Value = new DateTime(DateTime.Now.Year, 1, 1);
            dtpToDate.Value = DateTime.Now.Date;
            LoadData();
        }

        #endregion
    }
}



