using System;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نموذج إدارة السنوات المالية - Fiscal Year Management Form
    /// </summary>
    public partial class FiscalYearForm : Form
    {
        #region Fields

        private BindingSource _bindingSource;
        private int? _selectedFiscalYearId;

        #endregion

        #region Constructor

        public FiscalYearForm()
        {
            InitializeComponent();
            SetupForm();
            LoadData();
        }

        #endregion

        #region Setup Methods

        private void SetupForm()
        {
            this.Text = "إدارة السنوات المالية";
            this.WindowState = FormWindowState.Maximized;
            
            // إعداد التصميم المسطح
            SetupFlatDesign();
            
            // إعداد الشبكة
            SetupDataGrid();
            
            // إعداد الأحداث
            SetupEvents();
        }

        private void SetupFlatDesign()
        {
            // إعدادات النموذج
            this.BackColor = Color.White;
            this.Font = new Font("Segoe UI", 9F);
            
            // إعداد الأزرار
            foreach (Control control in this.Controls)
            {
                if (control is Button btn)
                {
                    btn.FlatStyle = FlatStyle.Flat;
                    btn.FlatAppearance.BorderSize = 1;
                    btn.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
                }
                else if (control is TextBox txt)
                {
                    txt.BorderStyle = BorderStyle.FixedSingle;
                    txt.Font = new Font("Segoe UI", 10F);
                }
                else if (control is ComboBox cmb)
                {
                    cmb.FlatStyle = FlatStyle.Flat;
                    cmb.Font = new Font("Segoe UI", 10F);
                }
            }

            // ألوان الأزرار
            btnAdd.BackColor = Color.FromArgb(46, 204, 113);
            btnAdd.ForeColor = Color.White;
            btnEdit.BackColor = Color.FromArgb(52, 152, 219);
            btnEdit.ForeColor = Color.White;
            btnDelete.BackColor = Color.FromArgb(231, 76, 60);
            btnDelete.ForeColor = Color.White;
            btnActivate.BackColor = Color.FromArgb(155, 89, 182);
            btnActivate.ForeColor = Color.White;
            btnClose.BackColor = Color.FromArgb(243, 156, 18);
            btnClose.ForeColor = Color.White;
            btnReopen.BackColor = Color.FromArgb(26, 188, 156);
            btnReopen.ForeColor = Color.White;
        }

        private void SetupDataGrid()
        {
            // إعداد الشبكة
            dgvFiscalYears.AutoGenerateColumns = false;
            dgvFiscalYears.AllowUserToAddRows = false;
            dgvFiscalYears.AllowUserToDeleteRows = false;
            dgvFiscalYears.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvFiscalYears.MultiSelect = false;
            dgvFiscalYears.BackgroundColor = Color.White;
            dgvFiscalYears.BorderStyle = BorderStyle.FixedSingle;
            dgvFiscalYears.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 152, 219);
            dgvFiscalYears.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvFiscalYears.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            dgvFiscalYears.DefaultCellStyle.Font = new Font("Segoe UI", 9F);
            dgvFiscalYears.RowHeadersVisible = false;
            dgvFiscalYears.EnableHeadersVisualStyles = false;

            // إضافة الأعمدة
            dgvFiscalYears.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "YearName",
                HeaderText = "اسم السنة",
                DataPropertyName = "YearName",
                Width = 120
            });

            dgvFiscalYears.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "StartDate",
                HeaderText = "تاريخ البداية",
                DataPropertyName = "StartDate",
                Width = 120,
                DefaultCellStyle = { Format = "dd/MM/yyyy" }
            });

            dgvFiscalYears.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "EndDate",
                HeaderText = "تاريخ النهاية",
                DataPropertyName = "EndDate",
                Width = 120,
                DefaultCellStyle = { Format = "dd/MM/yyyy" }
            });

            dgvFiscalYears.Columns.Add(new DataGridViewCheckBoxColumn
            {
                Name = "IsActive",
                HeaderText = "نشطة",
                DataPropertyName = "IsActive",
                Width = 80
            });

            dgvFiscalYears.Columns.Add(new DataGridViewCheckBoxColumn
            {
                Name = "IsClosed",
                HeaderText = "مقفلة",
                DataPropertyName = "IsClosed",
                Width = 80
            });

            dgvFiscalYears.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CreatedDate",
                HeaderText = "تاريخ الإنشاء",
                DataPropertyName = "CreatedDate",
                Width = 120,
                DefaultCellStyle = { Format = "dd/MM/yyyy" }
            });

            dgvFiscalYears.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ClosedDate",
                HeaderText = "تاريخ الإقفال",
                DataPropertyName = "ClosedDate",
                Width = 120,
                DefaultCellStyle = { Format = "dd/MM/yyyy" }
            });

            // إعداد مصدر البيانات
            _bindingSource = new BindingSource();
            dgvFiscalYears.DataSource = _bindingSource;
        }

        private void SetupEvents()
        {
            // ربط الأحداث
            btnAdd.Click += BtnAdd_Click;
            btnEdit.Click += BtnEdit_Click;
            btnDelete.Click += BtnDelete_Click;
            btnActivate.Click += BtnActivate_Click;
            btnClose.Click += BtnClose_Click;
            btnReopen.Click += BtnReopen_Click;
            btnRefresh.Click += BtnRefresh_Click;
            
            dgvFiscalYears.SelectionChanged += DgvFiscalYears_SelectionChanged;
            dgvFiscalYears.CellFormatting += DgvFiscalYears_CellFormatting;
        }

        #endregion

        #region Load Data Methods

        private void LoadData()
        {
            try
            {
                var fiscalYears = FiscalYearManager.GetAllFiscalYears();
                _bindingSource.DataSource = fiscalYears;
                
                UpdateButtonStates();
                UpdateStatusLabel();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Event Handlers

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                using (var addForm = new FiscalYearAddEditForm())
                {
                    if (addForm.ShowDialog() == DialogResult.OK)
                    {
                        LoadData();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة السنة المالية: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            try
            {
                if (_selectedFiscalYearId.HasValue)
                {
                    using (var editForm = new FiscalYearAddEditForm(_selectedFiscalYearId.Value))
                    {
                        if (editForm.ShowDialog() == DialogResult.OK)
                        {
                            LoadData();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعديل السنة المالية: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            try
            {
                if (_selectedFiscalYearId.HasValue)
                {
                    var result = MessageBox.Show("هل تريد حذف السنة المالية المحددة؟", "تأكيد الحذف",
                        MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    
                    if (result == DialogResult.Yes)
                    {
                        if (FiscalYearManager.DeleteFiscalYear(_selectedFiscalYearId.Value))
                        {
                            MessageBox.Show("تم حذف السنة المالية بنجاح", "نجح",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                            LoadData();
                        }
                        else
                        {
                            MessageBox.Show("فشل في حذف السنة المالية", "خطأ",
                                MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف السنة المالية: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnActivate_Click(object sender, EventArgs e)
        {
            try
            {
                if (_selectedFiscalYearId.HasValue)
                {
                    var result = MessageBox.Show("هل تريد تفعيل السنة المالية المحددة؟", "تأكيد التفعيل",
                        MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    
                    if (result == DialogResult.Yes)
                    {
                        if (FiscalYearManager.ActivateFiscalYear(_selectedFiscalYearId.Value))
                        {
                            MessageBox.Show("تم تفعيل السنة المالية بنجاح", "نجح",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                            LoadData();
                        }
                        else
                        {
                            MessageBox.Show("فشل في تفعيل السنة المالية", "خطأ",
                                MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تفعيل السنة المالية: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnClose_Click(object sender, EventArgs e)
        {
            try
            {
                if (_selectedFiscalYearId.HasValue)
                {
                    var result = MessageBox.Show("هل تريد إقفال السنة المالية المحددة؟ لن تتمكن من إضافة معاملات جديدة بعد الإقفال.", 
                        "تأكيد الإقفال", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
                    
                    if (result == DialogResult.Yes)
                    {
                        if (FiscalYearManager.CloseFiscalYear(_selectedFiscalYearId.Value))
                        {
                            MessageBox.Show("تم إقفال السنة المالية بنجاح", "نجح",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                            LoadData();
                        }
                        else
                        {
                            MessageBox.Show("فشل في إقفال السنة المالية", "خطأ",
                                MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إقفال السنة المالية: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnReopen_Click(object sender, EventArgs e)
        {
            try
            {
                if (_selectedFiscalYearId.HasValue)
                {
                    var result = MessageBox.Show("هل تريد إعادة فتح السنة المالية المحددة؟", "تأكيد إعادة الفتح",
                        MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    
                    if (result == DialogResult.Yes)
                    {
                        if (FiscalYearManager.ReopenFiscalYear(_selectedFiscalYearId.Value))
                        {
                            MessageBox.Show("تم إعادة فتح السنة المالية بنجاح", "نجح",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                            LoadData();
                        }
                        else
                        {
                            MessageBox.Show("فشل في إعادة فتح السنة المالية", "خطأ",
                                MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعادة فتح السنة المالية: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadData();
        }

        private void DgvFiscalYears_SelectionChanged(object sender, EventArgs e)
        {
            if (dgvFiscalYears.SelectedRows.Count > 0)
            {
                var selectedRow = dgvFiscalYears.SelectedRows[0];
                if (selectedRow.DataBoundItem is FiscalYear fiscalYear)
                {
                    _selectedFiscalYearId = fiscalYear.FiscalYearID;
                    UpdateButtonStates();
                }
            }
            else
            {
                _selectedFiscalYearId = null;
                UpdateButtonStates();
            }
        }

        private void DgvFiscalYears_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (dgvFiscalYears.Rows[e.RowIndex].DataBoundItem is FiscalYear fiscalYear)
            {
                // تلوين الصف حسب الحالة
                if (fiscalYear.IsActive)
                {
                    dgvFiscalYears.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.LightGreen;
                }
                else if (fiscalYear.IsClosed)
                {
                    dgvFiscalYears.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.LightCoral;
                }
                else
                {
                    dgvFiscalYears.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.White;
                }
            }
        }

        #endregion

        #region Helper Methods

        private void UpdateButtonStates()
        {
            bool hasSelection = _selectedFiscalYearId.HasValue;
            
            btnEdit.Enabled = hasSelection;
            btnDelete.Enabled = hasSelection;
            btnActivate.Enabled = hasSelection;
            btnClose.Enabled = hasSelection;
            btnReopen.Enabled = hasSelection;

            if (hasSelection && dgvFiscalYears.SelectedRows.Count > 0)
            {
                var selectedRow = dgvFiscalYears.SelectedRows[0];
                if (selectedRow.DataBoundItem is FiscalYear fiscalYear)
                {
                    btnActivate.Enabled = !fiscalYear.IsActive && !fiscalYear.IsClosed;
                    btnClose.Enabled = !fiscalYear.IsClosed;
                    btnReopen.Enabled = fiscalYear.IsClosed;
                    btnEdit.Enabled = !fiscalYear.IsClosed;
                    btnDelete.Enabled = !fiscalYear.IsClosed;
                }
            }
        }

        private void UpdateStatusLabel()
        {
            var activeFiscalYear = FiscalYearManager.GetActiveFiscalYear();
            if (activeFiscalYear != null)
            {
                lblActiveYear.Text = $"السنة المالية النشطة: {activeFiscalYear.YearName} ({activeFiscalYear.StartDate:dd/MM/yyyy} - {activeFiscalYear.EndDate:dd/MM/yyyy})";
                lblActiveYear.ForeColor = Color.FromArgb(46, 204, 113);
            }
            else
            {
                lblActiveYear.Text = "لا توجد سنة مالية نشطة";
                lblActiveYear.ForeColor = Color.FromArgb(231, 76, 60);
            }
        }

        #endregion
    }
}
