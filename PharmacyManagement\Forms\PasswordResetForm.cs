using System;
using System.Drawing;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نافذة إعادة تعيين كلمة المرور - Password Reset Form
    /// </summary>
    public partial class PasswordResetForm : Form
    {
        #region Fields - الحقول

        private int _userID;
        private User _user;

        #endregion

        #region Constructor - المنشئ

        /// <summary>
        /// منشئ نافذة إعادة تعيين كلمة المرور
        /// </summary>
        /// <param name="userID">معرف المستخدم</param>
        public PasswordResetForm(int userID)
        {
            InitializeComponent();
            _userID = userID;
            SetupForm();
            LoadUserData();
        }

        #endregion

        #region Form Setup - إعداد النافذة

        /// <summary>
        /// إعداد النافذة
        /// </summary>
        private void SetupForm()
        {
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            ApplyFlatDesign();
        }

        /// <summary>
        /// تطبيق التصميم المسطح
        /// </summary>
        private void ApplyFlatDesign()
        {
            this.BackColor = Color.White;
            
            foreach (Control control in this.Controls)
            {
                if (control is Button button)
                {
                    button.FlatStyle = FlatStyle.Flat;
                    button.FlatAppearance.BorderSize = 0;
                }
            }
        }

        #endregion

        #region Data Loading - تحميل البيانات

        /// <summary>
        /// تحميل بيانات المستخدم
        /// </summary>
        private void LoadUserData()
        {
            try
            {
                _user = UserManager.GetUserById(_userID);
                if (_user != null)
                {
                    this.Text = $"إعادة تعيين كلمة المرور - {_user.FullName}";
                    lblUserName.Text = _user.FullName;
                    lblUsername2.Text = _user.Username;
                    lblRole.Text = _user.Role;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات المستخدم: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Event Handlers - معالجات الأحداث

        /// <summary>
        /// إعادة تعيين كلمة المرور
        /// </summary>
        private void btnReset_Click(object sender, EventArgs e)
        {
            try
            {
                string newPassword = txtNewPassword.Text.Trim();
                string confirmPassword = txtConfirmPassword.Text.Trim();

                // التحقق من صحة البيانات
                if (!ValidateInput(newPassword, confirmPassword))
                    return;

                // إعادة تعيين كلمة المرور
                if (UserManager.ResetPassword(_userID, newPassword))
                {
                    MessageBox.Show("تم إعادة تعيين كلمة المرور بنجاح", "نجح", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("فشل في إعادة تعيين كلمة المرور", "خطأ", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعادة تعيين كلمة المرور: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إلغاء العملية
        /// </summary>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// إعادة تعيين كلمة المرور الافتراضية
        /// </summary>
        private void btnDefault_Click(object sender, EventArgs e)
        {
            txtNewPassword.Text = "123456";
            txtConfirmPassword.Text = "123456";
        }

        #endregion

        #region Validation - التحقق من صحة البيانات

        /// <summary>
        /// التحقق من صحة البيانات المدخلة
        /// </summary>
        private bool ValidateInput(string newPassword, string confirmPassword)
        {
            if (string.IsNullOrWhiteSpace(newPassword))
            {
                MessageBox.Show("يرجى إدخال كلمة المرور الجديدة", "تنبيه", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtNewPassword.Focus();
                return false;
            }

            if (newPassword.Length < 6)
            {
                MessageBox.Show("كلمة المرور يجب أن تكون 6 أحرف على الأقل", "تنبيه", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtNewPassword.Focus();
                return false;
            }

            if (newPassword != confirmPassword)
            {
                MessageBox.Show("كلمة المرور وتأكيد كلمة المرور غير متطابقتين", "تنبيه", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtConfirmPassword.Focus();
                return false;
            }

            return true;
        }

        #endregion
    }
}
