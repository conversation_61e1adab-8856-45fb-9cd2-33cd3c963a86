using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using PharmacyManagement.Models;

namespace PharmacyManagement.Classes
{
    /// <summary>
    /// مدير الدليل المحاسبي المحسن - Enhanced Chart of Accounts Manager
    /// تم إزالة جميع التكرارات وتحسين الأداء
    /// </summary>
    public static class ChartOfAccountsManager
    {
        #region Account Management - إدارة الحسابات

        /// <summary>
        /// إضافة حساب محاسبي جديد
        /// </summary>
        /// <param name="account">بيانات الحساب</param>
        /// <returns>معرف الحساب الجديد</returns>
        public static int AddAccount(ChartOfAccount account)
        {
            try
            {
                // التحقق من صحة البيانات
                ValidateAccountData(account);

                // التحقق من عدم تكرار كود الحساب
                if (IsAccountCodeExists(account.AccountCode))
                {
                    throw new Exception("كود الحساب موجود مسبقاً");
                }

                // تحديد مستوى الحساب تلقائياً
                account.Level = CalculateAccountLevel(account.AccountCode);

                string query = @"
                    INSERT INTO ChartOfAccounts (AccountCode, AccountName, AccountType, ParentAccountID, 
                                               Level, IsActive, IsSystemAccount, Description, CreatedBy, CreatedDate)
                    VALUES (@AccountCode, @AccountName, @AccountType, @ParentAccountID, 
                           @Level, @IsActive, @IsSystemAccount, @Description, @CreatedBy, @CreatedDate);
                    SELECT SCOPE_IDENTITY();";

                var parameters = new[]
                {
                    DatabaseHelper.CreateParameter("@AccountCode", account.AccountCode),
                    DatabaseHelper.CreateParameter("@AccountName", account.AccountName),
                    DatabaseHelper.CreateParameter("@AccountType", account.AccountType),
                    DatabaseHelper.CreateParameter("@ParentAccountID", account.ParentAccountID),
                    DatabaseHelper.CreateParameter("@Level", account.Level),
                    DatabaseHelper.CreateParameter("@IsActive", account.IsActive),
                    DatabaseHelper.CreateParameter("@IsSystemAccount", account.IsSystemAccount),
                    DatabaseHelper.CreateParameter("@Description", account.Description ?? ""),
                    DatabaseHelper.CreateParameter("@CreatedBy", UserManager.CurrentUser?.UserID),
                    DatabaseHelper.CreateParameter("@CreatedDate", DateTime.Now)
                };

                var result = DatabaseHelper.ExecuteScalar(query, parameters);
                int accountId = Convert.ToInt32(result);

                if (accountId > 0)
                {
                    LogManager.LogInfo("تم إضافة حساب محاسبي جديد: " + account.AccountCode + " - " + account.AccountName);
                }

                return accountId;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في إضافة الحساب المحاسبي: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// تحديث حساب محاسبي موجود
        /// </summary>
        /// <param name="account">بيانات الحساب المحدثة</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public static bool UpdateAccount(ChartOfAccount account)
        {
            try
            {
                // التحقق من صحة البيانات
                ValidateAccountData(account);

                // التحقق من عدم تكرار كود الحساب
                if (IsAccountCodeExists(account.AccountCode, account.AccountID))
                {
                    throw new Exception("كود الحساب موجود مسبقاً");
                }

                // التحقق من عدم تعديل الحسابات النظامية
                if (IsSystemAccount(account.AccountID))
                {
                    throw new Exception("لا يمكن تعديل الحسابات النظامية");
                }

                // تحديث مستوى الحساب
                account.Level = CalculateAccountLevel(account.AccountCode);

                string query = @"
                    UPDATE ChartOfAccounts SET 
                        AccountCode = @AccountCode,
                        AccountName = @AccountName,
                        AccountType = @AccountType,
                        ParentAccountID = @ParentAccountID,
                        Level = @Level,
                        IsActive = @IsActive,
                        Description = @Description,
                        ModifiedDate = @ModifiedDate,
                        ModifiedBy = @ModifiedBy
                    WHERE AccountID = @AccountID AND IsSystemAccount = 0";

                var parameters = new[]
                {
                    DatabaseHelper.CreateParameter("@AccountID", account.AccountID),
                    DatabaseHelper.CreateParameter("@AccountCode", account.AccountCode),
                    DatabaseHelper.CreateParameter("@AccountName", account.AccountName),
                    DatabaseHelper.CreateParameter("@AccountType", account.AccountType),
                    DatabaseHelper.CreateParameter("@ParentAccountID", account.ParentAccountID),
                    DatabaseHelper.CreateParameter("@Level", account.Level),
                    DatabaseHelper.CreateParameter("@IsActive", account.IsActive),
                    DatabaseHelper.CreateParameter("@Description", account.Description ?? ""),
                    DatabaseHelper.CreateParameter("@ModifiedDate", DateTime.Now),
                    DatabaseHelper.CreateParameter("@ModifiedBy", UserManager.CurrentUser?.UserID)
                };

                int rowsAffected = DatabaseHelper.ExecuteNonQuery(query, parameters);

                if (rowsAffected > 0)
                {
                    LogManager.LogInfo($"تم تحديث الحساب المحاسبي: {account.AccountCode} - {account.AccountName}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تحديث الحساب المحاسبي: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// حذف حساب محاسبي
        /// </summary>
        /// <param name="accountId">معرف الحساب</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public static bool DeleteAccount(int accountId)
        {
            try
            {
                // التحقق من عدم حذف الحسابات النظامية
                if (IsSystemAccount(accountId))
                {
                    throw new Exception("لا يمكن حذف الحسابات النظامية");
                }

                // التحقق من عدم وجود حسابات فرعية
                if (HasChildAccounts(accountId))
                {
                    throw new Exception("لا يمكن حذف الحساب لوجود حسابات فرعية");
                }

                // التحقق من عدم وجود معاملات مرتبطة
                if (HasTransactions(accountId))
                {
                    throw new Exception("لا يمكن حذف الحساب لوجود معاملات مرتبطة به");
                }

                string query = "DELETE FROM ChartOfAccounts WHERE AccountID = @AccountID AND IsSystemAccount = 0";
                var parameters = new[] { DatabaseHelper.CreateParameter("@AccountID", accountId) };

                int rowsAffected = DatabaseHelper.ExecuteNonQuery(query, parameters);

                if (rowsAffected > 0)
                {
                    LogManager.LogInfo($"تم حذف الحساب المحاسبي رقم: {accountId}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في حذف الحساب المحاسبي: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Data Retrieval - استرجاع البيانات

        /// <summary>
        /// الحصول على حساب محاسبي بالمعرف
        /// </summary>
        /// <param name="accountId">معرف الحساب</param>
        /// <returns>بيانات الحساب</returns>
        public static ChartOfAccount GetAccount(int accountId)
        {
            try
            {
                string query = @"
                    SELECT coa.*, parent.AccountName AS ParentAccountName
                    FROM ChartOfAccounts coa
                    LEFT JOIN ChartOfAccounts parent ON coa.ParentAccountID = parent.AccountID
                    WHERE coa.AccountID = @AccountID";

                var parameters = new[] { DatabaseHelper.CreateParameter("@AccountID", accountId) };

                using (var reader = DatabaseHelper.ExecuteReader(query, parameters))
                {
                    if (reader.Read())
                    {
                        return MapAccountFromReader(reader);
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على الحساب المحاسبي {accountId}: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// الحصول على جميع الحسابات المحاسبية
        /// </summary>
        /// <returns>قائمة جميع الحسابات</returns>
        public static List<ChartOfAccount> GetAllAccounts()
        {
            try
            {
                string query = @"
                    SELECT coa.*, parent.AccountName AS ParentAccountName
                    FROM ChartOfAccounts coa
                    LEFT JOIN ChartOfAccounts parent ON coa.ParentAccountID = parent.AccountID
                    ORDER BY coa.AccountCode";

                var accounts = ExecuteAccountQuery(query);
                LogManager.LogInfo($"تم استرجاع {accounts.Count} حساب محاسبي");
                return accounts;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على جميع الحسابات المحاسبية: {ex.Message}");
                return new List<ChartOfAccount>();
            }
        }

        /// <summary>
        /// الحصول على الحسابات النشطة فقط
        /// </summary>
        /// <returns>قائمة الحسابات النشطة</returns>
        public static List<ChartOfAccount> GetActiveAccounts()
        {
            try
            {
                string query = @"
                    SELECT coa.*, parent.AccountName AS ParentAccountName
                    FROM ChartOfAccounts coa
                    LEFT JOIN ChartOfAccounts parent ON coa.ParentAccountID = parent.AccountID
                    WHERE coa.IsActive = 1
                    ORDER BY coa.AccountCode";

                var accounts = ExecuteAccountQuery(query);
                LogManager.LogInfo($"تم استرجاع {accounts.Count} حساب نشط");
                return accounts;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على الحسابات النشطة: {ex.Message}");
                return new List<ChartOfAccount>();
            }
        }

        /// <summary>
        /// الحصول على الحسابات حسب النوع
        /// </summary>
        /// <param name="accountType">نوع الحساب</param>
        /// <returns>قائمة الحسابات</returns>
        public static List<ChartOfAccount> GetAccountsByType(string accountType)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(accountType))
                {
                    return new List<ChartOfAccount>();
                }

                string query = @"
                    SELECT coa.*, parent.AccountName AS ParentAccountName
                    FROM ChartOfAccounts coa
                    LEFT JOIN ChartOfAccounts parent ON coa.ParentAccountID = parent.AccountID
                    WHERE coa.AccountType = @AccountType AND coa.IsActive = 1
                    ORDER BY coa.AccountCode";

                var parameters = new[] { DatabaseHelper.CreateParameter("@AccountType", accountType) };
                var accounts = ExecuteAccountQuery(query, parameters);
                
                LogManager.LogInfo($"تم العثور على {accounts.Count} حساب من نوع: {accountType}");
                return accounts;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على الحسابات حسب النوع {accountType}: {ex.Message}");
                return new List<ChartOfAccount>();
            }
        }

        /// <summary>
        /// الحصول على الحسابات الفرعية
        /// </summary>
        /// <param name="parentAccountId">معرف الحساب الأب</param>
        /// <returns>قائمة الحسابات الفرعية</returns>
        public static List<ChartOfAccount> GetChildAccounts(int parentAccountId)
        {
            try
            {
                string query = @"
                    SELECT coa.*, parent.AccountName AS ParentAccountName
                    FROM ChartOfAccounts coa
                    LEFT JOIN ChartOfAccounts parent ON coa.ParentAccountID = parent.AccountID
                    WHERE coa.ParentAccountID = @ParentAccountID AND coa.IsActive = 1
                    ORDER BY coa.AccountCode";

                var parameters = new[] { DatabaseHelper.CreateParameter("@ParentAccountID", parentAccountId) };
                var accounts = ExecuteAccountQuery(query, parameters);
                
                LogManager.LogInfo($"تم العثور على {accounts.Count} حساب فرعي للحساب: {parentAccountId}");
                return accounts;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على الحسابات الفرعية للحساب {parentAccountId}: {ex.Message}");
                return new List<ChartOfAccount>();
            }
        }

        /// <summary>
        /// البحث في الحسابات المحاسبية
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <returns>قائمة الحسابات المطابقة للبحث</returns>
        public static List<ChartOfAccount> SearchAccounts(string searchTerm)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                {
                    return GetActiveAccounts();
                }

                string query = @"
                    SELECT coa.*, parent.AccountName AS ParentAccountName
                    FROM ChartOfAccounts coa
                    LEFT JOIN ChartOfAccounts parent ON coa.ParentAccountID = parent.AccountID
                    WHERE (coa.AccountCode LIKE @SearchTerm OR 
                           coa.AccountName LIKE @SearchTerm OR 
                           coa.Description LIKE @SearchTerm) 
                    AND coa.IsActive = 1
                    ORDER BY coa.AccountCode";

                var parameters = new[] { DatabaseHelper.CreateParameter("@SearchTerm", $"%{searchTerm}%") };
                var accounts = ExecuteAccountQuery(query, parameters);
                
                LogManager.LogInfo($"تم العثور على {accounts.Count} حساب مطابق للبحث: {searchTerm}");
                return accounts;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في البحث في الحسابات: {ex.Message}");
                return new List<ChartOfAccount>();
            }
        }

        #endregion

        #region Utility Methods - الطرق المساعدة

        /// <summary>
        /// توليد كود حساب جديد تلقائياً
        /// </summary>
        /// <param name="accountType">نوع الحساب</param>
        /// <param name="parentAccountId">معرف الحساب الأب (اختياري)</param>
        /// <returns>كود الحساب الجديد</returns>
        public static string GenerateAccountCode(string accountType, int? parentAccountId = null)
        {
            try
            {
                string baseCode = "";

                // تحديد الكود الأساسي حسب نوع الحساب
                switch (accountType?.ToUpper())
                {
                    case "ASSETS":
                    case "أصول":
                        baseCode = "1";
                        break;
                    case "LIABILITIES":
                    case "خصوم":
                        baseCode = "2";
                        break;
                    case "EQUITY":
                    case "حقوق الملكية":
                        baseCode = "3";
                        break;
                    case "REVENUE":
                    case "إيرادات":
                        baseCode = "4";
                        break;
                    case "EXPENSES":
                    case "مصروفات":
                        baseCode = "5";
                        break;
                    default:
                        baseCode = "9";
                        break;
                }

                // إذا كان هناك حساب أب، استخدم كوده كأساس
                if (parentAccountId.HasValue)
                {
                    var parentAccount = GetAccount(parentAccountId.Value);
                    if (parentAccount != null)
                    {
                        baseCode = parentAccount.AccountCode;
                    }
                }

                // البحث عن أعلى رقم متاح
                string query = @"
                    SELECT ISNULL(MAX(CAST(SUBSTRING(AccountCode, LEN(@BaseCode) + 1, 10) AS INT)), 0)
                    FROM ChartOfAccounts
                    WHERE AccountCode LIKE @Pattern AND LEN(AccountCode) = @Length";

                int nextLevel = baseCode.Length + 1;
                var parameters = new[]
                {
                    DatabaseHelper.CreateParameter("@BaseCode", baseCode),
                    DatabaseHelper.CreateParameter("@Pattern", baseCode + "%"),
                    DatabaseHelper.CreateParameter("@Length", nextLevel)
                };

                var result = DatabaseHelper.ExecuteScalar(query, parameters);
                int nextNumber = Convert.ToInt32(result) + 1;

                string newCode = baseCode + nextNumber.ToString();
                LogManager.LogInfo($"تم توليد كود حساب جديد: {newCode}");
                return newCode;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في توليد كود الحساب: {ex.Message}");
                return DateTime.Now.Ticks.ToString().Substring(10);
            }
        }

        /// <summary>
        /// التحقق من وجود كود الحساب
        /// </summary>
        /// <param name="accountCode">كود الحساب</param>
        /// <param name="excludeAccountId">معرف الحساب المستثنى (للتحديث)</param>
        /// <returns>true إذا كان الكود موجود</returns>
        public static bool IsAccountCodeExists(string accountCode, int? excludeAccountId = null)
        {
            try
            {
                string query = "SELECT COUNT(*) FROM ChartOfAccounts WHERE AccountCode = @AccountCode";
                var parameters = new List<SqlParameter>
                {
                    DatabaseHelper.CreateParameter("@AccountCode", accountCode)
                };

                if (excludeAccountId.HasValue)
                {
                    query += " AND AccountID != @ExcludeAccountID";
                    parameters.Add(DatabaseHelper.CreateParameter("@ExcludeAccountID", excludeAccountId.Value));
                }

                var result = DatabaseHelper.ExecuteScalar(query, parameters.ToArray());
                return Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في التحقق من كود الحساب: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Private Helper Methods - الطرق المساعدة الخاصة

        /// <summary>
        /// التحقق من صحة بيانات الحساب
        /// </summary>
        /// <param name="account">بيانات الحساب</param>
        private static void ValidateAccountData(ChartOfAccount account)
        {
            if (account == null)
                throw new ArgumentNullException(nameof(account), "بيانات الحساب مطلوبة");

            if (string.IsNullOrWhiteSpace(account.AccountCode))
                throw new ArgumentException("كود الحساب مطلوب", nameof(account.AccountCode));

            if (string.IsNullOrWhiteSpace(account.AccountName))
                throw new ArgumentException("اسم الحساب مطلوب", nameof(account.AccountName));

            if (string.IsNullOrWhiteSpace(account.AccountType))
                throw new ArgumentException("نوع الحساب مطلوب", nameof(account.AccountType));

            if (account.AccountCode.Length > 20)
                throw new ArgumentException("كود الحساب يجب أن يكون أقل من 20 حرف", nameof(account.AccountCode));

            if (account.AccountName.Length > 200)
                throw new ArgumentException("اسم الحساب يجب أن يكون أقل من 200 حرف", nameof(account.AccountName));
        }

        /// <summary>
        /// التحقق من كون الحساب نظامي
        /// </summary>
        /// <param name="accountId">معرف الحساب</param>
        /// <returns>true إذا كان الحساب نظامي</returns>
        private static bool IsSystemAccount(int accountId)
        {
            try
            {
                string query = "SELECT IsSystemAccount FROM ChartOfAccounts WHERE AccountID = @AccountID";
                var parameters = new[] { DatabaseHelper.CreateParameter("@AccountID", accountId) };

                var result = DatabaseHelper.ExecuteScalar(query, parameters);
                return result != null && Convert.ToBoolean(result);
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في التحقق من الحساب النظامي: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// التحقق من وجود حسابات فرعية
        /// </summary>
        /// <param name="accountId">معرف الحساب</param>
        /// <returns>true إذا كان يوجد حسابات فرعية</returns>
        private static bool HasChildAccounts(int accountId)
        {
            try
            {
                string query = "SELECT COUNT(*) FROM ChartOfAccounts WHERE ParentAccountID = @AccountID";
                var parameters = new[] { DatabaseHelper.CreateParameter("@AccountID", accountId) };

                var result = DatabaseHelper.ExecuteScalar(query, parameters);
                return Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في التحقق من الحسابات الفرعية: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// التحقق من وجود معاملات مرتبطة بالحساب
        /// </summary>
        /// <param name="accountId">معرف الحساب</param>
        /// <returns>true إذا كان يوجد معاملات مرتبطة</returns>
        private static bool HasTransactions(int accountId)
        {
            try
            {
                string query = "SELECT COUNT(*) FROM VoucherDetails WHERE AccountID = @AccountID";
                var parameters = new[] { DatabaseHelper.CreateParameter("@AccountID", accountId) };

                var result = DatabaseHelper.ExecuteScalar(query, parameters);
                return Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في التحقق من المعاملات المرتبطة: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// حساب مستوى الحساب بناءً على الكود
        /// </summary>
        /// <param name="accountCode">كود الحساب</param>
        /// <returns>مستوى الحساب</returns>
        private static int CalculateAccountLevel(string accountCode)
        {
            if (string.IsNullOrWhiteSpace(accountCode))
                return 1;

            // حساب المستوى بناءً على طول الكود
            return Math.Min(accountCode.Length, 5); // الحد الأقصى 5 مستويات
        }

        /// <summary>
        /// تنفيذ استعلام الحسابات وإرجاع النتائج
        /// </summary>
        /// <param name="query">الاستعلام</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>قائمة الحسابات</returns>
        private static List<ChartOfAccount> ExecuteAccountQuery(string query, SqlParameter[] parameters = null)
        {
            var accounts = new List<ChartOfAccount>();
            try
            {
                using (var reader = DatabaseHelper.ExecuteReader(query, parameters))
                {
                    while (reader.Read())
                    {
                        accounts.Add(MapAccountFromReader(reader));
                    }
                }
                return accounts;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تنفيذ استعلام الحسابات: {ex.Message}");
                return accounts;
            }
        }

        /// <summary>
        /// تحويل بيانات القارئ إلى كائن حساب محاسبي
        /// </summary>
        /// <param name="reader">قارئ البيانات</param>
        /// <returns>كائن الحساب المحاسبي</returns>
        private static ChartOfAccount MapAccountFromReader(SqlDataReader reader)
        {
            return new ChartOfAccount
            {
                AccountID = Convert.ToInt32(reader["AccountID"]),
                AccountCode = reader["AccountCode"].ToString(),
                AccountName = reader["AccountName"].ToString(),
                AccountType = reader["AccountType"].ToString(),
                ParentAccountID = reader["ParentAccountID"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["ParentAccountID"]),
                Level = Convert.ToInt32(reader["Level"]),
                IsActive = Convert.ToBoolean(reader["IsActive"]),
                IsSystemAccount = Convert.ToBoolean(reader["IsSystemAccount"]),
                Description = reader["Description"] == DBNull.Value ? null : reader["Description"].ToString(),
                CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                CreatedBy = reader["CreatedBy"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["CreatedBy"])
            };
        }

        #endregion

        #region Statistics and Reports - الإحصائيات والتقارير

        /// <summary>
        /// الحصول على إحصائيات الحسابات المحاسبية
        /// </summary>
        /// <returns>إحصائيات الحسابات</returns>
        public static AccountStatistics GetAccountStatistics()
        {
            try
            {
                string query = @"
                    SELECT
                        COUNT(*) as TotalAccounts,
                        SUM(CASE WHEN IsActive = 1 THEN 1 ELSE 0 END) as ActiveAccounts,
                        SUM(CASE WHEN IsActive = 0 THEN 1 ELSE 0 END) as InactiveAccounts,
                        SUM(CASE WHEN IsSystemAccount = 1 THEN 1 ELSE 0 END) as SystemAccounts,
                        COUNT(DISTINCT AccountType) as AccountTypes
                    FROM ChartOfAccounts";

                using (var reader = DatabaseHelper.ExecuteReader(query))
                {
                    if (reader.Read())
                    {
                        return new AccountStatistics
                        {
                            TotalAccounts = Convert.ToInt32(reader["TotalAccounts"]),
                            ActiveAccounts = Convert.ToInt32(reader["ActiveAccounts"]),
                            InactiveAccounts = Convert.ToInt32(reader["InactiveAccounts"]),
                            SystemAccounts = Convert.ToInt32(reader["SystemAccounts"]),
                            AccountTypes = Convert.ToInt32(reader["AccountTypes"])
                        };
                    }
                }

                return new AccountStatistics();
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على إحصائيات الحسابات: {ex.Message}");
                return new AccountStatistics();
            }
        }

        /// <summary>
        /// الحصول على الحسابات حسب المستوى
        /// </summary>
        /// <param name="level">مستوى الحساب</param>
        /// <returns>قائمة الحسابات في المستوى المحدد</returns>
        public static List<ChartOfAccount> GetAccountsByLevel(int level)
        {
            try
            {
                string query = @"
                    SELECT coa.*, parent.AccountName AS ParentAccountName
                    FROM ChartOfAccounts coa
                    LEFT JOIN ChartOfAccounts parent ON coa.ParentAccountID = parent.AccountID
                    WHERE coa.Level = @Level AND coa.IsActive = 1
                    ORDER BY coa.AccountCode";

                var parameters = new[] { DatabaseHelper.CreateParameter("@Level", level) };
                return ExecuteAccountQuery(query, parameters);
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على حسابات المستوى {level}: {ex.Message}");
                return new List<ChartOfAccount>();
            }
        }

        #endregion
    }

    #region Helper Models - النماذج المساعدة

    /// <summary>
    /// إحصائيات الحسابات المحاسبية
    /// </summary>
    public class AccountStatistics
    {
        public int TotalAccounts { get; set; }
        public int ActiveAccounts { get; set; }
        public int InactiveAccounts { get; set; }
        public int SystemAccounts { get; set; }
        public int AccountTypes { get; set; }
    }

    #endregion
}
