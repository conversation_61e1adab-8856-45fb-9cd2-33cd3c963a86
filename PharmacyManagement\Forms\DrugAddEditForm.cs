using System;
using System.Drawing;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نافذة إضافة/تعديل الأدوية - Drug Add/Edit Form
    /// </summary>
    public partial class DrugAddEditForm : Form
    {
        #region Fields - الحقول

        private Drug _currentDrug;
        private bool _isEditMode;

        // UI Controls
      

        #endregion

        #region Constructors - المنشئات

        /// <summary>
        /// منشئ لإضافة دواء جديد
        /// </summary>
        public DrugAddEditForm()
        {
            InitializeComponent();
            _isEditMode = false;
            _currentDrug = new Drug();
            SetupForm();
        }

        /// <summary>
        /// منشئ لتعديل دواء موجود
        /// </summary>
        /// <param name="drug">الدواء المراد تعديله</param>
        public DrugAddEditForm(Drug drug)
        {
            InitializeComponent();
            _isEditMode = true;
            _currentDrug = drug;
            SetupForm();
            LoadDrugData();
        }

        #endregion

        #region Form Setup - إعداد النافذة

        /// <summary>
        /// إعداد النافذة
        /// </summary>
        private void SetupForm()
        {
            // تحديث العنوان
            this.Text = _isEditMode ? "تعديل دواء" : "إضافة دواء جديد";
            lblTitle.Text = _isEditMode ? "✏️ تعديل دواء" : "➕ إضافة دواء جديد";
            
            // تحميل البيانات المرجعية
            LoadCategories();
            LoadManufacturers();
            
            // إعداد التصميم
            ApplyFlatDesign();
            
            // إعداد التحقق من صحة البيانات
            SetupValidation();
        }

        /// <summary>
        /// تحميل فئات الأدوية
        /// </summary>
        private void LoadCategories()
        {
            try
            {
                string query = "SELECT CategoryID, CategoryName FROM DrugCategories ORDER BY CategoryName";
                var dataTable = DatabaseHelper.ExecuteQuery(query);
                
                cmbCategory.DisplayMember = "CategoryName";
                cmbCategory.ValueMember = "CategoryID";
                cmbCategory.DataSource = dataTable;
                cmbCategory.SelectedIndex = -1;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الفئات: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحميل الشركات المصنعة
        /// </summary>
        private void LoadManufacturers()
        {
            try
            {
                string query = "SELECT ManufacturerID, ManufacturerName FROM Manufacturers ORDER BY ManufacturerName";
                var dataTable = DatabaseHelper.ExecuteQuery(query);
                
                cmbManufacturer.DisplayMember = "ManufacturerName";
                cmbManufacturer.ValueMember = "ManufacturerID";
                cmbManufacturer.DataSource = dataTable;
                cmbManufacturer.SelectedIndex = -1;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الشركات: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تطبيق التصميم المسطح
        /// </summary>
        private void ApplyFlatDesign()
        {
            btnSave.FlatAppearance.BorderSize = 0;
            btnCancel.FlatAppearance.BorderSize = 0;
        }

        /// <summary>
        /// إعداد التحقق من صحة البيانات
        /// </summary>
        private void SetupValidation()
        {
            // التحقق من الأرقام فقط في حقول الأسعار والكميات
            txtPurchasePrice.KeyPress += NumericTextBox_KeyPress;
            txtSalePrice.KeyPress += NumericTextBox_KeyPress;
            txtMinStock.KeyPress += IntegerTextBox_KeyPress;
            txtMaxStock.KeyPress += IntegerTextBox_KeyPress;
            txtCurrentStock.KeyPress += IntegerTextBox_KeyPress;
            txtPackSize.KeyPress += IntegerTextBox_KeyPress;
        }

        /// <summary>
        /// تحميل بيانات الدواء للتعديل
        /// </summary>
        private void LoadDrugData()
        {
            if (_currentDrug == null) return;

            txtDrugCode.Text = _currentDrug.DrugCode;
            txtTradeName.Text = _currentDrug.DrugName;
            txtScientificName.Text = _currentDrug.ScientificName;
            txtBarcode.Text = _currentDrug.Barcode;
            txtDosageForm.Text = _currentDrug.DosageForm;
            txtStrength.Text = _currentDrug.Strength;
            txtUnit.Text = _currentDrug.Unit;
            txtLocation.Text = _currentDrug.Location;
            txtNotes.Text = _currentDrug.Notes;

            txtPackSize.Text = _currentDrug.PackSize.ToString();
            txtPurchasePrice.Text = _currentDrug.PurchasePrice.ToString("F2");
            txtSalePrice.Text = _currentDrug.SalePrice.ToString("F2");
            txtMinStock.Text = _currentDrug.MinStockLevel.ToString();
            txtMaxStock.Text = _currentDrug.MaxStockLevel.ToString();
            txtCurrentStock.Text = _currentDrug.CurrentStock.ToString();

            chkRequiresPrescription.Checked = _currentDrug.RequiresPrescription;
            chkIsActive.Checked = _currentDrug.IsActive;

            // تحديد الفئة والشركة المصنعة
            if (_currentDrug.CategoryID.HasValue)
                cmbCategory.SelectedValue = _currentDrug.CategoryID.Value;

            if (_currentDrug.ManufacturerID.HasValue)
                cmbManufacturer.SelectedValue = _currentDrug.ManufacturerID.Value;

            // تحديد تاريخ الانتهاء
            if (_currentDrug.ExpiryDate.HasValue)
                dtpExpiryDate.Value = _currentDrug.ExpiryDate.Value;
        }

        #endregion

        #region Event Handlers - معالجات الأحداث

        /// <summary>
        /// حفظ البيانات
        /// </summary>
        private void btnSave_Click(object sender, EventArgs e)
        {
            if (ValidateData())
            {
                if (SaveDrug())
                {
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
            }
        }

        /// <summary>
        /// إلغاء العملية
        /// </summary>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// التحقق من الأرقام العشرية
        /// </summary>
        private void NumericTextBox_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (!char.IsControl(e.KeyChar) && !char.IsDigit(e.KeyChar) && e.KeyChar != '.')
            {
                e.Handled = true;
            }

            // السماح بنقطة عشرية واحدة فقط
            if (e.KeyChar == '.' && (sender as TextBox).Text.IndexOf('.') > -1)
            {
                e.Handled = true;
            }
        }

        /// <summary>
        /// التحقق من الأرقام الصحيحة
        /// </summary>
        private void IntegerTextBox_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (!char.IsControl(e.KeyChar) && !char.IsDigit(e.KeyChar))
            {
                e.Handled = true;
            }
        }

        #endregion

        #region Validation and Save - التحقق والحفظ

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        private bool ValidateData()
        {
            // التحقق من الحقول المطلوبة
            if (string.IsNullOrWhiteSpace(txtDrugCode.Text))
            {
                MessageBox.Show("يرجى إدخال كود الدواء", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtDrugCode.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtTradeName.Text))
            {
                MessageBox.Show("يرجى إدخال الاسم التجاري", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtTradeName.Focus();
                return false;
            }

            // التحقق من الأسعار
            if (!decimal.TryParse(txtPurchasePrice.Text, out decimal purchasePrice) || purchasePrice < 0)
            {
                MessageBox.Show("يرجى إدخال سعر شراء صحيح", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPurchasePrice.Focus();
                return false;
            }

            if (!decimal.TryParse(txtSalePrice.Text, out decimal salePrice) || salePrice < 0)
            {
                MessageBox.Show("يرجى إدخال سعر بيع صحيح", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtSalePrice.Focus();
                return false;
            }

            if (salePrice <= purchasePrice)
            {
                MessageBox.Show("سعر البيع يجب أن يكون أكبر من سعر الشراء", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtSalePrice.Focus();
                return false;
            }

            // التحقق من المخزون
            if (!int.TryParse(txtMinStock.Text, out int minStock) || minStock < 0)
            {
                MessageBox.Show("يرجى إدخال حد أدنى صحيح للمخزون", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtMinStock.Focus();
                return false;
            }

            if (!int.TryParse(txtMaxStock.Text, out int maxStock) || maxStock < 0)
            {
                MessageBox.Show("يرجى إدخال حد أقصى صحيح للمخزون", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtMaxStock.Focus();
                return false;
            }

            if (maxStock <= minStock)
            {
                MessageBox.Show("الحد الأقصى للمخزون يجب أن يكون أكبر من الحد الأدنى", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtMaxStock.Focus();
                return false;
            }

            return true;
        }

        /// <summary>
        /// حفظ بيانات الدواء
        /// </summary>
        /// <returns>true إذا تم الحفظ بنجاح</returns>
        private bool SaveDrug()
        {
            try
            {
                // تحديث بيانات الدواء
                _currentDrug.DrugCode = txtDrugCode.Text.Trim();
                _currentDrug.DrugName = txtTradeName.Text.Trim();
                _currentDrug.ScientificName = txtScientificName.Text.Trim();
                _currentDrug.Barcode = txtBarcode.Text.Trim();
                _currentDrug.DosageForm = txtDosageForm.Text.Trim();
                _currentDrug.Strength = txtStrength.Text.Trim();
                _currentDrug.Unit = txtUnit.Text.Trim();
                _currentDrug.Location = txtLocation.Text.Trim();
                _currentDrug.Notes = txtNotes.Text.Trim();
                
                _currentDrug.CategoryID = cmbCategory.SelectedValue as int?;
                _currentDrug.ManufacturerID = cmbManufacturer.SelectedValue as int?;
                
                _currentDrug.PackSize = int.Parse(txtPackSize.Text);
                _currentDrug.PurchasePrice = decimal.Parse(txtPurchasePrice.Text);
                _currentDrug.SalePrice = decimal.Parse(txtSalePrice.Text);
                _currentDrug.MinStockLevel = int.Parse(txtMinStock.Text);
                _currentDrug.MaxStockLevel = int.Parse(txtMaxStock.Text);
                
                _currentDrug.RequiresPrescription = chkRequiresPrescription.Checked;
                _currentDrug.IsActive = chkIsActive.Checked;

                // حفظ في قاعدة البيانات
                int result;
                if (_isEditMode)
                {
                    result = DrugManager.UpdateDrug(_currentDrug) ? _currentDrug.DrugID : -1;
                }
                else
                {
                    _currentDrug.CurrentStock = int.Parse(txtCurrentStock.Text);
                    result = DrugManager.AddDrug(_currentDrug);
                }

                if (result > 0)
                {
                    string message = _isEditMode ? "تم تحديث الدواء بنجاح" : "تم إضافة الدواء بنجاح";
                    MessageBox.Show(message, "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return true;
                }
                else
                {
                    MessageBox.Show("فشل في حفظ البيانات", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return false;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        #endregion
    }
}
