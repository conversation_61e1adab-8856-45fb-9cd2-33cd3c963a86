using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using PharmacyManagement.Models;

namespace PharmacyManagement.Classes
{
    /// <summary>
    /// مدير الموردين - Supplier Manager
    /// </summary>
    public static class SupplierManager
    {
        #region Basic Operations - العمليات الأساسية

        /// <summary>
        /// الحصول على إجمالي عدد الموردين
        /// </summary>
        /// <returns>عدد الموردين</returns>
        public static int GetTotalSuppliersCount()
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = "SELECT COUNT(*) FROM Suppliers WHERE IsActive = 1";

                    using (var command = new SqlCommand(query, connection))
                    {
                        var result = command.ExecuteScalar();
                        return result != null ? Convert.ToInt32(result) : 0;
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في حساب عدد الموردين: " + ex.Message);
                return 0;
            }
        }

        /// <summary>
        /// الحصول على إجمالي رصيد الموردين
        /// </summary>
        /// <returns>إجمالي الرصيد</returns>
        public static decimal GetTotalSupplierBalance()
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = "SELECT ISNULL(SUM(CurrentBalance), 0) FROM Suppliers WHERE IsActive = 1";

                    using (var command = new SqlCommand(query, connection))
                    {
                        var result = command.ExecuteScalar();
                        return result != null ? Convert.ToDecimal(result) : 0;
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في حساب إجمالي رصيد الموردين: " + ex.Message);
                return 0;
            }
        }

        /// <summary>
        /// الحصول على مورد بالمعرف
        /// </summary>
        /// <param name="supplierId">معرف المورد</param>
        /// <returns>المورد</returns>
        public static Supplier GetSupplier(int supplierId)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = "SELECT * FROM Suppliers WHERE SupplierID = @SupplierID";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@SupplierID", supplierId);

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return new Supplier
                                {
                                    SupplierID = Convert.ToInt32(reader["SupplierID"]),
                                    SupplierName = reader["SupplierName"].ToString(),
                                    ContactPerson = reader["ContactPerson"].ToString(),
                                    Phone = reader["Phone"].ToString(),
                                    Email = reader["Email"].ToString(),
                                    Address = reader["Address"].ToString(),
                                    IsActive = Convert.ToBoolean(reader["IsActive"])
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في الحصول على المورد: " + ex.Message);
            }
            return null;
        }

        /// <summary>
        /// الحصول على مورد بالمعرف (اسم بديل)
        /// </summary>
        /// <param name="supplierId">معرف المورد</param>
        /// <returns>المورد</returns>
        public static Supplier GetSupplierById(int supplierId)
        {
            return GetSupplier(supplierId);
        }

        /// <summary>
        /// الحصول على جميع الموردين
        /// </summary>
        /// <returns>قائمة الموردين</returns>
        public static List<Supplier> GetAllSuppliers()
        {
            var suppliers = new List<Supplier>();
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = "SELECT * FROM Suppliers WHERE IsActive = 1 ORDER BY SupplierName";

                    using (var command = new SqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                suppliers.Add(new Supplier
                                {
                                    SupplierID = Convert.ToInt32(reader["SupplierID"]),
                                    SupplierCode = reader["SupplierCode"].ToString(),
                                    SupplierName = reader["SupplierName"].ToString(),
                                    ContactPerson = reader["ContactPerson"] != DBNull.Value ? reader["ContactPerson"].ToString() : null,
                                    Phone = reader["Phone"] != DBNull.Value ? reader["Phone"].ToString() : null,
                                    Mobile = reader["Mobile"] != DBNull.Value ? reader["Mobile"].ToString() : null,
                                    Email = reader["Email"] != DBNull.Value ? reader["Email"].ToString() : null,
                                    Address = reader["Address"] != DBNull.Value ? reader["Address"].ToString() : null,
                                    TaxNumber = reader["TaxNumber"] != DBNull.Value ? reader["TaxNumber"].ToString() : null,
                                    CreditLimit = Convert.ToDecimal(reader["CreditLimit"]),
                                    CurrentBalance = Convert.ToDecimal(reader["CurrentBalance"]),
                                    IsActive = Convert.ToBoolean(reader["IsActive"]),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"])
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في الحصول على جميع الموردين: " + ex.Message);
            }
            return suppliers;
        }

        /// <summary>
        /// البحث في الموردين
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <returns>قائمة الموردين المطابقة</returns>
        public static List<Supplier> SearchSuppliers(string searchTerm)
        {
            var suppliers = new List<Supplier>();
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT * FROM Suppliers
                        WHERE IsActive = 1
                        AND (SupplierName LIKE @SearchTerm
                             OR ContactPerson LIKE @SearchTerm
                             OR Phone LIKE @SearchTerm
                             OR Email LIKE @SearchTerm)
                        ORDER BY SupplierName";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@SearchTerm", "%" + searchTerm + "%");

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                suppliers.Add(new Supplier
                                {
                                    SupplierID = Convert.ToInt32(reader["SupplierID"]),
                                    SupplierCode = reader["SupplierCode"].ToString(),
                                    SupplierName = reader["SupplierName"].ToString(),
                                    ContactPerson = reader["ContactPerson"] != DBNull.Value ? reader["ContactPerson"].ToString() : null,
                                    Phone = reader["Phone"] != DBNull.Value ? reader["Phone"].ToString() : null,
                                    Mobile = reader["Mobile"] != DBNull.Value ? reader["Mobile"].ToString() : null,
                                    Email = reader["Email"] != DBNull.Value ? reader["Email"].ToString() : null,
                                    Address = reader["Address"] != DBNull.Value ? reader["Address"].ToString() : null,
                                    TaxNumber = reader["TaxNumber"] != DBNull.Value ? reader["TaxNumber"].ToString() : null,
                                    CreditLimit = Convert.ToDecimal(reader["CreditLimit"]),
                                    CurrentBalance = Convert.ToDecimal(reader["CurrentBalance"]),
                                    IsActive = Convert.ToBoolean(reader["IsActive"]),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"])
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في البحث في الموردين: " + ex.Message);
            }
            return suppliers;
        }

        /// <summary>
        /// حذف مورد
        /// </summary>
        /// <param name="supplierId">معرف المورد</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public static bool DeleteSupplier(int supplierId)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = "UPDATE Suppliers SET IsActive = 0 WHERE SupplierID = @SupplierID";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@SupplierID", supplierId);
                        int rowsAffected = command.ExecuteNonQuery();

                        if (rowsAffected > 0)
                        {
                            LogManager.LogInfo("تم حذف المورد رقم: " + supplierId.ToString());
                            return true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في حذف المورد: " + ex.Message);
            }
            return false;
        }

        /// <summary>
        /// الحصول على إحصائيات المورد
        /// </summary>
        /// <param name="supplierId">معرف المورد</param>
        /// <returns>إحصائيات المورد</returns>
        public static object GetSupplierStatistics(int supplierId)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT
                            COUNT(po.PurchaseOrderID) as TotalOrders,
                            ISNULL(SUM(po.TotalAmount), 0) as TotalPurchases,
                            ISNULL(AVG(po.TotalAmount), 0) as AveragePurchase,
                            ISNULL(MAX(po.OrderDate), '1900-01-01') as LastOrderDate
                        FROM PurchaseOrders po
                        WHERE po.SupplierID = @SupplierID";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@SupplierID", supplierId);

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return new
                                {
                                    TotalOrders = Convert.ToInt32(reader["TotalOrders"]),
                                    TotalPurchases = Convert.ToDecimal(reader["TotalPurchases"]),
                                    AveragePurchase = Convert.ToDecimal(reader["AveragePurchase"]),
                                    LastOrderDate = Convert.ToDateTime(reader["LastOrderDate"])
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في الحصول على إحصائيات المورد: " + ex.Message);
            }

            return new
            {
                TotalOrders = 0,
                TotalPurchases = 0m,
                AveragePurchase = 0m,
                LastOrderDate = DateTime.MinValue
            };
        }

        /// <summary>
        /// إضافة مورد جديد
        /// </summary>
        /// <param name="supplier">بيانات المورد</param>
        /// <returns>معرف المورد الجديد أو -1 في حالة الفشل</returns>
        public static int AddSupplier(Supplier supplier)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        INSERT INTO Suppliers (SupplierName, ContactPerson, Phone, Mobile, Email, Address,
                                             SupplierCode, TaxNumber, CreditLimit, CurrentBalance,
                                             IsActive, CreatedDate, CreatedBy)
                        VALUES (@SupplierName, @ContactPerson, @Phone, @Mobile, @Email, @Address,
                               @SupplierCode, @TaxNumber, @CreditLimit, @CurrentBalance,
                               @IsActive, @CreatedDate, @CreatedBy);
                        SELECT SCOPE_IDENTITY();";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@SupplierName", supplier.SupplierName);
                        command.Parameters.AddWithValue("@ContactPerson", supplier.ContactPerson ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Phone", supplier.Phone ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Mobile", supplier.Mobile ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Email", supplier.Email ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Address", supplier.Address ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@SupplierCode", supplier.SupplierCode ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@TaxNumber", supplier.TaxNumber ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@CreditLimit", supplier.CreditLimit);
                        command.Parameters.AddWithValue("@CurrentBalance", supplier.CurrentBalance);
                        command.Parameters.AddWithValue("@IsActive", supplier.IsActive);
                        command.Parameters.AddWithValue("@CreatedDate", DateTime.Now);
                        command.Parameters.AddWithValue("@CreatedBy", UserManager.CurrentUser?.UserID ?? 1);

                        var result = command.ExecuteScalar();
                        int supplierId = Convert.ToInt32(result);

                        if (supplierId > 0)
                        {
                            LogManager.LogInfo("تم إضافة مورد جديد: " + supplier.SupplierName);
                        }

                        return supplierId;
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في إضافة المورد: " + ex.Message);
                return -1;
            }
        }

        /// <summary>
        /// تحديث بيانات مورد
        /// </summary>
        /// <param name="supplier">بيانات المورد</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public static bool UpdateSupplier(Supplier supplier)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        UPDATE Suppliers SET
                            SupplierName = @SupplierName,
                            ContactPerson = @ContactPerson,
                            Phone = @Phone,
                            Mobile = @Mobile,
                            Email = @Email,
                            Address = @Address,
                            SupplierCode = @SupplierCode,
                            TaxNumber = @TaxNumber,
                            CreditLimit = @CreditLimit,
                            CurrentBalance = @CurrentBalance,
                            IsActive = @IsActive,
                            ModifiedDate = @ModifiedDate,
                            ModifiedBy = @ModifiedBy
                        WHERE SupplierID = @SupplierID";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@SupplierID", supplier.SupplierID);
                        command.Parameters.AddWithValue("@SupplierName", supplier.SupplierName);
                        command.Parameters.AddWithValue("@ContactPerson", supplier.ContactPerson ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Phone", supplier.Phone ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Mobile", supplier.Mobile ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Email", supplier.Email ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Address", supplier.Address ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@SupplierCode", supplier.SupplierCode ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@TaxNumber", supplier.TaxNumber ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@CreditLimit", supplier.CreditLimit);
                        command.Parameters.AddWithValue("@CurrentBalance", supplier.CurrentBalance);
                        command.Parameters.AddWithValue("@IsActive", supplier.IsActive);
                        command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);
                        command.Parameters.AddWithValue("@ModifiedBy", UserManager.CurrentUser?.UserID ?? 1);

                        int rowsAffected = command.ExecuteNonQuery();

                        if (rowsAffected > 0)
                        {
                            LogManager.LogInfo("تم تحديث المورد: " + supplier.SupplierName);
                            return true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في تحديث المورد: " + ex.Message);
            }
            return false;
        }

        /// <summary>
        /// الحصول على تاريخ معاملات المورد
        /// </summary>
        /// <param name="supplierId">معرف المورد</param>
        /// <returns>قائمة المعاملات</returns>
        public static List<object> GetSupplierTransactionHistory(int supplierId)
        {
            var transactions = new List<object>();
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT
                            'Purchase' as TransactionType,
                            po.OrderDate as TransactionDate,
                            po.TotalAmount as Amount,
                            po.Notes as Description
                        FROM PurchaseOrders po
                        WHERE po.SupplierID = @SupplierID
                        ORDER BY po.OrderDate DESC";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@SupplierID", supplierId);

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                transactions.Add(new
                                {
                                    TransactionType = reader["TransactionType"].ToString(),
                                    TransactionDate = Convert.ToDateTime(reader["TransactionDate"]),
                                    Amount = Convert.ToDecimal(reader["Amount"]),
                                    Description = reader["Description"] != DBNull.Value ? reader["Description"].ToString() : ""
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في الحصول على تاريخ معاملات المورد: " + ex.Message);
            }
            return transactions;
        }

        /// <summary>
        /// توليد كود مورد جديد
        /// </summary>
        /// <returns>كود المورد الجديد</returns>
        public static string GenerateSupplierCode()
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = "SELECT COUNT(*) FROM Suppliers";

                    using (var command = new SqlCommand(query, connection))
                    {
                        var result = command.ExecuteScalar();
                        int count = result != null ? Convert.ToInt32(result) : 0;

                        // توليد كود بصيغة SUP001, SUP002, إلخ
                        return "SUP" + (count + 1).ToString("D3");
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في توليد كود المورد: " + ex.Message);
                return "SUP001"; // كود افتراضي
            }
        }

        /// <summary>
        /// التحقق من وجود كود المورد
        /// </summary>
        /// <param name="supplierCode">كود المورد</param>
        /// <returns>true إذا كان الكود موجود</returns>
        public static bool IsSupplierCodeExists(string supplierCode)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = "SELECT COUNT(*) FROM Suppliers WHERE SupplierCode = @SupplierCode";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@SupplierCode", supplierCode);
                        var result = command.ExecuteScalar();
                        int count = result != null ? Convert.ToInt32(result) : 0;

                        return count > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في التحقق من كود المورد: " + ex.Message);
                return false;
            }
        }

        /// <summary>
        /// التحقق من صحة بيانات المورد
        /// </summary>
        /// <param name="supplier">بيانات المورد</param>
        /// <returns>رسالة الخطأ أو null إذا كانت البيانات صحيحة</returns>
        public static string ValidateSupplier(Supplier supplier)
        {
            if (supplier == null)
                return "بيانات المورد مطلوبة";

            if (string.IsNullOrWhiteSpace(supplier.SupplierName))
                return "اسم المورد مطلوب";

            if (supplier.SupplierName.Length > 100)
                return "اسم المورد يجب أن يكون أقل من 100 حرف";

            if (!string.IsNullOrWhiteSpace(supplier.Email) && !IsValidEmail(supplier.Email))
                return "البريد الإلكتروني غير صحيح";

            if (!string.IsNullOrWhiteSpace(supplier.Phone) && supplier.Phone.Length > 20)
                return "رقم الهاتف يجب أن يكون أقل من 20 رقم";

            if (supplier.CreditLimit < 0)
                return "حد الائتمان لا يمكن أن يكون سالب";

            return null; // البيانات صحيحة
        }

        /// <summary>
        /// التحقق من صحة البريد الإلكتروني
        /// </summary>
        /// <param name="email">البريد الإلكتروني</param>
        /// <returns>true إذا كان صحيح</returns>
        private static bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        #endregion
    }
}
