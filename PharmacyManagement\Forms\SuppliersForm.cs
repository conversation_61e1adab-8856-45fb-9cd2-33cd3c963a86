using System;
using System.Data;
using System.Drawing;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نافذة إدارة الموردين - Suppliers Management Form
    /// </summary>
    public partial class SuppliersForm : Form
    {
        #region Constructor - المنشئ

        public SuppliersForm()
        {
            InitializeComponent();
            SetupForm();
            LoadData();
        }

        #endregion

        #region Form Setup - إعداد النافذة

        /// <summary>
        /// إعداد النافذة
        /// </summary>
        private void SetupForm()
        {
            // إعداد DataGridView
            SetupDataGridView();
            
            // إعداد التصميم المسطح
            ApplyFlatDesign();
        }

        /// <summary>
        /// إعداد DataGridView
        /// </summary>
        private void SetupDataGridView()
        {
            dgvSuppliers.AutoGenerateColumns = false;
            dgvSuppliers.AllowUserToAddRows = false;
            dgvSuppliers.AllowUserToDeleteRows = false;
            dgvSuppliers.ReadOnly = true;
            dgvSuppliers.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvSuppliers.MultiSelect = false;
            
            // تنسيق الألوان
            dgvSuppliers.BackgroundColor = Color.White;
            dgvSuppliers.GridColor = Color.FromArgb(189, 195, 199);
            dgvSuppliers.DefaultCellStyle.SelectionBackColor = Color.FromArgb(52, 152, 219);
            dgvSuppliers.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvSuppliers.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 73, 94);
            dgvSuppliers.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvSuppliers.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10, FontStyle.Bold);
        }

        /// <summary>
        /// تطبيق التصميم المسطح
        /// </summary>
        private void ApplyFlatDesign()
        {
            // إزالة حدود الأزرار
            foreach (Control control in this.Controls)
            {
                if (control is Button button)
                {
                    button.FlatAppearance.BorderSize = 0;
                    button.Cursor = Cursors.Hand;
                }
            }
        }

        #endregion

        #region Data Loading - تحميل البيانات

        /// <summary>
        /// تحميل بيانات الموردين
        /// </summary>
        private void LoadData()
        {
            try
            {
                var suppliers = SupplierManager.GetAllSuppliers();
                dgvSuppliers.DataSource = suppliers;
                
                // تحديث عدد السجلات
                lblRecordsCount.Text = $"عدد السجلات: {suppliers.Count}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Event Handlers - معالجات الأحداث

        /// <summary>
        /// البحث في الموردين
        /// </summary>
        private void btnSearch_Click(object sender, EventArgs e)
        {
            try
            {
                string searchTerm = txtSearch.Text.Trim();
                var suppliers = SupplierManager.SearchSuppliers(searchTerm);
                dgvSuppliers.DataSource = suppliers;
                
                lblRecordsCount.Text = $"عدد السجلات: {suppliers.Count}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إضافة مورد جديد
        /// </summary>
        private void btnAdd_Click(object sender, EventArgs e)
        {
            var addForm = new SupplierAddEditForm();
            if (addForm.ShowDialog() == DialogResult.OK)
            {
                LoadData();
            }
        }

        /// <summary>
        /// تعديل مورد
        /// </summary>
        private void btnEdit_Click(object sender, EventArgs e)
        {
            if (dgvSuppliers.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار مورد للتعديل", "تنبيه", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedSupplier = dgvSuppliers.SelectedRows[0].DataBoundItem as Supplier;
            if (selectedSupplier != null)
            {
                var editForm = new SupplierAddEditForm(selectedSupplier);
                if (editForm.ShowDialog() == DialogResult.OK)
                {
                    LoadData();
                }
            }
        }

        /// <summary>
        /// حذف مورد
        /// </summary>
        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (dgvSuppliers.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار مورد للحذف", "تنبيه", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show("هل أنت متأكد من حذف هذا المورد؟", "تأكيد الحذف", 
                                       MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            
            if (result == DialogResult.Yes)
            {
                var selectedSupplier = dgvSuppliers.SelectedRows[0].DataBoundItem as Supplier;
                if (selectedSupplier != null)
                {
                    if (SupplierManager.DeleteSupplier(selectedSupplier.SupplierID))
                    {
                        MessageBox.Show("تم حذف المورد بنجاح", "نجح", 
                                      MessageBoxButtons.OK, MessageBoxIcon.Information);
                        LoadData();
                    }
                    else
                    {
                        MessageBox.Show("فشل في حذف المورد", "خطأ", 
                                      MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        ///// <summary>
        ///// عرض تاريخ المورد
        ///// </summary>
        //private void btnHistory_Click(object sender, EventArgs e)
        //{
        //    if (dgvSuppliers.SelectedRows.Count == 0)
        //    {
        //        MessageBox.Show("يرجى اختيار مورد لعرض التاريخ", "تنبيه", 
        //                      MessageBoxButtons.OK, MessageBoxIcon.Warning);
        //        return;
        //    }

        //    var selectedSupplier = dgvSuppliers.SelectedRows[0].DataBoundItem as Supplier;
        //    if (selectedSupplier != null)
        //    {
        //        var historyForm = new SupplierHistoryForm(selectedSupplier);
        //        historyForm.ShowDialog();
        //    }
        //}

        /// <summary>
        /// عرض تاريخ المورد
        /// </summary>
        private void btnHistory_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvSuppliers.SelectedRows.Count == 0)
                {
                    MessageBox.Show("يرجى اختيار مورد لعرض تاريخه", "تنبيه",
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var selectedSupplier = dgvSuppliers.SelectedRows[0].DataBoundItem as Supplier;
                if (selectedSupplier != null)
                {
                    var historyForm = new SupplierHistoryForm(selectedSupplier.SupplierID);
                    historyForm.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض تاريخ المورد: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
                LogManager.LogError($"SuppliersForm.btnHistory_Click: {ex.Message}");
            }
        }

        /// <summary>
        /// إعادة تعيين البحث
        /// </summary>
        private void btnReset_Click(object sender, EventArgs e)
        {
            txtSearch.Clear();
            LoadData();
        }

        /// <summary>
        /// البحث عند الكتابة
        /// </summary>
        private void txtSearch_TextChanged(object sender, EventArgs e)
        {
            // البحث التلقائي بعد توقف الكتابة
            searchTimer.Stop();
            searchTimer.Start();
        }

        /// <summary>
        /// البحث التلقائي
        /// </summary>
        private void searchTimer_Tick(object sender, EventArgs e)
        {
            searchTimer.Stop();
            btnSearch_Click(sender, e);
        }

        /// <summary>
        /// النقر المزدوج لتعديل
        /// </summary>
        private void dgvSuppliers_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                btnEdit_Click(sender, e);
            }
        }

        #endregion
    }
}
