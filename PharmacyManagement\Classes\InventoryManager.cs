using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using PharmacyManagement.Models;

namespace PharmacyManagement.Classes
{
    /// <summary>
    /// مدير المخزون - Inventory Manager
    /// </summary>
    public static class InventoryManager
    {
        #region Inventory Operations - عمليات المخزون

        /// <summary>
        /// الحصول على جميع عناصر المخزون
        /// </summary>
        /// <returns>قائمة عناصر المخزون</returns>
        public static List<InventoryItem> GetAllInventoryItems()
        {
            var items = new List<InventoryItem>();
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT
                            d.DrugID,
                            d.DrugCode,
                            d.DrugName as TradeName,
                            c.CategoryName,
                            m.ManufacturerName,
                            d.PurchasePrice,
                            d.SalePrice,
                            ISNULL(i.TotalStock, 0) as CurrentStock,
                            d.MinStock as MinStockLevel,
                            d.MaxStock as MaxStockLevel,
                            i.NearestExpiryDate as ExpiryDate,
                            d.Unit,
                            d.IsActive
                        FROM Drugs d
                        LEFT JOIN DrugCategories c ON d.CategoryID = c.CategoryID
                        LEFT JOIN Manufacturers m ON d.ManufacturerID = m.ManufacturerID
                        LEFT JOIN (
                            SELECT
                                DrugID,
                                SUM(Quantity) as TotalStock,
                                MIN(ExpiryDate) as NearestExpiryDate
                            FROM Inventory
                            WHERE Quantity > 0
                            GROUP BY DrugID
                        ) i ON d.DrugID = i.DrugID
                        WHERE d.IsActive = 1
                        ORDER BY d.DrugName";

                    using (var command = new SqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                items.Add(new InventoryItem
                                {
                                    DrugID = Convert.ToInt32(reader["DrugID"]),
                                    DrugCode = reader["DrugCode"].ToString(),
                                    DrugName = reader["TradeName"].ToString(),
                                    TradeName = reader["TradeName"].ToString(),
                                    Category = reader["CategoryName"] != DBNull.Value ? reader["CategoryName"].ToString() : null,
                                    CategoryName = reader["CategoryName"] != DBNull.Value ? reader["CategoryName"].ToString() : null,
                                    Manufacturer = reader["ManufacturerName"] != DBNull.Value ? reader["ManufacturerName"].ToString() : null,
                                    ManufacturerName = reader["ManufacturerName"] != DBNull.Value ? reader["ManufacturerName"].ToString() : null,
                                    UnitPrice = Convert.ToDecimal(reader["PurchasePrice"]),
                                    PurchasePrice = Convert.ToDecimal(reader["PurchasePrice"]),
                                    SellingPrice = Convert.ToDecimal(reader["SalePrice"]),
                                    SalePrice = Convert.ToDecimal(reader["SalePrice"]),
                                    CurrentStock = Convert.ToDecimal(reader["CurrentStock"]),
                                    MinimumStock = Convert.ToDecimal(reader["MinStockLevel"]),
                                    MaximumStock = Convert.ToDecimal(reader["MaxStockLevel"]),
                                    MinStockLevel = Convert.ToDecimal(reader["MinStockLevel"]),
                                    MaxStockLevel = Convert.ToDecimal(reader["MaxStockLevel"]),
                                    Unit = reader["Unit"] != DBNull.Value ? reader["Unit"].ToString() : null,
                                    ExpiryDate = reader["ExpiryDate"] != DBNull.Value ? Convert.ToDateTime(reader["ExpiryDate"]) : (DateTime?)null,
                                    IsActive = Convert.ToBoolean(reader["IsActive"])
                                });
                            }
                        }
                    }
                }
                LogManager.LogInfo("تم الحصول على " + items.Count.ToString() + " عنصر مخزون");
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في الحصول على عناصر المخزون: " + ex.Message);
                throw;
            }
            return items;
        }

        /// <summary>
        /// الحصول على الأدوية منخفضة المخزون
        /// </summary>
        /// <returns>قائمة الأدوية منخفضة المخزون</returns>
        public static List<InventoryItem> GetLowStockItems()
        {
            var items = new List<InventoryItem>();
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT 
                            d.DrugID,
                            d.DrugCode,
                            d.DrugName,
                            d.Category,
                            d.Manufacturer,
                            d.UnitPrice,
                            d.SellingPrice,
                            ISNULL(SUM(CASE WHEN it.TransactionType = 'In' THEN it.Quantity ELSE -it.Quantity END), 0) as CurrentStock,
                            d.MinimumStock,
                            d.MaximumStock,
                            d.ExpiryDate,
                            d.IsActive
                        FROM Drugs d
                        LEFT JOIN InventoryTransactions it ON d.DrugID = it.DrugID
                        WHERE d.IsActive = 1
                        GROUP BY d.DrugID, d.DrugCode, d.DrugName, d.Category, d.Manufacturer, 
                                d.UnitPrice, d.SellingPrice, d.MinimumStock, d.MaximumStock, 
                                d.ExpiryDate, d.IsActive
                        HAVING ISNULL(SUM(CASE WHEN it.TransactionType = 'In' THEN it.Quantity ELSE -it.Quantity END), 0) <= d.MinimumStock
                        ORDER BY CurrentStock ASC";

                    using (var command = new SqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                items.Add(new InventoryItem
                                {
                                    DrugID = Convert.ToInt32(reader["DrugID"]),
                                    DrugCode = reader["DrugCode"].ToString(),
                                    DrugName = reader["DrugName"].ToString(),
                                    Category = reader["Category"].ToString(),
                                    Manufacturer = reader["Manufacturer"].ToString(),
                                    UnitPrice = Convert.ToDecimal(reader["UnitPrice"]),
                                    SellingPrice = Convert.ToDecimal(reader["SellingPrice"]),
                                    CurrentStock = Convert.ToInt32(reader["CurrentStock"]),
                                    MinimumStock = Convert.ToInt32(reader["MinimumStock"]),
                                    MaximumStock = Convert.ToInt32(reader["MaximumStock"]),
                                    ExpiryDate = reader["ExpiryDate"] != DBNull.Value ? Convert.ToDateTime(reader["ExpiryDate"]) : (DateTime?)null,
                                    IsActive = Convert.ToBoolean(reader["IsActive"])
                                });
                            }
                        }
                    }
                }
                LogManager.LogInfo("تم الحصول على " + items.Count.ToString() + " دواء منخفض المخزون");
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في الحصول على الأدوية منخفضة المخزون: " + ex.Message);
                throw;
            }
            return items;
        }

        /// <summary>
        /// الحصول على الأدوية منتهية الصلاحية
        /// </summary>
        /// <returns>قائمة الأدوية منتهية الصلاحية</returns>
        public static List<InventoryItem> GetExpiredItems()
        {
            var items = new List<InventoryItem>();
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT
                            d.DrugID,
                            d.DrugCode,
                            d.DrugName as TradeName,
                            c.CategoryName,
                            m.ManufacturerName,
                            d.PurchasePrice,
                            d.SalePrice,
                            ISNULL(i.Quantity, 0) as CurrentStock,
                            d.MinStock as MinStockLevel,
                            d.MaxStock as MaxStockLevel,
                            i.ExpiryDate,
                            d.IsActive
                        FROM Drugs d
                        LEFT JOIN DrugCategories c ON d.CategoryID = c.CategoryID
                        LEFT JOIN Manufacturers m ON d.ManufacturerID = m.ManufacturerID
                        LEFT JOIN Inventory i ON d.DrugID = i.DrugID
                        WHERE d.IsActive = 1 AND i.ExpiryDate <= GETDATE() AND i.Quantity > 0
                        ORDER BY i.ExpiryDate ASC";

                    using (var command = new SqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                items.Add(new InventoryItem
                                {
                                    DrugID = Convert.ToInt32(reader["DrugID"]),
                                    DrugCode = reader["DrugCode"].ToString(),
                                    DrugName = reader["TradeName"].ToString(),
                                    TradeName = reader["TradeName"].ToString(),
                                    Category = reader["CategoryName"] != DBNull.Value ? reader["CategoryName"].ToString() : null,
                                    CategoryName = reader["CategoryName"] != DBNull.Value ? reader["CategoryName"].ToString() : null,
                                    Manufacturer = reader["ManufacturerName"] != DBNull.Value ? reader["ManufacturerName"].ToString() : null,
                                    ManufacturerName = reader["ManufacturerName"] != DBNull.Value ? reader["ManufacturerName"].ToString() : null,
                                    UnitPrice = Convert.ToDecimal(reader["PurchasePrice"]),
                                    PurchasePrice = Convert.ToDecimal(reader["PurchasePrice"]),
                                    SellingPrice = Convert.ToDecimal(reader["SalePrice"]),
                                    SalePrice = Convert.ToDecimal(reader["SalePrice"]),
                                    CurrentStock = Convert.ToDecimal(reader["CurrentStock"]),
                                    MinimumStock = Convert.ToDecimal(reader["MinStockLevel"]),
                                    MaximumStock = Convert.ToDecimal(reader["MaxStockLevel"]),
                                    MinStockLevel = Convert.ToDecimal(reader["MinStockLevel"]),
                                    MaxStockLevel = Convert.ToDecimal(reader["MaxStockLevel"]),
                                    ExpiryDate = reader["ExpiryDate"] != DBNull.Value ? Convert.ToDateTime(reader["ExpiryDate"]) : (DateTime?)null,
                                    IsActive = Convert.ToBoolean(reader["IsActive"])
                                });
                            }
                        }
                    }
                }
                LogManager.LogInfo("تم الحصول على " + items.Count.ToString() + " دواء منتهي الصلاحية");
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في الحصول على الأدوية منتهية الصلاحية: " + ex.Message);
                throw;
            }
            return items;
        }

        #endregion

        #region Inventory Transactions - معاملات المخزون

        /// <summary>
        /// إضافة معاملة مخزون
        /// </summary>
        /// <param name="transaction">بيانات المعاملة</param>
        /// <returns>معرف المعاملة</returns>
        public static int AddInventoryTransaction(InventoryTransaction transaction)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        INSERT INTO InventoryTransactions (DrugID, TransactionType, Quantity, 
                                                          UnitPrice, TotalAmount, TransactionDate, 
                                                          Reference, Notes, CreatedBy, CreatedDate)
                        VALUES (@DrugID, @TransactionType, @Quantity, @UnitPrice, @TotalAmount, 
                               @TransactionDate, @Reference, @Notes, @CreatedBy, @CreatedDate);
                        SELECT SCOPE_IDENTITY();";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@DrugID", transaction.DrugID);
                        command.Parameters.AddWithValue("@TransactionType", transaction.TransactionType);
                        command.Parameters.AddWithValue("@Quantity", transaction.Quantity);
                        command.Parameters.AddWithValue("@UnitPrice", transaction.UnitPrice);
                        command.Parameters.AddWithValue("@TotalAmount", transaction.TotalAmount);
                        command.Parameters.AddWithValue("@TransactionDate", transaction.TransactionDate);
                        command.Parameters.AddWithValue("@Reference", transaction.Reference ?? "");
                        command.Parameters.AddWithValue("@Notes", transaction.Notes ?? "");
                        command.Parameters.AddWithValue("@CreatedBy", transaction.CreatedBy);
                        command.Parameters.AddWithValue("@CreatedDate", DateTime.Now);

                        int transactionId = Convert.ToInt32(command.ExecuteScalar());
                        LogManager.LogInfo("تم إضافة معاملة مخزون: " + transaction.TransactionType + " - " + transaction.Quantity.ToString() + " وحدة");
                        return transactionId;
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في إضافة معاملة المخزون: " + ex.Message);
                throw;
            }
        }

        /// <summary>
        /// الحصول على معاملات المخزون
        /// </summary>
        /// <param name="drugID">معرف الدواء (اختياري)</param>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <returns>قائمة معاملات المخزون</returns>
        public static List<InventoryTransaction> GetInventoryTransactions(int? drugID = null, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var transactions = new List<InventoryTransaction>();
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT 
                            it.TransactionID,
                            it.DrugID,
                            d.DrugName,
                            it.TransactionType,
                            it.Quantity,
                            it.UnitPrice,
                            it.TotalAmount,
                            it.TransactionDate,
                            it.Reference,
                            it.Notes,
                            it.CreatedBy,
                            u.FullName as CreatedByName,
                            it.CreatedDate
                        FROM InventoryTransactions it
                        INNER JOIN Drugs d ON it.DrugID = d.DrugID
                        LEFT JOIN Users u ON it.CreatedBy = u.UserID
                        WHERE 1=1";

                    if (drugID.HasValue)
                        query += " AND it.DrugID = @DrugID";
                    
                    if (fromDate.HasValue)
                        query += " AND it.TransactionDate >= @FromDate";
                    
                    if (toDate.HasValue)
                        query += " AND it.TransactionDate <= @ToDate";

                    query += " ORDER BY it.TransactionDate DESC";

                    using (var command = new SqlCommand(query, connection))
                    {
                        if (drugID.HasValue)
                            command.Parameters.AddWithValue("@DrugID", drugID.Value);
                        
                        if (fromDate.HasValue)
                            command.Parameters.AddWithValue("@FromDate", fromDate.Value);
                        
                        if (toDate.HasValue)
                            command.Parameters.AddWithValue("@ToDate", toDate.Value);

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                transactions.Add(new InventoryTransaction
                                {
                                    TransactionID = Convert.ToInt32(reader["TransactionID"]),
                                    DrugID = Convert.ToInt32(reader["DrugID"]),
                                    DrugName = reader["DrugName"].ToString(),
                                    TransactionType = reader["TransactionType"].ToString(),
                                    Quantity = Convert.ToInt32(reader["Quantity"]),
                                    UnitPrice = Convert.ToDecimal(reader["UnitPrice"]),
                                    TotalAmount = Convert.ToDecimal(reader["TotalAmount"]),
                                    TransactionDate = Convert.ToDateTime(reader["TransactionDate"]),
                                    Reference = reader["Reference"].ToString(),
                                    Notes = reader["Notes"].ToString(),
                                    CreatedBy = Convert.ToInt32(reader["CreatedBy"]),
                                    CreatedByName = reader["CreatedByName"].ToString(),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"])
                                });
                            }
                        }
                    }
                }
                LogManager.LogInfo("تم الحصول على " + transactions.Count.ToString() + " معاملة مخزون");
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في الحصول على معاملات المخزون: " + ex.Message);
                throw;
            }
            return transactions;
        }

        #endregion

        #region Stock Adjustments - تعديلات المخزون

        /// <summary>
        /// تعديل مخزون دواء
        /// </summary>
        /// <param name="drugID">معرف الدواء</param>
        /// <param name="newQuantity">الكمية الجديدة</param>
        /// <param name="reason">سبب التعديل</param>
        /// <param name="userID">معرف المستخدم</param>
        /// <returns>true إذا تم التعديل بنجاح</returns>
        public static bool AdjustStock(int drugID, int newQuantity, string reason, int userID)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    
                    // الحصول على المخزون الحالي
                    string currentStockQuery = @"
                        SELECT ISNULL(SUM(CASE WHEN TransactionType = 'In' THEN Quantity ELSE -Quantity END), 0)
                        FROM InventoryTransactions 
                        WHERE DrugID = @DrugID";
                    
                    int currentStock = 0;
                    using (var command = new SqlCommand(currentStockQuery, connection))
                    {
                        command.Parameters.AddWithValue("@DrugID", drugID);
                        currentStock = Convert.ToInt32(command.ExecuteScalar());
                    }
                    
                    // حساب الفرق
                    int difference = newQuantity - currentStock;
                    
                    if (difference != 0)
                    {
                        // إضافة معاملة التعديل
                        var transaction = new InventoryTransaction
                        {
                            DrugID = drugID,
                            TransactionType = difference > 0 ? "In" : "Out",
                            Quantity = Math.Abs(difference),
                            UnitPrice = 0,
                            TotalAmount = 0,
                            TransactionDate = DateTime.Now,
                            Reference = "تعديل مخزون",
                            Notes = reason,
                            CreatedBy = userID
                        };
                        
                        AddInventoryTransaction(transaction);
                        LogManager.LogInfo("تم تعديل مخزون الدواء " + drugID.ToString() + " من " + currentStock.ToString() + " إلى " + newQuantity.ToString());
                    }
                    
                    return true;
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في تعديل المخزون: " + ex.Message);
                throw;
            }
        }

        #endregion

        #region Statistics - الإحصائيات

        /// <summary>
        /// الحصول على إحصائيات المخزون
        /// </summary>
        /// <returns>إحصائيات المخزون</returns>
        public static InventoryStatistics GetInventoryStatistics()
        {
            var stats = new InventoryStatistics();
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    
                    // إجمالي الأدوية
                    string totalDrugsQuery = "SELECT COUNT(*) FROM Drugs WHERE IsActive = 1";
                    using (var command = new SqlCommand(totalDrugsQuery, connection))
                    {
                        stats.TotalDrugs = Convert.ToInt32(command.ExecuteScalar());
                    }
                    
                    // الأدوية منخفضة المخزون
                    string lowStockQuery = @"
                        SELECT COUNT(*)
                        FROM (
                            SELECT d.DrugID
                            FROM Drugs d
                            LEFT JOIN Inventory i ON d.DrugID = i.DrugID
                            WHERE d.IsActive = 1
                            GROUP BY d.DrugID, d.MinStock
                            HAVING ISNULL(SUM(i.Quantity), 0) <= d.MinStock
                        ) as LowStock";
                    
                    using (var command = new SqlCommand(lowStockQuery, connection))
                    {
                        stats.LowStockDrugs = Convert.ToInt32(command.ExecuteScalar());
                    }
                    
                    // الأدوية منتهية الصلاحية
                    string expiredQuery = "SELECT COUNT(*) FROM Inventory WHERE ExpiryDate <= GETDATE()";
                    using (var command = new SqlCommand(expiredQuery, connection))
                    {
                        stats.ExpiredDrugs = Convert.ToInt32(command.ExecuteScalar());
                    }
                    
                    // قيمة المخزون الإجمالية
                    string totalValueQuery = @"
                        SELECT ISNULL(SUM(
                            ISNULL(SUM(CASE WHEN it.TransactionType = 'In' THEN it.Quantity ELSE -it.Quantity END), 0) * d.UnitPrice
                        ), 0)
                        FROM Drugs d
                        LEFT JOIN InventoryTransactions it ON d.DrugID = it.DrugID
                        WHERE d.IsActive = 1
                        GROUP BY d.DrugID, d.UnitPrice";
                    
                    using (var command = new SqlCommand(totalValueQuery, connection))
                    {
                        var result = command.ExecuteScalar();
                        stats.TotalInventoryValue = result != DBNull.Value ? Convert.ToDecimal(result) : 0;
                    }
                }
                
                LogManager.LogInfo("تم الحصول على إحصائيات المخزون");
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في الحصول على إحصائيات المخزون: " + ex.Message);
                throw;
            }
            return stats;
        }

        /// <summary>
        /// الحصول على تنبيهات المخزون
        /// </summary>
        /// <returns>قائمة التنبيهات</returns>
        public static List<Models.InventoryAlert> GetInventoryAlerts()
        {
            var alerts = new List<Models.InventoryAlert>();
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT
                            ROW_NUMBER() OVER (ORDER BY CreatedDate DESC) as AlertID,
                            'انتهاء الصلاحية' as AlertType,
                            'عالية' as Priority,
                            'الدواء ' + d.DrugName + ' منتهي الصلاحية' as Message,
                            d.DrugName,
                            '' as WarehouseName,
                            d.ExpiryDate as CreatedDate,
                            0 as IsRead
                        FROM Drugs d
                        WHERE d.IsActive = 1 AND d.ExpiryDate <= GETDATE()

                        UNION ALL

                        SELECT
                            ROW_NUMBER() OVER (ORDER BY d.DrugName) + 1000 as AlertID,
                            'مخزون منخفض' as AlertType,
                            'متوسطة' as Priority,
                            'الدواء ' + d.DrugName + ' مخزونه منخفض' as Message,
                            d.DrugName,
                            '' as WarehouseName,
                            GETDATE() as CreatedDate,
                            0 as IsRead
                        FROM Drugs d
                        LEFT JOIN InventoryTransactions it ON d.DrugID = it.DrugID
                        WHERE d.IsActive = 1
                        GROUP BY d.DrugID, d.DrugName, d.MinimumStock
                        HAVING ISNULL(SUM(CASE WHEN it.TransactionType = 'In' THEN it.Quantity ELSE -it.Quantity END), 0) <= d.MinimumStock

                        ORDER BY CreatedDate DESC";

                    using (var command = new SqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                alerts.Add(new Models.InventoryAlert
                                {
                                    AlertID = Convert.ToInt32(reader["AlertID"]),
                                    DrugID = 0, // غير متوفر في الاستعلام
                                    DrugCode = "N/A", // غير متوفر في الاستعلام
                                    DrugName = reader["DrugName"].ToString(),
                                    AlertType = reader["AlertType"].ToString(),
                                    Message = reader["Message"].ToString(),
                                    Severity = reader["Priority"].ToString(),
                                    AlertDate = Convert.ToDateTime(reader["CreatedDate"]),
                                    IsResolved = Convert.ToBoolean(reader["IsRead"]),
                                    ResolvedDate = null, // غير متوفر في الاستعلام
                                    Notes = "" // غير متوفر في الاستعلام
                                });
                            }
                        }
                    }
                }
                LogManager.LogInfo("تم الحصول على " + alerts.Count.ToString() + " تنبيه مخزون");
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في الحصول على تنبيهات المخزون: " + ex.Message);
            }
            return alerts;
        }

        /// <summary>
        /// الحصول على الأدوية منخفضة المخزون
        /// </summary>
        /// <returns>قائمة الأدوية</returns>
        public static List<Drug> GetLowStockDrugs()
        {
            var drugs = new List<Drug>();
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT d.*
                        FROM Drugs d
                        LEFT JOIN InventoryTransactions it ON d.DrugID = it.DrugID
                        WHERE d.IsActive = 1
                        GROUP BY d.DrugID, d.DrugCode, d.DrugName, d.GenericName, d.Manufacturer,
                                d.Category, d.UnitPrice, d.SalePrice, d.MinStockLevel, d.MaxStockLevel,
                                d.ExpiryDate, d.IsActive, d.CreatedDate, d.CreatedBy
                        HAVING ISNULL(SUM(CASE WHEN it.TransactionType = 'In' THEN it.Quantity ELSE -it.Quantity END), 0) <= d.MinStockLevel
                        ORDER BY d.DrugName";

                    using (var command = new SqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                drugs.Add(new Drug
                                {
                                    DrugID = reader["DrugID"] != DBNull.Value ? Convert.ToInt32(reader["DrugID"]) : 0,
                                    DrugCode = reader["DrugCode"] != DBNull.Value ? reader["DrugCode"].ToString() : string.Empty,
                                    DrugName = reader["DrugName"] != DBNull.Value ? reader["DrugName"].ToString() : string.Empty,
                                    GenericName = reader["GenericName"] != DBNull.Value ? reader["GenericName"].ToString() : string.Empty,
                                    Manufacturer = reader["Manufacturer"] != DBNull.Value ? reader["Manufacturer"].ToString() : string.Empty,
                                    Category = reader["Category"] != DBNull.Value ? reader["Category"].ToString() : string.Empty,
                                    UnitPrice = reader["UnitPrice"] != DBNull.Value ? Convert.ToDecimal(reader["UnitPrice"]) : 0,
                                    SalePrice = reader["SalePrice"] != DBNull.Value ? Convert.ToDecimal(reader["SalePrice"]) : 0,
                                    MinStockLevel = reader["MinStockLevel"] != DBNull.Value ? Convert.ToInt32(reader["MinStockLevel"]) : 0,
                                    MaxStockLevel = reader["MaxStockLevel"] != DBNull.Value ? Convert.ToInt32(reader["MaxStockLevel"]) : 0,
                                    ExpiryDate = reader["ExpiryDate"] != DBNull.Value ? (DateTime?)Convert.ToDateTime(reader["ExpiryDate"]) : null,
                                    IsActive = reader["IsActive"] != DBNull.Value && Convert.ToBoolean(reader["IsActive"]),
                                    CreatedDate = reader["CreatedDate"] != DBNull.Value ? Convert.ToDateTime(reader["CreatedDate"]) : DateTime.MinValue,
                                    CreatedBy = reader["CreatedBy"] != DBNull.Value ? (int?)Convert.ToInt32(reader["CreatedBy"]) : null
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في الحصول على الأدوية منخفضة المخزون: " + ex.Message);
            }
            return drugs;
        }

        /// <summary>
        /// الحصول على الأدوية منتهية الصلاحية
        /// </summary>
        /// <returns>قائمة الأدوية</returns>
        public static List<Drug> GetExpiredDrugs()
        {
            var drugs = new List<Drug>();
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT d.*, c.CategoryName, m.ManufacturerName
                        FROM Drugs d
                        LEFT JOIN DrugCategories c ON d.CategoryID = c.CategoryID
                        LEFT JOIN Manufacturers m ON d.ManufacturerID = m.ManufacturerID
                        INNER JOIN Inventory i ON d.DrugID = i.DrugID
                        WHERE d.IsActive = 1 AND i.ExpiryDate <= GETDATE() AND i.Quantity > 0
                        ORDER BY i.ExpiryDate ASC";

                    using (var command = new SqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                drugs.Add(new Drug
                                {
                                    DrugID = Convert.ToInt32(reader["DrugID"]),
                                    DrugCode = reader["DrugCode"].ToString(),
                                    DrugName = reader["DrugName"].ToString(),
                                    GenericName = reader["GenericName"].ToString(),
                                    Manufacturer = reader["Manufacturer"] != DBNull.Value ? reader["Manufacturer"].ToString() : string.Empty,
                                    Category = reader["Category"] != DBNull.Value ? reader["Category"].ToString() : string.Empty,
                                    UnitPrice = reader["UnitPrice"] != DBNull.Value ? Convert.ToDecimal(reader["UnitPrice"]) : 0,
                                    SalePrice = reader["SalePrice"] != DBNull.Value ? Convert.ToDecimal(reader["SalePrice"]) : 0,
                                    MinStockLevel = reader["MinStockLevel"] != DBNull.Value ? Convert.ToInt32(reader["MinStockLevel"]) : 0,
                                    MaxStockLevel = reader["MaxStockLevel"] != DBNull.Value ? Convert.ToInt32(reader["MaxStockLevel"]) : 0,
                                    ExpiryDate = reader["ExpiryDate"] == DBNull.Value ? null : (DateTime?)Convert.ToDateTime(reader["ExpiryDate"]),
                                    IsActive = Convert.ToBoolean(reader["IsActive"]),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                                    CreatedBy = reader["CreatedBy"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["CreatedBy"])
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في الحصول على الأدوية منتهية الصلاحية: " + ex.Message);
            }
            return drugs;
        }

        /// <summary>
        /// الحصول على إجمالي قيمة المخزون
        /// </summary>
        /// <returns>إجمالي قيمة المخزون</returns>
        public static decimal GetTotalInventoryValue()
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT ISNULL(SUM(Quantity * UnitCost), 0)
                        FROM InventoryItems
                        WHERE IsActive = 1";

                    using (var command = new SqlCommand(query, connection))
                    {
                        var result = command.ExecuteScalar();
                        return result != null ? Convert.ToDecimal(result) : 0;
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في حساب إجمالي قيمة المخزون: " + ex.Message);
                return 0;
            }
        }

        #endregion
    }

    #region Inventory Models - نماذج المخزون

    /// <summary>
    /// نموذج معاملة المخزون
    /// </summary>
    public class InventoryTransaction
    {
        public int TransactionID { get; set; }
        public int DrugID { get; set; }
        public string DrugName { get; set; }
        public string TransactionType { get; set; } // In, Out, Adjustment
        public int Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalAmount { get; set; }
        public DateTime TransactionDate { get; set; }
        public string Reference { get; set; }
        public string Notes { get; set; }
        public int CreatedBy { get; set; }
        public string CreatedByName { get; set; }
        public DateTime CreatedDate { get; set; }
    }

    /// <summary>
    /// نموذج إحصائيات المخزون
    /// </summary>
    public class InventoryStatistics
    {
        public int TotalDrugs { get; set; }
        public int LowStockDrugs { get; set; }
        public int ExpiredDrugs { get; set; }
        public decimal TotalInventoryValue { get; set; }
    }



    #endregion
}
