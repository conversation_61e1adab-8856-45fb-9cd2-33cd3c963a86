using System;
using System.Data;
using System.Drawing;
using System.Windows.Forms;
using PharmacyManagement.Classes;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نافذة التقارير - Reports Form
    /// </summary>
    public partial class ReportsForm : Form
    {
        #region Constructor - المنشئ

        public ReportsForm()
        {
            InitializeComponent();
            SetupForm();
        }

        #endregion

        #region Form Setup - إعداد النافذة

        /// <summary>
        /// إعداد النافذة
        /// </summary>
        private void SetupForm()
        {
            // إعداد التواريخ الافتراضية
            dtpFromDate.Value = DateTime.Now.AddMonths(-1);
            dtpToDate.Value = DateTime.Now;
            
            // إعداد التصميم المسطح
            ApplyFlatDesign();
        }

        /// <summary>
        /// تطبيق التصميم المسطح
        /// </summary>
        private void ApplyFlatDesign()
        {
            // إزالة حدود الأزرار
            foreach (Control control in this.Controls)
            {
                ApplyFlatStyleToControl(control);
            }
        }

        /// <summary>
        /// تطبيق التصميم المسطح على عنصر
        /// </summary>
        private void ApplyFlatStyleToControl(Control control)
        {
            if (control is Button button)
            {
                button.FlatAppearance.BorderSize = 0;
                button.Cursor = Cursors.Hand;
            }
            
            // تطبيق على العناصر الفرعية
            foreach (Control child in control.Controls)
            {
                ApplyFlatStyleToControl(child);
            }
        }

        #endregion

        #region Event Handlers - معالجات الأحداث

        /// <summary>
        /// تقرير المبيعات
        /// </summary>
        private void btnSalesReport_Click(object sender, EventArgs e)
        {
            try
            {
                var reportForm = new SalesReportForm(dtpFromDate.Value, dtpToDate.Value);
                reportForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح تقرير المبيعات: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تقرير المخزون
        /// </summary>
        private void btnInventoryReport_Click(object sender, EventArgs e)
        {
            try
            {
                var reportForm = new InventoryReportForm();
                reportForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح تقرير المخزون: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تقرير العملاء
        /// </summary>
        private void btnCustomersReport_Click(object sender, EventArgs e)
        {
            try
            {
                var reportForm = new CustomersReportForm(dtpFromDate.Value, dtpToDate.Value);
                reportForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح تقرير العملاء: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تقرير الموردين
        /// </summary>
        private void btnSuppliersReport_Click(object sender, EventArgs e)
        {
            try
            {
                var reportForm = new SuppliersReportForm(dtpFromDate.Value, dtpToDate.Value);
                reportForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح تقرير الموردين: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// التقرير المالي
        /// </summary>
        private void btnFinancialReport_Click(object sender, EventArgs e)
        {
            try
            {
                // var reportForm = new FinancialReportForm(dtpFromDate.Value, dtpToDate.Value);
                // reportForm.ShowDialog();
                MessageBox.Show("التقرير المالي قيد التطوير", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح التقرير المالي: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تقرير الأدوية منتهية الصلاحية
        /// </summary>
        private void btnExpiryReport_Click(object sender, EventArgs e)
        {
            try
            {
                // var reportForm = new ExpiryReportForm();
                // reportForm.ShowDialog();
                MessageBox.Show("تقرير انتهاء الصلاحية قيد التطوير", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح تقرير انتهاء الصلاحية: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تقرير الأدوية الأكثر مبيعاً
        /// </summary>
        private void btnTopSellingReport_Click(object sender, EventArgs e)
        {
            try
            {
                // var reportForm = new TopSellingReportForm(dtpFromDate.Value, dtpToDate.Value);
                // reportForm.ShowDialog();
                MessageBox.Show("تقرير الأدوية الأكثر مبيعاً قيد التطوير", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح تقرير الأكثر مبيعاً: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تقرير الأرباح
        /// </summary>
        private void btnProfitReport_Click(object sender, EventArgs e)
        {
            try
            {
                // var reportForm = new ProfitReportForm(dtpFromDate.Value, dtpToDate.Value);
                // reportForm.ShowDialog();
                MessageBox.Show("تقرير الأرباح قيد التطوير", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح تقرير الأرباح: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تقرير شامل
        /// </summary>
        private void btnComprehensiveReport_Click(object sender, EventArgs e)
        {
            try
            {
                // var reportForm = new ComprehensiveReportForm(dtpFromDate.Value, dtpToDate.Value);
                // reportForm.ShowDialog();
                MessageBox.Show("التقرير الشامل قيد التطوير", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح التقرير الشامل: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تصدير جميع التقارير
        /// </summary>
        private void btnExportAll_Click(object sender, EventArgs e)
        {
            try
            {
                var folderDialog = new FolderBrowserDialog
                {
                    Description = "اختر مجلد لحفظ التقارير"
                };

                if (folderDialog.ShowDialog() == DialogResult.OK)
                {
                    string folderPath = folderDialog.SelectedPath;
                    DateTime fromDate = dtpFromDate.Value;
                    DateTime toDate = dtpToDate.Value;

                    // تصدير جميع التقارير
                    // ReportManager.ExportAllReports(folderPath, fromDate, toDate);
                    MessageBox.Show("تصدير التقارير قيد التطوير", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    
                    MessageBox.Show("تم تصدير جميع التقارير بنجاح", "نجح", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير التقارير: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// طباعة سريعة
        /// </summary>
        private void btnQuickPrint_Click(object sender, EventArgs e)
        {
            try
            {
                // var printForm = new QuickPrintForm(dtpFromDate.Value, dtpToDate.Value);
                // printForm.ShowDialog();
                MessageBox.Show("الطباعة السريعة قيد التطوير", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة السريعة: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إعدادات التقارير
        /// </summary>
        private void btnReportSettings_Click(object sender, EventArgs e)
        {
            try
            {
                // var settingsForm = new ReportSettingsForm();
                // settingsForm.ShowDialog();
                MessageBox.Show("إعدادات التقارير قيد التطوير", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح إعدادات التقارير: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion
    }
}
