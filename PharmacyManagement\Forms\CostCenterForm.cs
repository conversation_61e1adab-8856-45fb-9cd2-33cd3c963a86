using System;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نموذج إدارة مراكز التكاليف - Cost Center Management Form
    /// </summary>
    public partial class CostCenterForm : Form
    {
        #region Fields

        private BindingSource _bindingSource;
        private int? _selectedCostCenterId;

        #endregion

        #region Constructor

        public CostCenterForm()
        {
            InitializeComponent();
            SetupForm();
            LoadData();
        }

        #endregion

        #region Setup Methods

        private void SetupForm()
        {
            this.Text = "إدارة مراكز التكاليف";
            this.WindowState = FormWindowState.Maximized;
            
            // إعداد التصميم المسطح
            SetupFlatDesign();
            
            // إعداد الشبكة
            SetupDataGrid();
            
            // إعداد الأحداث
            SetupEvents();
        }

        private void SetupFlatDesign()
        {
            // إعدادات النموذج
            this.BackColor = Color.White;
            this.Font = new Font("Segoe UI", 9F);
            
            // إعداد الأزرار
            foreach (Control control in this.Controls)
            {
                if (control is Button btn)
                {
                    btn.FlatStyle = FlatStyle.Flat;
                    btn.FlatAppearance.BorderSize = 1;
                    btn.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
                }
                else if (control is TextBox txt)
                {
                    txt.BorderStyle = BorderStyle.FixedSingle;
                    txt.Font = new Font("Segoe UI", 10F);
                }
            }

            // ألوان الأزرار
            btnAdd.BackColor = Color.FromArgb(46, 204, 113);
            btnAdd.ForeColor = Color.White;
            btnEdit.BackColor = Color.FromArgb(52, 152, 219);
            btnEdit.ForeColor = Color.White;
            btnDelete.BackColor = Color.FromArgb(231, 76, 60);
            btnDelete.ForeColor = Color.White;
            btnSearch.BackColor = Color.FromArgb(155, 89, 182);
            btnSearch.ForeColor = Color.White;
        }

        private void SetupDataGrid()
        {
            // إعداد الشبكة
            dgvCostCenters.AutoGenerateColumns = false;
            dgvCostCenters.AllowUserToAddRows = false;
            dgvCostCenters.AllowUserToDeleteRows = false;
            dgvCostCenters.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvCostCenters.MultiSelect = false;
            dgvCostCenters.BackgroundColor = Color.White;
            dgvCostCenters.BorderStyle = BorderStyle.FixedSingle;
            dgvCostCenters.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 152, 219);
            dgvCostCenters.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvCostCenters.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            dgvCostCenters.DefaultCellStyle.Font = new Font("Segoe UI", 9F);
            dgvCostCenters.RowHeadersVisible = false;
            dgvCostCenters.EnableHeadersVisualStyles = false;

            // إضافة الأعمدة
            dgvCostCenters.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CostCenterCode",
                HeaderText = "كود مركز التكلفة",
                DataPropertyName = "CostCenterCode",
                Width = 150
            });

            dgvCostCenters.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CostCenterName",
                HeaderText = "اسم مركز التكلفة",
                DataPropertyName = "CostCenterName",
                Width = 250
            });

            dgvCostCenters.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Description",
                HeaderText = "الوصف",
                DataPropertyName = "Description",
                Width = 200
            });

            dgvCostCenters.Columns.Add(new DataGridViewCheckBoxColumn
            {
                Name = "IsActive",
                HeaderText = "نشط",
                DataPropertyName = "IsActive",
                Width = 80
            });

            dgvCostCenters.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CreatedDate",
                HeaderText = "تاريخ الإنشاء",
                DataPropertyName = "CreatedDate",
                Width = 120,
                DefaultCellStyle = { Format = "dd/MM/yyyy" }
            });

            // إعداد مصدر البيانات
            _bindingSource = new BindingSource();
            dgvCostCenters.DataSource = _bindingSource;
        }

        private void SetupEvents()
        {
            // ربط الأحداث
            btnAdd.Click += BtnAdd_Click;
            btnEdit.Click += BtnEdit_Click;
            btnDelete.Click += BtnDelete_Click;
            btnSearch.Click += BtnSearch_Click;
            btnRefresh.Click += BtnRefresh_Click;
            
            dgvCostCenters.SelectionChanged += DgvCostCenters_SelectionChanged;
            dgvCostCenters.CellFormatting += DgvCostCenters_CellFormatting;
            dgvCostCenters.CellDoubleClick += DgvCostCenters_CellDoubleClick;
            
            txtSearch.KeyPress += TxtSearch_KeyPress;
        }

        #endregion

        #region Load Data Methods

        private void LoadData()
        {
            try
            {
                var costCenters = CostCenterManager.GetAllCostCenters();
                _bindingSource.DataSource = costCenters;
                
                UpdateButtonStates();
                UpdateStatusLabel();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Event Handlers

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                // TODO: Create CostCenterAddEditForm
                MessageBox.Show("إضافة مركز تكلفة - قيد التطوير", "معلومات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                // using (var addForm = new CostCenterAddEditForm())
                // {
                //     if (addForm.ShowDialog() == DialogResult.OK)
                //     {
                //         LoadData();
                //     }
                // }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة مركز التكلفة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            try
            {
                // TODO: Create CostCenterAddEditForm
                MessageBox.Show("تعديل مركز تكلفة - قيد التطوير", "معلومات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعديل مركز التكلفة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            try
            {
                if (_selectedCostCenterId.HasValue)
                {
                    var result = MessageBox.Show("هل تريد حذف مركز التكلفة المحدد؟", "تأكيد الحذف",
                        MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    
                    if (result == DialogResult.Yes)
                    {
                        if (CostCenterManager.DeleteCostCenter(_selectedCostCenterId.Value))
                        {
                            MessageBox.Show("تم حذف مركز التكلفة بنجاح", "نجح",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                            LoadData();
                        }
                        else
                        {
                            MessageBox.Show("فشل في حذف مركز التكلفة", "خطأ",
                                MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف مركز التكلفة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnSearch_Click(object sender, EventArgs e)
        {
            PerformSearch();
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            txtSearch.Clear();
            LoadData();
        }

        private void DgvCostCenters_SelectionChanged(object sender, EventArgs e)
        {
            if (dgvCostCenters.SelectedRows.Count > 0)
            {
                var selectedRow = dgvCostCenters.SelectedRows[0];
                if (selectedRow.DataBoundItem is CostCenter costCenter)
                {
                    _selectedCostCenterId = costCenter.CostCenterID;
                    UpdateButtonStates();
                }
            }
            else
            {
                _selectedCostCenterId = null;
                UpdateButtonStates();
            }
        }

        private void DgvCostCenters_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (dgvCostCenters.Rows[e.RowIndex].DataBoundItem is CostCenter costCenter)
            {
                // تلوين الصف حسب الحالة
                if (costCenter.IsActive)
                {
                    dgvCostCenters.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.LightGreen;
                }
                else
                {
                    dgvCostCenters.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.LightGray;
                }
            }
        }

        private void DgvCostCenters_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                BtnEdit_Click(sender, e);
            }
        }

        private void TxtSearch_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                PerformSearch();
                e.Handled = true;
            }
        }

        #endregion

        #region Helper Methods

        private void UpdateButtonStates()
        {
            bool hasSelection = _selectedCostCenterId.HasValue;
            
            btnEdit.Enabled = hasSelection;
            btnDelete.Enabled = hasSelection;
        }

        private void UpdateStatusLabel()
        {
            var totalCostCenters = _bindingSource.Count;
            var activeCostCenters = 0;

            foreach (CostCenter costCenter in _bindingSource)
            {
                if (costCenter.IsActive)
                    activeCostCenters++;
            }

            lblStatus.Text = $"إجمالي مراكز التكاليف: {totalCostCenters} | نشط: {activeCostCenters}";
        }

        private void PerformSearch()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtSearch.Text))
                {
                    LoadData();
                    return;
                }

                var searchResults = CostCenterManager.SearchCostCenters(txtSearch.Text.Trim());
                _bindingSource.DataSource = searchResults;
                UpdateStatusLabel();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion
    }
}




