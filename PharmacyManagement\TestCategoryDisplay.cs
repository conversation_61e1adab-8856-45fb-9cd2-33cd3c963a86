using System;
using System.Linq;
using System.Windows.Forms;
using PharmacyManagement.Classes;

namespace PharmacyManagement
{
    /// <summary>
    /// اختبار عرض الفئات في المخزون
    /// </summary>
    public static class TestCategoryDisplay
    {
        /// <summary>
        /// اختبار عرض الفئات
        /// </summary>
        public static void TestCategoryDisplay()
        {
            try
            {
                Console.WriteLine("=== اختبار عرض الفئات في المخزون ===");
                
                // 1. اختبار جلب فئات الأدوية
                Console.WriteLine("1. اختبار جلب فئات الأدوية:");
                string categoryQuery = "SELECT CategoryID, CategoryName FROM DrugCategories ORDER BY CategoryName";
                var categoriesTable = DatabaseHelper.ExecuteQuery(categoryQuery);
                
                Console.WriteLine($"   عدد الفئات: {categoriesTable.Rows.Count}");
                foreach (System.Data.DataRow row in categoriesTable.Rows)
                {
                    Console.WriteLine($"   - {row["CategoryID"]}: {row["CategoryName"]}");
                }
                
                Console.WriteLine();
                
                // 2. اختبار جلب الأدوية مع الفئات
                Console.WriteLine("2. اختبار جلب الأدوية مع الفئات:");
                string drugsQuery = @"
                    SELECT TOP 5
                        d.DrugID,
                        d.DrugCode,
                        d.DrugName,
                        d.CategoryID,
                        c.CategoryName,
                        m.ManufacturerName
                    FROM Drugs d
                    LEFT JOIN DrugCategories c ON d.CategoryID = c.CategoryID
                    LEFT JOIN Manufacturers m ON d.ManufacturerID = m.ManufacturerID
                    WHERE d.IsActive = 1
                    ORDER BY d.DrugName";
                
                using (var reader = DatabaseHelper.ExecuteReader(drugsQuery))
                {
                    while (reader.Read())
                    {
                        Console.WriteLine($"   - الدواء: {reader["DrugName"]}");
                        Console.WriteLine($"     معرف الفئة: {reader["CategoryID"] ?? "NULL"}");
                        Console.WriteLine($"     اسم الفئة: {reader["CategoryName"] ?? "NULL"}");
                        Console.WriteLine($"     المصنع: {reader["ManufacturerName"] ?? "NULL"}");
                        Console.WriteLine("     ---");
                    }
                }
                
                Console.WriteLine();
                
                // 3. اختبار InventoryManager
                Console.WriteLine("3. اختبار InventoryManager:");
                var inventory = InventoryManager.GetAllInventoryItems();
                Console.WriteLine($"   عدد عناصر المخزون: {inventory.Count}");
                
                if (inventory.Count > 0)
                {
                    Console.WriteLine("   أول 3 عناصر:");
                    foreach (var item in inventory.Take(3))
                    {
                        Console.WriteLine($"   - كود الدواء: {item.DrugCode}");
                        Console.WriteLine($"     اسم الدواء: {item.TradeName ?? item.DrugName}");
                        Console.WriteLine($"     الفئة (Category): {item.Category ?? "NULL"}");
                        Console.WriteLine($"     الفئة (CategoryName): {item.CategoryName ?? "NULL"}");
                        Console.WriteLine($"     المصنع (Manufacturer): {item.Manufacturer ?? "NULL"}");
                        Console.WriteLine($"     المصنع (ManufacturerName): {item.ManufacturerName ?? "NULL"}");
                        Console.WriteLine("     ---");
                    }
                    
                    // إحصائيات الفئات
                    var categoriesWithData = inventory.Where(i => !string.IsNullOrEmpty(i.CategoryName)).Count();
                    var categoriesWithoutData = inventory.Where(i => string.IsNullOrEmpty(i.CategoryName)).Count();
                    
                    Console.WriteLine($"   عناصر لها فئة: {categoriesWithData}");
                    Console.WriteLine($"   عناصر بدون فئة: {categoriesWithoutData}");
                    
                    // عرض الفئات الموجودة
                    var uniqueCategories = inventory
                        .Where(i => !string.IsNullOrEmpty(i.CategoryName))
                        .Select(i => i.CategoryName)
                        .Distinct()
                        .OrderBy(c => c)
                        .ToList();
                    
                    Console.WriteLine($"   الفئات الموجودة في المخزون ({uniqueCategories.Count}):");
                    foreach (var category in uniqueCategories)
                    {
                        var count = inventory.Count(i => i.CategoryName == category);
                        Console.WriteLine($"     - {category}: {count} عنصر");
                    }
                }
                
                Console.WriteLine();
                Console.WriteLine("=== انتهى اختبار عرض الفئات ===");
                
                MessageBox.Show("تم اختبار عرض الفئات بنجاح!\nتحقق من وحدة التحكم للتفاصيل.", 
                              "اختبار الفئات", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار عرض الفئات: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
                MessageBox.Show($"خطأ في اختبار الفئات: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        /// <summary>
        /// اختبار الاستعلام المباشر
        /// </summary>
        public static void TestDirectQuery()
        {
            try
            {
                Console.WriteLine("=== اختبار الاستعلام المباشر ===");
                
                string query = @"
                    SELECT TOP 10
                        d.DrugID,
                        d.DrugCode,
                        d.DrugName as TradeName,
                        c.CategoryName,
                        m.ManufacturerName,
                        d.PurchasePrice,
                        d.SalePrice,
                        ISNULL(i.TotalStock, 0) as CurrentStock,
                        d.MinStock as MinStockLevel,
                        d.MaxStock as MaxStockLevel,
                        d.Unit,
                        d.IsActive
                    FROM Drugs d
                    LEFT JOIN DrugCategories c ON d.CategoryID = c.CategoryID
                    LEFT JOIN Manufacturers m ON d.ManufacturerID = m.ManufacturerID
                    LEFT JOIN (
                        SELECT 
                            DrugID, 
                            SUM(Quantity) as TotalStock
                        FROM Inventory 
                        WHERE Quantity > 0
                        GROUP BY DrugID
                    ) i ON d.DrugID = i.DrugID
                    WHERE d.IsActive = 1
                    ORDER BY d.DrugName";
                
                using (var reader = DatabaseHelper.ExecuteReader(query))
                {
                    int count = 0;
                    while (reader.Read() && count < 5)
                    {
                        Console.WriteLine($"السجل {count + 1}:");
                        Console.WriteLine($"  كود الدواء: {reader["DrugCode"]}");
                        Console.WriteLine($"  اسم الدواء: {reader["TradeName"]}");
                        Console.WriteLine($"  الفئة: {reader["CategoryName"] ?? "NULL"}");
                        Console.WriteLine($"  المصنع: {reader["ManufacturerName"] ?? "NULL"}");
                        Console.WriteLine($"  المخزون: {reader["CurrentStock"]}");
                        Console.WriteLine("  ---");
                        count++;
                    }
                }
                
                Console.WriteLine("=== انتهى الاختبار المباشر ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في الاختبار المباشر: {ex.Message}");
            }
        }
        
        /// <summary>
        /// تشغيل جميع اختبارات الفئات
        /// </summary>
        public static void RunAllCategoryTests()
        {
            TestCategoryDisplay();
            Console.WriteLine();
            TestDirectQuery();
        }
    }
}
