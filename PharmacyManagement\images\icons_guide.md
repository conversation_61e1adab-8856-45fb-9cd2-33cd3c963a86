# دليل الأيقونات - نظام إدارة الصيدلية اليمنية

## نظرة عامة
تم إنشاء جميع الأيقونات تلقائياً باستخدام رموز Unicode الحديثة والألوان المتناسقة مع تصميم النظام.

## الأيقونات المُنشأة

### 🛠️ أيقونات شريط الأدوات:

| الأيقونة | الملف | الوصف | الاستخدام |
|---------|-------|--------|----------|
| 💰 | `sale_icon.png` | أيقونة البيع السريع | شريط الأدوات - البيع السريع |
| 🔍 | `search_icon.png` | أيقونة البحث السريع | شريط الأدوات - البحث السريع |
| 🔔 | `alert_icon.png` | أيقونة التنبيهات | شريط الأدوات - التنبيهات |
| 🔄 | `refresh_icon.png` | أيقونة التحديث | شريط الأدوات - تحديث البيانات |

### 📋 أيقونات القوائم الرئيسية:

| الأيقونة | الملف | الوصف | الاستخدام |
|---------|-------|--------|----------|
| 📊 | `dashboard_icon.png` | لوحة المعلومات | القائمة الرئيسية |
| 💊 | `drugs_icon.png` | إدارة الأدوية | قائمة البيانات |
| 👥 | `customers_icon.png` | إدارة العملاء | قائمة البيانات |
| 🏢 | `suppliers_icon.png` | إدارة الموردين | قائمة البيانات |
| 📦 | `inventory_icon.png` | إدارة المخزون | القائمة الرئيسية |
| 📋 | `reports_icon.png` | التقارير | القائمة الرئيسية |
| 💳 | `financial_icon.png` | الإدارة المالية | القائمة الرئيسية |
| 👤 | `users_icon.png` | إدارة المستخدمين | قائمة الإدارة |
| ⚙️ | `settings_icon.png` | الإعدادات | قائمة الإدارة |

### 🏥 أيقونات النظام:

| الأيقونة | الملف | الوصف | الاستخدام |
|---------|-------|--------|----------|
| 🏥+ | `pharmacy_logo.png` | شعار الصيدلية | الشعار الرئيسي |
| 🏥 | `app_icon.png` | أيقونة التطبيق | أيقونة النافذة |

## الألوان المستخدمة

### 🎨 لوحة الألوان الأساسية:

- **اللون الأساسي**: `#34495e` (52, 73, 94) - أزرق داكن
- **اللون الثانوي**: `#2ecc71` (46, 204, 113) - أخضر
- **لون التنبيه**: `#e74c3c` (231, 76, 60) - أحمر
- **لون التحذير**: `#f1c40f` (241, 196, 15) - أصفر

### 🎯 استخدام الألوان:

- **الأخضر**: للعمليات الإيجابية (البيع، النجاح، المالية)
- **الأزرق**: للعمليات العادية (البحث، المستخدمين، الإعدادات)
- **الأحمر**: للتنبيهات والتحذيرات
- **الأصفر**: للمخزون والتحذيرات المتوسطة

## المواصفات التقنية

### 📐 الأحجام:
- **أيقونات شريط الأدوات**: 32x32 بكسل
- **أيقونات القوائم**: 32x32 بكسل
- **شعار الصيدلية**: 128x128 بكسل
- **أيقونة التطبيق**: 64x64 بكسل

### 🖼️ التنسيق:
- **نوع الملف**: PNG
- **الشفافية**: مدعومة
- **جودة الألوان**: 32-bit RGBA

## كيفية الاستخدام

### 💻 في الكود:
```csharp
// تحميل أيقونة
Image icon = IconManager.LoadIcon("sale_icon.png");

// الحصول على مسار الأيقونة
string iconPath = IconManager.GetIconPath("dashboard_icon.png");
```

### 🔧 في Designer:
- استخدم خاصية `Image` للكنترولز
- استخدم `ImageList` للقوائم والأشجار
- استخدم `Icon` للنوافذ

## الصيانة والتحديث

### 🔄 إعادة إنشاء الأيقونات:
```csharp
IconManager.Initialize(); // إنشاء جميع الأيقونات
```

### ➕ إضافة أيقونات جديدة:
1. أضف طريقة جديدة في `IconManager`
2. استخدم `CreateIconWithText()` أو `CreateCustomIcon()`
3. احفظ الأيقونة باستخدام `SaveIcon()`

## ملاحظات مهمة

### ⚠️ تحذيرات:
- تأكد من وجود مجلد `images` قبل تشغيل النظام
- الأيقونات تُنشأ تلقائياً عند أول تشغيل
- لا تحذف ملفات الأيقونات يدوياً

### 💡 نصائح:
- استخدم رموز Unicode للحصول على أفضل جودة
- حافظ على تناسق الألوان مع تصميم النظام
- اختبر الأيقونات على دقات شاشة مختلفة

## الدعم والمساعدة

للحصول على مساعدة إضافية أو الإبلاغ عن مشاكل في الأيقونات، راجع سجلات النظام أو اتصل بفريق التطوير.

---
**تم إنشاء هذا الدليل تلقائياً بواسطة نظام إدارة الصيدلية اليمنية**
