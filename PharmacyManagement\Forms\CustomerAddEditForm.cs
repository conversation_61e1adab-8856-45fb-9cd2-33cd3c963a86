using System;
using System.Drawing;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نافذة إضافة/تعديل العملاء - Customer Add/Edit Form
    /// </summary>
    public partial class CustomerAddEditForm : Form
    {
        #region Fields - الحقول

        private Customer _currentCustomer;
        private bool _isEditMode;
        #endregion

        #region Constructors - المنشئات

        /// <summary>
        /// منشئ لإضافة عميل جديد
        /// </summary>
        public CustomerAddEditForm()
        {
            InitializeComponent();
            _isEditMode = false;
            _currentCustomer = new Customer();
            SetupForm();
        }

        /// <summary>
        /// منشئ لتعديل عميل موجود
        /// </summary>
        /// <param name="customer">العميل المراد تعديله</param>
        public CustomerAddEditForm(Customer customer)
        {
            InitializeComponent();
            _isEditMode = true;
            _currentCustomer = customer;
            SetupForm();
            LoadCustomerData();
        }

        #endregion

        #region Form Setup - إعداد النافذة

        /// <summary>
        /// إعداد النافذة بدلاً من Designer
        /// </summary>
   
        /// <summary>
        /// إعداد النافذة
        /// </summary>
        private void SetupForm()
        {
            try
            {
                this.Text = _isEditMode ? "تعديل عميل" : "إضافة عميل جديد";
                lblTitle.Text = _isEditMode ? "✏️ تعديل عميل" : "➕ إضافة عميل جديد";

                // تعيين القيم الافتراضية
                dtpBirthDate.Value = DateTime.Now.AddYears(-25);
                cmbGender.SelectedIndex = 0;

                // إنشاء كود العميل تلقائياً للعملاء الجدد
                if (!_isEditMode)
                {
                    txtCustomerCode.Text = CustomerManager.GenerateUniqueCustomerCode();
                    txtCustomerCode.BackColor = Color.LightGreen; // لون مميز للكود المُنشأ تلقائياً
                }

                LogManager.LogInfo($"تم فتح نافذة {(_isEditMode ? "تعديل" : "إضافة")} عميل");
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في إعداد نافذة العميل: {ex.Message}");
                MessageBox.Show("حدث خطأ في إعداد النافذة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحميل بيانات العميل للتعديل
        /// </summary>
        private void LoadCustomerData()
        {
            try
            {
                if (_currentCustomer != null)
                {
                    txtCustomerCode.Text = _currentCustomer.CustomerCode;
                    txtCustomerName.Text = _currentCustomer.CustomerName;
                    txtPhone.Text = _currentCustomer.Phone;
                    txtEmail.Text = _currentCustomer.Email;
                    txtAddress.Text = _currentCustomer.Address;
                    if (_currentCustomer.BirthDate.HasValue)
                        dtpBirthDate.Value = _currentCustomer.BirthDate.Value;
                    cmbGender.Text = _currentCustomer.Gender;
                    txtNationalID.Text = _currentCustomer.NationalID;
                    chkIsActive.Checked = _currentCustomer.IsActive;
                    txtNotes.Text = _currentCustomer.Notes;
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تحميل بيانات العميل: {ex.Message}");
                MessageBox.Show("حدث خطأ في تحميل بيانات العميل", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Event Handlers - معالجات الأحداث

        /// <summary>
        /// حفظ العميل
        /// </summary>
        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (ValidateInput())
                {
                    SaveCustomer();
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في حفظ العميل: {ex.Message}");
                MessageBox.Show("حدث خطأ في حفظ العميل", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إلغاء العملية
        /// </summary>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        #endregion

        #region Auto Code Generation - إنشاء الكود التلقائي

        /// <summary>
        /// إنشاء كود عميل جديد
        /// </summary>
        private void GenerateNewCustomerCode()
        {
            try
            {
                string newCode = CustomerManager.GenerateUniqueCustomerCode();
                txtCustomerCode.Text = newCode;
                txtCustomerCode.BackColor = Color.LightGreen;

                // إظهار رسالة تأكيد
                MessageBox.Show($"تم إنشاء كود جديد: {newCode}", "كود جديد",
                              MessageBoxButtons.OK, MessageBoxIcon.Information);

                LogManager.LogInfo($"تم إنشاء كود عميل جديد: {newCode}");
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في إنشاء كود العميل: {ex.Message}");
                MessageBox.Show("حدث خطأ في إنشاء كود العميل", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// التحقق من كود العميل عند تغييره
        /// </summary>
        private void txtCustomerCode_TextChanged(object sender, EventArgs e)
        {
            try
            {
                string code = txtCustomerCode.Text.Trim();
                if (!string.IsNullOrEmpty(code))
                {
                    // التحقق من توفر الكود
                    bool isAvailable = CustomerManager.IsCustomerCodeAvailable(code);

                    if (_isEditMode && code == _currentCustomer.CustomerCode)
                    {
                        // الكود الحالي للعميل المُعدَّل
                        txtCustomerCode.BackColor = Color.White;
                    }
                    else if (isAvailable)
                    {
                        // كود متاح
                        txtCustomerCode.BackColor = Color.LightGreen;
                    }
                    else
                    {
                        // كود مُستخدم
                        txtCustomerCode.BackColor = Color.LightCoral;
                    }
                }
                else
                {
                    txtCustomerCode.BackColor = Color.White;
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في التحقق من كود العميل: {ex.Message}");
            }
        }

        /// <summary>
        /// النقر المزدوج على حقل الكود لإنشاء كود جديد
        /// </summary>
        private void txtCustomerCode_DoubleClick(object sender, EventArgs e)
        {
            if (!_isEditMode) // فقط للعملاء الجدد
            {
                var result = MessageBox.Show("اختر طريقة إنشاء الكود:\n\nYes = كود تلقائي (C001, C002...)\nNo = كود من الاسم",
                                           "إنشاء كود جديد", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    GenerateNewCustomerCode();
                }
                else if (result == DialogResult.No)
                {
                    GenerateCodeFromName();
                }
            }
        }

        /// <summary>
        /// إنشاء كود من اسم العميل
        /// </summary>
        private void GenerateCodeFromName()
        {
            try
            {
                string customerName = txtCustomerName.Text.Trim();
                if (string.IsNullOrWhiteSpace(customerName))
                {
                    MessageBox.Show("يرجى إدخال اسم العميل أولاً", "تنبيه",
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtCustomerName.Focus();
                    return;
                }

                string newCode = CustomerManager.GenerateCustomerCodeFromName(customerName);
                txtCustomerCode.Text = newCode;
                txtCustomerCode.BackColor = Color.LightBlue;

                MessageBox.Show($"تم إنشاء كود من الاسم: {newCode}", "كود جديد",
                              MessageBoxButtons.OK, MessageBoxIcon.Information);

                LogManager.LogInfo($"تم إنشاء كود من الاسم '{customerName}': {newCode}");
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في إنشاء كود من الاسم: {ex.Message}");
                MessageBox.Show("حدث خطأ في إنشاء الكود من الاسم", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحديث الكود تلقائياً عند تغيير الاسم (اختياري)
        /// </summary>
        private void txtCustomerName_TextChanged(object sender, EventArgs e)
        {
            // يمكن تفعيل هذه الميزة لاحقاً إذا رغب المستخدم
            // في إنشاء الكود تلقائياً عند كتابة الاسم
        }

        #endregion

        #region Private Methods - الطرق الخاصة

        /// <summary>
        /// التحقق من صحة البيانات المدخلة
        /// </summary>
        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtCustomerCode.Text))
            {
                MessageBox.Show("يرجى إدخال كود العميل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCustomerCode.Focus();
                return false;
            }

            // التحقق من تكرار كود العميل
            string customerCode = txtCustomerCode.Text.Trim();
            if (!_isEditMode || customerCode != _currentCustomer.CustomerCode)
            {
                if (!CustomerManager.IsCustomerCodeAvailable(customerCode))
                {
                    MessageBox.Show("كود العميل موجود مسبقاً، يرجى اختيار كود آخر", "تنبيه",
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtCustomerCode.Focus();
                    txtCustomerCode.SelectAll();
                    return false;
                }
            }

            if (string.IsNullOrWhiteSpace(txtCustomerName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم العميل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCustomerName.Focus();
                return false;
            }

            if (!string.IsNullOrWhiteSpace(txtEmail.Text) && !IsValidEmail(txtEmail.Text))
            {
                MessageBox.Show("يرجى إدخال بريد إلكتروني صحيح", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtEmail.Focus();
                return false;
            }

            return true;
        }

        /// <summary>
        /// التحقق من صحة البريد الإلكتروني
        /// </summary>
        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// حفظ بيانات العميل
        /// </summary>
        private void SaveCustomer()
        {
            // تحديث بيانات العميل من النموذج
            _currentCustomer.CustomerCode = txtCustomerCode.Text.Trim();
            _currentCustomer.CustomerName = txtCustomerName.Text.Trim();
            _currentCustomer.Phone = txtPhone.Text.Trim();
            _currentCustomer.Mobile = string.IsNullOrWhiteSpace(txtPhone?.Text) ? null : txtPhone.Text.Trim();
            _currentCustomer.Email = string.IsNullOrWhiteSpace(txtEmail.Text) ? null : txtEmail.Text.Trim();
            _currentCustomer.Address = string.IsNullOrWhiteSpace(txtAddress.Text) ? null : txtAddress.Text.Trim();
            _currentCustomer.BirthDate = dtpBirthDate.Value;
            _currentCustomer.Gender = cmbGender.Text;
            _currentCustomer.CustomerType = "عادي"; // قيمة افتراضية
            _currentCustomer.CreditLimit = 0; // قيمة افتراضية
            _currentCustomer.IsActive = chkIsActive.Checked;

            if (!_isEditMode)
            {
                _currentCustomer.RegistrationDate = DateTime.Now;
            }
            _currentCustomer.ModifiedDate = DateTime.Now;

            // حفظ في قاعدة البيانات
            bool success;
            if (_isEditMode)
            {
                success = CustomerManager.UpdateCustomer(_currentCustomer);
            }
            else
            {
                success =Convert.ToBoolean(CustomerManager.AddCustomer(_currentCustomer));
            }

            if (success)
            {
                string action = _isEditMode ? "تعديل" : "إضافة";
                MessageBox.Show($"تم {action} العميل بنجاح", "نجح",
                              MessageBoxButtons.OK, MessageBoxIcon.Information);
                LogManager.LogInfo($"تم {action} العميل: {_currentCustomer.CustomerName}");
            }
            else
            {
                string action = _isEditMode ? "تعديل" : "إضافة";
                throw new Exception($"فشل في {action} العميل");
            }
        }

        #endregion

        #region Properties - الخصائص

        /// <summary>
        /// العميل الحالي
        /// </summary>
        public Customer CurrentCustomer
        {
            get { return _currentCustomer; }
        }

        #endregion
    }
}
