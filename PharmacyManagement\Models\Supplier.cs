using System;

namespace PharmacyManagement.Models
{
    /// <summary>
    /// نموذج المورد - Supplier Model
    /// </summary>
    public class Supplier
    {
        /// <summary>
        /// معرف المورد
        /// </summary>
        public int SupplierID { get; set; }

        /// <summary>
        /// اسم المورد
        /// </summary>
        public string SupplierName { get; set; }

        /// <summary>
        /// الشخص المسؤول
        /// </summary>
        public string ContactPerson { get; set; }

        /// <summary>
        /// رقم الهاتف
        /// </summary>
        public string Phone { get; set; }

        /// <summary>
        /// رقم الجوال
        /// </summary>
        public string Mobile { get; set; }

        /// <summary>
        /// البريد الإلكتروني
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// العنوان
        /// </summary>
        public string Address { get; set; }

        /// <summary>
        /// الرصيد الحالي
        /// </summary>
        public decimal CurrentBalance { get; set; }

        /// <summary>
        /// الرصيد (اسم بديل للتوافق)
        /// </summary>
        public decimal Balance
        {
            get { return CurrentBalance; }
            set { CurrentBalance = value; }
        }

        /// <summary>
        /// هل المورد نشط
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// تاريخ التعديل
        /// </summary>
        public DateTime? ModifiedDate { get; set; }

        /// <summary>
        /// كود المورد
        /// </summary>
        public string SupplierCode { get; set; }

        /// <summary>
        /// الرقم الضريبي
        /// </summary>
        public string TaxNumber { get; set; }

        /// <summary>
        /// حد الائتمان
        /// </summary>
        public decimal CreditLimit { get; set; }

        /// <summary>
        /// ملاحظات
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// معرف المستخدم الذي أنشأ السجل
        /// </summary>
        public int? CreatedBy { get; set; }

        /// <summary>
        /// معرف المستخدم الذي عدل السجل
        /// </summary>
        public int? ModifiedBy { get; set; }
    }
}
