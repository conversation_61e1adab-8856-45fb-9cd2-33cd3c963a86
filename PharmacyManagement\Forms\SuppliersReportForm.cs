using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نافذة تقرير الموردين
    /// </summary>
    public partial class SuppliersReportForm : Form
    {
        #region Fields - الحقول

        private DateTime _fromDate;
        private DateTime _toDate;
        private List<Supplier> _suppliers;

        #endregion

        #region Constructor - المنشئ

        public SuppliersReportForm(DateTime fromDate, DateTime toDate)
        {
            _fromDate = fromDate;
            _toDate = toDate;
            InitializeComponent();
            SetupDataGridView();
            SetupDateLabels();
            LoadData();
        }

        private void SetupDateLabels()
        {
            lblFromDate.Text = "من تاريخ: " + _fromDate.ToString("yyyy/MM/dd");
            lblToDate.Text = "إلى تاريخ: " + _toDate.ToString("yyyy/MM/dd");

            // ربط الأحداث
            btnExport.Click += BtnExport_Click;
            btnPrint.Click += BtnPrint_Click;
            btnRefresh.Click += BtnRefresh_Click;
            btnClose.Click += BtnClose_Click;
        }

        private void BtnExport_Click(object sender, EventArgs e)
        {
            ExportToExcel();
        }

        private void BtnPrint_Click(object sender, EventArgs e)
        {
            PrintReport();
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadData();
        }

        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        #endregion

        #region Form Setup - إعداد النافذة

        private void SetupDataGridView()
        {
            dgvSuppliers.Columns.Clear();

            dgvSuppliers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "SupplierID",
                HeaderText = "المعرف",
                DataPropertyName = "SupplierID",
                Width = 80
            });

            dgvSuppliers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "SupplierName",
                HeaderText = "اسم المورد",
                DataPropertyName = "SupplierName",
                Width = 200
            });

            dgvSuppliers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Phone",
                HeaderText = "الهاتف",
                DataPropertyName = "Phone",
                Width = 120
            });

            dgvSuppliers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Email",
                HeaderText = "البريد الإلكتروني",
                DataPropertyName = "Email",
                Width = 180
            });

            dgvSuppliers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Balance",
                HeaderText = "الرصيد",
                DataPropertyName = "Balance",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N2" }
            });

            dgvSuppliers.Columns.Add(new DataGridViewCheckBoxColumn
            {
                Name = "IsActive",
                HeaderText = "نشط",
                DataPropertyName = "IsActive",
                Width = 60
            });
        }

        #endregion

        #region Data Loading - تحميل البيانات

        private void LoadData()
        {
            try
            {
                // تحميل بيانات الموردين
                _suppliers = SupplierManager.GetAllSuppliers();
                
                // تطبيق فلتر التاريخ إذا لزم الأمر
                var filteredSuppliers = _suppliers.Where(s => 
                    s.CreatedDate >= _fromDate && s.CreatedDate <= _toDate).ToList();

                // عرض البيانات
                dgvSuppliers.DataSource = filteredSuppliers;

                // تحديث الملخص
                UpdateSummary(filteredSuppliers);
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تحميل بيانات الموردين: " + ex.Message, "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateSummary(List<Supplier> suppliers)
        {
            try
            {
                var totalSuppliers = suppliers.Count;
                var activeSuppliers = suppliers.Count(s => s.IsActive);
                var totalBalance = suppliers.Sum(s => s.Balance);
                var averageBalance = totalSuppliers > 0 ? totalBalance / totalSuppliers : 0;

                lblTotalSuppliers.Text = "إجمالي الموردين: " + totalSuppliers.ToString();
                lblActiveSuppliers.Text = "الموردين النشطين: " + activeSuppliers.ToString();
                lblTotalBalance.Text = "إجمالي الرصيد: " + totalBalance.ToString("N2") + " ر.ي";
                lblAverageBalance.Text = "متوسط الرصيد: " + averageBalance.ToString("N2") + " ر.ي";
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تحديث الملخص: " + ex.Message, "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Event Handlers - معالجات الأحداث

        private void dgvSuppliers_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                try
                {
                    var selectedSupplier = dgvSuppliers.Rows[e.RowIndex].DataBoundItem as Supplier;
                    if (selectedSupplier != null)
                    {
                        var historyForm = new SupplierHistoryForm(selectedSupplier.SupplierID);
                        historyForm.ShowDialog();
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show("خطأ في فتح تاريخ المورد: " + ex.Message, "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        #endregion

        #region Helper Methods - الطرق المساعدة

        /// <summary>
        /// تصدير التقرير إلى Excel
        /// </summary>
        public void ExportToExcel()
        {
            try
            {
                // TODO: تنفيذ تصدير Excel
                MessageBox.Show("سيتم تنفيذ تصدير Excel قريباً", "معلومات",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تصدير التقرير: " + ex.Message, "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// طباعة التقرير
        /// </summary>
        public void PrintReport()
        {
            try
            {
                // TODO: تنفيذ الطباعة
                MessageBox.Show("سيتم تنفيذ الطباعة قريباً", "معلومات",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في طباعة التقرير: " + ex.Message, "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion
    }
}
