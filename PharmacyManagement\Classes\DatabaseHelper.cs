using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Windows.Forms;
using System.IO;

namespace PharmacyManagement.Classes
{
    /// <summary>
    /// مساعد قاعدة البيانات - Database Helper
    /// يوفر طرق للاتصال والتعامل مع قاعدة البيانات
    /// </summary>
    public static class DatabaseHelper
    {
        #region Properties - الخصائص

        /// <summary>
        /// سلسلة الاتصال بقاعدة البيانات
        /// </summary>
        private static string ConnectionString
        {
            get
            {
                try
                {
                    // محاولة الحصول على سلسلة الاتصال من App.config
                    var configConnection = ConfigurationManager.ConnectionStrings["PharmacyDB"] != null ? ConfigurationManager.ConnectionStrings["PharmacyDB"].ConnectionString : null;
                    if (!string.IsNullOrEmpty(configConnection))
                    {
                        return configConnection;
                    }

                    // سلسلة اتصال افتراضية للـ LocalDB
                    var dataDirectory = Path.Combine(Application.StartupPath, "Data");
                    if (!Directory.Exists(dataDirectory))
                    {
                        Directory.CreateDirectory(dataDirectory);
                    }

                    return "Data Source=(LocalDB)\\MSSQLLocalDB;AttachDbFilename=" + dataDirectory + "\\PharmacyDB.mdf;Integrated Security=True;Connect Timeout=30";
                }
                catch (Exception ex)
                {
                    LogManager.LogError("خطأ في الحصول على سلسلة الاتصال: " + ex.Message);
                    // سلسلة اتصال احتياطية
                    return "Data Source=(LocalDB)\\MSSQLLocalDB;Initial Catalog=PharmacyDB;Integrated Security=True;Connect Timeout=30";
                }
            }
        }

        #endregion

        #region Connection Methods - طرق الاتصال

        /// <summary>
        /// إنشاء اتصال جديد بقاعدة البيانات
        /// </summary>
        /// <returns>كائن الاتصال</returns>
        public static SqlConnection GetConnection()
        {
            return new SqlConnection(ConnectionString);
        }

        /// <summary>
        /// إنشاء قاعدة البيانات إذا لم تكن موجودة
        /// </summary>
        public static bool CreateDatabaseIfNotExists()
        {
            try
            {
                // محاولة الاتصال أولاً
                if (TestConnection())
                {
                    LogManager.LogInfo("قاعدة البيانات موجودة ومتصلة");
                    return true;
                }

                // إنشاء قاعدة البيانات
                LogManager.LogInfo("محاولة إنشاء قاعدة البيانات...");

                // قراءة سكريبت قاعدة البيانات الشاملة الموحدة
                var scriptPath = Path.Combine(Application.StartupPath, "Database", "PharmacyDB_Complete.sql");
                if (!File.Exists(scriptPath))
                {
                    LogManager.LogError("ملف قاعدة البيانات الشاملة غير موجود: " + scriptPath);
                    LogManager.LogError("سيتم إنشاء قاعدة بيانات أساسية بدلاً من ذلك");
                    return CreateBasicDatabase();
                }

                var script = File.ReadAllText(scriptPath);
                return ExecuteDatabaseScript(script);
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في إنشاء قاعدة البيانات: " + ex.Message);
                return CreateBasicDatabase();
            }
        }

        /// <summary>
        /// إنشاء قاعدة بيانات أساسية
        /// </summary>
        private static bool CreateBasicDatabase()
        {
            try
            {
                LogManager.LogInfo("إنشاء قاعدة بيانات أساسية...");

                var basicScript = @"
                    -- إنشاء الجداول الأساسية
                    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Users' AND xtype='U')
                    BEGIN
                        CREATE TABLE Users (
                            UserID INT IDENTITY(1,1) PRIMARY KEY,
                            Username NVARCHAR(50) NOT NULL UNIQUE,
                            Password NVARCHAR(255) NOT NULL,
                            FullName NVARCHAR(100) NOT NULL,
                            Email NVARCHAR(100),
                            Phone NVARCHAR(20),
                            Role NVARCHAR(20) NOT NULL DEFAULT 'User',
                            BranchID INT,
                            IsActive BIT NOT NULL DEFAULT 1,
                            CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
                            LastLogin DATETIME
                        )
                        PRINT 'تم إنشاء جدول المستخدمين'
                    END

                    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Branches' AND xtype='U')
                    BEGIN
                        CREATE TABLE Branches (
                            BranchID INT IDENTITY(1,1) PRIMARY KEY,
                            BranchName NVARCHAR(100) NOT NULL,
                            BranchCode NVARCHAR(10) UNIQUE,
                            Address NVARCHAR(200),
                            Phone NVARCHAR(20),
                            Manager NVARCHAR(100),
                            IsActive BIT NOT NULL DEFAULT 1,
                            CreatedDate DATETIME NOT NULL DEFAULT GETDATE()
                        )
                        PRINT 'تم إنشاء جدول الفروع'
                    END

                    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Drugs' AND xtype='U')
                    BEGIN
                        CREATE TABLE Drugs (
                            DrugID INT IDENTITY(1,1) PRIMARY KEY,
                            DrugName NVARCHAR(200) NOT NULL,
                            ScientificName NVARCHAR(200),
                            Barcode NVARCHAR(50) UNIQUE,
                            Category NVARCHAR(100),
                            Unit NVARCHAR(20) NOT NULL DEFAULT 'قرص',
                            PurchasePrice DECIMAL(10,2) NOT NULL DEFAULT 0,
                            SalePrice DECIMAL(10,2) NOT NULL DEFAULT 0,
                            MinStock INT NOT NULL DEFAULT 10,
                            IsActive BIT NOT NULL DEFAULT 1,
                            CreatedDate DATETIME NOT NULL DEFAULT GETDATE()
                        )
                        PRINT 'تم إنشاء جدول الأدوية'
                    END

                    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Customers' AND xtype='U')
                    BEGIN
                        CREATE TABLE Customers (
                            CustomerID INT IDENTITY(1,1) PRIMARY KEY,
                            CustomerName NVARCHAR(100) NOT NULL,
                            Phone NVARCHAR(20),
                            Address NVARCHAR(200),
                            Email NVARCHAR(100),
                            IsActive BIT NOT NULL DEFAULT 1,
                            CreatedDate DATETIME NOT NULL DEFAULT GETDATE()
                        )
                        PRINT 'تم إنشاء جدول العملاء'
                    END

                    -- إدراج بيانات افتراضية
                    IF NOT EXISTS (SELECT * FROM Users WHERE Username = 'admin')
                    BEGIN
                        INSERT INTO Users (Username, Password, FullName, Role)
                        VALUES ('admin', '123456', 'مدير النظام', 'Admin')
                        PRINT 'تم إدراج المستخدم الافتراضي'
                    END

                    IF NOT EXISTS (SELECT * FROM Branches WHERE BranchName = 'الفرع الرئيسي')
                    BEGIN
                        INSERT INTO Branches (BranchName, BranchCode, Address, Phone, Manager)
                        VALUES ('الفرع الرئيسي', 'BR001', 'صنعاء - اليمن', '+967-1-123456', 'مدير النظام')
                        PRINT 'تم إدراج الفرع الافتراضي'
                    END

                    IF NOT EXISTS (SELECT * FROM Drugs WHERE DrugName = 'باراسيتامول 500 مجم')
                    BEGIN
                        INSERT INTO Drugs (DrugName, ScientificName, Category, Unit, PurchasePrice, SalePrice, MinStock) VALUES
                        ('باراسيتامول 500 مجم', 'Paracetamol', 'مسكنات', 'قرص', 0.50, 1.00, 100),
                        ('أسبرين 100 مجم', 'Aspirin', 'مسكنات', 'قرص', 0.30, 0.60, 50),
                        ('أموكسيسيلين 500 مجم', 'Amoxicillin', 'مضادات حيوية', 'كبسولة', 2.00, 4.00, 30)
                        PRINT 'تم إدراج الأدوية التجريبية'
                    END

                    PRINT '✅ تم إنشاء قاعدة البيانات الأساسية بنجاح!'
                ";

                var result = ExecuteDatabaseScript(basicScript);
                if (result)
                {
                    LogManager.LogInfo("تم إنشاء قاعدة البيانات الأساسية بنجاح");
                }
                return result;
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في إنشاء قاعدة البيانات الأساسية: " + ex.Message);
                return false;
            }
        }

        /// <summary>
        /// تنفيذ سكريبت قاعدة البيانات
        /// </summary>
        private static bool ExecuteDatabaseScript(string script)
        {
            try
            {
                LogManager.LogInfo("بدء تنفيذ سكريبت قاعدة البيانات...");

                using (var connection = GetConnection())
                {
                    connection.Open();
                    LogManager.LogInfo("تم الاتصال بقاعدة البيانات");

                    // تقسيم السكريبت إلى أوامر منفصلة
                    var commands = script.Split(new[] { "GO" }, StringSplitOptions.RemoveEmptyEntries);
                    LogManager.LogInfo("عدد الأوامر المراد تنفيذها: " + commands.Length.ToString());

                    int executedCommands = 0;
                    foreach (var commandText in commands)
                    {
                        if (string.IsNullOrWhiteSpace(commandText)) continue;

                        try
                        {
                            using (var command = new SqlCommand(commandText.Trim(), connection))
                            {
                                command.CommandTimeout = 120; // زيادة المهلة الزمنية
                                command.ExecuteNonQuery();
                                executedCommands++;
                            }
                        }
                        catch (Exception cmdEx)
                        {
                            LogManager.LogError("خطأ في تنفيذ الأمر رقم " + (executedCommands + 1).ToString() + ": " + cmdEx.Message);
                            // المتابعة مع الأوامر الأخرى
                        }
                    }

                    LogManager.LogInfo("تم تنفيذ " + executedCommands.ToString() + " أمر من أصل " + commands.Length.ToString());
                }

                LogManager.LogInfo("تم تنفيذ سكريبت قاعدة البيانات بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في تنفيذ سكريبت قاعدة البيانات: " + ex.Message);
                LogManager.LogError("تفاصيل الخطأ: " + ex.StackTrace);
                return false;
            }
        }

        /// <summary>
        /// فحص وجود جدول معين
        /// </summary>
        public static bool TableExists(string tableName)
        {
            try
            {
                using (var connection = GetConnection())
                {
                    connection.Open();
                    var query = "SELECT COUNT(*) FROM sysobjects WHERE name = @tableName AND xtype = 'U'";
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@tableName", tableName);
                        var result = Convert.ToInt32(command.ExecuteScalar());
                        return result > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في فحص وجود الجدول " + tableName + ": " + ex.Message);
                return false;
            }
        }

        /// <summary>
        /// الحصول على قائمة الجداول الموجودة
        /// </summary>
        public static List<string> GetExistingTables()
        {
            var tables = new List<string>();
            try
            {
                using (var connection = GetConnection())
                {
                    connection.Open();
                    var query = "SELECT name FROM sysobjects WHERE xtype = 'U' ORDER BY name";
                    using (var command = new SqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                tables.Add(reader["name"].ToString());
                            }
                        }
                    }
                }
                LogManager.LogInfo("تم العثور على " + tables.Count.ToString() + " جدول في قاعدة البيانات");
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في الحصول على قائمة الجداول: " + ex.Message);
            }
            return tables;
        }

        /// <summary>
        /// إنشاء نسخة احتياطية من قاعدة البيانات
        /// </summary>
        public static bool BackupDatabase(string backupPath = null)
        {
            try
            {
                if (string.IsNullOrEmpty(backupPath))
                {
                    var backupDir = Path.Combine(Application.StartupPath, "Backup");
                    if (!Directory.Exists(backupDir))
                        Directory.CreateDirectory(backupDir);

                    backupPath = Path.Combine(backupDir, "PharmacyDB_Backup_" + DateTime.Now.ToString("yyyyMMdd_HHmmss") + ".bak");
                }

                using (var connection = GetConnection())
                {
                    connection.Open();
                    var query = "BACKUP DATABASE PharmacyDB TO DISK = '" + backupPath + "'";
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.CommandTimeout = 300; // 5 دقائق
                        command.ExecuteNonQuery();
                    }
                }

                LogManager.LogInfo("تم إنشاء نسخة احتياطية: " + backupPath);
                return true;
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في إنشاء النسخة الاحتياطية: " + ex.Message);
                return false;
            }
        }

        /// <summary>
        /// اختبار الاتصال بقاعدة البيانات
        /// </summary>
        /// <returns>true إذا كان الاتصال ناجحاً</returns>
        public static bool TestConnection()
        {
            try
            {
                using (var connection = GetConnection())
                {
                    connection.Open();
                    return connection.State == ConnectionState.Open;
                }
            }
            catch (Exception ex)
            {
                LogError("TestConnection", ex);
                return false;
            }
        }



        #endregion

        #region Execute Methods - طرق التنفيذ

        /// <summary>
        /// تنفيذ استعلام وإرجاع عدد الصفوف المتأثرة
        /// </summary>
        /// <param name="query">الاستعلام</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>عدد الصفوف المتأثرة</returns>
        public static int ExecuteNonQuery(string query, params SqlParameter[] parameters)
        {
            try
            {
                using (var connection = GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        if (parameters != null)
                            command.Parameters.AddRange(parameters);
                        
                        return command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                LogError("ExecuteNonQuery: " + query, ex);
                throw;
            }
        }

        /// <summary>
        /// تنفيذ استعلام وإرجاع قيمة واحدة
        /// </summary>
        /// <param name="query">الاستعلام</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>القيمة المرجعة</returns>
        public static object ExecuteScalar(string query, params SqlParameter[] parameters)
        {
            try
            {
                using (var connection = GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        if (parameters != null)
                            command.Parameters.AddRange(parameters);
                        
                        return command.ExecuteScalar();
                    }
                }
            }
            catch (Exception ex)
            {
                LogError("ExecuteScalar: " + query, ex);
                throw;
            }
        }

        /// <summary>
        /// تنفيذ استعلام وإرجاع DataTable
        /// </summary>
        /// <param name="query">الاستعلام</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>جدول البيانات</returns>
        public static DataTable ExecuteQuery(string query, params SqlParameter[] parameters)
        {
            try
            {
                using (var connection = GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        if (parameters != null)
                            command.Parameters.AddRange(parameters);
                        
                        using (var adapter = new SqlDataAdapter(command))
                        {
                            var dataTable = new DataTable();
                            adapter.Fill(dataTable);
                            return dataTable;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogError("ExecuteQuery: " + query, ex);
                throw;
            }
        }

        /// <summary>
        /// تنفيذ استعلام وإرجاع SqlDataReader
        /// </summary>
        /// <param name="query">الاستعلام</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>قارئ البيانات</returns>
        public static SqlDataReader ExecuteReader(string query, params SqlParameter[] parameters)
        {
            try
            {
                var connection = GetConnection();
                connection.Open();
                var command = new SqlCommand(query, connection);
                
                if (parameters != null)
                    command.Parameters.AddRange(parameters);
                
                return command.ExecuteReader(CommandBehavior.CloseConnection);
            }
            catch (Exception ex)
            {
                LogError("ExecuteReader: " + query, ex);
                throw;
            }
        }

        #endregion

        #region Transaction Methods - طرق المعاملات

        /// <summary>
        /// تنفيذ مجموعة من الاستعلامات في معاملة واحدة
        /// </summary>
        /// <param name="queries">قائمة الاستعلامات</param>
        /// <param name="parameters">قائمة المعاملات المقابلة</param>
        /// <returns>true إذا تم التنفيذ بنجاح</returns>
        public static bool ExecuteTransaction(string[] queries, SqlParameter[][] parameters = null)
        {
            SqlConnection connection = null;
            SqlTransaction transaction = null;

            try
            {
                connection = GetConnection();
                connection.Open();
                transaction = connection.BeginTransaction();

                for (int i = 0; i < queries.Length; i++)
                {
                    using (var command = new SqlCommand(queries[i], connection, transaction))
                    {
                        if (parameters != null && i < parameters.Length && parameters[i] != null)
                            command.Parameters.AddRange(parameters[i]);

                        command.ExecuteNonQuery();
                    }
                }

                transaction.Commit();
                return true;
            }
            catch (Exception ex)
            {
                if (transaction != null)
                    transaction.Rollback();
                LogError("ExecuteTransaction", ex);
                return false;
            }
            finally
            {
                if (transaction != null)
                    transaction.Dispose();
                if (connection != null)
                {
                    connection.Close();
                    connection.Dispose();
                }
            }
        }

        #endregion

        #region Helper Methods - الطرق المساعدة

        /// <summary>
        /// إنشاء معامل SQL
        /// </summary>
        /// <param name="name">اسم المعامل</param>
        /// <param name="value">قيمة المعامل</param>
        /// <returns>معامل SQL</returns>
        public static SqlParameter CreateParameter(string name, object value)
        {
            return new SqlParameter(name, value ?? DBNull.Value);
        }

        /// <summary>
        /// تسجيل الأخطاء
        /// </summary>
        /// <param name="method">اسم الطريقة</param>
        /// <param name="ex">الاستثناء</param>
        private static void LogError(string method, Exception ex)
        {
            try
            {
                string errorMessage = "خطأ في " + method + ": " + ex.Message;
                
                // يمكن إضافة تسجيل الأخطاء في ملف أو قاعدة بيانات هنا
                System.Diagnostics.Debug.WriteLine(errorMessage);
                
                // عرض رسالة للمستخدم في حالة الأخطاء الحرجة
                var sqlEx = ex as SqlException;
                if (sqlEx != null && sqlEx.Number == 2) // خطأ الاتصال
                {
                    MessageBox.Show("لا يمكن الاتصال بقاعدة البيانات. تأكد من تشغيل SQL Server.",
                                  "خطأ في الاتصال", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch
            {
                // تجاهل أخطاء التسجيل
            }
        }



        /// <summary>
        /// الحصول على معرف جديد للجدول
        /// </summary>
        /// <param name="tableName">اسم الجدول</param>
        /// <param name="idColumnName">اسم عمود المعرف</param>
        /// <returns>المعرف الجديد</returns>
        public static int GetNextId(string tableName, string idColumnName)
        {
            try
            {
                string query = "SELECT ISNULL(MAX(" + idColumnName + "), 0) + 1 FROM " + tableName;
                var result = ExecuteScalar(query);
                return Convert.ToInt32(result);
            }
            catch
            {
                return 1;
            }
        }

        /// <summary>
        /// تحسين قاعدة البيانات
        /// </summary>
        /// <returns>true إذا تم التحسين بنجاح</returns>
        public static bool OptimizeDatabase()
        {
            try
            {
                using (var connection = GetConnection())
                {
                    connection.Open();

                    // تحديث الإحصائيات
                    string updateStatsQuery = "UPDATE STATISTICS";
                    using (var command = new SqlCommand(updateStatsQuery, connection))
                    {
                        command.ExecuteNonQuery();
                    }

                    // إعادة فهرسة الجداول
                    string reindexQuery = "DBCC REINDEX";
                    using (var command = new SqlCommand(reindexQuery, connection))
                    {
                        command.ExecuteNonQuery();
                    }

                    LogManager.LogInfo("تم تحسين قاعدة البيانات بنجاح");
                    return true;
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في تحسين قاعدة البيانات: " + ex.Message);
                return false;
            }
        }

        #endregion
    }
}
