-- ===================================================================
-- ملف استعلامات الاختبار - Test Queries
-- تاريخ الإنشاء: 2025-01-28
-- الغرض: اختبار والتحقق من صحة تحديثات قاعدة البيانات
-- ===================================================================

USE PharmacyDB
GO

PRINT '🧪 بدء تشغيل استعلامات الاختبار...'
PRINT '📅 التاريخ: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT ''

-- ===================================================================
-- 1. اختبار الجداول الأساسية
-- ===================================================================

PRINT '🔍 اختبار الجداول الأساسية:'

-- اختبار جدول الأدوية
SELECT 'Drugs' as TableName, COUNT(*) as RecordCount, 
       SUM(CASE WHEN IsActive = 1 THEN 1 ELSE 0 END) as ActiveRecords
FROM Drugs

-- اختبار جدول فئات الأدوية
SELECT 'DrugCategories' as TableName, COUNT(*) as RecordCount,
       SUM(CASE WHEN IsActive = 1 THEN 1 ELSE 0 END) as ActiveRecords
FROM DrugCategories

-- اختبار جدول المخزون
SELECT 'Inventory' as TableName, COUNT(*) as RecordCount,
       SUM(CASE WHEN Quantity > 0 THEN 1 ELSE 0 END) as NonZeroQuantity
FROM Inventory

-- اختبار جدول حركات المخزون
SELECT 'StockMovements' as TableName, COUNT(*) as RecordCount,
       COUNT(DISTINCT DrugID) as UniqueDrugs
FROM StockMovements

PRINT ''

-- ===================================================================
-- 2. اختبار Views المنشأة
-- ===================================================================

PRINT '👁️ اختبار Views:'

-- اختبار View المخزون الحالي
SELECT 'vw_CurrentStock' as ViewName, COUNT(*) as RecordCount
FROM vw_CurrentStock

-- اختبار View الأدوية منخفضة المخزون
SELECT 'vw_LowStockDrugs' as ViewName, COUNT(*) as RecordCount
FROM vw_LowStockDrugs

-- اختبار View الأدوية منتهية الصلاحية
SELECT 'vw_ExpiredDrugs' as ViewName, COUNT(*) as RecordCount
FROM vw_ExpiredDrugs

PRINT ''

-- ===================================================================
-- 3. اختبار الاستعلامات الأساسية للمخزون
-- ===================================================================

PRINT '📦 اختبار استعلامات المخزون:'

-- استعلام المخزون الحالي (مثل الذي يستخدمه التطبيق)
SELECT TOP 5
    d.DrugID,
    d.DrugCode,
    d.DrugName as TradeName,
    c.CategoryName,
    m.ManufacturerName,
    d.PurchasePrice,
    d.SalePrice,
    ISNULL(i.TotalStock, 0) as CurrentStock,
    d.MinStock as MinStockLevel,
    d.MaxStock as MaxStockLevel,
    i.NearestExpiryDate as ExpiryDate,
    d.Unit,
    d.IsActive
FROM Drugs d
LEFT JOIN DrugCategories c ON d.CategoryID = c.CategoryID
LEFT JOIN Manufacturers m ON d.ManufacturerID = m.ManufacturerID
LEFT JOIN (
    SELECT 
        DrugID, 
        SUM(Quantity) as TotalStock,
        MIN(ExpiryDate) as NearestExpiryDate
    FROM Inventory 
    WHERE Quantity > 0
    GROUP BY DrugID
) i ON d.DrugID = i.DrugID
WHERE d.IsActive = 1
ORDER BY d.DrugName

PRINT ''

-- ===================================================================
-- 4. اختبار إحصائيات المخزون
-- ===================================================================

PRINT '📊 إحصائيات المخزون:'

-- إحصائيات عامة
SELECT 
    'إجمالي الأدوية' as Description,
    COUNT(*) as Count
FROM Drugs 
WHERE IsActive = 1

UNION ALL

SELECT 
    'أدوية لها مخزون' as Description,
    COUNT(DISTINCT DrugID) as Count
FROM Inventory 
WHERE Quantity > 0

UNION ALL

SELECT 
    'أدوية نفد مخزونها' as Description,
    COUNT(*) as Count
FROM vw_CurrentStock 
WHERE CurrentStock = 0

UNION ALL

SELECT 
    'أدوية مخزون منخفض' as Description,
    COUNT(*) as Count
FROM vw_LowStockDrugs

UNION ALL

SELECT 
    'أدوية منتهية الصلاحية' as Description,
    COUNT(DISTINCT DrugID) as Count
FROM vw_ExpiredDrugs

PRINT ''

-- ===================================================================
-- 5. اختبار الفئات والربط
-- ===================================================================

PRINT '🏷️ اختبار الفئات والربط:'

-- التحقق من ربط الأدوية بالفئات
SELECT 
    CASE 
        WHEN c.CategoryName IS NULL THEN 'بدون فئة'
        ELSE c.CategoryName 
    END as CategoryName,
    COUNT(d.DrugID) as DrugCount
FROM Drugs d
LEFT JOIN DrugCategories c ON d.CategoryID = c.CategoryID
WHERE d.IsActive = 1
GROUP BY c.CategoryName
ORDER BY DrugCount DESC

PRINT ''

-- ===================================================================
-- 6. اختبار Stored Procedure
-- ===================================================================

PRINT '⚙️ اختبار Stored Procedure:'

-- اختبار إضافة مخزون (محاكاة)
DECLARE @TestDrugID INT

-- الحصول على أول دواء للاختبار
SELECT TOP 1 @TestDrugID = DrugID FROM Drugs WHERE IsActive = 1

IF @TestDrugID IS NOT NULL
BEGIN
    PRINT 'اختبار إضافة 10 وحدات للدواء رقم: ' + CAST(@TestDrugID AS VARCHAR)
    
    -- عرض المخزون قبل الإضافة
    SELECT 
        'قبل الإضافة' as Status,
        ISNULL(SUM(Quantity), 0) as CurrentStock
    FROM Inventory 
    WHERE DrugID = @TestDrugID AND Quantity > 0
    
    -- تنفيذ الإضافة (تعليق لتجنب التغيير الفعلي)
    -- EXEC sp_UpdateStock @TestDrugID, 10, 1, 'اختبار إضافة مخزون', 1
    
    PRINT '✅ تم اختبار Stored Procedure (بدون تنفيذ فعلي)'
END
ELSE
    PRINT '❌ لا توجد أدوية للاختبار'

PRINT ''

-- ===================================================================
-- 7. اختبار الفهارس
-- ===================================================================

PRINT '🚀 اختبار الفهارس:'

-- عرض الفهارس المنشأة
SELECT 
    t.name as TableName,
    i.name as IndexName,
    i.type_desc as IndexType
FROM sys.indexes i
INNER JOIN sys.tables t ON i.object_id = t.object_id
WHERE t.name IN ('Drugs', 'Inventory', 'StockMovements', 'DrugCategories')
  AND i.name IS NOT NULL
ORDER BY t.name, i.name

PRINT ''

-- ===================================================================
-- 8. اختبار الأداء
-- ===================================================================

PRINT '⚡ اختبار الأداء:'

-- قياس وقت تنفيذ استعلام المخزون
DECLARE @StartTime DATETIME = GETDATE()

SELECT COUNT(*) as TotalRecords
FROM vw_CurrentStock

DECLARE @EndTime DATETIME = GETDATE()
DECLARE @Duration INT = DATEDIFF(MILLISECOND, @StartTime, @EndTime)

PRINT 'وقت تنفيذ استعلام المخزون: ' + CAST(@Duration AS VARCHAR) + ' ميلي ثانية'

PRINT ''

-- ===================================================================
-- 9. اختبار سلامة البيانات
-- ===================================================================

PRINT '🛡️ اختبار سلامة البيانات:'

-- التحقق من وجود أدوية بدون فئة
SELECT 
    'أدوية بدون فئة' as Issue,
    COUNT(*) as Count
FROM Drugs 
WHERE CategoryID IS NULL AND IsActive = 1

UNION ALL

-- التحقق من وجود مخزون بكميات سالبة
SELECT 
    'مخزون بكميات سالبة' as Issue,
    COUNT(*) as Count
FROM Inventory 
WHERE Quantity < 0

UNION ALL

-- التحقق من وجود أدوية بدون مخزون
SELECT 
    'أدوية بدون مخزون' as Issue,
    COUNT(*) as Count
FROM Drugs d
LEFT JOIN Inventory i ON d.DrugID = i.DrugID
WHERE d.IsActive = 1 AND i.DrugID IS NULL

PRINT ''

-- ===================================================================
-- 10. تقرير نهائي
-- ===================================================================

PRINT '📋 التقرير النهائي:'

-- ملخص حالة قاعدة البيانات
SELECT 
    'حالة قاعدة البيانات' as Status,
    CASE 
        WHEN EXISTS (SELECT 1 FROM Drugs WHERE IsActive = 1) 
         AND EXISTS (SELECT 1 FROM DrugCategories WHERE IsActive = 1)
         AND EXISTS (SELECT 1 FROM Inventory WHERE Quantity > 0)
        THEN '✅ جاهزة للاستخدام'
        ELSE '❌ تحتاج إلى مراجعة'
    END as Result

PRINT ''
PRINT '✅ تم تشغيل جميع استعلامات الاختبار بنجاح!'
PRINT '📅 تاريخ الانتهاء: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT ''
PRINT '📝 ملاحظات:'
PRINT '   - تحقق من النتائج أعلاه للتأكد من سلامة البيانات'
PRINT '   - إذا ظهرت أي مشاكل، راجع ملف DatabaseUpdates.sql'
PRINT '   - يمكن تشغيل هذا الملف دورياً للمراقبة'
PRINT ''

-- ===================================================================
-- 11. استعلامات مفيدة للصيانة
-- ===================================================================

PRINT '🔧 استعلامات الصيانة المفيدة:'

-- حجم قاعدة البيانات
SELECT 
    DB_NAME() as DatabaseName,
    SUM(size * 8.0 / 1024) as SizeMB
FROM sys.database_files
WHERE type = 0

-- آخر تحديث للجداول
SELECT 
    t.name as TableName,
    p.rows as RowCount,
    CAST(ROUND(((SUM(a.total_pages) * 8) / 1024.00), 2) AS NUMERIC(36, 2)) as SizeMB
FROM sys.tables t
INNER JOIN sys.indexes i ON t.OBJECT_ID = i.object_id
INNER JOIN sys.partitions p ON i.object_id = p.OBJECT_ID AND i.index_id = p.index_id
INNER JOIN sys.allocation_units a ON p.partition_id = a.container_id
WHERE t.name IN ('Drugs', 'DrugCategories', 'Inventory', 'StockMovements')
  AND i.OBJECT_ID > 255
GROUP BY t.name, p.rows
ORDER BY SizeMB DESC

PRINT ''
PRINT '🎯 انتهى ملف استعلامات الاختبار'
