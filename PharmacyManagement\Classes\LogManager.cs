using System;
using System.IO;
using System.Windows.Forms;

namespace PharmacyManagement.Classes
{
    /// <summary>
    /// مدير السجلات - Log Manager
    /// </summary>
    public static class LogManager
    {
        private static readonly string LogDirectory = Path.Combine(Application.StartupPath, "Logs");
        private static readonly string LogFileName = Path.Combine(LogDirectory, "PharmacyLog_" + DateTime.Now.ToString("yyyyMM") + ".txt");

        static LogManager()
        {
            try
            {
                if (!Directory.Exists(LogDirectory))
                {
                    Directory.CreateDirectory(LogDirectory);
                }
            }
            catch (Exception ex)
            {
                // في حالة فشل إنشاء مجلد السجلات، نستخدم مجلد التطبيق
                System.Diagnostics.Debug.WriteLine("خطأ في إنشاء مجلد السجلات: " + ex.Message);
            }
        }

        /// <summary>
        /// تهيئة نظام السجلات
        /// </summary>
        public static void Initialize()
        {
            try
            {
                // إنشاء مجلد السجلات إذا لم يكن موجوداً
                if (!Directory.Exists(LogDirectory))
                {
                    Directory.CreateDirectory(LogDirectory);
                }

                // كتابة رسالة بداية التشغيل
                LogInfo("تم تهيئة نظام السجلات بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("خطأ في تهيئة نظام السجلات: " + ex.Message);
            }
        }

        /// <summary>
        /// تسجيل رسالة معلومات
        /// </summary>
        /// <param name="message">الرسالة</param>
        public static void LogInfo(string message)
        {
            WriteLog("INFO", message);
        }

        /// <summary>
        /// تسجيل رسالة خطأ
        /// </summary>
        /// <param name="message">الرسالة</param>
        public static void LogError(string message)
        {
            WriteLog("ERROR", message);
        }

        /// <summary>
        /// تسجيل رسالة تحذير
        /// </summary>
        /// <param name="message">الرسالة</param>
        public static void LogWarning(string level,string message)
        {
            WriteLog("WARNING", message);
        }

        /// <summary>
        /// كتابة السجل
        /// </summary>
        /// <param name="level">مستوى السجل</param>
        /// <param name="message">الرسالة</param>
        private static void WriteLog(string level, string message)
        {
            try
            {
                string logEntry = "[" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + "] [" + level + "] " + message;
                File.AppendAllText(LogFileName, logEntry + Environment.NewLine);
            }
            catch (Exception ex)
            {
                // في حالة فشل الكتابة، نكتب في Debug
                System.Diagnostics.Debug.WriteLine("خطأ في كتابة السجل: " + ex.Message);
                System.Diagnostics.Debug.WriteLine("السجل الأصلي: [" + level + "] " + message);
            }
        }

       

        /// <summary>
        /// تسجيل خطأ مع استثناء
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        /// <param name="ex">الاستثناء</param>
        public static void LogError(string message, Exception ex)
        {
            WriteLog("ERROR", message + " - " + ex.Message);
        }

      
        /// <summary>
        /// تسجيل نشاط
        /// </summary>
        /// <param name="message">رسالة النشاط</param>
        public static void LogActivity(string statem,string message)
        {
            WriteLog(statem, message);
        }
    }
}
