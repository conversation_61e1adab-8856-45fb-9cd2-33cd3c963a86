using System;

namespace PharmacyManagement.Models
{
    /// <summary>
    /// نموذج ملخص العملاء
    /// </summary>
    public class CustomerSummary
    {
        /// <summary>
        /// إجمالي العملاء
        /// </summary>
        public int TotalCustomers { get; set; }

        /// <summary>
        /// العملاء النشطون
        /// </summary>
        public int ActiveCustomers { get; set; }

        /// <summary>
        /// العملاء غير النشطين
        /// </summary>
        public int InactiveCustomers { get; set; }

        /// <summary>
        /// العملاء الجدد
        /// </summary>
        public int NewCustomers { get; set; }

        /// <summary>
        /// إجمالي الرصيد
        /// </summary>
        public decimal TotalBalance { get; set; }

        /// <summary>
        /// إجمالي المبيعات
        /// </summary>
        public decimal TotalSales { get; set; }

        /// <summary>
        /// متوسط المبيعات لكل عميل
        /// </summary>
        public decimal AverageSalesPerCustomer { get; set; }

        /// <summary>
        /// متوسط حد الائتمان
        /// </summary>
        public decimal AverageCreditLimit { get; set; }

        /// <summary>
        /// أعلى رصيد
        /// </summary>
        public decimal HighestBalance { get; set; }

        /// <summary>
        /// أقل رصيد
        /// </summary>
        public decimal LowestBalance { get; set; }

        /// <summary>
        /// عدد العملاء المدينين
        /// </summary>
        public int DebtorCustomers { get; set; }

        /// <summary>
        /// عدد العملاء الدائنين
        /// </summary>
        public int CreditorCustomers { get; set; }

        /// <summary>
        /// تاريخ التقرير
        /// </summary>
        public DateTime ReportDate { get; set; }

        /// <summary>
        /// من تاريخ
        /// </summary>
        public DateTime FromDate { get; set; }

        /// <summary>
        /// إلى تاريخ
        /// </summary>
        public DateTime ToDate { get; set; }
    }
}


