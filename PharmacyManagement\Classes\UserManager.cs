using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Security.Cryptography;
using System.Text;
using PharmacyManagement.Models;

namespace PharmacyManagement.Classes
{
    /// <summary>
    /// مدير المستخدمين - User Manager
    /// يوفر طرق إدارة المستخدمين والمصادقة
    /// </summary>
    public static class UserManager
    {
        #region Properties - الخصائص

        /// <summary>
        /// المستخدم الحالي المسجل دخوله
        /// </summary>
        public static User CurrentUser { get; private set; }

        /// <summary>
        /// هل يوجد مستخدم مسجل دخوله
        /// </summary>
        public static bool IsLoggedIn => CurrentUser != null;

        #endregion

        #region Authentication Methods - طرق المصادقة

        /// <summary>
        /// تسجيل دخول المستخدم
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <param name="password">كلمة المرور</param>
        /// <returns>true إذا تم تسجيل الدخول بنجاح</returns>
        public static bool Login(string username, string password)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
                    return false;

                string hashedPassword = HashPassword(password);
                
                string query = @"
                    SELECT u.UserID, u.Username, u.FullName, u.Email, u.Phone, u.Role, u.IsActive, u.CreatedDate, u.LastLoginDate,
                           MAX(CASE WHEN up.ModuleName = 'Users' AND up.CanAddUsers = 1 THEN 1 ELSE 0 END) as CanAddUsers,
                           MAX(CASE WHEN up.ModuleName = 'Users' AND up.CanEditUsers = 1 THEN 1 ELSE 0 END) as CanEditUsers,
                           MAX(CASE WHEN up.ModuleName = 'Users' AND up.CanDeleteUsers = 1 THEN 1 ELSE 0 END) as CanDeleteUsers,
                           MAX(CASE WHEN up.ModuleName = 'Reports' AND up.CanViewReports = 1 THEN 1 ELSE 0 END) as CanViewReports,
                           MAX(CASE WHEN up.CanPrint = 1 THEN 1 ELSE 0 END) as CanPrint
                    FROM Users u
                    LEFT JOIN UserPermissions up ON u.UserID = up.UserID
                    WHERE u.Username = @Username AND u.Password = @Password AND u.IsActive = 1
                    GROUP BY u.UserID, u.Username, u.FullName, u.Email, u.Phone, u.Role, u.IsActive, u.CreatedDate, u.LastLoginDate";

                var parameters = new[]
                {
                    DatabaseHelper.CreateParameter("@Username", username),
                    DatabaseHelper.CreateParameter("@Password", hashedPassword)
                };

                using (var reader = DatabaseHelper.ExecuteReader(query, parameters))
                {
                    if (reader.Read())
                    {
                        CurrentUser = new User
                        {
                            UserID = Convert.ToInt32(reader["UserID"]),
                            Username = reader["Username"].ToString(),
                            FullName = reader["FullName"].ToString(),
                            Email = reader["Email"] == DBNull.Value ? null : reader["Email"].ToString(),
                            Phone = reader["Phone"] == DBNull.Value ? null : reader["Phone"].ToString(),
                            Role = reader["Role"].ToString(),
                            IsActive = Convert.ToBoolean(reader["IsActive"]),
                            CanAddUsers = Convert.ToBoolean(reader["CanAddUsers"]),
                            CanEditUsers = Convert.ToBoolean(reader["CanEditUsers"]),
                            CanDeleteUsers = Convert.ToBoolean(reader["CanDeleteUsers"]),
                            CanViewReports = Convert.ToBoolean(reader["CanViewReports"]),
                            CanPrint = Convert.ToBoolean(reader["CanPrint"]),
                            CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                            LastLoginDate = reader["LastLoginDate"] == DBNull.Value ? null : (DateTime?)Convert.ToDateTime(reader["LastLoginDate"])
                        };

                        // تحديث آخر تسجيل دخول
                        UpdateLastLogin(CurrentUser.UserID);

                        // تسجيل النشاط
                        LogActivity(CurrentUser.UserID, "تسجيل دخول", $"تم تسجيل دخول المستخدم {username}");

                        return true;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تسجيل الدخول: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تسجيل خروج المستخدم
        /// </summary>
        public static void Logout()
        {
            if (CurrentUser != null)
            {
                LogActivity(CurrentUser.UserID, "تسجيل خروج", $"تم تسجيل خروج المستخدم {CurrentUser.Username}");
                CurrentUser = null;
            }
        }

        /// <summary>
        /// تشفير كلمة المرور
        /// </summary>
        /// <param name="password">كلمة المرور</param>
        /// <returns>كلمة المرور المشفرة</returns>
        public static string HashPassword(string password)
        {
            if (string.IsNullOrEmpty(password))
                return string.Empty;

            using (var sha256 = SHA256.Create())
            {
                byte[] hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password + "PharmacySalt"));
                return Convert.ToBase64String(hashedBytes);
            }
        }

        /// <summary>
        /// تعيين المستخدم الحالي (للاختبار والتطوير)
        /// </summary>
        /// <param name="user">المستخدم المراد تعيينه</param>
        public static void SetCurrentUser(User user)
        {
            CurrentUser = user;
            if (user != null)
            {
                LogActivity(user.UserID, "تعيين مستخدم", $"تم تعيين المستخدم {user.Username} كمستخدم حالي");
            }
        }

        #endregion

        #region User Management Methods - طرق إدارة المستخدمين

        /// <summary>
        /// إضافة مستخدم جديد
        /// </summary>
        /// <param name="user">بيانات المستخدم</param>
        /// <returns>معرف المستخدم الجديد أو -1 في حالة الفشل</returns>
        public static int AddUser(User user)
        {
            try
            {
                if (!user.IsValid())
                    return -1;

                // التحقق من عدم وجود اسم المستخدم مسبقاً
                if (UsernameExists(user.Username))
                    return -1;

                string query = @"
                    INSERT INTO Users (Username, Password, FullName, Email, Phone, Role, IsActive, CreatedDate, CreatedBy)
                    VALUES (@Username, @Password, @FullName, @Email, @Phone, @Role, @IsActive, @CreatedDate, @CreatedBy);
                    SELECT SCOPE_IDENTITY();";

                var parameters = new[]
                {
                    DatabaseHelper.CreateParameter("@Username", user.Username),
                    DatabaseHelper.CreateParameter("@Password", HashPassword(user.Password)),
                    DatabaseHelper.CreateParameter("@FullName", user.FullName),
                    DatabaseHelper.CreateParameter("@Email", user.Email),
                    DatabaseHelper.CreateParameter("@Phone", user.Phone),
                    DatabaseHelper.CreateParameter("@Role", user.Role),
                    DatabaseHelper.CreateParameter("@IsActive", user.IsActive),
                    DatabaseHelper.CreateParameter("@CreatedDate", DateTime.Now),
                    DatabaseHelper.CreateParameter("@CreatedBy", CurrentUser?.UserID)
                };

                var result = DatabaseHelper.ExecuteScalar(query, parameters);
                int userId = Convert.ToInt32(result);

                if (userId > 0)
                {
                    LogActivity(CurrentUser?.UserID ?? 0, "إضافة مستخدم", $"تم إضافة المستخدم {user.Username}");
                }

                return userId;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إضافة المستخدم: {ex.Message}");
                return -1;
            }
        }

        /// <summary>
        /// تحديث بيانات المستخدم
        /// </summary>
        /// <param name="user">بيانات المستخدم</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public static bool UpdateUser(User user)
        {
            try
            {
                if (!user.IsValid() || user.UserID <= 0)
                    return false;

                string query = @"
                    UPDATE Users 
                    SET FullName = @FullName, Email = @Email, Phone = @Phone, 
                        Role = @Role, IsActive = @IsActive
                    WHERE UserID = @UserID";

                var parameters = new[]
                {
                    DatabaseHelper.CreateParameter("@UserID", user.UserID),
                    DatabaseHelper.CreateParameter("@FullName", user.FullName),
                    DatabaseHelper.CreateParameter("@Email", user.Email),
                    DatabaseHelper.CreateParameter("@Phone", user.Phone),
                    DatabaseHelper.CreateParameter("@Role", user.Role),
                    DatabaseHelper.CreateParameter("@IsActive", user.IsActive)
                };

                int rowsAffected = DatabaseHelper.ExecuteNonQuery(query, parameters);
                
                if (rowsAffected > 0)
                {
                    LogActivity(CurrentUser?.UserID ?? 0, "تحديث مستخدم", $"تم تحديث بيانات المستخدم {user.Username}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث المستخدم: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تغيير كلمة مرور المستخدم
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="newPassword">كلمة المرور الجديدة</param>
        /// <returns>true إذا تم التغيير بنجاح</returns>
        public static bool ChangePassword(int userId, string newPassword)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(newPassword) || userId <= 0)
                    return false;

                string query = "UPDATE Users SET Password = @Password WHERE UserID = @UserID";
                
                var parameters = new[]
                {
                    DatabaseHelper.CreateParameter("@UserID", userId),
                    DatabaseHelper.CreateParameter("@Password", HashPassword(newPassword))
                };

                int rowsAffected = DatabaseHelper.ExecuteNonQuery(query, parameters);
                
                if (rowsAffected > 0)
                {
                    LogActivity(CurrentUser?.UserID ?? 0, "تغيير كلمة المرور", $"تم تغيير كلمة مرور المستخدم ID: {userId}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تغيير كلمة المرور: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// حذف المستخدم (إلغاء تفعيل)
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public static bool DeleteUser(int userId)
        {
            try
            {
                if (userId <= 0 || userId == CurrentUser?.UserID)
                    return false;

                string query = "UPDATE Users SET IsActive = 0 WHERE UserID = @UserID";
                var parameters = new[] { DatabaseHelper.CreateParameter("@UserID", userId) };

                int rowsAffected = DatabaseHelper.ExecuteNonQuery(query, parameters);
                
                if (rowsAffected > 0)
                {
                    LogActivity(CurrentUser?.UserID ?? 0, "حذف مستخدم", $"تم حذف المستخدم ID: {userId}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حذف المستخدم: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// الحصول على جميع المستخدمين
        /// </summary>
        /// <returns>قائمة المستخدمين</returns>
        public static List<User> GetAllUsers()
        {
            var users = new List<User>();

            try
            {
                string query = @"
                    SELECT u.UserID, u.Username, u.FullName, u.Email, u.Phone, u.Role, u.IsActive,
                           u.CreatedDate, u.ModifiedDate, u.LastLoginDate,
                           MAX(CASE WHEN up.ModuleName = 'Users' AND up.CanAddUsers = 1 THEN 1 ELSE 0 END) as CanAddUsers,
                           MAX(CASE WHEN up.ModuleName = 'Users' AND up.CanEditUsers = 1 THEN 1 ELSE 0 END) as CanEditUsers,
                           MAX(CASE WHEN up.ModuleName = 'Users' AND up.CanDeleteUsers = 1 THEN 1 ELSE 0 END) as CanDeleteUsers,
                           MAX(CASE WHEN up.ModuleName = 'Reports' AND up.CanViewReports = 1 THEN 1 ELSE 0 END) as CanViewReports,
                           MAX(CASE WHEN up.CanPrint = 1 THEN 1 ELSE 0 END) as CanPrint
                    FROM Users u
                    LEFT JOIN UserPermissions up ON u.UserID = up.UserID
                    GROUP BY u.UserID, u.Username, u.FullName, u.Email, u.Phone, u.Role, u.IsActive,
                             u.CreatedDate, u.ModifiedDate, u.LastLoginDate
                    ORDER BY u.FullName";

                using (var reader = DatabaseHelper.ExecuteReader(query))
                {
                    while (reader.Read())
                    {
                        users.Add(new User
                        {
                            UserID = Convert.ToInt32(reader["UserID"]),
                            Username = reader["Username"].ToString(),
                            FullName = reader["FullName"].ToString(),
                            Email = reader["Email"] != DBNull.Value ? reader["Email"].ToString() : null,
                            Phone = reader["Phone"] != DBNull.Value ? reader["Phone"].ToString() : null,
                            Role = reader["Role"].ToString(),
                            IsActive = Convert.ToBoolean(reader["IsActive"]),
                            CanAddUsers = Convert.ToBoolean(reader["CanAddUsers"]),
                            CanEditUsers = Convert.ToBoolean(reader["CanEditUsers"]),
                            CanDeleteUsers = Convert.ToBoolean(reader["CanDeleteUsers"]),
                            CanViewReports = Convert.ToBoolean(reader["CanViewReports"]),
                            CanPrint = Convert.ToBoolean(reader["CanPrint"]),
                            CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                            ModifiedDate = reader["ModifiedDate"] != DBNull.Value ? Convert.ToDateTime(reader["ModifiedDate"]) : (DateTime?)null,
                            LastLoginDate = reader["LastLoginDate"] != DBNull.Value ? Convert.ToDateTime(reader["LastLoginDate"]) : (DateTime?)null
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في جلب المستخدمين: {ex.Message}");
            }

            return users;
        }

        #endregion

        #region Helper Methods - الطرق المساعدة

        /// <summary>
        /// التحقق من وجود اسم المستخدم
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <returns>true إذا كان موجوداً</returns>
        private static bool UsernameExists(string username)
        {
            try
            {
                string query = "SELECT COUNT(*) FROM Users WHERE Username = @Username";
                var parameters = new[] { DatabaseHelper.CreateParameter("@Username", username) };
                
                var result = DatabaseHelper.ExecuteScalar(query, parameters);
                return Convert.ToInt32(result) > 0;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// تحديث آخر تسجيل دخول
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        private static void UpdateLastLogin(int userId)
        {
            try
            {
                string query = "UPDATE Users SET LastLoginDate = @LastLoginDate WHERE UserID = @UserID";
                var parameters = new[]
                {
                    DatabaseHelper.CreateParameter("@UserID", userId),
                    DatabaseHelper.CreateParameter("@LastLoginDate", DateTime.Now)
                };

                DatabaseHelper.ExecuteNonQuery(query, parameters);
            }
            catch
            {
                // تجاهل الأخطاء في تحديث آخر تسجيل دخول
            }
        }

        /// <summary>
        /// تسجيل نشاط المستخدم
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="activity">النشاط</param>
        /// <param name="description">الوصف</param>
        private static void LogActivity(int userId, string activity, string description)
        {
            try
            {
                string query = @"
                    INSERT INTO ActivityLog (UserID, Activity, Description, LogDate)
                    VALUES (@UserID, @Activity, @Description, @LogDate)";

                var parameters = new[]
                {
                    DatabaseHelper.CreateParameter("@UserID", userId),
                    DatabaseHelper.CreateParameter("@Activity", activity),
                    DatabaseHelper.CreateParameter("@Description", description),
                    DatabaseHelper.CreateParameter("@LogDate", DateTime.Now)
                };

                DatabaseHelper.ExecuteNonQuery(query, parameters);
            }
            catch
            {
                // تجاهل أخطاء تسجيل النشاط
            }
        }

        /// <summary>
        /// التحقق من صلاحيات المستخدم
        /// </summary>
        /// <param name="requiredRole">الدور المطلوب</param>
        /// <returns>true إذا كان المستخدم يملك الصلاحية</returns>
        public static bool HasPermission(string requiredRole)
        {
            return CurrentUser?.HasPermission(requiredRole) ?? false;
        }

        /// <summary>
        /// البحث عن المستخدمين
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <returns>قائمة المستخدمين المطابقة</returns>
        public static List<User> SearchUsers(string searchTerm)
        {
            var users = new List<User>();
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT u.UserID, u.Username, u.FullName, u.Email, u.Phone, u.Role, u.IsActive,
                               u.CreatedDate, u.ModifiedDate, u.LastLoginDate,
                               MAX(CASE WHEN up.ModuleName = 'Users' AND up.CanAddUsers = 1 THEN 1 ELSE 0 END) as CanAddUsers,
                               MAX(CASE WHEN up.ModuleName = 'Users' AND up.CanEditUsers = 1 THEN 1 ELSE 0 END) as CanEditUsers,
                               MAX(CASE WHEN up.ModuleName = 'Users' AND up.CanDeleteUsers = 1 THEN 1 ELSE 0 END) as CanDeleteUsers,
                               MAX(CASE WHEN up.ModuleName = 'Reports' AND up.CanViewReports = 1 THEN 1 ELSE 0 END) as CanViewReports,
                               MAX(CASE WHEN up.CanPrint = 1 THEN 1 ELSE 0 END) as CanPrint
                        FROM Users u
                        LEFT JOIN UserPermissions up ON u.UserID = up.UserID
                        WHERE u.Username LIKE @SearchTerm
                           OR u.FullName LIKE @SearchTerm
                           OR u.Email LIKE @SearchTerm
                           OR u.Phone LIKE @SearchTerm
                        GROUP BY u.UserID, u.Username, u.FullName, u.Email, u.Phone, u.Role, u.IsActive,
                                 u.CreatedDate, u.ModifiedDate, u.LastLoginDate
                        ORDER BY u.Username";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@SearchTerm", $"%{searchTerm}%");
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                users.Add(new User
                                {
                                    UserID = Convert.ToInt32(reader["UserID"]),
                                    Username = reader["Username"].ToString(),
                                    FullName = reader["FullName"].ToString(),
                                    Email = reader["Email"] != DBNull.Value ? reader["Email"].ToString() : null,
                                    Phone = reader["Phone"] != DBNull.Value ? reader["Phone"].ToString() : null,
                                    Role = reader["Role"].ToString(),
                                    IsActive = Convert.ToBoolean(reader["IsActive"]),
                                    CanAddUsers = Convert.ToBoolean(reader["CanAddUsers"]),
                                    CanEditUsers = Convert.ToBoolean(reader["CanEditUsers"]),
                                    CanDeleteUsers = Convert.ToBoolean(reader["CanDeleteUsers"]),
                                    CanViewReports = Convert.ToBoolean(reader["CanViewReports"]),
                                    CanPrint = Convert.ToBoolean(reader["CanPrint"]),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                                    ModifiedDate = reader["ModifiedDate"] != DBNull.Value ? Convert.ToDateTime(reader["ModifiedDate"]) : (DateTime?)null,
                                    LastLoginDate = reader["LastLoginDate"] != DBNull.Value ? Convert.ToDateTime(reader["LastLoginDate"]) : (DateTime?)null
                                });
                            }
                        }
                    }
                }
                LogManager.LogInfo($"تم البحث عن المستخدمين بالمصطلح: {searchTerm}، النتائج: {users.Count}");
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في البحث عن المستخدمين: {ex.Message}");
                throw;
            }
            return users;
        }

        /// <summary>
        /// الحصول على عدد المستخدمين النشطين
        /// </summary>
        /// <returns>عدد المستخدمين النشطين</returns>
        public static int GetActiveUsersCount()
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = "SELECT COUNT(*) FROM Users WHERE IsActive = 1";
                    using (var command = new SqlCommand(query, connection))
                    {
                        return Convert.ToInt32(command.ExecuteScalar());
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على عدد المستخدمين النشطين: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// التحقق من وجود اسم مستخدم
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <param name="excludeUserID">معرف المستخدم المستبعد (للتعديل)</param>
        /// <returns>true إذا كان موجود</returns>
        public static bool UsernameExists(string username, int excludeUserID = 0)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = "SELECT COUNT(*) FROM Users WHERE Username = @Username AND UserID != @ExcludeUserID";
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@Username", username);
                        command.Parameters.AddWithValue("@ExcludeUserID", excludeUserID);
                        return Convert.ToInt32(command.ExecuteScalar()) > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في التحقق من وجود اسم المستخدم: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إعادة تعيين كلمة مرور المستخدم
        /// </summary>
        /// <param name="userID">معرف المستخدم</param>
        /// <param name="newPassword">كلمة المرور الجديدة</param>
        /// <returns>true إذا تم إعادة التعيين بنجاح</returns>
        public static bool ResetPassword(int userID, string newPassword)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(newPassword))
                    return false;

                string Password = HashPassword(newPassword);

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        UPDATE Users
                        SET Password = @Password,
                            ModifiedDate = @ModifiedDate
                        WHERE UserID = @UserID";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@UserID", userID);
                        command.Parameters.AddWithValue("@Password", Password);
                        command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);

                        int rowsAffected = command.ExecuteNonQuery();

                        if (rowsAffected > 0)
                        {
                            LogManager.LogInfo($"تم إعادة تعيين كلمة المرور للمستخدم ID: {userID}");
                            return true;
                        }
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في إعادة تعيين كلمة المرور: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// الحصول على مستخدم بالمعرف
        /// </summary>
        /// <param name="userID">معرف المستخدم</param>
        /// <returns>بيانات المستخدم</returns>
        public static User GetUserById(int userID)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT UserID, Username, FullName, Email, Phone, Role, IsActive,
                               CreatedDate, LastLoginDate
                        FROM Users
                        WHERE UserID = @UserID";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@UserID", userID);

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return new User
                                {
                                    UserID = Convert.ToInt32(reader["UserID"]),
                                    Username = reader["Username"].ToString(),
                                    FullName = reader["FullName"].ToString(),
                                    Email = reader["Email"].ToString(),
                                    Phone = reader["Phone"].ToString(),
                                    Role = reader["Role"].ToString(),
                                    IsActive = Convert.ToBoolean(reader["IsActive"]),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                                    LastLoginDate = reader["LastLoginDate"] != DBNull.Value ?
                                                  Convert.ToDateTime(reader["LastLoginDate"]) : (DateTime?)null
                                };
                            }
                        }
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على المستخدم: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على صلاحيات المستخدم
        /// </summary>
        /// <param name="userID">معرف المستخدم</param>
        /// <returns>قائمة صلاحيات المستخدم</returns>
        public static List<UserPermission> GetUserPermissions(int userID)
        {
            var permissions = new List<UserPermission>();
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT PermissionID, UserID, ModuleName, CanViewReports, CanAddUsers, CanEditUsers, CanDeleteUsers, CanPrint
                        FROM UserPermissions
                        WHERE UserID = @UserID";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@UserID", userID);

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                permissions.Add(new UserPermission
                                {
                                    PermissionID = Convert.ToInt32(reader["PermissionID"]),
                                    UserID = Convert.ToInt32(reader["UserID"]),
                                    ModuleName = reader["ModuleName"].ToString(),
                                    CanView = Convert.ToBoolean(reader["CanViewReports"]),
                                    CanAdd = Convert.ToBoolean(reader["CanAddUsers"]),
                                    CanEdit = Convert.ToBoolean(reader["CanEditUsers"]),
                                    CanDelete = Convert.ToBoolean(reader["CanDeleteUsers"]),
                                    CanPrint = Convert.ToBoolean(reader["CanPrint"])
                                });
                            }
                        }
                    }
                }

                LogManager.LogInfo($"تم الحصول على {permissions.Count} صلاحية للمستخدم {userID}");
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على صلاحيات المستخدم: {ex.Message}");
                throw;
            }

            return permissions;
        }

        /// <summary>
        /// تحديث صلاحيات المستخدم
        /// </summary>
        /// <param name="userID">معرف المستخدم</param>
        /// <param name="permissions">قائمة الصلاحيات الجديدة</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public static bool UpdateUserPermissions(int userID, List<UserPermission> permissions)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // حذف الصلاحيات الحالية
                            string deleteQuery = "DELETE FROM UserPermissions WHERE UserID = @UserID";
                            using (var deleteCommand = new SqlCommand(deleteQuery, connection, transaction))
                            {
                                deleteCommand.Parameters.AddWithValue("@UserID", userID);
                                deleteCommand.ExecuteNonQuery();
                            }

                            // إضافة الصلاحيات الجديدة
                            string insertQuery = @"
                                INSERT INTO UserPermissions (UserID, ModuleName, CanViewReports, CanAddUsers, CanEditUsers, CanDeleteUsers, CanPrint, CreatedDate, CreatedBy)
                                VALUES (@UserID, @ModuleName, @CanView, @CanAdd, @CanEdit, @CanDelete, @CanPrint, @CreatedDate, @CreatedBy)";

                            foreach (var permission in permissions)
                            {
                                using (var insertCommand = new SqlCommand(insertQuery, connection, transaction))
                                {
                                    insertCommand.Parameters.AddWithValue("@UserID", userID);
                                    insertCommand.Parameters.AddWithValue("@ModuleName", permission.ModuleName);
                                    insertCommand.Parameters.AddWithValue("@CanViewReports", permission.CanView);
                                    insertCommand.Parameters.AddWithValue("@CanAddUsers", permission.CanAdd);
                                    insertCommand.Parameters.AddWithValue("@CanEditUsers", permission.CanEdit);
                                    insertCommand.Parameters.AddWithValue("@CanDeleteUsers", permission.CanDelete);
                                    insertCommand.Parameters.AddWithValue("@CanPrint", permission.CanPrint);
                                    insertCommand.Parameters.AddWithValue("@CreatedDate", DateTime.Now);
                                    insertCommand.Parameters.AddWithValue("@CreatedBy", CurrentUser?.UserID);
                                    insertCommand.ExecuteNonQuery();
                                }
                            }

                            transaction.Commit();
                            LogManager.LogInfo($"تم تحديث صلاحيات المستخدم {userID}");
                            return true;
                        }
                        catch
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تحديث صلاحيات المستخدم: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// الحصول على اسم الصلاحية من المفتاح
        /// </summary>
        /// <param name="permissionKey">مفتاح الصلاحية</param>
        /// <returns>اسم الصلاحية</returns>
        private static string GetPermissionName(string permissionKey)
        {
            var permissionNames = new Dictionary<string, string>
            {
                { "Sales", "إدارة المبيعات" },
                { "Sales.View", "عرض المبيعات" },
                { "Sales.Add", "إضافة مبيعات" },
                { "Sales.Edit", "تعديل المبيعات" },
                { "Sales.Delete", "حذف المبيعات" },
                { "Inventory", "إدارة المخزون" },
                { "Inventory.View", "عرض المخزون" },
                { "Inventory.Add", "إضافة أدوية" },
                { "Inventory.Edit", "تعديل أدوية" },
                { "Inventory.Delete", "حذف أدوية" },
                { "Suppliers", "إدارة الموردين" },
                { "Suppliers.View", "عرض الموردين" },
                { "Suppliers.Add", "إضافة موردين" },
                { "Suppliers.Edit", "تعديل موردين" },
                { "Suppliers.Delete", "حذف موردين" },
                { "Customers", "إدارة العملاء" },
                { "Customers.View", "عرض العملاء" },
                { "Customers.Add", "إضافة عملاء" },
                { "Customers.Edit", "تعديل عملاء" },
                { "Customers.Delete", "حذف عملاء" },
                { "Reports", "التقارير" },
                { "Reports.Sales", "تقارير المبيعات" },
                { "Reports.Inventory", "تقارير المخزون" },
                { "Reports.Financial", "التقارير المالية" },
                { "Users", "إدارة المستخدمين" },
                { "Users.View", "عرض المستخدمين" },
                { "Users.Add", "إضافة مستخدمين" },
                { "Users.Edit", "تعديل مستخدمين" },
                { "Users.Delete", "حذف مستخدمين" },
                { "Users.Permissions", "إدارة الصلاحيات" },
                { "Settings", "الإعدادات" },
                { "Settings.View", "عرض الإعدادات" },
                { "Settings.Edit", "تعديل الإعدادات" },
                { "Settings.Backup", "النسخ الاحتياطي" }
            };

            return permissionNames.ContainsKey(permissionKey) ? permissionNames[permissionKey] : permissionKey;
        }

        /// <summary>
        /// الحصول على المستخدمين حسب الدور
        /// </summary>
        /// <param name="role">الدور</param>
        /// <returns>قائمة المستخدمين</returns>
        public static List<User> GetUsersByRole(string role)
        {
            try
            {
                var users = new List<User>();
                string query = @"
                    SELECT u.*, b.BranchName
                    FROM Users u
                    LEFT JOIN Branches b ON u.BranchID = b.BranchID
                    WHERE u.Role = @Role AND u.IsActive = 1
                    ORDER BY u.FullName";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@Role", role);
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                users.Add(new User
                                {
                                    UserID = Convert.ToInt32(reader["UserID"]),
                                    Username = reader["Username"].ToString(),
                                    FullName = reader["FullName"].ToString(),
                                    Email = reader["Email"].ToString(),
                                    Phone = reader["Phone"].ToString(),
                                    Role = reader["Role"].ToString(),
                                    BranchID = reader["BranchID"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["BranchID"]),
                                    BranchName = reader["BranchName"]?.ToString(),
                                    IsActive = Convert.ToBoolean(reader["IsActive"]),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"])
                                });
                            }
                        }
                    }
                }
                return users;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على المستخدمين حسب الدور: {ex.Message}");
                return new List<User>();
            }
        }





        #endregion
    }

    /// <summary>
    /// نموذج صلاحية المستخدم - User Permission Model
    /// </summary>
    public class UserPermission
    {
        public int PermissionID { get; set; }
        public int UserID { get; set; }
        public string ModuleName { get; set; }
        public bool CanView { get; set; }
        public bool CanAdd { get; set; }
        public bool CanEdit { get; set; }
        public bool CanDelete { get; set; }
        public bool CanPrint { get; set; }
        public DateTime CreatedDate { get; set; }
        public int? CreatedBy { get; set; }
    }
}
