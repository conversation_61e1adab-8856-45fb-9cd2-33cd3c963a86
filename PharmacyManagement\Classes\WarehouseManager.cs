using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using PharmacyManagement.Models;

namespace PharmacyManagement.Classes
{
    /// <summary>
    /// مدير المخازن - Warehouse Manager
    /// </summary>
    public static class WarehouseManager
    {
        #region Warehouse Management

        /// <summary>
        /// إضافة مخزن جديد
        /// </summary>
        /// <param name="warehouse">المخزن</param>
        /// <returns>معرف المخزن الجديد</returns>
        public static int AddWarehouse(Warehouse warehouse)
        {
            try
            {
                // التحقق من عدم تكرار كود المخزن
                if (IsWarehouseCodeExists(warehouse.WarehouseCode))
                {
                    throw new Exception("كود المخزن موجود مسبقاً");
                }

                string query = @"
                    INSERT INTO Warehouses (WarehouseCode, WarehouseName, BranchID, Location, Capacity, 
                                          Temperature, Humidity, KeeperID, IsActive, WarehouseType, 
                                          CreatedBy, CreatedDate)
                    VALUES (@WarehouseCode, @WarehouseName, @BranchID, @Location, @Capacity, 
                           @Temperature, @Humidity, @KeeperID, @IsActive, @WarehouseType, 
                           @CreatedBy, @CreatedDate);
                    SELECT SCOPE_IDENTITY();";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@WarehouseCode", warehouse.WarehouseCode);
                        command.Parameters.AddWithValue("@WarehouseName", warehouse.WarehouseName);
                        command.Parameters.AddWithValue("@BranchID", warehouse.BranchID);
                        command.Parameters.AddWithValue("@Location", warehouse.Location ?? "");
                        command.Parameters.AddWithValue("@Capacity", (object)warehouse.Capacity ?? DBNull.Value);
                        command.Parameters.AddWithValue("@Temperature", warehouse.Temperature ?? "");
                        command.Parameters.AddWithValue("@Humidity", warehouse.Humidity ?? "");
                        command.Parameters.AddWithValue("@KeeperID", (object)warehouse.KeeperID ?? DBNull.Value);
                        command.Parameters.AddWithValue("@IsActive", warehouse.IsActive);
                        command.Parameters.AddWithValue("@WarehouseType", warehouse.WarehouseType ?? "عادي");
                        command.Parameters.AddWithValue("@CreatedBy", warehouse.CreatedBy);
                        command.Parameters.AddWithValue("@CreatedDate", DateTime.Now);

                        int warehouseId = Convert.ToInt32(command.ExecuteScalar());
                        LogManager.LogActivity("إضافة مخزن" ,  $"تم إضافة المخزن: {warehouse.WarehouseCode} - {warehouse.WarehouseName}");
                        return warehouseId;
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في إضافة المخزن: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// تحديث مخزن
        /// </summary>
        /// <param name="warehouse">المخزن</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public static bool UpdateWarehouse(Warehouse warehouse)
        {
            try
            {
                // التحقق من عدم تكرار كود المخزن
                if (IsWarehouseCodeExists(warehouse.WarehouseCode, warehouse.WarehouseID))
                {
                    throw new Exception("كود المخزن موجود مسبقاً");
                }

                string query = @"
                    UPDATE Warehouses SET 
                        WarehouseCode = @WarehouseCode,
                        WarehouseName = @WarehouseName,
                        BranchID = @BranchID,
                        Location = @Location,
                        Capacity = @Capacity,
                        Temperature = @Temperature,
                        Humidity = @Humidity,
                        KeeperID = @KeeperID,
                        IsActive = @IsActive,
                        WarehouseType = @WarehouseType
                    WHERE WarehouseID = @WarehouseID";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@WarehouseID", warehouse.WarehouseID);
                        command.Parameters.AddWithValue("@WarehouseCode", warehouse.WarehouseCode);
                        command.Parameters.AddWithValue("@WarehouseName", warehouse.WarehouseName);
                        command.Parameters.AddWithValue("@BranchID", warehouse.BranchID);
                        command.Parameters.AddWithValue("@Location", warehouse.Location ?? "");
                        command.Parameters.AddWithValue("@Capacity", (object)warehouse.Capacity ?? DBNull.Value);
                        command.Parameters.AddWithValue("@Temperature", warehouse.Temperature ?? "");
                        command.Parameters.AddWithValue("@Humidity", warehouse.Humidity ?? "");
                        command.Parameters.AddWithValue("@KeeperID", (object)warehouse.KeeperID ?? DBNull.Value);
                        command.Parameters.AddWithValue("@IsActive", warehouse.IsActive);
                        command.Parameters.AddWithValue("@WarehouseType", warehouse.WarehouseType ?? "عادي");

                        int rowsAffected = command.ExecuteNonQuery();
                        if (rowsAffected > 0)
                        {
                            LogManager.LogActivity("تحديث مخزن " , $"تم تحديث المخزن: {warehouse.WarehouseCode} - {warehouse.WarehouseName}");
                            return true;
                        }
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تحديث المخزن: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// حذف مخزن
        /// </summary>
        /// <param name="warehouseId">معرف المخزن</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public static bool DeleteWarehouse(int warehouseId)
        {
            try
            {
                // التحقق من عدم وجود مخزون
                if (HasStock(warehouseId))
                {
                    throw new Exception("لا يمكن حذف المخزن لوجود مخزون به");
                }

                // التحقق من عدم وجود حركات مخزون
                if (HasStockMovements(warehouseId))
                {
                    throw new Exception("لا يمكن حذف المخزن لوجود حركات مخزون مرتبطة به");
                }

                string query = "DELETE FROM Warehouses WHERE WarehouseID = @WarehouseID";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@WarehouseID", warehouseId);
                        int rowsAffected = command.ExecuteNonQuery();

                        if (rowsAffected > 0)
                        {
                            LogManager.LogActivity("حذف مخزن" ,  $"تم حذف المخزن رقم: {warehouseId}");
                            return true;
                        }
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في حذف المخزن: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// الحصول على مخزن
        /// </summary>
        /// <param name="warehouseId">معرف المخزن</param>
        /// <returns>المخزن</returns>
        public static Warehouse GetWarehouse(int warehouseId)
        {
            try
            {
                string query = @"
                    SELECT w.*, b.BranchName, u.FullName AS KeeperName, creator.FullName AS CreatedByName
                    FROM Warehouses w
                    INNER JOIN Branches b ON w.BranchID = b.BranchID
                    LEFT JOIN Users u ON w.KeeperID = u.UserID
                    LEFT JOIN Users creator ON w.CreatedBy = creator.UserID
                    WHERE w.WarehouseID = @WarehouseID";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@WarehouseID", warehouseId);
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return new Warehouse
                                {
                                    WarehouseID = Convert.ToInt32(reader["WarehouseID"]),
                                    WarehouseCode = reader["WarehouseCode"].ToString(),
                                    WarehouseName = reader["WarehouseName"].ToString(),
                                    BranchID = Convert.ToInt32(reader["BranchID"]),
                                    BranchName = reader["BranchName"].ToString(),
                                    Location = reader["Location"].ToString(),
                                    Capacity = reader["Capacity"] == DBNull.Value ? null : (decimal?)Convert.ToDecimal(reader["Capacity"]),
                                    Temperature = reader["Temperature"].ToString(),
                                    Humidity = reader["Humidity"].ToString(),
                                    KeeperID = reader["KeeperID"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["KeeperID"]),
                                    KeeperName = reader["KeeperName"].ToString(),
                                    IsActive = Convert.ToBoolean(reader["IsActive"]),
                                    WarehouseType = reader["WarehouseType"].ToString(),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                                    CreatedBy = reader["CreatedBy"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["CreatedBy"]),
                                    CreatedByName = reader["CreatedByName"].ToString()
                                };
                            }
                        }
                    }
                }
                return null;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على المخزن: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// الحصول على جميع المخازن
        /// </summary>
        /// <returns>قائمة المخازن</returns>
        public static List<Warehouse> GetAllWarehouses()
        {
            try
            {
                var warehouses = new List<Warehouse>();
                string query = @"
                    SELECT w.*, b.BranchName, u.FullName AS KeeperName, creator.FullName AS CreatedByName
                    FROM Warehouses w
                    INNER JOIN Branches b ON w.BranchID = b.BranchID
                    LEFT JOIN Users u ON w.KeeperID = u.UserID
                    LEFT JOIN Users creator ON w.CreatedBy = creator.UserID
                    ORDER BY b.BranchName, w.WarehouseName";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                warehouses.Add(new Warehouse
                                {
                                    WarehouseID = Convert.ToInt32(reader["WarehouseID"]),
                                    WarehouseCode = reader["WarehouseCode"].ToString(),
                                    WarehouseName = reader["WarehouseName"].ToString(),
                                    BranchID = Convert.ToInt32(reader["BranchID"]),
                                    BranchName = reader["BranchName"].ToString(),
                                    Location = reader["Location"].ToString(),
                                    Capacity = reader["Capacity"] == DBNull.Value ? null : (decimal?)Convert.ToDecimal(reader["Capacity"]),
                                    Temperature = reader["Temperature"].ToString(),
                                    Humidity = reader["Humidity"].ToString(),
                                    KeeperID = reader["KeeperID"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["KeeperID"]),
                                    KeeperName = reader["KeeperName"].ToString(),
                                    IsActive = Convert.ToBoolean(reader["IsActive"]),
                                    WarehouseType = reader["WarehouseType"].ToString(),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                                    CreatedBy = reader["CreatedBy"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["CreatedBy"]),
                                    CreatedByName = reader["CreatedByName"].ToString()
                                });
                            }
                        }
                    }
                }
                return warehouses;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على المخازن: {ex.Message}");
                return new List<Warehouse>();
            }
        }

        /// <summary>
        /// الحصول على المخازن النشطة
        /// </summary>
        /// <returns>قائمة المخازن النشطة</returns>
        public static List<Warehouse> GetActiveWarehouses()
        {
            try
            {
                var warehouses = new List<Warehouse>();
                string query = @"
                    SELECT w.*, b.BranchName, u.FullName AS KeeperName
                    FROM Warehouses w
                    INNER JOIN Branches b ON w.BranchID = b.BranchID
                    LEFT JOIN Users u ON w.KeeperID = u.UserID
                    WHERE w.IsActive = 1 AND b.IsActive = 1
                    ORDER BY b.BranchName, w.WarehouseName";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                warehouses.Add(new Warehouse
                                {
                                    WarehouseID = Convert.ToInt32(reader["WarehouseID"]),
                                    WarehouseCode = reader["WarehouseCode"].ToString(),
                                    WarehouseName = reader["WarehouseName"].ToString(),
                                    BranchID = Convert.ToInt32(reader["BranchID"]),
                                    BranchName = reader["BranchName"].ToString(),
                                    Location = reader["Location"].ToString(),
                                    Capacity = reader["Capacity"] == DBNull.Value ? null : (decimal?)Convert.ToDecimal(reader["Capacity"]),
                                    Temperature = reader["Temperature"].ToString(),
                                    Humidity = reader["Humidity"].ToString(),
                                    KeeperID = reader["KeeperID"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["KeeperID"]),
                                    KeeperName = reader["KeeperName"].ToString(),
                                    IsActive = Convert.ToBoolean(reader["IsActive"]),
                                    WarehouseType = reader["WarehouseType"].ToString(),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                                    CreatedBy = reader["CreatedBy"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["CreatedBy"])
                                });
                            }
                        }
                    }
                }
                return warehouses;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على المخازن النشطة: {ex.Message}");
                return new List<Warehouse>();
            }
        }

        /// <summary>
        /// الحصول على مخازن فرع معين
        /// </summary>
        /// <param name="branchId">معرف الفرع</param>
        /// <returns>قائمة مخازن الفرع</returns>
        public static List<Warehouse> GetWarehousesByBranch(int branchId)
        {
            try
            {
                var warehouses = new List<Warehouse>();
                string query = @"
                    SELECT w.*, b.BranchName, u.FullName AS KeeperName
                    FROM Warehouses w
                    INNER JOIN Branches b ON w.BranchID = b.BranchID
                    LEFT JOIN Users u ON w.KeeperID = u.UserID
                    WHERE w.BranchID = @BranchID AND w.IsActive = 1
                    ORDER BY w.WarehouseName";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@BranchID", branchId);
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                warehouses.Add(new Warehouse
                                {
                                    WarehouseID = Convert.ToInt32(reader["WarehouseID"]),
                                    WarehouseCode = reader["WarehouseCode"].ToString(),
                                    WarehouseName = reader["WarehouseName"].ToString(),
                                    BranchID = Convert.ToInt32(reader["BranchID"]),
                                    BranchName = reader["BranchName"].ToString(),
                                    Location = reader["Location"].ToString(),
                                    Capacity = reader["Capacity"] == DBNull.Value ? null : (decimal?)Convert.ToDecimal(reader["Capacity"]),
                                    Temperature = reader["Temperature"].ToString(),
                                    Humidity = reader["Humidity"].ToString(),
                                    KeeperID = reader["KeeperID"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["KeeperID"]),
                                    KeeperName = reader["KeeperName"].ToString(),
                                    IsActive = Convert.ToBoolean(reader["IsActive"]),
                                    WarehouseType = reader["WarehouseType"].ToString(),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                                    CreatedBy = reader["CreatedBy"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["CreatedBy"])
                                });
                            }
                        }
                    }
                }
                return warehouses;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على مخازن الفرع: {ex.Message}");
                return new List<Warehouse>();
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// التحقق من وجود كود المخزن
        /// </summary>
        private static bool IsWarehouseCodeExists(string warehouseCode, int? excludeWarehouseId = null)
        {
            try
            {
                string query = "SELECT COUNT(*) FROM Warehouses WHERE WarehouseCode = @WarehouseCode";
                if (excludeWarehouseId.HasValue)
                {
                    query += " AND WarehouseID != @ExcludeWarehouseID";
                }

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@WarehouseCode", warehouseCode);
                        if (excludeWarehouseId.HasValue)
                        {
                            command.Parameters.AddWithValue("@ExcludeWarehouseID", excludeWarehouseId.Value);
                        }

                        int count = Convert.ToInt32(command.ExecuteScalar());
                        return count > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في التحقق من كود المخزن: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// التحقق من وجود مخزون
        /// </summary>
        private static bool HasStock(int warehouseId)
        {
            try
            {
                string query = "SELECT COUNT(*) FROM DrugStock WHERE WarehouseID = @WarehouseID AND Quantity > 0";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@WarehouseID", warehouseId);
                        int count = Convert.ToInt32(command.ExecuteScalar());
                        return count > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في التحقق من المخزون: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// التحقق من وجود حركات مخزون
        /// </summary>
        private static bool HasStockMovements(int warehouseId)
        {
            try
            {
                string query = "SELECT COUNT(*) FROM StockMovements WHERE WarehouseID = @WarehouseID";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@WarehouseID", warehouseId);
                        int count = Convert.ToInt32(command.ExecuteScalar());
                        return count > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في التحقق من حركات المخزون: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// البحث في المخازن
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <returns>قائمة المخازن المطابقة</returns>
        public static List<Warehouse> SearchWarehouses(string searchTerm)
        {
            try
            {
                var warehouses = new List<Warehouse>();
                string query = @"
                    SELECT w.*, b.BranchName, u.FullName AS KeeperName
                    FROM Warehouses w
                    INNER JOIN Branches b ON w.BranchID = b.BranchID
                    LEFT JOIN Users u ON w.KeeperID = u.UserID
                    WHERE (w.WarehouseCode LIKE @SearchTerm OR w.WarehouseName LIKE @SearchTerm OR 
                           b.BranchName LIKE @SearchTerm OR w.Location LIKE @SearchTerm) 
                    AND w.IsActive = 1
                    ORDER BY b.BranchName, w.WarehouseName";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@SearchTerm", $"%{searchTerm}%");
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                warehouses.Add(new Warehouse
                                {
                                    WarehouseID = Convert.ToInt32(reader["WarehouseID"]),
                                    WarehouseCode = reader["WarehouseCode"].ToString(),
                                    WarehouseName = reader["WarehouseName"].ToString(),
                                    BranchID = Convert.ToInt32(reader["BranchID"]),
                                    BranchName = reader["BranchName"].ToString(),
                                    Location = reader["Location"].ToString(),
                                    Capacity = reader["Capacity"] == DBNull.Value ? null : (decimal?)Convert.ToDecimal(reader["Capacity"]),
                                    Temperature = reader["Temperature"].ToString(),
                                    Humidity = reader["Humidity"].ToString(),
                                    KeeperID = reader["KeeperID"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["KeeperID"]),
                                    KeeperName = reader["KeeperName"].ToString(),
                                    IsActive = Convert.ToBoolean(reader["IsActive"]),
                                    WarehouseType = reader["WarehouseType"].ToString(),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                                    CreatedBy = reader["CreatedBy"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["CreatedBy"])
                                });
                            }
                        }
                    }
                }
                return warehouses;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في البحث في المخازن: {ex.Message}");
                return new List<Warehouse>();
            }
        }

        /// <summary>
        /// توليد كود مخزن جديد
        /// </summary>
        /// <param name="branchId">معرف الفرع</param>
        /// <returns>كود المخزن</returns>
        public static string GenerateWarehouseCode(int branchId)
        {
            try
            {
                string query = "SELECT COUNT(*) FROM Warehouses WHERE BranchID = @BranchID";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@BranchID", branchId);
                        int count = Convert.ToInt32(command.ExecuteScalar()) + 1;
                        return $"WH{branchId:D2}{count:D2}";
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في توليد كود المخزن: {ex.Message}");
                return $"WH{branchId:D2}{DateTime.Now.Ticks.ToString().Substring(10)}";
            }
        }







        #endregion
    }
}
