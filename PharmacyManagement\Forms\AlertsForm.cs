using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using InventoryItem = PharmacyManagement.Models.InventoryItem;
using InventoryAlert = PharmacyManagement.Models.InventoryAlert;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نموذج التنبيهات المحسن - Enhanced Alerts Form
    /// تصميم مسطح حديث مع ميزات متقدمة
    /// </summary>
    public partial class AlertsForm : Form
    {
        #region Fields

        private BindingSource _alertsBindingSource;
        private List<InventoryAlert> _allAlerts;
        private Timer _refreshTimer;

        #endregion

        #region Constructor

        public AlertsForm()
        {
            InitializeComponent();
            SetupForm();
            SetupDataGrid();
            SetupEvents();
            SetupAutoRefresh();
            LoadAlerts();
        }

        #endregion

        #region Setup Methods

        private void SetupForm()
        {
            // إعداد النموذج الأساسي
            this.Text = "التنبيهات والإشعارات";
            this.WindowState = FormWindowState.Maximized;
            this.BackColor = Color.White;
            this.Font = new Font("Segoe UI", 9F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // إعداد التصميم المسطح
            SetupFlatDesign();
        }

        private void SetupFlatDesign()
        {
            // تطبيق التصميم المسطح على جميع العناصر
            ApplyFlatDesignToControls(this);

            // إعداد ألوان خاصة للأزرار
            SetupButtonColors();

            // إعداد ألوان الشبكة
            SetupDataGridColors();
        }

        private void ApplyFlatDesignToControls(Control parent)
        {
            foreach (Control control in parent.Controls)
            {
                if (control is Button btn)
                {
                    btn.FlatStyle = FlatStyle.Flat;
                    btn.FlatAppearance.BorderSize = 0;
                    btn.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
                    btn.Cursor = Cursors.Hand;
                }
                else if (control is ComboBox cmb)
                {
                    cmb.FlatStyle = FlatStyle.Flat;
                    cmb.Font = new Font("Segoe UI", 10F);
                }
                else if (control is CheckBox chk)
                {
                    chk.FlatStyle = FlatStyle.Flat;
                    chk.Font = new Font("Segoe UI", 10F);
                }

                // تطبيق التصميم على العناصر الفرعية
                if (control.HasChildren)
                {
                    ApplyFlatDesignToControls(control);
                }
            }
        }

        private void SetupButtonColors()
        {
            // ألوان الأزرار حسب الوظيفة
            btnRefresh.BackColor = Color.FromArgb(52, 152, 219);
            btnRefresh.ForeColor = Color.White;

            btnMarkAsRead.BackColor = Color.FromArgb(46, 204, 113);
            btnMarkAsRead.ForeColor = Color.White;

            btnMarkAllAsRead.BackColor = Color.FromArgb(26, 188, 156);
            btnMarkAllAsRead.ForeColor = Color.White;

            btnResolve.BackColor = Color.FromArgb(155, 89, 182);
            btnResolve.ForeColor = Color.White;

            btnViewDetails.BackColor = Color.FromArgb(52, 152, 219);
            btnViewDetails.ForeColor = Color.White;

            btnClose.BackColor = Color.FromArgb(149, 165, 166);
            btnClose.ForeColor = Color.White;
        }

        private void SetupDataGridColors()
        {
            dgvAlerts.BackgroundColor = Color.White;
            dgvAlerts.BorderStyle = BorderStyle.FixedSingle;
            dgvAlerts.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 73, 94);
            dgvAlerts.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvAlerts.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            dgvAlerts.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgvAlerts.DefaultCellStyle.Font = new Font("Segoe UI", 10F);
            dgvAlerts.DefaultCellStyle.SelectionBackColor = Color.FromArgb(52, 152, 219);
            dgvAlerts.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvAlerts.RowHeadersVisible = false;
            dgvAlerts.EnableHeadersVisualStyles = false;
            dgvAlerts.GridColor = Color.FromArgb(189, 195, 199);
            dgvAlerts.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);
        }

        private void SetupDataGrid()
        {
            // إعداد شبكة التنبيهات
            dgvAlerts.AutoGenerateColumns = false;
            dgvAlerts.AllowUserToAddRows = false;
            dgvAlerts.AllowUserToDeleteRows = false;
            dgvAlerts.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvAlerts.MultiSelect = true;
            dgvAlerts.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dgvAlerts.ColumnHeadersHeight = 40;
            dgvAlerts.RowTemplate.Height = 35;

            // إضافة الأعمدة المحسنة
            AddDataGridColumns();

            // إعداد مصدر البيانات
            _alertsBindingSource = new BindingSource();
            dgvAlerts.DataSource = _alertsBindingSource;
        }

        private void AddDataGridColumns()
        {
            // عمود نوع التنبيه مع أيقونة
            var alertTypeColumn = new DataGridViewTextBoxColumn
            {
                Name = "AlertType",
                HeaderText = "نوع التنبيه",
                DataPropertyName = "AlertType",
                FillWeight = 15,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Font = new Font("Segoe UI", 10F, FontStyle.Bold)
                }
            };
            dgvAlerts.Columns.Add(alertTypeColumn);

            // عمود الأولوية مع ألوان
            var priorityColumn = new DataGridViewTextBoxColumn
            {
                Name = "Severity",
                HeaderText = "الأولوية",
                DataPropertyName = "Severity",
                FillWeight = 12,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Font = new Font("Segoe UI", 10F, FontStyle.Bold)
                }
            };
            dgvAlerts.Columns.Add(priorityColumn);

            // عمود الرسالة
            var messageColumn = new DataGridViewTextBoxColumn
            {
                Name = "Message",
                HeaderText = "الرسالة",
                DataPropertyName = "Message",
                FillWeight = 35,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    WrapMode = DataGridViewTriState.True,
                    Alignment = DataGridViewContentAlignment.MiddleRight
                }
            };
            dgvAlerts.Columns.Add(messageColumn);

            // عمود الدواء
            var drugColumn = new DataGridViewTextBoxColumn
            {
                Name = "DrugName",
                HeaderText = "الدواء",
                DataPropertyName = "DrugName",
                FillWeight = 20,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Alignment = DataGridViewContentAlignment.MiddleRight,
                    Font = new Font("Segoe UI", 10F, FontStyle.Bold)
                }
            };
            dgvAlerts.Columns.Add(drugColumn);

            // عمود تاريخ التنبيه
            var dateColumn = new DataGridViewTextBoxColumn
            {
                Name = "AlertDate",
                HeaderText = "تاريخ التنبيه",
                DataPropertyName = "AlertDate",
                FillWeight = 15,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Format = "yyyy/MM/dd HH:mm",
                    Alignment = DataGridViewContentAlignment.MiddleCenter
                }
            };
            dgvAlerts.Columns.Add(dateColumn);

            // عمود الحالة
            var statusColumn = new DataGridViewTextBoxColumn
            {
                Name = "Status",
                HeaderText = "الحالة",
                FillWeight = 12,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Font = new Font("Segoe UI", 9F, FontStyle.Bold)
                }
            };
            dgvAlerts.Columns.Add(statusColumn);

            // عمود مقروء
            var readColumn = new DataGridViewCheckBoxColumn
            {
                Name = "IsRead",
                HeaderText = "مقروء",
                DataPropertyName = "IsRead",
                FillWeight = 8,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Alignment = DataGridViewContentAlignment.MiddleCenter
                }
            };
            dgvAlerts.Columns.Add(readColumn);
        }

        private void SetupEvents()
        {
            // أحداث الأزرار
            btnRefresh.Click += BtnRefresh_Click;
            btnMarkAsRead.Click += BtnMarkAsRead_Click;
            btnMarkAllAsRead.Click += BtnMarkAllAsRead_Click;
            btnResolve.Click += BtnResolve_Click;
            btnViewDetails.Click += BtnViewDetails_Click;
            btnClose.Click += BtnClose_Click;

            // أحداث الفلاتر
            cmbAlertType.SelectedIndexChanged += CmbAlertType_SelectedIndexChanged;
            cmbPriority.SelectedIndexChanged += CmbPriority_SelectedIndexChanged;
            chkShowRead.CheckedChanged += ChkShowRead_CheckedChanged;

            // أحداث الشبكة
            dgvAlerts.SelectionChanged += DgvAlerts_SelectionChanged;
            dgvAlerts.CellFormatting += DgvAlerts_CellFormatting;
            dgvAlerts.CellDoubleClick += DgvAlerts_CellDoubleClick;
            dgvAlerts.CellMouseEnter += DgvAlerts_CellMouseEnter;
            dgvAlerts.CellMouseLeave += DgvAlerts_CellMouseLeave;

            // أحداث النموذج
            this.Load += AlertsForm_Load;
            this.FormClosing += AlertsForm_FormClosing;
        }

        private void SetupAutoRefresh()
        {
            // إعداد التحديث التلقائي كل 30 ثانية
            _refreshTimer = new Timer();
            _refreshTimer.Interval = 30000; // 30 ثانية
            _refreshTimer.Tick += RefreshTimer_Tick;
            _refreshTimer.Start();
        }

        #endregion

        #region Load Data Methods

        private void LoadAlerts()
        {
            try
            {
                // عرض مؤشر التحميل
                this.Cursor = Cursors.WaitCursor;

                // تحميل التنبيهات من InventoryManager المحسن
                _allAlerts = InventoryManager.GetInventoryAlerts();

                // إعداد الفلاتر إذا لم تكن معدة
                if (cmbAlertType.Items.Count == 0)
                {
                    SetupFilters();
                }

                // تطبيق الفلاتر
                ApplyFilters();

                // تحديث الواجهة
                UpdateStatusLabel();
                UpdateButtonStates();

                LogManager.LogInfo($"تم تحميل {_allAlerts.Count} تنبيه بنجاح");
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تحميل التنبيهات: {ex.Message}");
                MessageBox.Show($"خطأ في تحميل التنبيهات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                this.Cursor = Cursors.Default;
            }
        }

        private void SetupFilters()
        {
            // إعداد فلتر نوع التنبيه
            var alertTypes = new[]
            {
                "جميع الأنواع",
                "LowStock",
                "OutOfStock",
                "NearExpiry",
                "Expired"
            };

            cmbAlertType.Items.Clear();
            cmbAlertType.Items.AddRange(alertTypes);
            cmbAlertType.SelectedIndex = 0;

            // إعداد فلتر الأولوية
            var priorities = new[]
            {
                "جميع الأولويات",
                "Critical",
                "High",
                "Medium",
                "Low"
            };

            cmbPriority.Items.Clear();
            cmbPriority.Items.AddRange(priorities);
            cmbPriority.SelectedIndex = 0;
        }

        private void RefreshAlerts()
        {
            LoadAlerts();
        }

        #endregion

        #region Event Handlers

        private void AlertsForm_Load(object sender, EventArgs e)
        {
            // تحديث إضافي عند تحميل النموذج
            RefreshAlerts();
        }

        private void AlertsForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            // إيقاف المؤقت عند إغلاق النموذج
            _refreshTimer?.Stop();
            _refreshTimer?.Dispose();
        }

        private void RefreshTimer_Tick(object sender, EventArgs e)
        {
            // التحديث التلقائي
            RefreshAlerts();
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            RefreshAlerts();
        }

        private void BtnMarkAsRead_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvAlerts.SelectedRows.Count > 0)
                {
                    var selectedAlerts = dgvAlerts.SelectedRows.Cast<DataGridViewRow>()
                        .Where(row => row.DataBoundItem is InventoryAlert)
                        .Select(row => row.DataBoundItem as InventoryAlert)
                        .Where(alert => !alert.IsRead)
                        .ToList();

                    if (selectedAlerts.Count == 0)
                    {
                        MessageBox.Show("جميع التنبيهات المحددة مقروءة بالفعل", "تنبيه",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }

                    foreach (var alert in selectedAlerts)
                    {
                        alert.IsRead = true;
                        // هنا يمكن إضافة استدعاء لحفظ التغيير في قاعدة البيانات
                        // InventoryManager.MarkAlertAsRead(alert.AlertID);
                    }

                    RefreshAlerts();
                    MessageBox.Show($"تم تحديد {selectedAlerts.Count} تنبيه كمقروء", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("يرجى تحديد تنبيه واحد على الأقل", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تحديث التنبيهات: {ex.Message}");
                MessageBox.Show($"خطأ في تحديث التنبيهات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnMarkAllAsRead_Click(object sender, EventArgs e)
        {
            try
            {
                var unreadCount = _allAlerts?.Count(a => !a.IsRead) ?? 0;

                if (unreadCount == 0)
                {
                    MessageBox.Show("جميع التنبيهات مقروءة بالفعل", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var result = MessageBox.Show($"هل تريد تحديد جميع التنبيهات ({unreadCount}) كمقروءة؟", "تأكيد",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    foreach (var alert in _allAlerts.Where(a => !a.IsRead))
                    {
                        alert.IsRead = true;
                        // هنا يمكن إضافة استدعاء لحفظ التغيير في قاعدة البيانات
                        // InventoryManager.MarkAlertAsRead(alert.AlertID);
                    }

                    RefreshAlerts();
                    MessageBox.Show($"تم تحديد {unreadCount} تنبيه كمقروء", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تحديث التنبيهات: {ex.Message}");
                MessageBox.Show($"خطأ في تحديث التنبيهات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnResolve_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvAlerts.SelectedRows.Count > 0)
                {
                    var selectedAlert = dgvAlerts.SelectedRows[0].DataBoundItem as InventoryAlert;
                    if (selectedAlert != null)
                    {
                        if (selectedAlert.IsResolved)
                        {
                            MessageBox.Show("هذا التنبيه محلول بالفعل", "تنبيه",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                            return;
                        }

                        var result = MessageBox.Show($"هل تريد تحديد هذا التنبيه كمحلول؟\n\n{selectedAlert.Message}", "تأكيد",
                            MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                        if (result == DialogResult.Yes)
                        {
                            selectedAlert.IsResolved = true;
                            selectedAlert.ResolvedDate = DateTime.Now;
                            selectedAlert.IsRead = true;

                            RefreshAlerts();
                            MessageBox.Show("تم تحديد التنبيه كمحلول", "نجح",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                }
                else
                {
                    MessageBox.Show("يرجى تحديد تنبيه لحله", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في حل التنبيه: {ex.Message}");
                MessageBox.Show($"خطأ في حل التنبيه: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnViewDetails_Click(object sender, EventArgs e)
        {
            ShowAlertDetails();
        }

        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void CmbAlertType_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void CmbPriority_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void ChkShowRead_CheckedChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void DgvAlerts_SelectionChanged(object sender, EventArgs e)
        {
            UpdateButtonStates();
        }

        private void DgvAlerts_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (e.RowIndex < 0 || dgvAlerts.Rows[e.RowIndex].DataBoundItem == null)
                return;

            if (dgvAlerts.Rows[e.RowIndex].DataBoundItem is InventoryAlert alert)
            {
                var row = dgvAlerts.Rows[e.RowIndex];

                // تلوين الصف حسب الأولوية والحالة
                if (alert.IsResolved)
                {
                    // التنبيهات المحلولة
                    row.DefaultCellStyle.BackColor = Color.FromArgb(230, 255, 230);
                    row.DefaultCellStyle.ForeColor = Color.FromArgb(100, 100, 100);
                }
                else if (!alert.IsRead)
                {
                    // التنبيهات غير المقروءة حسب الأولوية
                    switch (alert.Severity?.ToUpper())
                    {
                        case "CRITICAL":
                            row.DefaultCellStyle.BackColor = Color.FromArgb(255, 220, 220);
                            row.DefaultCellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
                            break;
                        case "HIGH":
                            row.DefaultCellStyle.BackColor = Color.FromArgb(255, 235, 235);
                            row.DefaultCellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
                            break;
                        case "MEDIUM":
                            row.DefaultCellStyle.BackColor = Color.FromArgb(255, 248, 220);
                            break;
                        case "LOW":
                            row.DefaultCellStyle.BackColor = Color.FromArgb(235, 255, 235);
                            break;
                    }
                }
                else
                {
                    // التنبيهات المقروءة
                    row.DefaultCellStyle.BackColor = Color.FromArgb(248, 248, 248);
                    row.DefaultCellStyle.ForeColor = Color.FromArgb(120, 120, 120);
                }

                // تنسيق خاص للأعمدة
                if (e.ColumnIndex == dgvAlerts.Columns["Severity"]?.Index)
                {
                    FormatSeverityColumn(e, alert.Severity);
                }
                else if (e.ColumnIndex == dgvAlerts.Columns["AlertType"]?.Index)
                {
                    FormatAlertTypeColumn(e, alert.AlertType);
                }
                else if (e.ColumnIndex == dgvAlerts.Columns["Status"]?.Index)
                {
                    FormatStatusColumn(e, alert);
                }
            }
        }

        private void FormatSeverityColumn(DataGridViewCellFormattingEventArgs e, string severity)
        {
            switch (severity?.ToUpper())
            {
                case "CRITICAL":
                    e.CellStyle.ForeColor = Color.DarkRed;
                    e.CellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
                    e.Value = "حرج";
                    break;
                case "HIGH":
                    e.CellStyle.ForeColor = Color.Red;
                    e.CellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
                    e.Value = "عالي";
                    break;
                case "MEDIUM":
                    e.CellStyle.ForeColor = Color.Orange;
                    e.Value = "متوسط";
                    break;
                case "LOW":
                    e.CellStyle.ForeColor = Color.Green;
                    e.Value = "منخفض";
                    break;
                default:
                    e.Value = severity ?? "غير محدد";
                    break;
            }
        }

        private void FormatAlertTypeColumn(DataGridViewCellFormattingEventArgs e, string alertType)
        {
            switch (alertType?.ToUpper())
            {
                case "LOWSTOCK":
                    e.Value = "مخزون منخفض";
                    e.CellStyle.ForeColor = Color.Orange;
                    break;
                case "OUTOFSTOCK":
                    e.Value = "نفد المخزون";
                    e.CellStyle.ForeColor = Color.Red;
                    break;
                case "NEAREXPIRY":
                    e.Value = "قرب انتهاء الصلاحية";
                    e.CellStyle.ForeColor = Color.DarkOrange;
                    break;
                case "EXPIRED":
                    e.Value = "منتهي الصلاحية";
                    e.CellStyle.ForeColor = Color.DarkRed;
                    break;
                default:
                    e.Value = alertType ?? "غير محدد";
                    break;
            }
        }

        private void FormatStatusColumn(DataGridViewCellFormattingEventArgs e, InventoryAlert alert)
        {
            if (alert.IsResolved)
            {
                e.Value = "محلول";
                e.CellStyle.ForeColor = Color.Green;
                e.CellStyle.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            }
            else if (alert.IsRead)
            {
                e.Value = "مقروء";
                e.CellStyle.ForeColor = Color.Blue;
            }
            else
            {
                e.Value = "جديد";
                e.CellStyle.ForeColor = Color.Red;
                e.CellStyle.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            }
        }

        private void DgvAlerts_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                ShowAlertDetails();
            }
        }

        private void DgvAlerts_CellMouseEnter(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                dgvAlerts.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.FromArgb(52, 152, 219, 50);
            }
        }

        private void DgvAlerts_CellMouseLeave(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                // إعادة تطبيق التنسيق الأصلي
                dgvAlerts.InvalidateRow(e.RowIndex);
            }
        }

        #endregion

        #region Helper Methods

        private void UpdateButtonStates()
        {
            bool hasSelection = dgvAlerts.SelectedRows.Count > 0;
            bool hasUnreadSelection = false;
            bool hasUnresolvedSelection = false;

            if (hasSelection)
            {
                var selectedAlerts = dgvAlerts.SelectedRows.Cast<DataGridViewRow>()
                    .Where(row => row.DataBoundItem is InventoryAlert)
                    .Select(row => row.DataBoundItem as InventoryAlert)
                    .ToList();

                hasUnreadSelection = selectedAlerts.Any(alert => !alert.IsRead);
                hasUnresolvedSelection = selectedAlerts.Any(alert => !alert.IsResolved);
            }

            btnMarkAsRead.Enabled = hasUnreadSelection;
            btnResolve.Enabled = hasUnresolvedSelection;
            btnViewDetails.Enabled = hasSelection;
        }

        private void UpdateStatusLabel()
        {
            if (_allAlerts == null)
            {
                lblStatus.Text = "إجمالي التنبيهات: 0";
                return;
            }

            var totalAlerts = _allAlerts.Count;
            var unreadCount = _allAlerts.Count(a => !a.IsRead);
            var resolvedCount = _allAlerts.Count(a => a.IsResolved);
            var criticalCount = _allAlerts.Count(a => a.Severity?.ToUpper() == "CRITICAL");
            var highCount = _allAlerts.Count(a => a.Severity?.ToUpper() == "HIGH");

            var filteredCount = _alertsBindingSource != null ? _alertsBindingSource.Count : 0;

            lblStatus.Text = "إجمالي: " + totalAlerts.ToString() + " | المعروض: " + filteredCount.ToString() + " | غير مقروء: " + unreadCount.ToString() + " | محلول: " + resolvedCount.ToString() + " | حرج: " + criticalCount.ToString() + " | عالي: " + highCount.ToString();
        }

        private void ApplyFilters()
        {
            try
            {
                if (_allAlerts == null)
                    return;

                var filteredAlerts = _allAlerts.AsEnumerable();

                // فلتر نوع التنبيه
                if (cmbAlertType.SelectedIndex > 0)
                {
                    var selectedType = GetAlertTypeFromDisplay(cmbAlertType.Text);
                    filteredAlerts = filteredAlerts.Where(a => a.AlertType?.ToUpper() == selectedType?.ToUpper());
                }

                // فلتر الأولوية
                if (cmbPriority.SelectedIndex > 0)
                {
                    var selectedPriority = cmbPriority.Text;
                    filteredAlerts = filteredAlerts.Where(a => a.Severity?.ToUpper() == selectedPriority?.ToUpper());
                }

                // فلتر إظهار المقروءة
                if (!chkShowRead.Checked)
                {
                    filteredAlerts = filteredAlerts.Where(a => !a.IsRead);
                }

                var resultList = filteredAlerts.OrderByDescending(a => GetSeverityOrder(a.Severity))
                                              .ThenByDescending(a => a.AlertDate)
                                              .ToList();

                _alertsBindingSource.DataSource = resultList;
                UpdateStatusLabel();
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تطبيق الفلاتر: {ex.Message}");
                MessageBox.Show($"خطأ في تطبيق الفلاتر: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private string GetAlertTypeFromDisplay(string displayText)
        {
            switch (displayText)
            {
                case "LowStock": return "LowStock";
                case "OutOfStock": return "OutOfStock";
                case "NearExpiry": return "NearExpiry";
                case "Expired": return "Expired";
                default: return displayText;
            }
        }

        private int GetSeverityOrder(string severity)
        {
            switch (severity?.ToUpper())
            {
                case "CRITICAL": return 4;
                case "HIGH": return 3;
                case "MEDIUM": return 2;
                case "LOW": return 1;
                default: return 0;
            }
        }

        private void ShowAlertDetails()
        {
            try
            {
                if (dgvAlerts.SelectedRows.Count > 0)
                {
                    var selectedRow = dgvAlerts.SelectedRows[0];
                    if (selectedRow.DataBoundItem is InventoryAlert alert)
                    {
                        ShowAlertDetailsDialog(alert);

                        // تحديد التنبيه كمقروء إذا لم يكن كذلك
                        if (!alert.IsRead)
                        {
                            alert.IsRead = true;
                            RefreshAlerts();
                        }
                    }
                }
                else
                {
                    MessageBox.Show("يرجى تحديد تنبيه لعرض تفاصيله", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في عرض تفاصيل التنبيه: {ex.Message}");
                MessageBox.Show($"خطأ في عرض تفاصيل التنبيه: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowAlertDetailsDialog(InventoryAlert alert)
        {
            var alertTypeText = GetAlertTypeDisplayText(alert.AlertType);
            var severityText = GetSeverityDisplayText(alert.Severity);
            var statusText = GetStatusDisplayText(alert);

            var details = $"📋 تفاصيل التنبيه\n" +
                         $"{'=',-50}\n\n" +
                         $"🔔 نوع التنبيه: {alertTypeText}\n" +
                         $"⚠️ الأولوية: {severityText}\n" +
                         $"💬 الرسالة: {alert.Message}\n" +
                         $"💊 الدواء: {alert.DrugName}\n" +
                         $"🏪 كود الدواء: {alert.DrugCode}\n" +
                         $"📅 تاريخ التنبيه: {alert.AlertDate:yyyy/MM/dd HH:mm}\n" +
                         $"📊 الحالة: {statusText}\n";

            if (alert.IsResolved)
            {
                details += $"✅ تاريخ الحل: {alert.ResolvedDate:yyyy/MM/dd HH:mm}\n";
            }

            if (!string.IsNullOrEmpty(alert.Notes))
            {
                details += $"📝 ملاحظات: {alert.Notes}\n";
            }

            MessageBox.Show(details, "تفاصيل التنبيه",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private string GetAlertTypeDisplayText(string alertType)
        {
            switch (alertType?.ToUpper())
            {
                case "LOWSTOCK": return "مخزون منخفض";
                case "OUTOFSTOCK": return "نفد المخزون";
                case "NEAREXPIRY": return "قرب انتهاء الصلاحية";
                case "EXPIRED": return "منتهي الصلاحية";
                default: return alertType ?? "غير محدد";
            }
        }

        private string GetSeverityDisplayText(string severity)
        {
            switch (severity?.ToUpper())
            {
                case "CRITICAL": return "حرج";
                case "HIGH": return "عالي";
                case "MEDIUM": return "متوسط";
                case "LOW": return "منخفض";
                default: return severity ?? "غير محدد";
            }
        }

        private string GetStatusDisplayText(InventoryAlert alert)
        {
            if (alert.IsResolved)
                return "محلول ✅";
            else if (alert.IsRead)
                return "مقروء 👁️";
            else
                return "جديد 🆕";
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// تحديث التنبيهات من خارج النموذج
        /// </summary>
        public void RefreshAlertsFromExternal()
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new Action(RefreshAlerts));
            }
            else
            {
                RefreshAlerts();
            }
        }

        /// <summary>
        /// الحصول على عدد التنبيهات غير المقروءة
        /// </summary>
        /// <returns>عدد التنبيهات غير المقروءة</returns>
        public int GetUnreadAlertsCount()
        {
            return _allAlerts?.Count(a => !a.IsRead) ?? 0;
        }

        /// <summary>
        /// الحصول على عدد التنبيهات الحرجة
        /// </summary>
        /// <returns>عدد التنبيهات الحرجة</returns>
        public int GetCriticalAlertsCount()
        {
            return _allAlerts?.Count(a => a.Severity?.ToUpper() == "CRITICAL" && !a.IsResolved) ?? 0;
        }




        #endregion

     
    }
}
