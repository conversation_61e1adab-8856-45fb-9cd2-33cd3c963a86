using System;
using System.Drawing;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// الواجهة الرئيسية الحديثة بتصميم Flat
    /// </summary>
    public partial class ModernMainForm : Form
    {
        #region Fields

        private Timer _clockTimer;
        private Timer _alertTimer;

        #endregion

        #region Constructor

        public ModernMainForm()
        {
            InitializeComponent();
            LoadUserInfo();
            StartTimers();
        }

        #endregion

        #region User Info and Timers

        /// <summary>
        /// تحميل معلومات المستخدم
        /// </summary>
        private void LoadUserInfo()
        {
            try
            {
                if (UserManager.CurrentUser != null)
                {
                    userLabel.Text = "مرحباً، " + UserManager.CurrentUser.FullName;
                    roleLabel.Text = "الدور: " + UserManager.CurrentUser.Role;
                }
                else
                {
                    userLabel.Text = "مرحباً، المستخدم";
                    roleLabel.Text = "الدور: غير محدد";
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في تحميل معلومات المستخدم: " + ex.Message);
            }
        }

        /// <summary>
        /// بدء المؤقتات
        /// </summary>
        private void StartTimers()
        {
            try
            {
                // مؤقت الساعة
                _clockTimer = new Timer();
                _clockTimer.Interval = 1000; // كل ثانية
                _clockTimer.Tick += ClockTimer_Tick;
                _clockTimer.Start();

                // مؤقت التنبيهات
                _alertTimer = new Timer();
                _alertTimer.Interval = 60000; // كل دقيقة
                _alertTimer.Tick += AlertTimer_Tick;
                _alertTimer.Start();
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في بدء المؤقتات: " + ex.Message);
            }
        }

        /// <summary>
        /// حدث مؤقت الساعة
        /// </summary>
        private void ClockTimer_Tick(object sender, EventArgs e)
        {
            lblClock.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        }

        /// <summary>
        /// حدث مؤقت التنبيهات
        /// </summary>
        private void AlertTimer_Tick(object sender, EventArgs e)
        {
            // تحديث التنبيهات كل دقيقة
            UpdateAlerts();
        }

        /// <summary>
        /// تحديث التنبيهات
        /// </summary>
        private void UpdateAlerts()
        {
            try
            {
                // هنا يمكن إضافة منطق التحقق من التنبيهات
                // مثل الأدوية منتهية الصلاحية، المخزون المنخفض، إلخ
                lblAlerts.Text = "لا توجد تنبيهات";
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في تحديث التنبيهات: " + ex.Message);
            }
        }

        #endregion

        #region Event Handlers

        // Quick Buttons Events
        private void btnQuickRefresh_Click(object sender, EventArgs e)
        {
            RefreshData();
        }

        private void btnQuickSale_Click(object sender, EventArgs e)
        {
            OpenForm(new QuickSaleForm());
        }

        private void btnQuickSearch_Click(object sender, EventArgs e)
        {
            OpenForm(new QuickSearchForm());
        }

        private void btnQuickAlerts_Click(object sender, EventArgs e)
        {
            OpenForm(new AlertsForm());
        }

        // Side Menu Events
        private void btnDashboard_Click(object sender, EventArgs e)
        {
            // TODO: Create DashboardForm when needed
            MessageBox.Show("لوحة المعلومات - قيد التطوير", "معلومات", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
            
            // var dashboardForm = new DashboardForm();
            // dashboardForm.ShowDialog();
        }

        private void btnDrugs_Click(object sender, EventArgs e)
        {
            OpenForm(new DrugsForm());
        }

        private void btnCustomers_Click(object sender, EventArgs e)
        {
            OpenForm(new CustomersForm());
        }

        private void btnSuppliers_Click(object sender, EventArgs e)
        {
            OpenForm(new SuppliersForm());
        }

        private void btnSales_Click(object sender, EventArgs e)
        {
            OpenForm(new SalesForm());
        }

        private void btnInventory_Click(object sender, EventArgs e)
        {
            OpenForm(new InventoryForm());
        }

        private void btnReports_Click(object sender, EventArgs e)
        {
            OpenForm(new ReportsForm());
        }

        private void btnFinancial_Click(object sender, EventArgs e)
        {
            OpenForm(new FinancialForm());
        }

        private void btnBranches_Click(object sender, EventArgs e)
        {
            OpenForm(new BranchForm());
        }

        private void btnWarehouses_Click(object sender, EventArgs e)
        {
            OpenForm(new WarehouseForm());
        }

        private void btnUsers_Click(object sender, EventArgs e)
        {
            OpenForm(new UsersForm());
        }

        private void btnSettings_Click(object sender, EventArgs e)
        {
            OpenForm(new SettingsForm());
        }

        private void btnLogout_Click(object sender, EventArgs e)
        {
            try
            {
                if (MessageBox.Show("هل تريد تسجيل الخروج؟", "تأكيد الخروج", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    UserManager.Logout();
                    this.Hide();
                    var loginForm = new LoginForm();
                    loginForm.ShowDialog();
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في تسجيل الخروج: " + ex.Message);
                MessageBox.Show("خطأ في تسجيل الخروج: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// فتح نافذة في المنطقة الرئيسية
        /// </summary>
        private void OpenForm(Form form)
        {
            try
            {
                // إغلاق النافذة الحالية إن وجدت
                foreach (Control control in contentPanel.Controls)
                {
                    if (control is Form)
                    {
                        ((Form)control).Close();
                    }
                }
                contentPanel.Controls.Clear();

                // إعداد النافذة الجديدة
                form.TopLevel = false;
                form.FormBorderStyle = FormBorderStyle.None;
                form.Dock = DockStyle.Fill;
                
                // إضافة النافذة للمنطقة الرئيسية
                contentPanel.Controls.Add(form);
                form.Show();
                form.BringToFront();

                LogManager.LogInfo("تم فتح نافذة: " + form.GetType().Name);
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في فتح النافذة " + form.GetType().Name + ": " + ex.Message);
                MessageBox.Show("خطأ في فتح النافذة: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحميل لوحة المعلومات
        /// </summary>
        private void LoadDashboard()
        {
            try
            {
                // TODO: Create DashboardForm when needed
                MessageBox.Show("لوحة المعلومات - قيد التطوير", "معلومات",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في تحميل لوحة المعلومات: " + ex.Message);
            }
        }

        /// <summary>
        /// تحديث البيانات
        /// </summary>
        private void RefreshData()
        {
            try
            {
                // تحديث معلومات المستخدم
                LoadUserInfo();
                
                // تحديث التنبيهات
                UpdateAlerts();

                lblStatus.Text = "تم التحديث";
                LogManager.LogInfo("تم تحديث البيانات بنجاح");
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في تحديث البيانات: " + ex.Message);
                lblStatus.Text = "خطأ في التحديث";
            }
        }

        #endregion
    }
}



