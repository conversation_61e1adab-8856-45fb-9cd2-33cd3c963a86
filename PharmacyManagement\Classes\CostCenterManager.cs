using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using PharmacyManagement.Models;

namespace PharmacyManagement.Classes
{
    /// <summary>
    /// مدير مراكز التكاليف المحسن - Enhanced Cost Center Manager
    /// تم إزالة جميع التكرارات وتحسين الأداء
    /// </summary>
    public static class CostCenterManager
    {
        #region Cost Center Management - إدارة مراكز التكاليف

        /// <summary>
        /// إضافة مركز تكلفة جديد
        /// </summary>
        /// <param name="costCenter">بيانات مركز التكلفة</param>
        /// <returns>معرف مركز التكلفة الجديد</returns>
        public static int AddCostCenter(CostCenter costCenter)
        {
            try
            {
                // التحقق من صحة البيانات
                ValidateCostCenterData(costCenter);

                // التحقق من عدم تكرار كود مركز التكلفة
                if (IsCostCenterCodeExists(costCenter.CostCenterCode))
                {
                    throw new Exception("كود مركز التكلفة موجود مسبقاً");
                }

                string query = @"
                    INSERT INTO CostCenters (CostCenterCode, CostCenterName, Description, 
                                           ParentCostCenterID, IsActive, CreatedBy, CreatedDate)
                    VALUES (@CostCenterCode, @CostCenterName, @Description, 
                           @ParentCostCenterID, @IsActive, @CreatedBy, @CreatedDate);
                    SELECT SCOPE_IDENTITY();";

                var parameters = new[]
                {
                    DatabaseHelper.CreateParameter("@CostCenterCode", costCenter.CostCenterCode),
                    DatabaseHelper.CreateParameter("@CostCenterName", costCenter.CostCenterName),
                    DatabaseHelper.CreateParameter("@Description", costCenter.Description ?? ""),
                    DatabaseHelper.CreateParameter("@ParentCostCenterID", costCenter.ParentCostCenterID),
                    DatabaseHelper.CreateParameter("@IsActive", costCenter.IsActive),
                    DatabaseHelper.CreateParameter("@CreatedBy", UserManager.CurrentUser?.UserID),
                    DatabaseHelper.CreateParameter("@CreatedDate", DateTime.Now)
                };

                var result = DatabaseHelper.ExecuteScalar(query, parameters);
                int costCenterId = Convert.ToInt32(result);

                if (costCenterId > 0)
                {
                    LogManager.LogInfo($"تم إضافة مركز تكلفة جديد: {costCenter.CostCenterCode} - {costCenter.CostCenterName}");
                }

                return costCenterId;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في إضافة مركز التكلفة: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// تحديث مركز تكلفة موجود
        /// </summary>
        /// <param name="costCenter">بيانات مركز التكلفة المحدثة</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public static bool UpdateCostCenter(CostCenter costCenter)
        {
            try
            {
                // التحقق من صحة البيانات
                ValidateCostCenterData(costCenter);

                // التحقق من عدم تكرار كود مركز التكلفة
                if (IsCostCenterCodeExists(costCenter.CostCenterCode, costCenter.CostCenterID))
                {
                    throw new Exception("كود مركز التكلفة موجود مسبقاً");
                }

                string query = @"
                    UPDATE CostCenters SET 
                        CostCenterCode = @CostCenterCode,
                        CostCenterName = @CostCenterName,
                        Description = @Description,
                        ParentCostCenterID = @ParentCostCenterID,
                        IsActive = @IsActive,
                        ModifiedDate = @ModifiedDate,
                        ModifiedBy = @ModifiedBy
                    WHERE CostCenterID = @CostCenterID";

                var parameters = new[]
                {
                    DatabaseHelper.CreateParameter("@CostCenterID", costCenter.CostCenterID),
                    DatabaseHelper.CreateParameter("@CostCenterCode", costCenter.CostCenterCode),
                    DatabaseHelper.CreateParameter("@CostCenterName", costCenter.CostCenterName),
                    DatabaseHelper.CreateParameter("@Description", costCenter.Description ?? ""),
                    DatabaseHelper.CreateParameter("@ParentCostCenterID", costCenter.ParentCostCenterID),
                    DatabaseHelper.CreateParameter("@IsActive", costCenter.IsActive),
                    DatabaseHelper.CreateParameter("@ModifiedDate", DateTime.Now),
                    DatabaseHelper.CreateParameter("@ModifiedBy", UserManager.CurrentUser?.UserID)
                };

                int rowsAffected = DatabaseHelper.ExecuteNonQuery(query, parameters);

                if (rowsAffected > 0)
                {
                    LogManager.LogInfo($"تم تحديث مركز التكلفة: {costCenter.CostCenterCode} - {costCenter.CostCenterName}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تحديث مركز التكلفة: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// حذف مركز تكلفة
        /// </summary>
        /// <param name="costCenterId">معرف مركز التكلفة</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public static bool DeleteCostCenter(int costCenterId)
        {
            try
            {
                // التحقق من عدم وجود مراكز تكلفة فرعية
                if (HasChildCostCenters(costCenterId))
                {
                    throw new Exception("لا يمكن حذف مركز التكلفة لوجود مراكز تكلفة فرعية");
                }

                // التحقق من عدم وجود معاملات مرتبطة
                if (HasTransactions(costCenterId))
                {
                    throw new Exception("لا يمكن حذف مركز التكلفة لوجود معاملات مرتبطة به");
                }

                string query = "DELETE FROM CostCenters WHERE CostCenterID = @CostCenterID";
                var parameters = new[] { DatabaseHelper.CreateParameter("@CostCenterID", costCenterId) };

                int rowsAffected = DatabaseHelper.ExecuteNonQuery(query, parameters);

                if (rowsAffected > 0)
                {
                    LogManager.LogInfo($"تم حذف مركز التكلفة رقم: {costCenterId}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في حذف مركز التكلفة: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Data Retrieval - استرجاع البيانات

        /// <summary>
        /// الحصول على مركز تكلفة بالمعرف
        /// </summary>
        /// <param name="costCenterId">معرف مركز التكلفة</param>
        /// <returns>بيانات مركز التكلفة</returns>
        public static CostCenter GetCostCenter(int costCenterId)
        {
            try
            {
                string query = @"
                    SELECT cc.*, parent.CostCenterName AS ParentCostCenterName
                    FROM CostCenters cc
                    LEFT JOIN CostCenters parent ON cc.ParentCostCenterID = parent.CostCenterID
                    WHERE cc.CostCenterID = @CostCenterID";

                var parameters = new[] { DatabaseHelper.CreateParameter("@CostCenterID", costCenterId) };

                using (var reader = DatabaseHelper.ExecuteReader(query, parameters))
                {
                    if (reader.Read())
                    {
                        return MapCostCenterFromReader(reader);
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على مركز التكلفة {costCenterId}: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// الحصول على جميع مراكز التكاليف
        /// </summary>
        /// <returns>قائمة جميع مراكز التكاليف</returns>
        public static List<CostCenter> GetAllCostCenters()
        {
            try
            {
                string query = @"
                    SELECT cc.*, parent.CostCenterName AS ParentCostCenterName
                    FROM CostCenters cc
                    LEFT JOIN CostCenters parent ON cc.ParentCostCenterID = parent.CostCenterID
                    ORDER BY cc.CostCenterCode";

                var costCenters = ExecuteCostCenterQuery(query);
                LogManager.LogInfo($"تم استرجاع {costCenters.Count} مركز تكلفة");
                return costCenters;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على جميع مراكز التكاليف: {ex.Message}");
                return new List<CostCenter>();
            }
        }

        /// <summary>
        /// الحصول على مراكز التكاليف النشطة فقط
        /// </summary>
        /// <returns>قائمة مراكز التكاليف النشطة</returns>
        public static List<CostCenter> GetActiveCostCenters()
        {
            try
            {
                string query = @"
                    SELECT cc.*, parent.CostCenterName AS ParentCostCenterName
                    FROM CostCenters cc
                    LEFT JOIN CostCenters parent ON cc.ParentCostCenterID = parent.CostCenterID
                    WHERE cc.IsActive = 1
                    ORDER BY cc.CostCenterCode";

                var costCenters = ExecuteCostCenterQuery(query);
                LogManager.LogInfo($"تم استرجاع {costCenters.Count} مركز تكلفة نشط");
                return costCenters;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على مراكز التكاليف النشطة: {ex.Message}");
                return new List<CostCenter>();
            }
        }

        /// <summary>
        /// الحصول على مراكز التكاليف الفرعية
        /// </summary>
        /// <param name="parentCostCenterId">معرف مركز التكلفة الأب</param>
        /// <returns>قائمة مراكز التكاليف الفرعية</returns>
        public static List<CostCenter> GetChildCostCenters(int parentCostCenterId)
        {
            try
            {
                string query = @"
                    SELECT cc.*, parent.CostCenterName AS ParentCostCenterName
                    FROM CostCenters cc
                    LEFT JOIN CostCenters parent ON cc.ParentCostCenterID = parent.CostCenterID
                    WHERE cc.ParentCostCenterID = @ParentCostCenterID AND cc.IsActive = 1
                    ORDER BY cc.CostCenterCode";

                var parameters = new[] { DatabaseHelper.CreateParameter("@ParentCostCenterID", parentCostCenterId) };
                var costCenters = ExecuteCostCenterQuery(query, parameters);
                
                LogManager.LogInfo($"تم العثور على {costCenters.Count} مركز تكلفة فرعي للمركز: {parentCostCenterId}");
                return costCenters;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على مراكز التكاليف الفرعية للمركز {parentCostCenterId}: {ex.Message}");
                return new List<CostCenter>();
            }
        }

        /// <summary>
        /// البحث في مراكز التكاليف
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <returns>قائمة مراكز التكاليف المطابقة للبحث</returns>
        public static List<CostCenter> SearchCostCenters(string searchTerm)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                {
                    return GetActiveCostCenters();
                }

                string query = @"
                    SELECT cc.*, parent.CostCenterName AS ParentCostCenterName
                    FROM CostCenters cc
                    LEFT JOIN CostCenters parent ON cc.ParentCostCenterID = parent.CostCenterID
                    WHERE (cc.CostCenterCode LIKE @SearchTerm OR 
                           cc.CostCenterName LIKE @SearchTerm OR 
                           cc.Description LIKE @SearchTerm) 
                    AND cc.IsActive = 1
                    ORDER BY cc.CostCenterCode";

                var parameters = new[] { DatabaseHelper.CreateParameter("@SearchTerm", $"%{searchTerm}%") };
                var costCenters = ExecuteCostCenterQuery(query, parameters);
                
                LogManager.LogInfo($"تم العثور على {costCenters.Count} مركز تكلفة مطابق للبحث: {searchTerm}");
                return costCenters;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في البحث في مراكز التكاليف: {ex.Message}");
                return new List<CostCenter>();
            }
        }

        #endregion

        #region Utility Methods - الطرق المساعدة

        /// <summary>
        /// توليد كود مركز تكلفة جديد تلقائياً
        /// </summary>
        /// <param name="parentCostCenterId">معرف مركز التكلفة الأب (اختياري)</param>
        /// <returns>كود مركز التكلفة الجديد</returns>
        public static string GenerateCostCenterCode(int? parentCostCenterId = null)
        {
            try
            {
                string baseCode = "CC";

                // إذا كان هناك مركز تكلفة أب، استخدم كوده كأساس
                if (parentCostCenterId.HasValue)
                {
                    var parentCostCenter = GetCostCenter(parentCostCenterId.Value);
                    if (parentCostCenter != null)
                    {
                        baseCode = parentCostCenter.CostCenterCode;
                    }
                }

                // البحث عن أعلى رقم متاح
                string query = @"
                    SELECT ISNULL(MAX(CAST(SUBSTRING(CostCenterCode, LEN(@BaseCode) + 1, 10) AS INT)), 0)
                    FROM CostCenters
                    WHERE CostCenterCode LIKE @Pattern AND LEN(CostCenterCode) = @Length";

                int nextLevel = baseCode.Length + (parentCostCenterId.HasValue ? 1 : 3);
                var parameters = new[]
                {
                    DatabaseHelper.CreateParameter("@BaseCode", baseCode),
                    DatabaseHelper.CreateParameter("@Pattern", baseCode + "%"),
                    DatabaseHelper.CreateParameter("@Length", nextLevel)
                };

                var result = DatabaseHelper.ExecuteScalar(query, parameters);
                int nextNumber = Convert.ToInt32(result) + 1;

                string newCode = parentCostCenterId.HasValue ?
                    baseCode + nextNumber.ToString() :
                    "CC" + nextNumber.ToString("D3");

                LogManager.LogInfo("تم توليد كود مركز تكلفة جديد: " + newCode);
                return newCode;
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في توليد كود مركز التكلفة: " + ex.Message);
                return "CC" + DateTime.Now.Ticks.ToString().Substring(10);
            }
        }

        /// <summary>
        /// التحقق من وجود كود مركز التكلفة
        /// </summary>
        /// <param name="costCenterCode">كود مركز التكلفة</param>
        /// <param name="excludeCostCenterId">معرف مركز التكلفة المستثنى (للتحديث)</param>
        /// <returns>true إذا كان الكود موجود</returns>
        public static bool IsCostCenterCodeExists(string costCenterCode, int? excludeCostCenterId = null)
        {
            try
            {
                string query = "SELECT COUNT(*) FROM CostCenters WHERE CostCenterCode = @CostCenterCode";
                var parameters = new List<SqlParameter>
                {
                    DatabaseHelper.CreateParameter("@CostCenterCode", costCenterCode)
                };

                if (excludeCostCenterId.HasValue)
                {
                    query += " AND CostCenterID != @ExcludeCostCenterID";
                    parameters.Add(DatabaseHelper.CreateParameter("@ExcludeCostCenterID", excludeCostCenterId.Value));
                }

                var result = DatabaseHelper.ExecuteScalar(query, parameters.ToArray());
                return Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في التحقق من كود مركز التكلفة: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Private Helper Methods - الطرق المساعدة الخاصة

        /// <summary>
        /// التحقق من صحة بيانات مركز التكلفة
        /// </summary>
        /// <param name="costCenter">بيانات مركز التكلفة</param>
        private static void ValidateCostCenterData(CostCenter costCenter)
        {
            if (costCenter == null)
                throw new ArgumentNullException(nameof(costCenter), "بيانات مركز التكلفة مطلوبة");

            if (string.IsNullOrWhiteSpace(costCenter.CostCenterCode))
                throw new ArgumentException("كود مركز التكلفة مطلوب", nameof(costCenter.CostCenterCode));

            if (string.IsNullOrWhiteSpace(costCenter.CostCenterName))
                throw new ArgumentException("اسم مركز التكلفة مطلوب", nameof(costCenter.CostCenterName));

            if (costCenter.CostCenterCode.Length > 20)
                throw new ArgumentException("كود مركز التكلفة يجب أن يكون أقل من 20 حرف", nameof(costCenter.CostCenterCode));

            if (costCenter.CostCenterName.Length > 200)
                throw new ArgumentException("اسم مركز التكلفة يجب أن يكون أقل من 200 حرف", nameof(costCenter.CostCenterName));
        }

        /// <summary>
        /// التحقق من وجود مراكز تكلفة فرعية
        /// </summary>
        /// <param name="costCenterId">معرف مركز التكلفة</param>
        /// <returns>true إذا كان يوجد مراكز تكلفة فرعية</returns>
        private static bool HasChildCostCenters(int costCenterId)
        {
            try
            {
                string query = "SELECT COUNT(*) FROM CostCenters WHERE ParentCostCenterID = @CostCenterID";
                var parameters = new[] { DatabaseHelper.CreateParameter("@CostCenterID", costCenterId) };

                var result = DatabaseHelper.ExecuteScalar(query, parameters);
                return Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في التحقق من مراكز التكلفة الفرعية: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// التحقق من وجود معاملات مرتبطة بمركز التكلفة
        /// </summary>
        /// <param name="costCenterId">معرف مركز التكلفة</param>
        /// <returns>true إذا كان يوجد معاملات مرتبطة</returns>
        private static bool HasTransactions(int costCenterId)
        {
            try
            {
                string query = @"
                    SELECT COUNT(*) FROM (
                        SELECT 1 FROM Vouchers WHERE CostCenterID = @CostCenterID
                        UNION ALL
                        SELECT 1 FROM VoucherDetails WHERE CostCenterID = @CostCenterID
                        UNION ALL
                        SELECT 1 FROM SalesInvoices WHERE CostCenterID = @CostCenterID
                        UNION ALL
                        SELECT 1 FROM PurchaseInvoices WHERE CostCenterID = @CostCenterID
                        UNION ALL
                        SELECT 1 FROM Expenses WHERE CostCenterID = @CostCenterID
                    ) AS Transactions";

                var parameters = new[] { DatabaseHelper.CreateParameter("@CostCenterID", costCenterId) };
                var result = DatabaseHelper.ExecuteScalar(query, parameters);
                return Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في التحقق من المعاملات المرتبطة: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تنفيذ استعلام مراكز التكاليف وإرجاع النتائج
        /// </summary>
        /// <param name="query">الاستعلام</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>قائمة مراكز التكاليف</returns>
        private static List<CostCenter> ExecuteCostCenterQuery(string query, SqlParameter[] parameters = null)
        {
            var costCenters = new List<CostCenter>();
            try
            {
                using (var reader = DatabaseHelper.ExecuteReader(query, parameters))
                {
                    while (reader.Read())
                    {
                        costCenters.Add(MapCostCenterFromReader(reader));
                    }
                }
                return costCenters;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تنفيذ استعلام مراكز التكاليف: {ex.Message}");
                return costCenters;
            }
        }

        /// <summary>
        /// تحويل بيانات القارئ إلى كائن مركز تكلفة
        /// </summary>
        /// <param name="reader">قارئ البيانات</param>
        /// <returns>كائن مركز التكلفة</returns>
        private static CostCenter MapCostCenterFromReader(SqlDataReader reader)
        {
            return new CostCenter
            {
                CostCenterID = Convert.ToInt32(reader["CostCenterID"]),
                CostCenterCode = reader["CostCenterCode"].ToString(),
                CostCenterName = reader["CostCenterName"].ToString(),
                Description = reader["Description"] == DBNull.Value ? null : reader["Description"].ToString(),
                ParentCostCenterID = reader["ParentCostCenterID"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["ParentCostCenterID"]),
                IsActive = Convert.ToBoolean(reader["IsActive"]),
                CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                CreatedBy = reader["CreatedBy"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["CreatedBy"])
            };
        }

        #endregion

        #region Statistics and Reports - الإحصائيات والتقارير

        /// <summary>
        /// الحصول على إحصائيات مراكز التكاليف
        /// </summary>
        /// <returns>إحصائيات مراكز التكاليف</returns>
        public static CostCenterStatistics GetCostCenterStatistics()
        {
            try
            {
                string query = @"
                    SELECT
                        COUNT(*) as TotalCostCenters,
                        SUM(CASE WHEN IsActive = 1 THEN 1 ELSE 0 END) as ActiveCostCenters,
                        SUM(CASE WHEN IsActive = 0 THEN 1 ELSE 0 END) as InactiveCostCenters,
                        SUM(CASE WHEN ParentCostCenterID IS NULL THEN 1 ELSE 0 END) as MainCostCenters,
                        SUM(CASE WHEN ParentCostCenterID IS NOT NULL THEN 1 ELSE 0 END) as SubCostCenters
                    FROM CostCenters";

                using (var reader = DatabaseHelper.ExecuteReader(query))
                {
                    if (reader.Read())
                    {
                        return new CostCenterStatistics
                        {
                            TotalCostCenters = Convert.ToInt32(reader["TotalCostCenters"]),
                            ActiveCostCenters = Convert.ToInt32(reader["ActiveCostCenters"]),
                            InactiveCostCenters = Convert.ToInt32(reader["InactiveCostCenters"]),
                            MainCostCenters = Convert.ToInt32(reader["MainCostCenters"]),
                            SubCostCenters = Convert.ToInt32(reader["SubCostCenters"])
                        };
                    }
                }

                return new CostCenterStatistics();
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على إحصائيات مراكز التكاليف: {ex.Message}");
                return new CostCenterStatistics();
            }
        }

        /// <summary>
        /// الحصول على مراكز التكاليف الرئيسية
        /// </summary>
        /// <returns>قائمة مراكز التكاليف الرئيسية</returns>
        public static List<CostCenter> GetMainCostCenters()
        {
            try
            {
                string query = @"
                    SELECT cc.*, parent.CostCenterName AS ParentCostCenterName
                    FROM CostCenters cc
                    LEFT JOIN CostCenters parent ON cc.ParentCostCenterID = parent.CostCenterID
                    WHERE cc.ParentCostCenterID IS NULL AND cc.IsActive = 1
                    ORDER BY cc.CostCenterCode";

                return ExecuteCostCenterQuery(query);
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على مراكز التكاليف الرئيسية: {ex.Message}");
                return new List<CostCenter>();
            }
        }

        #endregion
    }

    #region Helper Models - النماذج المساعدة

    /// <summary>
    /// إحصائيات مراكز التكاليف
    /// </summary>
    public class CostCenterStatistics
    {
        public int TotalCostCenters { get; set; }
        public int ActiveCostCenters { get; set; }
        public int InactiveCostCenters { get; set; }
        public int MainCostCenters { get; set; }
        public int SubCostCenters { get; set; }
    }

    #endregion
}
