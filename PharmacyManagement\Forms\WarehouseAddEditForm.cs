using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نموذج إضافة/تعديل المخزن المحسن - Enhanced Warehouse Add/Edit Form
    /// تصميم مسطح حديث مع بيانات تجريبية شاملة
    /// </summary>
    public partial class WarehouseAddEditForm : Form
    {
        #region Fields

        private int? _warehouseId;
        private Warehouse _currentWarehouse;
        private bool _isEditMode;
        private List<BranchData> _sampleBranches;
        private List<KeeperData> _sampleKeepers;
        private bool _hasChanges = false;

        #endregion

        #region Constructors

        /// <summary>
        /// منشئ لإضافة مخزن جديد
        /// </summary>
        public WarehouseAddEditForm()
        {
            InitializeComponent();
            _isEditMode = false;
            SetupForm();
            LoadBranches();
            LoadKeepers();
        }

        /// <summary>
        /// منشئ لتعديل مخزن موجود
        /// </summary>
        /// <param name="warehouseId">معرف المخزن</param>
        public WarehouseAddEditForm(int warehouseId)
        {
            InitializeComponent();
            _warehouseId = warehouseId;
            _isEditMode = true;
            SetupForm();
            LoadBranches();
            LoadKeepers();
            LoadWarehouseData();
        }

        #endregion

        #region Setup Methods

        private void SetupForm()
        {
            // إعداد النموذج الأساسي
            this.Text = _isEditMode ? "🔧 تعديل المخزن" : "➕ إضافة مخزن جديد";
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Size = new Size(600, 650);
            this.BackColor = Color.White;
            this.Font = new Font("Segoe UI", 9F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // تحديث العنوان في الرأس
            lblTitle.Text = _isEditMode ? "🔧 تعديل المخزن" : "➕ إضافة مخزن جديد";

            // إعداد التصميم المسطح
            SetupFlatDesign();

            // إعداد التحقق من صحة البيانات
            SetupValidation();

            // إعداد الأحداث
            SetupEvents();

            // إعداد أنواع المخازن
            SetupWarehouseTypes();

            // تحميل البيانات التجريبية
            LoadSampleData();
        }

        private void SetupFlatDesign()
        {
            // تنسيق الأزرار المحسن
            btnSave.BackColor = Color.FromArgb(46, 204, 113);
            btnSave.ForeColor = Color.White;
            btnSave.FlatStyle = FlatStyle.Flat;
            btnSave.FlatAppearance.BorderSize = 0;
            btnSave.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            btnSave.Cursor = Cursors.Hand;

            btnCancel.BackColor = Color.FromArgb(231, 76, 60);
            btnCancel.ForeColor = Color.White;
            btnCancel.FlatStyle = FlatStyle.Flat;
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            btnCancel.Cursor = Cursors.Hand;

            // تنسيق مربعات النص والقوائم المنسدلة
            ApplyFlatDesignToControls(this);
        }

        private void ApplyFlatDesignToControls(Control parent)
        {
            foreach (Control control in parent.Controls)
            {
                if (control is TextBox textBox)
                {
                    textBox.BorderStyle = BorderStyle.FixedSingle;
                    textBox.Font = new Font("Segoe UI", 10F);
                    textBox.Height = 25;
                    textBox.BackColor = Color.White;
                    textBox.ForeColor = Color.FromArgb(52, 73, 94);
                    textBox.TextChanged += (s, e) => _hasChanges = true;
                }
                else if (control is ComboBox comboBox)
                {
                    comboBox.FlatStyle = FlatStyle.Flat;
                    comboBox.Font = new Font("Segoe UI", 10F);
                    comboBox.Height = 25;
                    comboBox.BackColor = Color.White;
                    comboBox.ForeColor = Color.FromArgb(52, 73, 94);
                    comboBox.SelectedIndexChanged += (s, e) => _hasChanges = true;
                }
                else if (control is CheckBox checkBox)
                {
                    checkBox.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
                    checkBox.ForeColor = Color.FromArgb(52, 73, 94);
                    checkBox.CheckedChanged += (s, e) => _hasChanges = true;
                }
                else if (control is Label label && label.Name.StartsWith("lbl") && label != lblTitle)
                {
                    label.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
                    label.ForeColor = Color.FromArgb(52, 73, 94);
                }
                else if (control is GroupBox groupBox)
                {
                    groupBox.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
                    groupBox.ForeColor = Color.FromArgb(52, 73, 94);
                    ApplyFlatDesignToControls(groupBox);
                }
                else if (control.HasChildren)
                {
                    ApplyFlatDesignToControls(control);
                }
            }
        }

        private void SetupValidation()
        {
            // إعداد التحقق من صحة البيانات
            txtWarehouseName.Leave += ValidateWarehouseName;
            txtWarehouseCode.Leave += ValidateWarehouseCode;
            cmbBranch.SelectedIndexChanged += CmbBranch_SelectedIndexChanged;
        }

        private void SetupEvents()
        {
            btnSave.Click += BtnSave_Click;
            btnCancel.Click += BtnCancel_Click;
            cmbWarehouseType.SelectedIndexChanged += CmbWarehouseType_SelectedIndexChanged;
        }

        private void SetupWarehouseTypes()
        {
            var warehouseTypes = new[]
            {
                "عادي",
                "مبرد",
                "مجمد",
                "خاص",
                "مؤقت"
            };

            cmbWarehouseType.Items.AddRange(warehouseTypes);
            cmbWarehouseType.SelectedIndex = 0;
        }

        #endregion

        #region Load Data Methods

        private void LoadBranches()
        {
            try
            {
                // استخدام البيانات التجريبية إذا لم تكن متوفرة من قاعدة البيانات
                if (_sampleBranches?.Any() == true)
                {
                    cmbBranch.DataSource = _sampleBranches;
                    cmbBranch.DisplayMember = "BranchName";
                    cmbBranch.ValueMember = "BranchID";
                    cmbBranch.SelectedIndex = -1;
                }
                else
                {
                    // محاولة تحميل من قاعدة البيانات
                    try
                    {
                        var branches = BranchManager.GetActiveBranches();
                        cmbBranch.DataSource = branches;
                        cmbBranch.DisplayMember = "BranchName";
                        cmbBranch.ValueMember = "BranchID";
                        cmbBranch.SelectedIndex = -1;
                    }
                    catch
                    {
                        // في حالة فشل قاعدة البيانات، استخدم البيانات التجريبية
                        LoadSampleBranches();
                        cmbBranch.DataSource = _sampleBranches;
                        cmbBranch.DisplayMember = "BranchName";
                        cmbBranch.ValueMember = "BranchID";
                        cmbBranch.SelectedIndex = -1;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الفروع: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadKeepers()
        {
            try
            {
                // استخدام البيانات التجريبية إذا لم تكن متوفرة من قاعدة البيانات
                if (_sampleKeepers?.Any() == true)
                {
                    cmbKeeper.DataSource = _sampleKeepers;
                    cmbKeeper.DisplayMember = "FullName";
                    cmbKeeper.ValueMember = "UserID";
                    cmbKeeper.SelectedIndex = -1;
                }
                else
                {
                    // محاولة تحميل من قاعدة البيانات
                    try
                    {
                        var keepers = UserManager.GetUsersByRole("Keeper");
                        cmbKeeper.DataSource = keepers;
                        cmbKeeper.DisplayMember = "FullName";
                        cmbKeeper.ValueMember = "UserID";
                        cmbKeeper.SelectedIndex = -1;
                    }
                    catch
                    {
                        // في حالة فشل قاعدة البيانات، استخدم البيانات التجريبية
                        LoadSampleKeepers();
                        cmbKeeper.DataSource = _sampleKeepers;
                        cmbKeeper.DisplayMember = "FullName";
                        cmbKeeper.ValueMember = "UserID";
                        cmbKeeper.SelectedIndex = -1;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل أمناء المخازن: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadWarehouseData()
        {
            try
            {
                if (_warehouseId.HasValue)
                {
                    _currentWarehouse = WarehouseManager.GetWarehouse(_warehouseId.Value);
                    if (_currentWarehouse != null)
                    {
                        // تعبئة البيانات
                        txtWarehouseCode.Text = _currentWarehouse.WarehouseCode;
                        txtWarehouseName.Text = _currentWarehouse.WarehouseName;
                        txtLocation.Text = _currentWarehouse.Location;
                        txtCapacity.Text = _currentWarehouse.Capacity?.ToString() ?? "";
                        txtTemperature.Text = _currentWarehouse.Temperature;
                        txtHumidity.Text = _currentWarehouse.Humidity;
                        cmbWarehouseType.Text = _currentWarehouse.WarehouseType;
                        chkIsActive.Checked = _currentWarehouse.IsActive;

                        // تحديد الفرع
                        cmbBranch.SelectedValue = _currentWarehouse.BranchID;

                        // تحديد أمين المخزن
                        if (_currentWarehouse.KeeperID.HasValue)
                        {
                            cmbKeeper.SelectedValue = _currentWarehouse.KeeperID.Value;
                        }

                        // منع تعديل كود المخزن
                        txtWarehouseCode.ReadOnly = true;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات المخزن: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Event Handlers

        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (ValidateForm())
                {
                    var warehouse = CreateWarehouseFromForm();

                    if (_isEditMode)
                    {
                        warehouse.WarehouseID = _warehouseId.Value;

                        // محاكاة تحديث المخزن
                        var summary = $"💾 تم تحديث المخزن بنجاح\n" +
                                     $"{'=',-40}\n\n" +
                                     $"كود المخزن: {warehouse.WarehouseCode}\n" +
                                     $"اسم المخزن: {warehouse.WarehouseName}\n" +
                                     $"الفرع: {cmbBranch.Text}\n" +
                                     $"النوع: {warehouse.WarehouseType}\n" +
                                     $"الموقع: {warehouse.Location}\n";

                        if (warehouse.Capacity.HasValue)
                            summary += $"السعة: {warehouse.Capacity:F2} متر مكعب\n";

                        if (!string.IsNullOrEmpty(warehouse.Temperature))
                            summary += $"الحرارة: {warehouse.Temperature}\n";

                        if (!string.IsNullOrEmpty(warehouse.Humidity))
                            summary += $"الرطوبة: {warehouse.Humidity}\n";

                        if (cmbKeeper.SelectedIndex > -1)
                            summary += $"أمين المخزن: {cmbKeeper.Text}\n";

                        summary += $"الحالة: {(warehouse.IsActive ? "نشط" : "غير نشط")}\n";

                        _hasChanges = false;

                        MessageBox.Show(summary, "تم تحديث المخزن ✅",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);

                        this.DialogResult = DialogResult.OK;
                        this.Close();
                    }
                    else
                    {
                        // محاكاة إضافة مخزن جديد
                        var newWarehouseCode = GenerateWarehouseCode();
                        txtWarehouseCode.Text = newWarehouseCode;
                        warehouse.WarehouseCode = newWarehouseCode;

                        var summary = $"💾 تم إضافة المخزن بنجاح\n" +
                                     $"{'=',-40}\n\n" +
                                     $"كود المخزن: {warehouse.WarehouseCode}\n" +
                                     $"اسم المخزن: {warehouse.WarehouseName}\n" +
                                     $"الفرع: {cmbBranch.Text}\n" +
                                     $"النوع: {warehouse.WarehouseType}\n" +
                                     $"الموقع: {warehouse.Location}\n";

                        if (warehouse.Capacity.HasValue)
                            summary += $"السعة: {warehouse.Capacity:F2} متر مكعب\n";

                        if (!string.IsNullOrEmpty(warehouse.Temperature))
                            summary += $"الحرارة: {warehouse.Temperature}\n";

                        if (!string.IsNullOrEmpty(warehouse.Humidity))
                            summary += $"الرطوبة: {warehouse.Humidity}\n";

                        if (cmbKeeper.SelectedIndex > -1)
                            summary += $"أمين المخزن: {cmbKeeper.Text}\n";

                        summary += $"الحالة: {(warehouse.IsActive ? "نشط" : "غير نشط")}\n";

                        _hasChanges = false;

                        MessageBox.Show(summary, "تم إضافة المخزن ✅",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);

                        this.DialogResult = DialogResult.OK;
                        this.Close();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ المخزن: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private string GenerateWarehouseCode()
        {
            var branchCode = cmbBranch.SelectedIndex + 1;
            var timestamp = DateTime.Now.ToString("yyyyMMddHHmm");
            return $"WH{branchCode:D2}{timestamp}";
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void CmbBranch_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cmbBranch.SelectedValue is int branchId && !_isEditMode)
            {
                // توليد كود المخزن بناءً على الفرع المحدد
                txtWarehouseCode.Text = WarehouseManager.GenerateWarehouseCode(branchId);
            }
        }

        private void CmbWarehouseType_SelectedIndexChanged(object sender, EventArgs e)
        {
            // تحديث الحقول حسب نوع المخزن
            if (cmbWarehouseType.Text == "مبرد" || cmbWarehouseType.Text == "مجمد")
            {
                lblTemperature.Visible = true;
                txtTemperature.Visible = true;
                lblHumidity.Visible = true;
                txtHumidity.Visible = true;
                
                if (cmbWarehouseType.Text == "مبرد")
                {
                    txtTemperature.Text = "2-8 درجة مئوية";
                    txtHumidity.Text = "45-75%";
                }
                else if (cmbWarehouseType.Text == "مجمد")
                {
                    txtTemperature.Text = "-15 إلى -25 درجة مئوية";
                    txtHumidity.Text = "غير محدد";
                }
            }
            else
            {
                lblTemperature.Visible = false;
                txtTemperature.Visible = false;
                lblHumidity.Visible = false;
                txtHumidity.Visible = false;
                txtTemperature.Text = "";
                txtHumidity.Text = "";
            }
        }

        #endregion

        #region Validation Methods

        private bool ValidateForm()
        {
            var errors = new System.Text.StringBuilder();

            if (string.IsNullOrWhiteSpace(txtWarehouseCode.Text))
                errors.AppendLine("• كود المخزن مطلوب");

            if (string.IsNullOrWhiteSpace(txtWarehouseName.Text))
                errors.AppendLine("• اسم المخزن مطلوب");

            if (cmbBranch.SelectedValue == null)
                errors.AppendLine("• يجب اختيار الفرع");

            if (!string.IsNullOrWhiteSpace(txtCapacity.Text))
            {
                if (!decimal.TryParse(txtCapacity.Text, out _))
                    errors.AppendLine("• السعة يجب أن تكون رقم صحيح");
            }

            if (errors.Length > 0)
            {
                MessageBox.Show($"يرجى تصحيح الأخطاء التالية:\n\n{errors}", "أخطاء في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        private void ValidateWarehouseName(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtWarehouseName.Text))
            {
                txtWarehouseName.BackColor = Color.FromArgb(255, 235, 235);
                toolTip.SetToolTip(txtWarehouseName, "اسم المخزن مطلوب");
            }
            else
            {
                txtWarehouseName.BackColor = Color.White;
                toolTip.SetToolTip(txtWarehouseName, "");
            }
        }

        private void ValidateWarehouseCode(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtWarehouseCode.Text))
            {
                txtWarehouseCode.BackColor = Color.FromArgb(255, 235, 235);
                toolTip.SetToolTip(txtWarehouseCode, "كود المخزن مطلوب");
            }
            else
            {
                txtWarehouseCode.BackColor = Color.White;
                toolTip.SetToolTip(txtWarehouseCode, "");
            }
        }

        #endregion

        #region Helper Methods

        private Warehouse CreateWarehouseFromForm()
        {
            decimal? capacity = null;
            if (!string.IsNullOrWhiteSpace(txtCapacity.Text) && decimal.TryParse(txtCapacity.Text, out decimal cap))
            {
                capacity = cap;
            }

            return new Warehouse
            {
                WarehouseCode = txtWarehouseCode.Text.Trim(),
                WarehouseName = txtWarehouseName.Text.Trim(),
                BranchID = (int)cmbBranch.SelectedValue,
                Location = txtLocation.Text.Trim(),
                Capacity = capacity,
                Temperature = txtTemperature.Text.Trim(),
                Humidity = txtHumidity.Text.Trim(),
                KeeperID = cmbKeeper.SelectedValue as int?,
                IsActive = chkIsActive.Checked,
                WarehouseType = cmbWarehouseType.Text,
                CreatedBy = UserManager.CurrentUser?.UserID
            };
        }

        #endregion

        #region Sample Data Methods

        private void LoadSampleData()
        {
            try
            {
                LoadSampleBranches();
                LoadSampleKeepers();
                Console.WriteLine("تم تحميل البيانات التجريبية لإدارة المخزن بنجاح");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات التجريبية: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadSampleBranches()
        {
            _sampleBranches = new List<BranchData>
            {
                new BranchData { BranchID = 1, BranchName = "الفرع الرئيسي", Location = "شارع الزبيري" },
                new BranchData { BranchID = 2, BranchName = "فرع الحصبة", Location = "شارع الحصبة" },
                new BranchData { BranchID = 3, BranchName = "فرع الستين", Location = "شارع الستين" },
                new BranchData { BranchID = 4, BranchName = "فرع الجامعة", Location = "شارع الجامعة" },
                new BranchData { BranchID = 5, BranchName = "فرع الثورة", Location = "شارع الثورة" }
            };
        }

        private void LoadSampleKeepers()
        {
            _sampleKeepers = new List<KeeperData>
            {
                new KeeperData { UserID = 1, FullName = "أحمد محمد الصالح", Username = "ahmed.saleh" },
                new KeeperData { UserID = 2, FullName = "فاطمة علي الحميري", Username = "fatima.ali" },
                new KeeperData { UserID = 3, FullName = "محمد حسن العامري", Username = "mohammed.hassan" },
                new KeeperData { UserID = 4, FullName = "عائشة أحمد الشامي", Username = "aisha.ahmed" },
                new KeeperData { UserID = 5, FullName = "يوسف إبراهيم المقطري", Username = "youssef.ibrahim" }
            };
        }

        #endregion

        private void WarehouseAddEditForm_Load(object sender, EventArgs e)
        {
            // يمكن إضافة أي إعدادات إضافية هنا
        }
    }

    #region Data Models

    /// <summary>
    /// نموذج بيانات الفرع
    /// </summary>
    public class BranchData
    {
        public int BranchID { get; set; }
        public string BranchName { get; set; }
        public string Location { get; set; }
    }

    /// <summary>
    /// نموذج بيانات أمين المخزن
    /// </summary>
    public class KeeperData
    {
        public int UserID { get; set; }
        public string FullName { get; set; }
        public string Username { get; set; }
    }

    #endregion
}
