using System;
using System.Collections.Generic;

namespace PharmacyManagement.Models
{
    /// <summary>
    /// نموذج الملخص المالي
    /// </summary>
    public class FinancialSummary
    {
        /// <summary>
        /// إجمالي المبيعات
        /// </summary>
        public decimal TotalSales { get; set; }

        /// <summary>
        /// إجمالي المشتريات
        /// </summary>
        public decimal TotalPurchases { get; set; }

        /// <summary>
        /// إجمالي المصروفات
        /// </summary>
        public decimal TotalExpenses { get; set; }

        /// <summary>
        /// إجمالي الأرباح
        /// </summary>
        public decimal TotalProfit { get; set; }

        /// <summary>
        /// صافي الربح
        /// </summary>
        public decimal NetProfit { get; set; }

        /// <summary>
        /// هامش الربح الإجمالي
        /// </summary>
        public decimal GrossProfitMargin
        {
            get
            {
                return TotalSales > 0 ? (TotalProfit / TotalSales) * 100 : 0;
            }
        }

        /// <summary>
        /// هامش الربح الصافي
        /// </summary>
        public decimal NetProfitMargin
        {
            get
            {
                return TotalSales > 0 ? (NetProfit / TotalSales) * 100 : 0;
            }
        }
    }







    /// <summary>
    /// نموذج السنة المالية - Fiscal Year
    /// </summary>
    public class FiscalYear
    {
        public int FiscalYearID { get; set; }
        public string YearName { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public bool IsActive { get; set; }
        public bool IsClosed { get; set; }
        public DateTime CreatedDate { get; set; }
        public int? CreatedBy { get; set; }
        public DateTime? ClosedDate { get; set; }
        public int? ClosedBy { get; set; }
    }



    /// <summary>
    /// نموذج الدليل المحاسبي - Chart of Accounts
    /// </summary>
    public class ChartOfAccount
    {
        public int AccountID { get; set; }
        public string AccountCode { get; set; }
        public string AccountName { get; set; }
        public string AccountType { get; set; } // Asset, Liability, Equity, Revenue, Expense
        public int? ParentAccountID { get; set; }
        public int Level { get; set; }
        public bool IsActive { get; set; }
        public bool IsSystemAccount { get; set; }
        public string Description { get; set; }
        public DateTime CreatedDate { get; set; }
        public int? CreatedBy { get; set; }
    }

    /// <summary>
    /// نموذج نوع السند - Voucher Type
    /// </summary>
    public class VoucherType
    {
        public int VoucherTypeID { get; set; }
        public string VoucherTypeCode { get; set; }
        public string VoucherTypeName { get; set; }
        public string Description { get; set; }
        public bool IsActive { get; set; }
    }



    /// <summary>
    /// نموذج تفاصيل السند - Voucher Detail
    /// </summary>
    public class VoucherDetail
    {
        public int VoucherDetailID { get; set; }
        public int VoucherID { get; set; }
        public int AccountID { get; set; }
        public string Description { get; set; }
        public decimal DebitAmount { get; set; }
        public decimal CreditAmount { get; set; }
        public int? CostCenterID { get; set; }
    }



    /// <summary>
    /// نموذج سند القبض - Receipt Voucher
    /// </summary>
    public class ReceiptVoucher
    {
        public int ReceiptID { get; set; }
        public string ReceiptNumber { get; set; }
        public DateTime ReceiptDate { get; set; }
        public int FiscalYearID { get; set; }
        public int? CustomerID { get; set; }
        public decimal Amount { get; set; }
        public string PaymentMethod { get; set; } // Cash, Bank, Card
        public string BankName { get; set; }
        public string CheckNumber { get; set; }
        public DateTime? CheckDate { get; set; }
        public string Description { get; set; }
        public int? VoucherID { get; set; }
        public string Status { get; set; } // Active, Cancelled
        public DateTime CreatedDate { get; set; }
        public int? CreatedBy { get; set; }
    }

    /// <summary>
    /// نموذج سند الصرف - Payment Voucher
    /// </summary>
    public class PaymentVoucher
    {
        public int PaymentID { get; set; }
        public string PaymentNumber { get; set; }
        public DateTime PaymentDate { get; set; }
        public int FiscalYearID { get; set; }
        public int? SupplierID { get; set; }
        public decimal Amount { get; set; }
        public string PaymentMethod { get; set; } // Cash, Bank, Card
        public string BankName { get; set; }
        public string CheckNumber { get; set; }
        public DateTime? CheckDate { get; set; }
        public string Description { get; set; }
        public int? VoucherID { get; set; }
        public string Status { get; set; } // Active, Cancelled
        public DateTime CreatedDate { get; set; }
        public int? CreatedBy { get; set; }
    }

    /// <summary>
    /// نموذج فئة المصروفات - Expense Category
    /// </summary>
    public class ExpenseCategory
    {
        public int CategoryID { get; set; }
        public string CategoryCode { get; set; }
        public string CategoryName { get; set; }
        public int? AccountID { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedDate { get; set; }
    }

    /// <summary>
    /// نموذج السند المحاسبي - Voucher
    /// </summary>
    public class Voucher
    {
        public int VoucherID { get; set; }
        public string VoucherNumber { get; set; }
        public DateTime VoucherDate { get; set; }
        public int FiscalYearID { get; set; }
        public int VoucherTypeID { get; set; }
        public string VoucherType { get; set; }
        public string Description { get; set; }
        public decimal TotalDebit { get; set; }
        public decimal TotalCredit { get; set; }
        public decimal TotalAmount { get; set; }
        public int? CostCenterID { get; set; }
        public string ReferenceType { get; set; }
        public int? ReferenceID { get; set; }
        public string Status { get; set; }
        public int CreatedBy { get; set; }
        public DateTime CreatedDate { get; set; }
        public int? ModifiedBy { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public DateTime? PostedDate { get; set; }
        public int? PostedBy { get; set; }
        public string Reference { get; set; }
        public List<VoucherDetail> Details { get; set; }

        public Voucher()
        {
            Details = new List<VoucherDetail>();
            VoucherDate = DateTime.Now;
            CreatedDate = DateTime.Now;
            Status = "Draft";
            TotalDebit = 0;
            TotalCredit = 0;
            TotalAmount = 0;
        }
    }

    /// <summary>
    /// نموذج مركز التكلفة - Cost Center
    /// </summary>
    public class CostCenter
    {
        public int CostCenterID { get; set; }
        public string CostCenterCode { get; set; }
        public string CostCenterName { get; set; }
        public string Description { get; set; }
        public int? ParentCostCenterID { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedDate { get; set; }
        public int? CreatedBy { get; set; }
    }

    /// <summary>
    /// نموذج المصروف - Expense
    /// </summary>
    public class Expense
    {
        public int ExpenseID { get; set; }
        public string ExpenseNumber { get; set; }
        public string Description { get; set; }
        public decimal Amount { get; set; }
        public string Category { get; set; }
        public DateTime ExpenseDate { get; set; }
        public string PaymentMethod { get; set; }
        public string Vendor { get; set; }
        public string InvoiceReference { get; set; }
        public string Notes { get; set; }
        public bool IsApproved { get; set; }
        public DateTime CreatedDate { get; set; }
        public int? CreatedBy { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public int? ModifiedBy { get; set; }
    }
}


