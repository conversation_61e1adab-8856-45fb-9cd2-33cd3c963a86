using System;

namespace PharmacyManagement.Models
{
    /// <summary>
    /// نموذج العملة - Currency Model
    /// </summary>
    public class Currency
    {
        #region Properties - الخصائص

        /// <summary>
        /// معرف العملة
        /// </summary>
        public int CurrencyID { get; set; }

        /// <summary>
        /// كود العملة (مثل USD, EUR, YER)
        /// </summary>
        public string CurrencyCode { get; set; }

        /// <summary>
        /// اسم العملة
        /// </summary>
        public string CurrencyName { get; set; }

        /// <summary>
        /// رمز العملة (مثل $, €, ﷼)
        /// </summary>
        public string CurrencySymbol { get; set; }

        /// <summary>
        /// سعر الصرف مقابل العملة الأساسية
        /// </summary>
        public decimal ExchangeRate { get; set; }

        /// <summary>
        /// هل هي العملة الأساسية
        /// </summary>
        public bool IsBaseCurrency { get; set; }

        /// <summary>
        /// هل العملة نشطة
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// عدد الخانات العشرية
        /// </summary>
        public int DecimalPlaces { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// تاريخ آخر تعديل
        /// </summary>
        public DateTime? ModifiedDate { get; set; }

        /// <summary>
        /// تاريخ آخر تحديث لسعر الصرف
        /// </summary>
        public DateTime? LastRateUpdate { get; set; }

        #endregion

        #region Constructors - المنشئات

        /// <summary>
        /// منشئ افتراضي
        /// </summary>
        public Currency()
        {
            CurrencyCode = string.Empty;
            CurrencyName = string.Empty;
            CurrencySymbol = string.Empty;
            ExchangeRate = 1;
            IsBaseCurrency = false;
            IsActive = true;
            DecimalPlaces = 2;
            CreatedDate = DateTime.Now;
        }

        /// <summary>
        /// منشئ مع المعاملات الأساسية
        /// </summary>
        /// <param name="code">كود العملة</param>
        /// <param name="name">اسم العملة</param>
        /// <param name="symbol">رمز العملة</param>
        /// <param name="rate">سعر الصرف</param>
        public Currency(string code, string name, string symbol, decimal rate)
        {
            CurrencyCode = code;
            CurrencyName = name;
            CurrencySymbol = symbol;
            ExchangeRate = rate;
            IsBaseCurrency = false;
            IsActive = true;
            DecimalPlaces = 2;
            CreatedDate = DateTime.Now;
        }

        #endregion

        #region Methods - الطرق

        /// <summary>
        /// تمثيل نصي للعملة
        /// </summary>
        /// <returns>كود العملة واسمها</returns>
        public override string ToString()
        {
            return CurrencyCode + " - " + CurrencyName;
        }

        /// <summary>
        /// التحقق من صحة بيانات العملة
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(CurrencyCode) &&
                   !string.IsNullOrWhiteSpace(CurrencyName) &&
                   !string.IsNullOrWhiteSpace(CurrencySymbol) &&
                   ExchangeRate > 0 &&
                   DecimalPlaces >= 0 &&
                   DecimalPlaces <= 10;
        }

        /// <summary>
        /// تنسيق مبلغ بعملة معينة
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <returns>المبلغ منسق</returns>
        public string FormatAmount(decimal amount)
        {
            var format = "N" + DecimalPlaces.ToString();
            return amount.ToString(format) + " " + CurrencySymbol;
        }

        /// <summary>
        /// تحويل مبلغ من العملة الأساسية إلى هذه العملة
        /// </summary>
        /// <param name="baseAmount">المبلغ بالعملة الأساسية</param>
        /// <returns>المبلغ بهذه العملة</returns>
        public decimal ConvertFromBase(decimal baseAmount)
        {
            if (IsBaseCurrency)
                return baseAmount;
            
            return Math.Round(baseAmount * ExchangeRate, DecimalPlaces);
        }

        /// <summary>
        /// تحويل مبلغ من هذه العملة إلى العملة الأساسية
        /// </summary>
        /// <param name="amount">المبلغ بهذه العملة</param>
        /// <returns>المبلغ بالعملة الأساسية</returns>
        public decimal ConvertToBase(decimal amount)
        {
            if (IsBaseCurrency)
                return amount;
            
            if (ExchangeRate == 0)
                return 0;
            
            return Math.Round(amount / ExchangeRate, 2);
        }

        /// <summary>
        /// الحصول على معلومات العملة
        /// </summary>
        /// <returns>معلومات العملة</returns>
        public string GetCurrencyInfo()
        {
            var status = IsActive ? "نشطة" : "غير نشطة";
            var type = IsBaseCurrency ? "أساسية" : "فرعية";
            return CurrencyName + " (" + CurrencyCode + ") - " + type + " - " + status + " - سعر الصرف: " + ExchangeRate.ToString();
        }

        #endregion
    }
}
