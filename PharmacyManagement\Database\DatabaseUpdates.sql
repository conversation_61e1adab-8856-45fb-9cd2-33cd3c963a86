-- ===================================================================
-- ملف تحديثات قاعدة البيانات - Database Updates
-- تاريخ الإنشاء: 2025-01-28
-- الغرض: تطبيق التحديثات المطلوبة لإصلاح مشاكل المخزون
-- ===================================================================

USE PharmacyDB
GO

PRINT '🔄 بدء تطبيق تحديثات قاعدة البيانات...'
PRINT '📅 التاريخ: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT ''

-- ===================================================================
-- 1. التحقق من وجود الجداول المطلوبة
-- ===================================================================

PRINT '🔍 التحقق من وجود الجداول المطلوبة...'

-- التحقق من جدول Drugs
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Drugs' AND xtype='U')
BEGIN
    PRINT '❌ جدول Drugs غير موجود - يجب تشغيل ملف إنشاء قاعدة البيانات أولاً'
    RETURN
END
ELSE
    PRINT '✅ جدول Drugs موجود'

-- التحقق من جدول DrugCategories
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='DrugCategories' AND xtype='U')
BEGIN
    PRINT '❌ جدول DrugCategories غير موجود - يجب تشغيل ملف إنشاء قاعدة البيانات أولاً'
    RETURN
END
ELSE
    PRINT '✅ جدول DrugCategories موجود'

-- التحقق من جدول Inventory
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Inventory' AND xtype='U')
BEGIN
    PRINT '❌ جدول Inventory غير موجود - يجب تشغيل ملف إنشاء قاعدة البيانات أولاً'
    RETURN
END
ELSE
    PRINT '✅ جدول Inventory موجود'

-- التحقق من جدول Warehouses
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Warehouses' AND xtype='U')
BEGIN
    PRINT '❌ جدول Warehouses غير موجود - يجب تشغيل ملف إنشاء قاعدة البيانات أولاً'
    RETURN
END
ELSE
    PRINT '✅ جدول Warehouses موجود'

PRINT ''

-- ===================================================================
-- 2. إضافة أعمدة مفقودة إذا لم تكن موجودة
-- ===================================================================

PRINT '🔧 التحقق من الأعمدة المطلوبة وإضافة المفقود منها...'

-- إضافة عمود UpdatedDate لجدول Inventory إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Inventory') AND name = 'UpdatedDate')
BEGIN
    ALTER TABLE Inventory ADD UpdatedDate DATETIME NULL
    PRINT '✅ تم إضافة عمود UpdatedDate لجدول Inventory'
END
ELSE
    PRINT '✅ عمود UpdatedDate موجود في جدول Inventory'

-- التأكد من وجود عمود Quantity في جدول Inventory
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Inventory') AND name = 'Quantity')
BEGIN
    PRINT '❌ عمود Quantity مفقود من جدول Inventory'
    RETURN
END
ELSE
    PRINT '✅ عمود Quantity موجود في جدول Inventory'

PRINT ''

-- ===================================================================
-- 3. إنشاء جدول حركات المخزون إذا لم يكن موجوداً
-- ===================================================================

PRINT '📋 التحقق من جدول حركات المخزون...'

IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='StockMovements' AND xtype='U')
BEGIN
    CREATE TABLE StockMovements (
        MovementID INT IDENTITY(1,1) PRIMARY KEY,
        DrugID INT NOT NULL,
        WarehouseID INT NOT NULL DEFAULT 1,
        MovementType NVARCHAR(20) NOT NULL, -- دخول، خروج، تحويل، تسوية، إتلاف
        Quantity INT NOT NULL,
        UnitCost DECIMAL(10,2) NULL,
        TotalCost DECIMAL(10,2) NULL,
        ReferenceNumber NVARCHAR(50) NULL,
        ReferenceType NVARCHAR(20) NULL, -- فاتورة_شراء، فاتورة_بيع، تحويل، تسوية
        Notes NVARCHAR(200) NULL,
        UserID INT NOT NULL DEFAULT 1,
        MovementDate DATETIME NOT NULL DEFAULT GETDATE(),
        FOREIGN KEY (DrugID) REFERENCES Drugs(DrugID),
        FOREIGN KEY (WarehouseID) REFERENCES Warehouses(WarehouseID)
    )
    PRINT '✅ تم إنشاء جدول StockMovements'
END
ELSE
    PRINT '✅ جدول StockMovements موجود'

PRINT ''

-- ===================================================================
-- 4. إنشاء فهارس لتحسين الأداء
-- ===================================================================

PRINT '🚀 إنشاء الفهارس لتحسين الأداء...'

-- فهرس على DrugID في جدول Inventory
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Inventory_DrugID')
BEGIN
    CREATE INDEX IX_Inventory_DrugID ON Inventory(DrugID)
    PRINT '✅ تم إنشاء فهرس IX_Inventory_DrugID'
END
ELSE
    PRINT '✅ فهرس IX_Inventory_DrugID موجود'

-- فهرس على Quantity في جدول Inventory
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Inventory_Quantity')
BEGIN
    CREATE INDEX IX_Inventory_Quantity ON Inventory(Quantity) WHERE Quantity > 0
    PRINT '✅ تم إنشاء فهرس IX_Inventory_Quantity'
END
ELSE
    PRINT '✅ فهرس IX_Inventory_Quantity موجود'

-- فهرس على ExpiryDate في جدول Inventory
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Inventory_ExpiryDate')
BEGIN
    CREATE INDEX IX_Inventory_ExpiryDate ON Inventory(ExpiryDate)
    PRINT '✅ تم إنشاء فهرس IX_Inventory_ExpiryDate'
END
ELSE
    PRINT '✅ فهرس IX_Inventory_ExpiryDate موجود'

-- فهرس على CategoryID في جدول Drugs
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Drugs_CategoryID')
BEGIN
    CREATE INDEX IX_Drugs_CategoryID ON Drugs(CategoryID)
    PRINT '✅ تم إنشاء فهرس IX_Drugs_CategoryID'
END
ELSE
    PRINT '✅ فهرس IX_Drugs_CategoryID موجود'

-- فهرس على MovementDate في جدول StockMovements
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_StockMovements_MovementDate')
BEGIN
    CREATE INDEX IX_StockMovements_MovementDate ON StockMovements(MovementDate)
    PRINT '✅ تم إنشاء فهرس IX_StockMovements_MovementDate'
END
ELSE
    PRINT '✅ فهرس IX_StockMovements_MovementDate موجود'

PRINT ''

-- ===================================================================
-- 5. إنشاء Views مفيدة للاستعلامات
-- ===================================================================

PRINT '👁️ إنشاء Views مفيدة...'

-- View للمخزون الحالي لكل دواء
IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_CurrentStock')
    DROP VIEW vw_CurrentStock
GO

CREATE VIEW vw_CurrentStock AS
SELECT 
    d.DrugID,
    d.DrugCode,
    d.DrugName,
    d.Unit,
    dc.CategoryName,
    m.ManufacturerName,
    ISNULL(SUM(i.Quantity), 0) as CurrentStock,
    d.MinStock,
    d.MaxStock,
    d.PurchasePrice,
    d.SalePrice,
    CASE 
        WHEN ISNULL(SUM(i.Quantity), 0) = 0 THEN 'نفد المخزون'
        WHEN ISNULL(SUM(i.Quantity), 0) <= d.MinStock THEN 'مخزون منخفض'
        WHEN ISNULL(SUM(i.Quantity), 0) >= d.MaxStock THEN 'مخزون مرتفع'
        ELSE 'مخزون طبيعي'
    END as StockStatus,
    MIN(i.ExpiryDate) as NearestExpiryDate
FROM Drugs d
LEFT JOIN DrugCategories dc ON d.CategoryID = dc.CategoryID
LEFT JOIN Manufacturers m ON d.ManufacturerID = m.ManufacturerID
LEFT JOIN Inventory i ON d.DrugID = i.DrugID AND i.Quantity > 0
WHERE d.IsActive = 1
GROUP BY d.DrugID, d.DrugCode, d.DrugName, d.Unit, dc.CategoryName, 
         m.ManufacturerName, d.MinStock, d.MaxStock, d.PurchasePrice, d.SalePrice
GO

PRINT '✅ تم إنشاء View vw_CurrentStock'

-- View للأدوية منخفضة المخزون
IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_LowStockDrugs')
    DROP VIEW vw_LowStockDrugs
GO

CREATE VIEW vw_LowStockDrugs AS
SELECT *
FROM vw_CurrentStock
WHERE CurrentStock <= MinStock AND CurrentStock >= 0
GO

PRINT '✅ تم إنشاء View vw_LowStockDrugs'

-- View للأدوية منتهية الصلاحية
IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_ExpiredDrugs')
    DROP VIEW vw_ExpiredDrugs
GO

CREATE VIEW vw_ExpiredDrugs AS
SELECT 
    d.DrugID,
    d.DrugCode,
    d.DrugName,
    dc.CategoryName,
    m.ManufacturerName,
    i.BatchNumber,
    i.Quantity,
    i.ExpiryDate,
    DATEDIFF(DAY, GETDATE(), i.ExpiryDate) as DaysToExpiry
FROM Drugs d
INNER JOIN Inventory i ON d.DrugID = i.DrugID
LEFT JOIN DrugCategories dc ON d.CategoryID = dc.CategoryID
LEFT JOIN Manufacturers m ON d.ManufacturerID = m.ManufacturerID
WHERE d.IsActive = 1 
  AND i.Quantity > 0 
  AND i.ExpiryDate <= GETDATE()
GO

PRINT '✅ تم إنشاء View vw_ExpiredDrugs'

PRINT ''

-- ===================================================================
-- 6. إنشاء Stored Procedures مفيدة
-- ===================================================================

PRINT '⚙️ إنشاء Stored Procedures...'

-- Procedure لتحديث المخزون
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_UpdateStock')
    DROP PROCEDURE sp_UpdateStock
GO

CREATE PROCEDURE sp_UpdateStock
    @DrugID INT,
    @Quantity DECIMAL(10,2),
    @IsAddition BIT,
    @Notes NVARCHAR(200) = NULL,
    @UserID INT = 1
AS
BEGIN
    SET NOCOUNT ON
    
    BEGIN TRY
        BEGIN TRANSACTION
        
        IF @IsAddition = 1
        BEGIN
            -- إضافة دفعة جديدة
            INSERT INTO Inventory (DrugID, WarehouseID, BatchNumber, Quantity, 
                                 ExpiryDate, PurchasePrice, SalePrice, CreatedDate)
            SELECT @DrugID, 1, 'BATCH_' + FORMAT(GETDATE(), 'yyyyMMddHHmmss'), @Quantity,
                   DATEADD(YEAR, 2, GETDATE()), PurchasePrice, SalePrice, GETDATE()
            FROM Drugs WHERE DrugID = @DrugID
            
            -- تسجيل الحركة
            INSERT INTO StockMovements (DrugID, WarehouseID, MovementType, Quantity, Notes, UserID)
            VALUES (@DrugID, 1, 'دخول', @Quantity, @Notes, @UserID)
        END
        ELSE
        BEGIN
            -- خصم من أقدم دفعة
            DECLARE @RemainingToDeduct DECIMAL(10,2) = @Quantity
            
            WHILE @RemainingToDeduct > 0
            BEGIN
                DECLARE @InventoryID INT, @AvailableQty DECIMAL(10,2)
                
                SELECT TOP 1 @InventoryID = InventoryID, @AvailableQty = Quantity
                FROM Inventory 
                WHERE DrugID = @DrugID AND Quantity > 0
                ORDER BY ExpiryDate ASC, CreatedDate ASC
                
                IF @InventoryID IS NULL
                    THROW 50001, 'لا يوجد مخزون كافي للخصم', 1
                
                DECLARE @DeductFromThis DECIMAL(10,2) = 
                    CASE WHEN @RemainingToDeduct <= @AvailableQty 
                         THEN @RemainingToDeduct 
                         ELSE @AvailableQty END
                
                UPDATE Inventory 
                SET Quantity = Quantity - @DeductFromThis, UpdatedDate = GETDATE()
                WHERE InventoryID = @InventoryID
                
                SET @RemainingToDeduct = @RemainingToDeduct - @DeductFromThis
            END
            
            -- تسجيل الحركة
            INSERT INTO StockMovements (DrugID, WarehouseID, MovementType, Quantity, Notes, UserID)
            VALUES (@DrugID, 1, 'خروج', @Quantity, @Notes, @UserID)
        END
        
        COMMIT TRANSACTION
        
        SELECT 'SUCCESS' as Result, 'تم تحديث المخزون بنجاح' as Message
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION
        
        SELECT 'ERROR' as Result, ERROR_MESSAGE() as Message
    END CATCH
END
GO

PRINT '✅ تم إنشاء Stored Procedure sp_UpdateStock'

PRINT ''

-- ===================================================================
-- 7. تنظيف البيانات المكررة أو الخاطئة
-- ===================================================================

PRINT '🧹 تنظيف البيانات...'

-- حذف السجلات ذات الكمية صفر من جدول Inventory
DELETE FROM Inventory WHERE Quantity = 0
PRINT '✅ تم حذف السجلات ذات الكمية صفر من جدول Inventory'

-- تحديث التواريخ الفارغة
UPDATE Inventory 
SET CreatedDate = GETDATE() 
WHERE CreatedDate IS NULL
PRINT '✅ تم تحديث التواريخ الفارغة في جدول Inventory'

PRINT ''

-- ===================================================================
-- 8. إحصائيات نهائية
-- ===================================================================

PRINT '📊 إحصائيات قاعدة البيانات بعد التحديث:'

DECLARE @DrugsCount INT, @CategoriesCount INT, @InventoryCount INT, @MovementsCount INT

SELECT @DrugsCount = COUNT(*) FROM Drugs WHERE IsActive = 1
SELECT @CategoriesCount = COUNT(*) FROM DrugCategories WHERE IsActive = 1
SELECT @InventoryCount = COUNT(*) FROM Inventory WHERE Quantity > 0
SELECT @MovementsCount = COUNT(*) FROM StockMovements

PRINT '   📦 عدد الأدوية النشطة: ' + CAST(@DrugsCount AS VARCHAR)
PRINT '   🏷️ عدد فئات الأدوية: ' + CAST(@CategoriesCount AS VARCHAR)
PRINT '   📋 عدد سجلات المخزون: ' + CAST(@InventoryCount AS VARCHAR)
PRINT '   🔄 عدد حركات المخزون: ' + CAST(@MovementsCount AS VARCHAR)

-- إحصائيات المخزون
SELECT 
    COUNT(*) as TotalDrugs,
    SUM(CASE WHEN CurrentStock = 0 THEN 1 ELSE 0 END) as OutOfStock,
    SUM(CASE WHEN CurrentStock > 0 AND CurrentStock <= MinStock THEN 1 ELSE 0 END) as LowStock,
    SUM(CASE WHEN CurrentStock > MinStock THEN 1 ELSE 0 END) as NormalStock
FROM vw_CurrentStock

PRINT ''
PRINT '✅ تم تطبيق جميع تحديثات قاعدة البيانات بنجاح!'
PRINT '🎉 قاعدة البيانات جاهزة للاستخدام'
PRINT '📅 تاريخ الانتهاء: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT ''
PRINT '📝 ملاحظات مهمة:'
PRINT '   - تم إنشاء Views لتسهيل الاستعلامات'
PRINT '   - تم إنشاء Stored Procedure لتحديث المخزون'
PRINT '   - تم إنشاء فهارس لتحسين الأداء'
PRINT '   - تم تنظيف البيانات المكررة'
PRINT ''
