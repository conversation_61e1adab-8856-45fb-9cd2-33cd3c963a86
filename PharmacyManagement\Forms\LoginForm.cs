using System;
using System.Drawing;
using System.Windows.Forms;
using PharmacyManagement.Classes;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نافذة تسجيل الدخول - Login Form
    /// نافذة تسجيل دخول المستخدمين للنظام
    /// </summary>
    public partial class LoginForm : Form
    {
        #region Constructor - المنشئ

        public LoginForm()
        {
            InitializeComponent();
            SetupForm();
        }

        #endregion

        #region Form Setup - إعداد النافذة

        /// <summary>
        /// إعداد النافذة والتصميم المسطح
        /// </summary>
        private void SetupForm()
        {
            // إعدادات النافذة الأساسية
            this.CenterToScreen();

            // تطبيق التصميم المسطح على الأزرار
            ApplyFlatDesign();

            // إعداد القيم الافتراضية للاختبار
            txtUsername.Text = "admin";
            txtPassword.Text = "admin123";
        }

        /// <summary>
        /// تطبيق التصميم المسطح
        /// </summary>
        private void ApplyFlatDesign()
        {
            // إزالة حدود الأزرار
            btnLogin.FlatAppearance.BorderSize = 0;
            btnClose.FlatAppearance.BorderSize = 0;

            // تأثيرات التمرير للأزرار
            btnLogin.MouseEnter += (s, e) => btnLogin.BackColor = Color.FromArgb(41, 128, 185);
            btnLogin.MouseLeave += (s, e) => btnLogin.BackColor = Color.FromArgb(52, 152, 219);

            btnClose.MouseEnter += (s, e) => btnClose.BackColor = Color.FromArgb(192, 57, 43);
            btnClose.MouseLeave += (s, e) => btnClose.BackColor = Color.FromArgb(231, 76, 60);
        }

        #endregion

        #region Event Handlers - معالجات الأحداث

        /// <summary>
        /// معالج حدث النقر على زر تسجيل الدخول
        /// </summary>
        private void btnLogin_Click(object sender, EventArgs e)
        {
            PerformLogin();
        }

        /// <summary>
        /// معالج حدث النقر على زر الإغلاق
        /// </summary>
        private void btnClose_Click(object sender, EventArgs e)
        {
            Application.Exit();
        }

        /// <summary>
        /// معالج حدث الضغط على مفتاح في حقل كلمة المرور
        /// </summary>
        private void txtPassword_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                PerformLogin();
                e.Handled = true;
            }
        }

        /// <summary>
        /// معالج حدث الضغط على مفتاح في حقل اسم المستخدم
        /// </summary>
        private void txtUsername_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                txtPassword.Focus();
                e.Handled = true;
            }
        }

        #endregion

        #region Methods - الطرق

        /// <summary>
        /// تنفيذ عملية تسجيل الدخول
        /// </summary>
        private void PerformLogin()
        {
            try
            {
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(txtUsername.Text))
                {
                    ShowMessage("يرجى إدخال اسم المستخدم", MessageType.Warning);
                    txtUsername.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtPassword.Text))
                {
                    ShowMessage("يرجى إدخال كلمة المرور", MessageType.Warning);
                    txtPassword.Focus();
                    return;
                }

                // تعطيل الزر أثناء المعالجة
                btnLogin.Enabled = false;
                btnLogin.Text = "جاري التحقق...";
                Application.DoEvents();

                // التحقق من الاتصال بقاعدة البيانات
                if (!DatabaseHelper.TestConnection())
                {
                    ShowMessage("لا يمكن الاتصال بقاعدة البيانات", MessageType.Error);
                    return;
                }

                // محاولة تسجيل الدخول
                bool loginSuccess = UserManager.Login(txtUsername.Text.Trim(), txtPassword.Text);

                if (loginSuccess)
                {
                    ShowMessage($"مرحباً {UserManager.CurrentUser.FullName}", MessageType.Success);
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    ShowMessage("اسم المستخدم أو كلمة المرور غير صحيحة", MessageType.Error);
                    txtPassword.Clear();
                    txtUsername.Focus();
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"خطأ في تسجيل الدخول: {ex.Message}", MessageType.Error);
            }
            finally
            {
                // إعادة تفعيل الزر
                btnLogin.Enabled = true;
                btnLogin.Text = "تسجيل الدخول";
            }
        }

        /// <summary>
        /// عرض رسالة للمستخدم
        /// </summary>
        /// <param name="message">الرسالة</param>
        /// <param name="type">نوع الرسالة</param>
        private void ShowMessage(string message, MessageType type)
        {
            MessageBoxIcon icon;
            string title;

            switch (type)
            {
                case MessageType.Success:
                    icon = MessageBoxIcon.Information;
                    title = "نجح";
                    break;
                case MessageType.Warning:
                    icon = MessageBoxIcon.Warning;
                    title = "تنبيه";
                    break;
                case MessageType.Error:
                    icon = MessageBoxIcon.Error;
                    title = "خطأ";
                    break;
                default:
                    icon = MessageBoxIcon.Information;
                    title = "معلومات";
                    break;
            }

            MessageBox.Show(message, title, MessageBoxButtons.OK, icon);
        }

        #endregion

        #region Enums - التعدادات

        private enum MessageType
        {
            Success,
            Warning,
            Error,
            Info
        }

        #endregion
    }
}
