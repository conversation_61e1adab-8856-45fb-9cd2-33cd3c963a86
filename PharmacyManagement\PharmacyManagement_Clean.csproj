<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>PharmacyManagement</RootNamespace>
    <AssemblyName>PharmacyManagement</AssemblyName>
    <TargetFrameworkVersion>v4.6</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
  </ItemGroup>
  <ItemGroup>
    <!-- Program Entry Point -->
    <Compile Include="Program.cs" />
    
    <!-- Properties -->
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    
    <!-- Models -->
    <Compile Include="Models\User.cs" />
    <Compile Include="Models\Drug.cs" />
    <Compile Include="Models\Branch.cs" />
    <Compile Include="Models\Customer.cs" />
    <Compile Include="Models\Supplier.cs" />
    <Compile Include="Models\Invoice.cs" />
    <Compile Include="Models\InvoiceDetail.cs" />
    <Compile Include="Models\InventoryItem.cs" />
    <Compile Include="Models\StockMovement.cs" />
    
    <!-- Classes -->
    <Compile Include="Classes\DatabaseHelper.cs" />
    <Compile Include="Classes\LogManager.cs" />
    <Compile Include="Classes\UserManager.cs" />
    <Compile Include="Classes\DrugManager.cs" />
    <Compile Include="Classes\BranchManager.cs" />
    <Compile Include="Classes\WarehouseManager.cs" />
    <Compile Include="Classes\CustomerManager.cs" />
    <Compile Include="Classes\SupplierManager.cs" />
    <Compile Include="Classes\InventoryManager.cs" />
    <Compile Include="Classes\SalesManager.cs" />
    <Compile Include="Classes\InvoiceManager.cs" />
    <Compile Include="Classes\StockTransferManager.cs" />
    <Compile Include="Classes\ReportsManager.cs" />
    <Compile Include="Classes\FinancialManager.cs" />
    <Compile Include="Classes\SettingsManager.cs" />
    
    <!-- Main Forms -->
    <Compile Include="Forms\LoginForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\LoginForm.Designer.cs">
      <DependentUpon>LoginForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\MainFormUpdated.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\MainFormUpdated.Designer.cs">
      <DependentUpon>MainFormUpdated.cs</DependentUpon>
    </Compile>
    
    <!-- Management Forms -->
    <Compile Include="Forms\BranchForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\BranchForm.Designer.cs">
      <DependentUpon>BranchForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\WarehouseForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\WarehouseForm.Designer.cs">
      <DependentUpon>WarehouseForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\DrugsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\DrugsForm.Designer.cs">
      <DependentUpon>DrugsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\CustomersForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\CustomersForm.Designer.cs">
      <DependentUpon>CustomersForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SuppliersForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SuppliersForm.Designer.cs">
      <DependentUpon>SuppliersForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\InventoryForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\InventoryForm.Designer.cs">
      <DependentUpon>InventoryForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SalesForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SalesForm.Designer.cs">
      <DependentUpon>SalesForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ReportsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ReportsForm.Designer.cs">
      <DependentUpon>ReportsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FinancialForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FinancialForm.Designer.cs">
      <DependentUpon>FinancialForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\UsersForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\UsersForm.Designer.cs">
      <DependentUpon>UsersForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SettingsForm.Designer.cs">
      <DependentUpon>SettingsForm.cs</DependentUpon>
    </Compile>
    
    <!-- Quick Action Forms -->
    <Compile Include="Forms\QuickSaleForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\QuickSearchForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\AlertsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\StockTransferForm.cs">
      <SubType>Form</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <!-- Resources -->
    <EmbeddedResource Include="Forms\LoginForm.resx">
      <DependentUpon>LoginForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\MainFormUpdated.resx">
      <DependentUpon>MainFormUpdated.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SalesForm.resx">
      <DependentUpon>SalesForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\QuickSaleForm.resx">
      <DependentUpon>QuickSaleForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <!-- Configuration -->
    <None Include="App.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <!-- Directories -->
    <Folder Include="Database\" />
    <Folder Include="Reports\" />
    <Folder Include="Assets\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>
