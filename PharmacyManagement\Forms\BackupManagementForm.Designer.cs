namespace PharmacyManagement.Forms
{
    partial class BackupManagementForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.topPanel = new System.Windows.Forms.Panel();
            this.lblTitle = new System.Windows.Forms.Label();
            this.statsPanel = new System.Windows.Forms.Panel();
            this.lblNextBackup = new System.Windows.Forms.Label();
            this.lblLastBackup = new System.Windows.Forms.Label();
            this.lblTotalSize = new System.Windows.Forms.Label();
            this.lblTotalBackups = new System.Windows.Forms.Label();
            this.settingsPanel = new System.Windows.Forms.Panel();
            this.btnSaveSettings = new System.Windows.Forms.Button();
            this.btnBrowseBackupPath = new System.Windows.Forms.Button();
            this.txtBackupPath = new System.Windows.Forms.TextBox();
            this.lblBackupPath = new System.Windows.Forms.Label();
            this.numKeepBackups = new System.Windows.Forms.NumericUpDown();
            this.lblKeepBackups = new System.Windows.Forms.Label();
            this.numBackupInterval = new System.Windows.Forms.NumericUpDown();
            this.lblBackupInterval = new System.Windows.Forms.Label();
            this.chkAutoBackup = new System.Windows.Forms.CheckBox();
            this.buttonsPanel = new System.Windows.Forms.Panel();
            this.progressBar = new System.Windows.Forms.ProgressBar();
            this.btnRefresh = new System.Windows.Forms.Button();
            this.btnExportBackup = new System.Windows.Forms.Button();
            this.btnVerifyBackup = new System.Windows.Forms.Button();
            this.btnDeleteBackup = new System.Windows.Forms.Button();
            this.btnRestoreBackup = new System.Windows.Forms.Button();
            this.btnCreateBackup = new System.Windows.Forms.Button();
            this.mainPanel = new System.Windows.Forms.Panel();
            this.dgvBackups = new System.Windows.Forms.DataGridView();
            this.FileName = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.FileSizeFormatted = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.CreatedDate = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.ModifiedDate = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.FilePath = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.bottomPanel = new System.Windows.Forms.Panel();
            this.lblStatus = new System.Windows.Forms.Label();
            this.topPanel.SuspendLayout();
            this.statsPanel.SuspendLayout();
            this.settingsPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numKeepBackups)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numBackupInterval)).BeginInit();
            this.buttonsPanel.SuspendLayout();
            this.mainPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvBackups)).BeginInit();
            this.bottomPanel.SuspendLayout();
            this.SuspendLayout();
            //
            // topPanel
            //
            this.topPanel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(52)))), ((int)(((byte)(73)))), ((int)(((byte)(94)))));
            this.topPanel.Controls.Add(this.lblTitle);
            this.topPanel.Dock = System.Windows.Forms.DockStyle.Top;
            this.topPanel.Location = new System.Drawing.Point(0, 0);
            this.topPanel.Name = "topPanel";
            this.topPanel.Size = new System.Drawing.Size(1400, 60);
            this.topPanel.TabIndex = 0;
            //
            // lblTitle
            //
            this.lblTitle.Font = new System.Drawing.Font("Segoe UI", 18F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblTitle.ForeColor = System.Drawing.Color.White;
            this.lblTitle.Location = new System.Drawing.Point(20, 15);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Size = new System.Drawing.Size(300, 30);
            this.lblTitle.TabIndex = 0;
            this.lblTitle.Text = "💾 إدارة النسخ الاحتياطية";
            this.lblTitle.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            //
            // statsPanel
            //
            this.statsPanel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(236)))), ((int)(((byte)(240)))), ((int)(((byte)(241)))));
            this.statsPanel.Controls.Add(this.lblNextBackup);
            this.statsPanel.Controls.Add(this.lblLastBackup);
            this.statsPanel.Controls.Add(this.lblTotalSize);
            this.statsPanel.Controls.Add(this.lblTotalBackups);
            this.statsPanel.Dock = System.Windows.Forms.DockStyle.Top;
            this.statsPanel.Location = new System.Drawing.Point(0, 60);
            this.statsPanel.Name = "statsPanel";
            this.statsPanel.Padding = new System.Windows.Forms.Padding(20, 10, 20, 10);
            this.statsPanel.Size = new System.Drawing.Size(1400, 80);
            this.statsPanel.TabIndex = 1;
            //
            // lblTotalBackups
            //
            this.lblTotalBackups.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(52)))), ((int)(((byte)(152)))), ((int)(((byte)(219)))));
            this.lblTotalBackups.Font = new System.Drawing.Font("Segoe UI", 11F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblTotalBackups.ForeColor = System.Drawing.Color.White;
            this.lblTotalBackups.Location = new System.Drawing.Point(20, 20);
            this.lblTotalBackups.Name = "lblTotalBackups";
            this.lblTotalBackups.Size = new System.Drawing.Size(200, 40);
            this.lblTotalBackups.TabIndex = 0;
            this.lblTotalBackups.Text = "إجمالي النسخ: 0";
            this.lblTotalBackups.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            //
            // lblTotalSize
            //
            this.lblTotalSize.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(46)))), ((int)(((byte)(204)))), ((int)(((byte)(113)))));
            this.lblTotalSize.Font = new System.Drawing.Font("Segoe UI", 11F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblTotalSize.ForeColor = System.Drawing.Color.White;
            this.lblTotalSize.Location = new System.Drawing.Point(240, 20);
            this.lblTotalSize.Name = "lblTotalSize";
            this.lblTotalSize.Size = new System.Drawing.Size(200, 40);
            this.lblTotalSize.TabIndex = 1;
            this.lblTotalSize.Text = "الحجم الإجمالي: 0 MB";
            this.lblTotalSize.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            //
            // lblLastBackup
            //
            this.lblLastBackup.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(230)))), ((int)(((byte)(126)))), ((int)(((byte)(34)))));
            this.lblLastBackup.Font = new System.Drawing.Font("Segoe UI", 11F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblLastBackup.ForeColor = System.Drawing.Color.White;
            this.lblLastBackup.Location = new System.Drawing.Point(460, 20);
            this.lblLastBackup.Name = "lblLastBackup";
            this.lblLastBackup.Size = new System.Drawing.Size(250, 40);
            this.lblLastBackup.TabIndex = 2;
            this.lblLastBackup.Text = "آخر نسخة: غير محدد";
            this.lblLastBackup.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            //
            // lblNextBackup
            //
            this.lblNextBackup.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(89)))), ((int)(((byte)(182)))));
            this.lblNextBackup.Font = new System.Drawing.Font("Segoe UI", 11F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblNextBackup.ForeColor = System.Drawing.Color.White;
            this.lblNextBackup.Location = new System.Drawing.Point(730, 20);
            this.lblNextBackup.Name = "lblNextBackup";
            this.lblNextBackup.Size = new System.Drawing.Size(250, 40);
            this.lblNextBackup.TabIndex = 3;
            this.lblNextBackup.Text = "النسخة التالية: غير محدد";
            this.lblNextBackup.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            //
            // settingsPanel
            //
            this.settingsPanel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(236)))), ((int)(((byte)(240)))), ((int)(((byte)(241)))));
            this.settingsPanel.Controls.Add(this.btnSaveSettings);
            this.settingsPanel.Controls.Add(this.btnBrowseBackupPath);
            this.settingsPanel.Controls.Add(this.txtBackupPath);
            this.settingsPanel.Controls.Add(this.lblBackupPath);
            this.settingsPanel.Controls.Add(this.numKeepBackups);
            this.settingsPanel.Controls.Add(this.lblKeepBackups);
            this.settingsPanel.Controls.Add(this.numBackupInterval);
            this.settingsPanel.Controls.Add(this.lblBackupInterval);
            this.settingsPanel.Controls.Add(this.chkAutoBackup);
            this.settingsPanel.Dock = System.Windows.Forms.DockStyle.Top;
            this.settingsPanel.Location = new System.Drawing.Point(0, 140);
            this.settingsPanel.Name = "settingsPanel";
            this.settingsPanel.Padding = new System.Windows.Forms.Padding(20, 10, 20, 10);
            this.settingsPanel.Size = new System.Drawing.Size(1400, 100);
            this.settingsPanel.TabIndex = 2;
            //
            // chkAutoBackup
            //
            this.chkAutoBackup.AutoSize = true;
            this.chkAutoBackup.Font = new System.Drawing.Font("Segoe UI", 10F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkAutoBackup.Location = new System.Drawing.Point(20, 15);
            this.chkAutoBackup.Name = "chkAutoBackup";
            this.chkAutoBackup.Size = new System.Drawing.Size(158, 23);
            this.chkAutoBackup.TabIndex = 0;
            this.chkAutoBackup.Text = "تفعيل النسخ التلقائي";
            this.chkAutoBackup.UseVisualStyleBackColor = true;
            //
            // numBackupInterval
            //
            this.numBackupInterval.Font = new System.Drawing.Font("Segoe UI", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numBackupInterval.Location = new System.Drawing.Point(20, 65);
            this.numBackupInterval.Maximum = new decimal(new int[] {
            168,
            0,
            0,
            0});
            this.numBackupInterval.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numBackupInterval.Name = "numBackupInterval";
            this.numBackupInterval.Size = new System.Drawing.Size(80, 25);
            this.numBackupInterval.TabIndex = 2;
            this.numBackupInterval.Value = new decimal(new int[] {
            24,
            0,
            0,
            0});
            //
            // lblBackupInterval
            //
            this.lblBackupInterval.AutoSize = true;
            this.lblBackupInterval.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblBackupInterval.Location = new System.Drawing.Point(20, 45);
            this.lblBackupInterval.Name = "lblBackupInterval";
            this.lblBackupInterval.Size = new System.Drawing.Size(95, 15);
            this.lblBackupInterval.TabIndex = 1;
            this.lblBackupInterval.Text = "الفترة (بالساعات):";
            //
            // numKeepBackups
            //
            this.numKeepBackups.Font = new System.Drawing.Font("Segoe UI", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.numKeepBackups.Location = new System.Drawing.Point(150, 65);
            this.numKeepBackups.Maximum = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.numKeepBackups.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numKeepBackups.Name = "numKeepBackups";
            this.numKeepBackups.Size = new System.Drawing.Size(80, 25);
            this.numKeepBackups.TabIndex = 4;
            this.numKeepBackups.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            //
            // lblKeepBackups
            //
            this.lblKeepBackups.AutoSize = true;
            this.lblKeepBackups.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblKeepBackups.Location = new System.Drawing.Point(150, 45);
            this.lblKeepBackups.Name = "lblKeepBackups";
            this.lblKeepBackups.Size = new System.Drawing.Size(80, 15);
            this.lblKeepBackups.TabIndex = 3;
            this.lblKeepBackups.Text = "عدد النسخ المحفوظة:";
            //
            // txtBackupPath
            //
            this.txtBackupPath.Font = new System.Drawing.Font("Segoe UI", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtBackupPath.Location = new System.Drawing.Point(280, 65);
            this.txtBackupPath.Name = "txtBackupPath";
            this.txtBackupPath.ReadOnly = true;
            this.txtBackupPath.Size = new System.Drawing.Size(400, 25);
            this.txtBackupPath.TabIndex = 6;
            //
            // lblBackupPath
            //
            this.lblBackupPath.AutoSize = true;
            this.lblBackupPath.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblBackupPath.Location = new System.Drawing.Point(280, 45);
            this.lblBackupPath.Name = "lblBackupPath";
            this.lblBackupPath.Size = new System.Drawing.Size(73, 15);
            this.lblBackupPath.TabIndex = 5;
            this.lblBackupPath.Text = "مجلد النسخ:";
            //
            // btnBrowseBackupPath
            //
            this.btnBrowseBackupPath.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(149)))), ((int)(((byte)(165)))), ((int)(((byte)(166)))));
            this.btnBrowseBackupPath.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnBrowseBackupPath.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnBrowseBackupPath.ForeColor = System.Drawing.Color.White;
            this.btnBrowseBackupPath.Location = new System.Drawing.Point(690, 65);
            this.btnBrowseBackupPath.Name = "btnBrowseBackupPath";
            this.btnBrowseBackupPath.Size = new System.Drawing.Size(60, 25);
            this.btnBrowseBackupPath.TabIndex = 7;
            this.btnBrowseBackupPath.Text = "تصفح";
            this.btnBrowseBackupPath.UseVisualStyleBackColor = false;
            this.btnBrowseBackupPath.Click += new System.EventHandler(this.btnBrowseBackupPath_Click);
            //
            // btnSaveSettings
            //
            this.btnSaveSettings.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(46)))), ((int)(((byte)(204)))), ((int)(((byte)(113)))));
            this.btnSaveSettings.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnSaveSettings.Font = new System.Drawing.Font("Segoe UI", 10F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnSaveSettings.ForeColor = System.Drawing.Color.White;
            this.btnSaveSettings.Location = new System.Drawing.Point(770, 60);
            this.btnSaveSettings.Name = "btnSaveSettings";
            this.btnSaveSettings.Size = new System.Drawing.Size(100, 35);
            this.btnSaveSettings.TabIndex = 8;
            this.btnSaveSettings.Text = "💾 حفظ الإعدادات";
            this.btnSaveSettings.UseVisualStyleBackColor = false;
            this.btnSaveSettings.Click += new System.EventHandler(this.btnSaveSettings_Click);
            //
            // buttonsPanel
            //
            this.buttonsPanel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(236)))), ((int)(((byte)(240)))), ((int)(((byte)(241)))));
            this.buttonsPanel.Controls.Add(this.progressBar);
            this.buttonsPanel.Controls.Add(this.btnRefresh);
            this.buttonsPanel.Controls.Add(this.btnExportBackup);
            this.buttonsPanel.Controls.Add(this.btnVerifyBackup);
            this.buttonsPanel.Controls.Add(this.btnDeleteBackup);
            this.buttonsPanel.Controls.Add(this.btnRestoreBackup);
            this.buttonsPanel.Controls.Add(this.btnCreateBackup);
            this.buttonsPanel.Dock = System.Windows.Forms.DockStyle.Top;
            this.buttonsPanel.Location = new System.Drawing.Point(0, 240);
            this.buttonsPanel.Name = "buttonsPanel";
            this.buttonsPanel.Padding = new System.Windows.Forms.Padding(20, 10, 20, 10);
            this.buttonsPanel.Size = new System.Drawing.Size(1400, 60);
            this.buttonsPanel.TabIndex = 3;
            //
            // btnCreateBackup
            //
            this.btnCreateBackup.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(46)))), ((int)(((byte)(204)))), ((int)(((byte)(113)))));
            this.btnCreateBackup.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnCreateBackup.Font = new System.Drawing.Font("Segoe UI", 10F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCreateBackup.ForeColor = System.Drawing.Color.White;
            this.btnCreateBackup.Location = new System.Drawing.Point(20, 15);
            this.btnCreateBackup.Name = "btnCreateBackup";
            this.btnCreateBackup.Size = new System.Drawing.Size(120, 35);
            this.btnCreateBackup.TabIndex = 0;
            this.btnCreateBackup.Text = "💾 إنشاء نسخة";
            this.btnCreateBackup.UseVisualStyleBackColor = false;
            this.btnCreateBackup.Click += new System.EventHandler(this.btnCreateBackup_Click);
            //
            // btnRestoreBackup
            //
            this.btnRestoreBackup.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(52)))), ((int)(((byte)(152)))), ((int)(((byte)(219)))));
            this.btnRestoreBackup.Enabled = false;
            this.btnRestoreBackup.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnRestoreBackup.Font = new System.Drawing.Font("Segoe UI", 10F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnRestoreBackup.ForeColor = System.Drawing.Color.White;
            this.btnRestoreBackup.Location = new System.Drawing.Point(150, 15);
            this.btnRestoreBackup.Name = "btnRestoreBackup";
            this.btnRestoreBackup.Size = new System.Drawing.Size(120, 35);
            this.btnRestoreBackup.TabIndex = 1;
            this.btnRestoreBackup.Text = "🔄 استعادة";
            this.btnRestoreBackup.UseVisualStyleBackColor = false;
            this.btnRestoreBackup.Click += new System.EventHandler(this.btnRestoreBackup_Click);
            //
            // btnDeleteBackup
            //
            this.btnDeleteBackup.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(231)))), ((int)(((byte)(76)))), ((int)(((byte)(60)))));
            this.btnDeleteBackup.Enabled = false;
            this.btnDeleteBackup.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnDeleteBackup.Font = new System.Drawing.Font("Segoe UI", 10F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnDeleteBackup.ForeColor = System.Drawing.Color.White;
            this.btnDeleteBackup.Location = new System.Drawing.Point(280, 15);
            this.btnDeleteBackup.Name = "btnDeleteBackup";
            this.btnDeleteBackup.Size = new System.Drawing.Size(120, 35);
            this.btnDeleteBackup.TabIndex = 2;
            this.btnDeleteBackup.Text = "🗑️ حذف";
            this.btnDeleteBackup.UseVisualStyleBackColor = false;
            this.btnDeleteBackup.Click += new System.EventHandler(this.btnDeleteBackup_Click);
            //
            // btnVerifyBackup
            //
            this.btnVerifyBackup.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(241)))), ((int)(((byte)(196)))), ((int)(((byte)(15)))));
            this.btnVerifyBackup.Enabled = false;
            this.btnVerifyBackup.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnVerifyBackup.Font = new System.Drawing.Font("Segoe UI", 10F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnVerifyBackup.ForeColor = System.Drawing.Color.White;
            this.btnVerifyBackup.Location = new System.Drawing.Point(410, 15);
            this.btnVerifyBackup.Name = "btnVerifyBackup";
            this.btnVerifyBackup.Size = new System.Drawing.Size(120, 35);
            this.btnVerifyBackup.TabIndex = 3;
            this.btnVerifyBackup.Text = "✅ تحقق";
            this.btnVerifyBackup.UseVisualStyleBackColor = false;
            this.btnVerifyBackup.Click += new System.EventHandler(this.btnVerifyBackup_Click);
            //
            // btnExportBackup
            //
            this.btnExportBackup.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(89)))), ((int)(((byte)(182)))));
            this.btnExportBackup.Enabled = false;
            this.btnExportBackup.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnExportBackup.Font = new System.Drawing.Font("Segoe UI", 10F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnExportBackup.ForeColor = System.Drawing.Color.White;
            this.btnExportBackup.Location = new System.Drawing.Point(540, 15);
            this.btnExportBackup.Name = "btnExportBackup";
            this.btnExportBackup.Size = new System.Drawing.Size(120, 35);
            this.btnExportBackup.TabIndex = 4;
            this.btnExportBackup.Text = "📤 تصدير";
            this.btnExportBackup.UseVisualStyleBackColor = false;
            this.btnExportBackup.Click += new System.EventHandler(this.btnExportBackup_Click);
            //
            // btnRefresh
            //
            this.btnRefresh.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(149)))), ((int)(((byte)(165)))), ((int)(((byte)(166)))));
            this.btnRefresh.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnRefresh.Font = new System.Drawing.Font("Segoe UI", 10F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnRefresh.ForeColor = System.Drawing.Color.White;
            this.btnRefresh.Location = new System.Drawing.Point(670, 15);
            this.btnRefresh.Name = "btnRefresh";
            this.btnRefresh.Size = new System.Drawing.Size(120, 35);
            this.btnRefresh.TabIndex = 5;
            this.btnRefresh.Text = "🔄 تحديث";
            this.btnRefresh.UseVisualStyleBackColor = false;
            this.btnRefresh.Click += new System.EventHandler(this.btnRefresh_Click);
            //
            // progressBar
            //
            this.progressBar.Location = new System.Drawing.Point(820, 20);
            this.progressBar.Name = "progressBar";
            this.progressBar.Size = new System.Drawing.Size(300, 25);
            this.progressBar.Style = System.Windows.Forms.ProgressBarStyle.Continuous;
            this.progressBar.TabIndex = 6;
            this.progressBar.Visible = false;
            //
            // mainPanel
            //
            this.mainPanel.Controls.Add(this.dgvBackups);
            this.mainPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.mainPanel.Location = new System.Drawing.Point(0, 300);
            this.mainPanel.Name = "mainPanel";
            this.mainPanel.Padding = new System.Windows.Forms.Padding(20);
            this.mainPanel.Size = new System.Drawing.Size(1400, 470);
            this.mainPanel.TabIndex = 4;
            //
            // dgvBackups
            //
            this.dgvBackups.AllowUserToAddRows = false;
            this.dgvBackups.AllowUserToDeleteRows = false;
            this.dgvBackups.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgvBackups.BackgroundColor = System.Drawing.Color.White;
            this.dgvBackups.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dgvBackups.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgvBackups.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.FileName,
            this.FileSizeFormatted,
            this.CreatedDate,
            this.ModifiedDate,
            this.FilePath});
            this.dgvBackups.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dgvBackups.Location = new System.Drawing.Point(20, 20);
            this.dgvBackups.MultiSelect = false;
            this.dgvBackups.Name = "dgvBackups";
            this.dgvBackups.ReadOnly = true;
            this.dgvBackups.RowHeadersVisible = false;
            this.dgvBackups.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgvBackups.Size = new System.Drawing.Size(1360, 430);
            this.dgvBackups.TabIndex = 0;
            this.dgvBackups.CellDoubleClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.dgvBackups_CellDoubleClick);
            this.dgvBackups.SelectionChanged += new System.EventHandler(this.dgvBackups_SelectionChanged);
            //
            // FileName
            //
            this.FileName.DataPropertyName = "FileName";
            this.FileName.HeaderText = "اسم الملف";
            this.FileName.Name = "FileName";
            this.FileName.ReadOnly = true;
            //
            // FileSizeFormatted
            //
            this.FileSizeFormatted.DataPropertyName = "FileSizeFormatted";
            this.FileSizeFormatted.HeaderText = "حجم الملف";
            this.FileSizeFormatted.Name = "FileSizeFormatted";
            this.FileSizeFormatted.ReadOnly = true;
            //
            // CreatedDate
            //
            this.CreatedDate.DataPropertyName = "CreatedDate";
            this.CreatedDate.HeaderText = "تاريخ الإنشاء";
            this.CreatedDate.Name = "CreatedDate";
            this.CreatedDate.ReadOnly = true;
            //
            // ModifiedDate
            //
            this.ModifiedDate.DataPropertyName = "ModifiedDate";
            this.ModifiedDate.HeaderText = "تاريخ التعديل";
            this.ModifiedDate.Name = "ModifiedDate";
            this.ModifiedDate.ReadOnly = true;
            //
            // FilePath
            //
            this.FilePath.DataPropertyName = "FilePath";
            this.FilePath.HeaderText = "المسار";
            this.FilePath.Name = "FilePath";
            this.FilePath.ReadOnly = true;
            //
            // bottomPanel
            //
            this.bottomPanel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(52)))), ((int)(((byte)(73)))), ((int)(((byte)(94)))));
            this.bottomPanel.Controls.Add(this.lblStatus);
            this.bottomPanel.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.bottomPanel.Location = new System.Drawing.Point(0, 770);
            this.bottomPanel.Name = "bottomPanel";
            this.bottomPanel.Size = new System.Drawing.Size(1400, 30);
            this.bottomPanel.TabIndex = 5;
            //
            // lblStatus
            //
            this.lblStatus.AutoSize = true;
            this.lblStatus.Font = new System.Drawing.Font("Segoe UI", 10F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblStatus.ForeColor = System.Drawing.Color.White;
            this.lblStatus.Location = new System.Drawing.Point(20, 5);
            this.lblStatus.Name = "lblStatus";
            this.lblStatus.Size = new System.Drawing.Size(120, 19);
            this.lblStatus.TabIndex = 0;
            this.lblStatus.Text = "جاهز للاستخدام";
            //
            // BackupManagementForm
            //
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.White;
            this.ClientSize = new System.Drawing.Size(1400, 800);
            this.Controls.Add(this.mainPanel);
            this.Controls.Add(this.buttonsPanel);
            this.Controls.Add(this.settingsPanel);
            this.Controls.Add(this.statsPanel);
            this.Controls.Add(this.topPanel);
            this.Controls.Add(this.bottomPanel);
            this.Name = "BackupManagementForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "إدارة النسخ الاحتياطية - نظام إدارة الصيدلية";
            this.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            this.topPanel.ResumeLayout(false);
            this.statsPanel.ResumeLayout(false);
            this.statsPanel.PerformLayout();
            this.settingsPanel.ResumeLayout(false);
            this.settingsPanel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numKeepBackups)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numBackupInterval)).EndInit();
            this.buttonsPanel.ResumeLayout(false);
            this.mainPanel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgvBackups)).EndInit();
            this.bottomPanel.ResumeLayout(false);
            this.bottomPanel.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel topPanel;
        private System.Windows.Forms.Label lblTitle;
        private System.Windows.Forms.Panel statsPanel;
        private System.Windows.Forms.Label lblNextBackup;
        private System.Windows.Forms.Label lblLastBackup;
        private System.Windows.Forms.Label lblTotalSize;
        private System.Windows.Forms.Label lblTotalBackups;
        private System.Windows.Forms.Panel settingsPanel;
        private System.Windows.Forms.Button btnSaveSettings;
        private System.Windows.Forms.Button btnBrowseBackupPath;
        private System.Windows.Forms.TextBox txtBackupPath;
        private System.Windows.Forms.Label lblBackupPath;
        private System.Windows.Forms.NumericUpDown numKeepBackups;
        private System.Windows.Forms.Label lblKeepBackups;
        private System.Windows.Forms.NumericUpDown numBackupInterval;
        private System.Windows.Forms.Label lblBackupInterval;
        private System.Windows.Forms.CheckBox chkAutoBackup;
        private System.Windows.Forms.Panel buttonsPanel;
        private System.Windows.Forms.ProgressBar progressBar;
        private System.Windows.Forms.Button btnRefresh;
        private System.Windows.Forms.Button btnExportBackup;
        private System.Windows.Forms.Button btnVerifyBackup;
        private System.Windows.Forms.Button btnDeleteBackup;
        private System.Windows.Forms.Button btnRestoreBackup;
        private System.Windows.Forms.Button btnCreateBackup;
        private System.Windows.Forms.Panel mainPanel;
        private System.Windows.Forms.DataGridView dgvBackups;
        private System.Windows.Forms.DataGridViewTextBoxColumn FileName;
        private System.Windows.Forms.DataGridViewTextBoxColumn FileSizeFormatted;
        private System.Windows.Forms.DataGridViewTextBoxColumn CreatedDate;
        private System.Windows.Forms.DataGridViewTextBoxColumn ModifiedDate;
        private System.Windows.Forms.DataGridViewTextBoxColumn FilePath;
        private System.Windows.Forms.Panel bottomPanel;
        private System.Windows.Forms.Label lblStatus;
    }
}