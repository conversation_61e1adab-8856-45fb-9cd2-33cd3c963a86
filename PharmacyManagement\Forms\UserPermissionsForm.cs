using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نافذة إدارة صلاحيات المستخدم - User Permissions Form
    /// </summary>
    public partial class UserPermissionsForm : Form
    {
        #region Fields - الحقول

        private int _userID;
        private User _user;
        private List<UserPermission> _permissions;

        #endregion

        #region Constructor - المنشئ

        /// <summary>
        /// منشئ نافذة إدارة صلاحيات المستخدم
        /// </summary>
        /// <param name="userID">معرف المستخدم</param>
        public UserPermissionsForm(int userID)
        {
            InitializeComponent();
            _userID = userID;
            SetupForm();
            LoadData();
        }

        #endregion

        #region Form Setup - إعداد النافذة

        /// <summary>
        /// إعداد النافذة
        /// </summary>
        private void SetupForm()
        {
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            SetupPermissionsTree();
            ApplyFlatDesign();
        }

        /// <summary>
        /// إعداد شجرة الصلاحيات
        /// </summary>
        private void SetupPermissionsTree()
        {
            treePermissions.CheckBoxes = true;
            treePermissions.FullRowSelect = true;
            treePermissions.ShowLines = true;
            treePermissions.ShowPlusMinus = true;
            treePermissions.ShowRootLines = true;
        }

        /// <summary>
        /// تطبيق التصميم المسطح
        /// </summary>
        private void ApplyFlatDesign()
        {
            this.BackColor = Color.White;
            
            foreach (Control control in this.Controls)
            {
                if (control is Button button)
                {
                    button.FlatStyle = FlatStyle.Flat;
                    button.FlatAppearance.BorderSize = 0;
                }
            }
        }

        #endregion

        #region Data Loading - تحميل البيانات

        /// <summary>
        /// تحميل البيانات
        /// </summary>
        private void LoadData()
        {
            try
            {
                // تحميل بيانات المستخدم
                _user = UserManager.GetUserById(_userID);
                if (_user != null)
                {
                    this.Text = $"صلاحيات المستخدم - {_user.FullName}";
                    lblUserName.Text = _user.FullName;
                    lblUsername2.Text = _user.Username;
                    lblRole.Text = _user.Role;
                }

                // تحميل الصلاحيات
                LoadPermissions();
                
                // تحميل صلاحيات المستخدم الحالية
                LoadUserPermissions();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحميل جميع الصلاحيات المتاحة
        /// </summary>
        private void LoadPermissions()
        {
            try
            {
                treePermissions.Nodes.Clear();

                // إضافة العقد الرئيسية للصلاحيات
                var salesNode = new TreeNode("إدارة المبيعات") { Tag = "Sales" };
                salesNode.Nodes.Add(new TreeNode("عرض المبيعات") { Tag = "Sales.View" });
                salesNode.Nodes.Add(new TreeNode("إضافة مبيعات") { Tag = "Sales.Add" });
                salesNode.Nodes.Add(new TreeNode("تعديل المبيعات") { Tag = "Sales.Edit" });
                salesNode.Nodes.Add(new TreeNode("حذف المبيعات") { Tag = "Sales.Delete" });
                salesNode.Nodes.Add(new TreeNode("طباعة المبيعات") { Tag = "Sales.Print" });

                var inventoryNode = new TreeNode("إدارة المخزون") { Tag = "Inventory" };
                inventoryNode.Nodes.Add(new TreeNode("عرض المخزون") { Tag = "Inventory.View" });
                inventoryNode.Nodes.Add(new TreeNode("إضافة أدوية") { Tag = "Inventory.Add" });
                inventoryNode.Nodes.Add(new TreeNode("تعديل أدوية") { Tag = "Inventory.Edit" });
                inventoryNode.Nodes.Add(new TreeNode("حذف أدوية") { Tag = "Inventory.Delete" });
                inventoryNode.Nodes.Add(new TreeNode("طباعة المخزون") { Tag = "Inventory.Print" });

                var suppliersNode = new TreeNode("إدارة الموردين") { Tag = "Suppliers" };
                suppliersNode.Nodes.Add(new TreeNode("عرض الموردين") { Tag = "Suppliers.View" });
                suppliersNode.Nodes.Add(new TreeNode("إضافة موردين") { Tag = "Suppliers.Add" });
                suppliersNode.Nodes.Add(new TreeNode("تعديل موردين") { Tag = "Suppliers.Edit" });
                suppliersNode.Nodes.Add(new TreeNode("حذف موردين") { Tag = "Suppliers.Delete" });
                suppliersNode.Nodes.Add(new TreeNode("طباعة الموردين") { Tag = "Suppliers.Print" });

                var customersNode = new TreeNode("إدارة العملاء") { Tag = "Customers" };
                customersNode.Nodes.Add(new TreeNode("عرض العملاء") { Tag = "Customers.View" });
                customersNode.Nodes.Add(new TreeNode("إضافة عملاء") { Tag = "Customers.Add" });
                customersNode.Nodes.Add(new TreeNode("تعديل عملاء") { Tag = "Customers.Edit" });
                customersNode.Nodes.Add(new TreeNode("حذف عملاء") { Tag = "Customers.Delete" });
                customersNode.Nodes.Add(new TreeNode("طباعة العملاء") { Tag = "Customers.Print" });

                var reportsNode = new TreeNode("التقارير") { Tag = "Reports" };
                reportsNode.Nodes.Add(new TreeNode("عرض التقارير") { Tag = "Reports.View" });
                reportsNode.Nodes.Add(new TreeNode("تقارير المبيعات") { Tag = "Reports.Sales" });
                reportsNode.Nodes.Add(new TreeNode("تقارير المخزون") { Tag = "Reports.Inventory" });
                reportsNode.Nodes.Add(new TreeNode("التقارير المالية") { Tag = "Reports.Financial" });
                reportsNode.Nodes.Add(new TreeNode("طباعة التقارير") { Tag = "Reports.Print" });

                var usersNode = new TreeNode("إدارة المستخدمين") { Tag = "Users" };
                usersNode.Nodes.Add(new TreeNode("عرض المستخدمين") { Tag = "Users.View" });
                usersNode.Nodes.Add(new TreeNode("إضافة مستخدمين") { Tag = "Users.Add" });
                usersNode.Nodes.Add(new TreeNode("تعديل مستخدمين") { Tag = "Users.Edit" });
                usersNode.Nodes.Add(new TreeNode("حذف مستخدمين") { Tag = "Users.Delete" });
                usersNode.Nodes.Add(new TreeNode("إدارة الصلاحيات") { Tag = "Users.Permissions" });
                usersNode.Nodes.Add(new TreeNode("طباعة المستخدمين") { Tag = "Users.Print" });

                var settingsNode = new TreeNode("الإعدادات") { Tag = "Settings" };
                settingsNode.Nodes.Add(new TreeNode("عرض الإعدادات") { Tag = "Settings.View" });
                settingsNode.Nodes.Add(new TreeNode("تعديل الإعدادات") { Tag = "Settings.Edit" });
                settingsNode.Nodes.Add(new TreeNode("النسخ الاحتياطي") { Tag = "Settings.Backup" });
                settingsNode.Nodes.Add(new TreeNode("طباعة الإعدادات") { Tag = "Settings.Print" });

                treePermissions.Nodes.Add(salesNode);
                treePermissions.Nodes.Add(inventoryNode);
                treePermissions.Nodes.Add(suppliersNode);
                treePermissions.Nodes.Add(customersNode);
                treePermissions.Nodes.Add(reportsNode);
                treePermissions.Nodes.Add(usersNode);
                treePermissions.Nodes.Add(settingsNode);

                treePermissions.ExpandAll();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الصلاحيات: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحميل صلاحيات المستخدم الحالية
        /// </summary>
        private void LoadUserPermissions()
        {
            try
            {
                _permissions = UserManager.GetUserPermissions(_userID);

                // تحديد الصلاحيات المحددة في الشجرة
                foreach (var permission in _permissions)
                {
                    // تحديد الصلاحيات بناءً على اسم الوحدة والأذونات
                    string moduleKey = permission.ModuleName;
                    SetNodeChecked(treePermissions.Nodes, moduleKey, permission.CanView);

                    if (permission.CanAdd)
                        SetNodeChecked(treePermissions.Nodes, $"{moduleKey}.Add", true);
                    if (permission.CanEdit)
                        SetNodeChecked(treePermissions.Nodes, $"{moduleKey}.Edit", true);
                    if (permission.CanDelete)
                        SetNodeChecked(treePermissions.Nodes, $"{moduleKey}.Delete", true);
                    if (permission.CanPrint)
                        SetNodeChecked(treePermissions.Nodes, $"{moduleKey}.Print", true);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل صلاحيات المستخدم: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحديد حالة العقدة في الشجرة
        /// </summary>
        private void SetNodeChecked(TreeNodeCollection nodes, string permissionKey, bool isChecked)
        {
            foreach (TreeNode node in nodes)
            {
                if (node.Tag?.ToString() == permissionKey)
                {
                    node.Checked = isChecked;
                    return;
                }
                
                if (node.Nodes.Count > 0)
                {
                    SetNodeChecked(node.Nodes, permissionKey, isChecked);
                }
            }
        }

        #endregion

        #region Event Handlers - معالجات الأحداث

        /// <summary>
        /// حفظ الصلاحيات
        /// </summary>
        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                var selectedPermissions = new List<string>();
                GetCheckedPermissions(treePermissions.Nodes, selectedPermissions);

                // تحويل الصلاحيات المحددة إلى نموذج UserPermission
                var userPermissions = ConvertToUserPermissions(selectedPermissions);

                if (UserManager.UpdateUserPermissions(_userID, userPermissions))
                {
                    MessageBox.Show("تم حفظ الصلاحيات بنجاح", "نجح",
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("فشل في حفظ الصلاحيات", "خطأ",
                                  MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الصلاحيات: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إلغاء العملية
        /// </summary>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// تحديد جميع الصلاحيات
        /// </summary>
        private void btnSelectAll_Click(object sender, EventArgs e)
        {
            SetAllNodesChecked(treePermissions.Nodes, true);
        }

        /// <summary>
        /// إلغاء تحديد جميع الصلاحيات
        /// </summary>
        private void btnDeselectAll_Click(object sender, EventArgs e)
        {
            SetAllNodesChecked(treePermissions.Nodes, false);
        }

        /// <summary>
        /// الحصول على الصلاحيات المحددة
        /// </summary>
        private void GetCheckedPermissions(TreeNodeCollection nodes, List<string> permissions)
        {
            foreach (TreeNode node in nodes)
            {
                if (node.Checked && node.Tag != null)
                {
                    permissions.Add(node.Tag.ToString());
                }

                if (node.Nodes.Count > 0)
                {
                    GetCheckedPermissions(node.Nodes, permissions);
                }
            }
        }

        /// <summary>
        /// تحويل قائمة الصلاحيات المحددة إلى نموذج UserPermission
        /// </summary>
        private List<UserPermission> ConvertToUserPermissions(List<string> selectedPermissions)
        {
            var userPermissions = new List<UserPermission>();
            var modules = new Dictionary<string, UserPermission>();

            // تجميع الصلاحيات حسب الوحدة
            foreach (var permission in selectedPermissions)
            {
                string moduleName;
                string action;

                if (permission.Contains("."))
                {
                    var parts = permission.Split('.');
                    moduleName = parts[0];
                    action = parts[1];
                }
                else
                {
                    moduleName = permission;
                    action = "View";
                }

                if (!modules.ContainsKey(moduleName))
                {
                    modules[moduleName] = new UserPermission
                    {
                        UserID = _userID,
                        ModuleName = moduleName,
                        CanView = false,
                        CanAdd = false,
                        CanEdit = false,
                        CanDelete = false,
                        CanPrint = false
                    };
                }

                var userPermission = modules[moduleName];
                switch (action.ToLower())
                {
                    case "view":
                        userPermission.CanView = true;
                        break;
                    case "add":
                        userPermission.CanAdd = true;
                        break;
                    case "edit":
                        userPermission.CanEdit = true;
                        break;
                    case "delete":
                        userPermission.CanDelete = true;
                        break;
                    case "print":
                        userPermission.CanPrint = true;
                        break;
                }
            }

            userPermissions.AddRange(modules.Values);
            return userPermissions;
        }

        /// <summary>
        /// تحديد جميع العقد
        /// </summary>
        private void SetAllNodesChecked(TreeNodeCollection nodes, bool isChecked)
        {
            foreach (TreeNode node in nodes)
            {
                node.Checked = isChecked;
                
                if (node.Nodes.Count > 0)
                {
                    SetAllNodesChecked(node.Nodes, isChecked);
                }
            }
        }

        #endregion
    }
}
