using System;
using System.Drawing;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نافذة إضافة/تعديل المصروف - Expense Add/Edit Form
    /// </summary>
    public partial class ExpenseAddForm : Form
    {
        #region Fields - الحقول

        private bool _isEditMode;
        private Expense _expense;

        #endregion

        #region Constructor - المنشئ

        /// <summary>
        /// منشئ نافذة إضافة مصروف جديد
        /// </summary>
        public ExpenseAddForm()
        {
            InitializeComponent();
            _isEditMode = false;
            SetupForm();
            LoadCategories();
        }

        /// <summary>
        /// منشئ نافذة تعديل مصروف
        /// </summary>
        /// <param name="expense">المصروف المراد تعديله</param>
        public ExpenseAddForm(Expense expense)
        {
            InitializeComponent();
            _isEditMode = true;
            _expense = expense;
            SetupForm();
            LoadCategories();
            LoadExpenseData();
        }

        #endregion

        #region Form Setup - إعداد النافذة

        /// <summary>
        /// إعداد النافذة
        /// </summary>
        private void SetupForm()
        {
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            if (_isEditMode)
            {
                this.Text = "تعديل المصروف";
                lblTitle.Text = "✏️ تعديل المصروف";
            }
            else
            {
                this.Text = "إضافة مصروف جديد";
                lblTitle.Text = "➕ إضافة مصروف جديد";
            }

            ApplyFlatDesign();
        }

        /// <summary>
        /// تطبيق التصميم المسطح
        /// </summary>
        private void ApplyFlatDesign()
        {
            this.BackColor = Color.White;
            
            foreach (Control control in this.Controls)
            {
                if (control is Button button)
                {
                    button.FlatStyle = FlatStyle.Flat;
                    button.FlatAppearance.BorderSize = 0;
                }
            }
        }

        #endregion

        #region Data Loading - تحميل البيانات

        /// <summary>
        /// تحميل فئات المصروفات
        /// </summary>
        private void LoadCategories()
        {
            try
            {
                cmbCategory.Items.Clear();
                cmbCategory.Items.AddRange(new string[]
                {
                    "مصروفات إدارية",
                    "مصروفات تشغيلية",
                    "مصروفات صيانة",
                    "مصروفات تسويق",
                    "مصروفات نقل",
                    "مصروفات اتصالات",
                    "مصروفات كهرباء",
                    "مصروفات مياه",
                    "مصروفات إيجار",
                    "مصروفات أخرى"
                });

                cmbPaymentMethod.Items.Clear();
                cmbPaymentMethod.Items.AddRange(new string[]
                {
                    "نقدي",
                    "شيك",
                    "تحويل بنكي",
                    "بطاقة ائتمان",
                    "بطاقة خصم"
                });

                if (!_isEditMode)
                {
                    cmbCategory.SelectedIndex = 0;
                    cmbPaymentMethod.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
                LogManager.LogError($"ExpenseAddForm.LoadCategories: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل بيانات المصروف للتعديل
        /// </summary>
        private void LoadExpenseData()
        {
            if (_expense != null)
            {
                txtExpenseNumber.Text = _expense.ExpenseNumber;
                dtpExpenseDate.Value = _expense.ExpenseDate;
                cmbCategory.Text = _expense.Category;
                txtDescription.Text = _expense.Description;
                numAmount.Value = _expense.Amount;
                cmbPaymentMethod.Text = _expense.PaymentMethod;
                txtNotes.Text = _expense.Notes;

                // منع تعديل رقم المصروف
                txtExpenseNumber.ReadOnly = true;
                txtExpenseNumber.BackColor = Color.LightGray;
            }
        }

        #endregion

        #region Event Handlers - معالجات الأحداث

        /// <summary>
        /// حفظ المصروف
        /// </summary>
        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                var expense = new  Expense
                {
                    ExpenseNumber = _isEditMode ? _expense.ExpenseNumber : FinancialManager.GenerateExpenseNumber(),
                    ExpenseDate = dtpExpenseDate.Value,
                    Category = cmbCategory.Text.Trim(),
                    Description = txtDescription.Text.Trim(),
                    Amount = numAmount.Value,
                    PaymentMethod = cmbPaymentMethod.Text.Trim(),
                    Notes = txtNotes.Text.Trim(),
                    CreatedBy = UserManager.CurrentUser?.UserID
                };

                bool success;
                if (_isEditMode)
                {
                    expense.ExpenseID = _expense.ExpenseID;
                    success = FinancialManager.UpdateExpense(expense);
                }
                else
                {
                    int expenseId = FinancialManager.AddExpense(expense);
                    success = expenseId > 0;
                }

                if (success)
                {
                    string action = _isEditMode ? "تعديل" : "إضافة";
                    MessageBox.Show($"تم {action} المصروف بنجاح", "نجح", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    string action = _isEditMode ? "تعديل" : "إضافة";
                    MessageBox.Show($"فشل في {action} المصروف", "خطأ", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ المصروف: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
                LogManager.LogError($"ExpenseAddForm.btnSave_Click: {ex.Message}");
            }
        }

        /// <summary>
        /// إلغاء العملية
        /// </summary>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        #endregion

        #region Validation - التحقق من صحة البيانات

        /// <summary>
        /// التحقق من صحة البيانات المدخلة
        /// </summary>
        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(cmbCategory.Text))
            {
                MessageBox.Show("يرجى اختيار فئة المصروف", "تنبيه", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbCategory.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtDescription.Text))
            {
                MessageBox.Show("يرجى إدخال وصف المصروف", "تنبيه", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtDescription.Focus();
                return false;
            }

            if (numAmount.Value <= 0)
            {
                MessageBox.Show("يرجى إدخال مبلغ صحيح", "تنبيه", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                numAmount.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(cmbPaymentMethod.Text))
            {
                MessageBox.Show("يرجى اختيار طريقة الدفع", "تنبيه", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbPaymentMethod.Focus();
                return false;
            }

            if (dtpExpenseDate.Value > DateTime.Now)
            {
                MessageBox.Show("لا يمكن أن يكون تاريخ المصروف في المستقبل", "تنبيه", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                dtpExpenseDate.Focus();
                return false;
            }

            return true;
        }

        #endregion
    }
}
