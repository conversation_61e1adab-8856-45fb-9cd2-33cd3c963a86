using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نموذج البحث السريع المحسن - Enhanced Quick Search Form
    /// تصميم مسطح حديث مع بيانات تجريبية شاملة
    /// </summary>
    public partial class QuickSearchForm : Form
    {
        #region Fields

        private BindingSource _resultsBindingSource;
        private string _currentSearchType = "الأدوية";
        private List<QuickSearchDrug> _sampleDrugs;
        private List<QuickSearchCustomer> _sampleCustomers;
        private List<QuickSearchSupplier> _sampleSuppliers;
        private List<QuickSearchInvoice> _sampleInvoices;
        private List<QuickSearchStock> _sampleStock;
        private List<QuickSearchTransfer> _sampleTransfers;

        #endregion

        #region Constructor

        public QuickSearchForm()
        {
            InitializeComponent();
            SetupForm();
            SetupDataGrid();
            SetupSearchTypes();
        }

        #endregion

        #region Setup Methods

        private void SetupForm()
        {
            // إعداد النموذج الأساسي
            this.Text = "🔍 البحث السريع";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.BackColor = Color.White;
            this.Font = new Font("Segoe UI", 9F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // إعداد التصميم المسطح
            SetupFlatDesign();

            // إعداد الأحداث
            SetupEvents();

            // تحميل البيانات التجريبية
            LoadSampleData();

            // تحديث placeholder
            UpdateSearchPlaceholder();
        }

        private void SetupFlatDesign()
        {
            // ألوان الأزرار
            btnSearch.BackColor = Color.FromArgb(52, 152, 219);
            btnSearch.ForeColor = Color.White;
            btnClear.BackColor = Color.FromArgb(149, 165, 166);
            btnClear.ForeColor = Color.White;
            btnClose.BackColor = Color.FromArgb(231, 76, 60);
            btnClose.ForeColor = Color.White;

            // إعداد الأزرار
            foreach (Control control in this.Controls)
            {
                if (control is Button btn)
                {
                    btn.FlatStyle = FlatStyle.Flat;
                    btn.FlatAppearance.BorderSize = 0;
                    btn.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
                }
                else if (control is TextBox txt)
                {
                    txt.BorderStyle = BorderStyle.FixedSingle;
                    txt.Font = new Font("Segoe UI", 12F);
                }
                else if (control is ComboBox cmb)
                {
                    cmb.FlatStyle = FlatStyle.Flat;
                    cmb.Font = new Font("Segoe UI", 10F);
                }
            }

            // تكبير مربع البحث
            txtSearch.Height = 35;
            txtSearch.Font = new Font("Segoe UI", 12F);
        }

        private void SetupDataGrid()
        {
            // إعداد شبكة النتائج
            dgvResults.AutoGenerateColumns = false;
            dgvResults.AllowUserToAddRows = false;
            dgvResults.AllowUserToDeleteRows = false;
            dgvResults.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvResults.MultiSelect = false;
            dgvResults.BackgroundColor = Color.White;
            dgvResults.BorderStyle = BorderStyle.FixedSingle;
            dgvResults.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 152, 219);
            dgvResults.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvResults.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            dgvResults.DefaultCellStyle.Font = new Font("Segoe UI", 9F);
            dgvResults.RowHeadersVisible = false;
            dgvResults.EnableHeadersVisualStyles = false;
            dgvResults.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;

            // إعداد مصدر البيانات
            _resultsBindingSource = new BindingSource();
            dgvResults.DataSource = _resultsBindingSource;
        }

        private void SetupSearchTypes()
        {
            var searchTypes = new[]
            {
                "الأدوية",
                "العملاء", 
                "الموردين",
                "الفواتير",
                "المخزون",
                "التحويلات"
            };

            cmbSearchType.Items.AddRange(searchTypes);
            cmbSearchType.SelectedIndex = 0;
        }

        private void SetupEvents()
        {
            btnSearch.Click += BtnSearch_Click;
            btnClear.Click += BtnClear_Click;
            btnClose.Click += BtnClose_Click;

            txtSearch.KeyPress += TxtSearch_KeyPress;
            cmbSearchType.SelectedIndexChanged += CmbSearchType_SelectedIndexChanged;

            dgvResults.CellDoubleClick += DgvResults_CellDoubleClick;
        }

        private void LoadSampleData()
        {
            try
            {
                // تحميل الأدوية التجريبية
                _sampleDrugs = new List<QuickSearchDrug>
                {
                    new QuickSearchDrug { DrugID = 1, DrugName = "باراسيتامول 500 مجم", DrugCode = "MED001", Category = "مسكنات", SalePrice = 2.50m, Stock = 100, Supplier = "شركة الدواء الأولى" },
                    new QuickSearchDrug { DrugID = 2, DrugName = "أموكسيسيلين 250 مجم", DrugCode = "MED002", Category = "مضادات حيوية", SalePrice = 8.75m, Stock = 50, Supplier = "شركة المضادات الحيوية" },
                    new QuickSearchDrug { DrugID = 3, DrugName = "إيبوبروفين 400 مجم", DrugCode = "MED003", Category = "مضادات التهاب", SalePrice = 3.25m, Stock = 75, Supplier = "شركة الدواء الثانية" },
                    new QuickSearchDrug { DrugID = 4, DrugName = "أسبرين 100 مجم", DrugCode = "MED004", Category = "مضادات تجلط", SalePrice = 1.50m, Stock = 200, Supplier = "شركة القلب والأوعية" },
                    new QuickSearchDrug { DrugID = 5, DrugName = "فيتامين د 1000 وحدة", DrugCode = "MED005", Category = "فيتامينات", SalePrice = 12.00m, Stock = 30, Supplier = "شركة الفيتامينات" },
                    new QuickSearchDrug { DrugID = 6, DrugName = "أوميجا 3 كبسولات", DrugCode = "MED006", Category = "مكملات غذائية", SalePrice = 15.50m, Stock = 40, Supplier = "شركة المكملات" },
                    new QuickSearchDrug { DrugID = 7, DrugName = "كالسيوم + مغنيسيوم", DrugCode = "MED007", Category = "مكملات عظام", SalePrice = 9.25m, Stock = 60, Supplier = "شركة العظام" },
                    new QuickSearchDrug { DrugID = 8, DrugName = "لوراتادين 10 مجم", DrugCode = "MED008", Category = "مضادات حساسية", SalePrice = 4.75m, Stock = 25, Supplier = "شركة الحساسية" },
                    new QuickSearchDrug { DrugID = 9, DrugName = "سيتريزين 10 مجم", DrugCode = "MED009", Category = "مضادات حساسية", SalePrice = 3.50m, Stock = 35, Supplier = "شركة الحساسية" },
                    new QuickSearchDrug { DrugID = 10, DrugName = "ديكلوفيناك 50 مجم", DrugCode = "MED010", Category = "مضادات التهاب", SalePrice = 2.75m, Stock = 80, Supplier = "شركة المفاصل" }
                };

                // تحميل العملاء التجريبيين
                _sampleCustomers = new List<QuickSearchCustomer>
                {
                    new QuickSearchCustomer { CustomerID = 1, CustomerName = "أحمد محمد علي", Phone = "777123456", Address = "شارع الزبيري، صنعاء", TotalPurchases = 1250.50m, LastVisit = DateTime.Now.AddDays(-2) },
                    new QuickSearchCustomer { CustomerID = 2, CustomerName = "فاطمة حسن أحمد", Phone = "777234567", Address = "شارع الستين، صنعاء", TotalPurchases = 890.25m, LastVisit = DateTime.Now.AddDays(-5) },
                    new QuickSearchCustomer { CustomerID = 3, CustomerName = "محمد عبدالله صالح", Phone = "777345678", Address = "شارع الثورة، صنعاء", TotalPurchases = 2100.75m, LastVisit = DateTime.Now.AddDays(-1) },
                    new QuickSearchCustomer { CustomerID = 4, CustomerName = "عائشة علي محمد", Phone = "777456789", Address = "شارع الحصبة، صنعاء", TotalPurchases = 650.00m, LastVisit = DateTime.Now.AddDays(-7) },
                    new QuickSearchCustomer { CustomerID = 5, CustomerName = "يوسف إبراهيم حسن", Phone = "777567890", Address = "شارع الجامعة، صنعاء", TotalPurchases = 1800.30m, LastVisit = DateTime.Now.AddDays(-3) }
                };

                Console.WriteLine("تم تحميل البيانات التجريبية بنجاح");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات التجريبية: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateSearchPlaceholder()
        {
            switch (cmbSearchType.Text)
            {
                case "الأدوية":
                    label1.Text = "ابحث عن دواء بالاسم أو الكود...";
                    break;
                case "العملاء":
                    label1.Text = "ابحث عن عميل بالاسم أو الهاتف...";
                    break;
                case "الموردين":
                    label1.Text = "ابحث عن مورد بالاسم أو الهاتف...";
                    break;
                case "الفواتير":
                    label1.Text = "ابحث عن فاتورة بالرقم أو العميل...";
                    break;
                case "المخزون":
                    label1.Text = "ابحث في المخزون بالدواء...";
                    break;
                case "التحويلات":
                    label1.Text = "ابحث عن تحويل بالرقم أو الفرع...";
                    break;
                default:
                    label1.Text = "أدخل نص البحث...";
                    break;
            }
        }

        #endregion

        #region Event Handlers

        private void BtnSearch_Click(object sender, EventArgs e)
        {
            try
            {
                var searchTerm = txtSearch.Text.Trim();
                if (string.IsNullOrWhiteSpace(searchTerm))
                {
                    MessageBox.Show("يرجى إدخال نص البحث", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtSearch.Focus();
                    return;
                }

                PerformSearch(searchTerm);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnClear_Click(object sender, EventArgs e)
        {
            txtSearch.Clear();
            _resultsBindingSource.DataSource = null;
            lblResultsCount.Text = "عدد النتائج: 0";
            txtSearch.Focus();
        }

        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void TxtSearch_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                BtnSearch_Click(sender, e);
                e.Handled = true;
            }
        }

        private void CmbSearchType_SelectedIndexChanged(object sender, EventArgs e)
        {
            _currentSearchType = cmbSearchType.Text;
            SetupColumnsForSearchType();
            
            // مسح النتائج السابقة
            _resultsBindingSource.DataSource = null;
            lblResultsCount.Text = "عدد النتائج: 0";
            
            // تحديث placeholder
            UpdateSearchPlaceholder();
        }

        private void DgvResults_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                OpenDetailsForm();
            }
        }

        #endregion

        #region Search Methods

        private void PerformSearch(string searchTerm)
        {
            try
            {
                object results = null;

                switch (_currentSearchType)
                {
                    case "الأدوية":
                        results = SearchDrugs(searchTerm);
                        break;
                    case "العملاء":
                        results = SearchCustomers(searchTerm);
                        break;
                    case "الموردين":
                        results = SearchSuppliers(searchTerm);
                        break;
                    case "الفواتير":
                        results = SearchInvoices(searchTerm);
                        break;
                    case "المخزون":
                        results = SearchStock(searchTerm);
                        break;
                    case "التحويلات":
                        results = SearchTransfers(searchTerm);
                        break;
                }

                _resultsBindingSource.DataSource = results;

                // تحديث عدد النتائج
                var count = 0;
                if (results is System.Collections.IList list)
                {
                    count = list.Count;
                }

                lblResultsCount.Text = $"عدد النتائج: {count}";

                if (count == 0)
                {
                    MessageBox.Show("لم يتم العثور على نتائج", "البحث",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show($"تم العثور على {count} نتيجة ✅", "البحث",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private List<QuickSearchDrug> SearchDrugs(string searchTerm)
        {
            return _sampleDrugs?.Where(d =>
                d.DrugName.Contains(searchTerm) ||
                d.DrugCode.Contains(searchTerm) ||
                d.Category.Contains(searchTerm) ||
                d.Supplier.Contains(searchTerm)
            ).ToList() ?? new List<QuickSearchDrug>();
        }

        private List<QuickSearchCustomer> SearchCustomers(string searchTerm)
        {
            return _sampleCustomers?.Where(c =>
                c.CustomerName.Contains(searchTerm) ||
                c.Phone.Contains(searchTerm) ||
                c.Address.Contains(searchTerm)
            ).ToList() ?? new List<QuickSearchCustomer>();
        }

        private List<QuickSearchSupplier> SearchSuppliers(string searchTerm)
        {
            return _sampleSuppliers?.Where(s =>
                s.SupplierName.Contains(searchTerm) ||
                s.Phone.Contains(searchTerm) ||
                s.Email.Contains(searchTerm)
            ).ToList() ?? new List<QuickSearchSupplier>();
        }

        private List<QuickSearchInvoice> SearchInvoices(string searchTerm)
        {
            return _sampleInvoices?.Where(i =>
                i.InvoiceNumber.Contains(searchTerm) ||
                i.CustomerName.Contains(searchTerm) ||
                i.Status.Contains(searchTerm)
            ).ToList() ?? new List<QuickSearchInvoice>();
        }

        private List<QuickSearchStock> SearchStock(string searchTerm)
        {
            return _sampleStock?.Where(s =>
                s.DrugName.Contains(searchTerm) ||
                s.Status.Contains(searchTerm)
            ).ToList() ?? new List<QuickSearchStock>();
        }

        private List<QuickSearchTransfer> SearchTransfers(string searchTerm)
        {
            return _sampleTransfers?.Where(t =>
                t.TransferNumber.Contains(searchTerm) ||
                t.FromBranch.Contains(searchTerm) ||
                t.ToBranch.Contains(searchTerm) ||
                t.Status.Contains(searchTerm)
            ).ToList() ?? new List<QuickSearchTransfer>();
        }

        private void SetupColumnsForSearchType()
        {
            dgvResults.Columns.Clear();

            switch (_currentSearchType)
            {
                case "الأدوية":
                    SetupDrugsColumns();
                    break;
                case "العملاء":
                    SetupCustomersColumns();
                    break;
                case "الموردين":
                    SetupSuppliersColumns();
                    break;
                case "الفواتير":
                    SetupInvoicesColumns();
                    break;
                case "المخزون":
                    SetupStockColumns();
                    break;
                case "التحويلات":
                    SetupTransfersColumns();
                    break;
            }
        }

        private void SetupDrugsColumns()
        {
            dgvResults.Columns.Clear();

            dgvResults.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "DrugCode",
                HeaderText = "الكود",
                DataPropertyName = "DrugCode",
                FillWeight = 15
            });

            dgvResults.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "DrugName",
                HeaderText = "اسم الدواء",
                DataPropertyName = "DrugName",
                FillWeight = 30
            });

            dgvResults.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Category",
                HeaderText = "الفئة",
                DataPropertyName = "Category",
                FillWeight = 20
            });

            dgvResults.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "SalePrice",
                HeaderText = "سعر البيع",
                DataPropertyName = "SalePrice",
                FillWeight = 15,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "F2" }
            });

            dgvResults.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Stock",
                HeaderText = "المخزون",
                DataPropertyName = "Stock",
                FillWeight = 10
            });

            dgvResults.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Supplier",
                HeaderText = "المورد",
                DataPropertyName = "Supplier",
                FillWeight = 20
            });
        }

        private void SetupCustomersColumns()
        {
            dgvResults.Columns.Clear();

            dgvResults.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CustomerName",
                HeaderText = "اسم العميل",
                DataPropertyName = "CustomerName",
                FillWeight = 30
            });

            dgvResults.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Phone",
                HeaderText = "الهاتف",
                DataPropertyName = "Phone",
                FillWeight = 20
            });

            dgvResults.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Address",
                HeaderText = "العنوان",
                DataPropertyName = "Address",
                FillWeight = 25
            });

            dgvResults.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TotalPurchases",
                HeaderText = "إجمالي المشتريات",
                DataPropertyName = "TotalPurchases",
                FillWeight = 15,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "F2" }
            });

            dgvResults.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "LastVisit",
                HeaderText = "آخر زيارة",
                DataPropertyName = "LastVisit",
                FillWeight = 15,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy/MM/dd" }
            });
        }

        private void SetupSuppliersColumns()
        {
            dgvResults.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "SupplierCode",
                HeaderText = "كود المورد",
                DataPropertyName = "SupplierCode",
                FillWeight = 15
            });

            dgvResults.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "SupplierName",
                HeaderText = "اسم المورد",
                DataPropertyName = "SupplierName",
                FillWeight = 30
            });

            dgvResults.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ContactPerson",
                HeaderText = "الشخص المسؤول",
                DataPropertyName = "ContactPerson",
                FillWeight = 25
            });

            dgvResults.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Phone",
                HeaderText = "الهاتف",
                DataPropertyName = "Phone",
                FillWeight = 20
            });

            dgvResults.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "City",
                HeaderText = "المدينة",
                DataPropertyName = "City",
                FillWeight = 10
            });
        }

        private void SetupInvoicesColumns()
        {
            dgvResults.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "InvoiceNumber",
                HeaderText = "رقم الفاتورة",
                DataPropertyName = "InvoiceNumber",
                FillWeight = 15
            });

            dgvResults.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "InvoiceDate",
                HeaderText = "التاريخ",
                DataPropertyName = "InvoiceDate",
                FillWeight = 15
            });

            dgvResults.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CustomerName",
                HeaderText = "العميل",
                DataPropertyName = "CustomerName",
                FillWeight = 25
            });

            dgvResults.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TotalAmount",
                HeaderText = "المبلغ الإجمالي",
                DataPropertyName = "TotalAmount",
                FillWeight = 15
            });

            dgvResults.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "InvoiceType",
                HeaderText = "نوع الفاتورة",
                DataPropertyName = "InvoiceType",
                FillWeight = 15
            });

            dgvResults.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CreatedByName",
                HeaderText = "أنشئت بواسطة",
                DataPropertyName = "CreatedByName",
                FillWeight = 15
            });
        }

        private void SetupStockColumns()
        {
            dgvResults.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "DrugName",
                HeaderText = "اسم الدواء",
                DataPropertyName = "DrugName",
                FillWeight = 30
            });

            dgvResults.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "WarehouseName",
                HeaderText = "المخزن",
                DataPropertyName = "WarehouseName",
                FillWeight = 20
            });

            dgvResults.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "BatchNumber",
                HeaderText = "رقم الدفعة",
                DataPropertyName = "BatchNumber",
                FillWeight = 15
            });

            dgvResults.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Quantity",
                HeaderText = "الكمية",
                DataPropertyName = "Quantity",
                FillWeight = 10
            });

            dgvResults.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ExpiryDate",
                HeaderText = "تاريخ الانتهاء",
                DataPropertyName = "ExpiryDate",
                FillWeight = 15
            });

            dgvResults.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "UnitCost",
                HeaderText = "سعر الوحدة",
                DataPropertyName = "UnitCost",
                FillWeight = 10
            });
        }

        private void SetupTransfersColumns()
        {
            dgvResults.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TransferNumber",
                HeaderText = "رقم التحويل",
                DataPropertyName = "TransferNumber",
                FillWeight = 15
            });

            dgvResults.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TransferDate",
                HeaderText = "التاريخ",
                DataPropertyName = "TransferDate",
                FillWeight = 15
            });

            dgvResults.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "FromWarehouseName",
                HeaderText = "من مخزن",
                DataPropertyName = "FromWarehouseName",
                FillWeight = 20
            });

            dgvResults.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ToWarehouseName",
                HeaderText = "إلى مخزن",
                DataPropertyName = "ToWarehouseName",
                FillWeight = 20
            });

            dgvResults.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Status",
                HeaderText = "الحالة",
                DataPropertyName = "Status",
                FillWeight = 15
            });

            dgvResults.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "RequestedByName",
                HeaderText = "طلب بواسطة",
                DataPropertyName = "RequestedByName",
                FillWeight = 15
            });
        }



        private void OpenDetailsForm()
        {
            try
            {
                if (dgvResults.SelectedRows.Count > 0)
                {
                    var selectedItem = dgvResults.SelectedRows[0].DataBoundItem;
                    
                    switch (_currentSearchType)
                    {
                        case "الأدوية":
                            if (selectedItem is Drug drug)
                            {
                                using (var drugForm = new DrugAddEditForm(drug))
                                {
                                    drugForm.ShowDialog();
                                }
                            }
                            break;
                        case "العملاء":
                            if (selectedItem is Customer customer)
                            {
                                using (var customerForm = new CustomerAddEditForm(customer))
                                {
                                    customerForm.ShowDialog();
                                }
                            }
                            break;
                        // يمكن إضافة المزيد من الحالات هنا
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح التفاصيل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion
    }

    #region Quick Search Models

    /// <summary>
    /// نموذج دواء للبحث السريع
    /// </summary>
    public class QuickSearchDrug
    {
        public int DrugID { get; set; }
        public string DrugName { get; set; }
        public string DrugCode { get; set; }
        public string Category { get; set; }
        public decimal SalePrice { get; set; }
        public int Stock { get; set; }
        public string Supplier { get; set; }
    }

    /// <summary>
    /// نموذج عميل للبحث السريع
    /// </summary>
    public class QuickSearchCustomer
    {
        public int CustomerID { get; set; }
        public string CustomerName { get; set; }
        public string Phone { get; set; }
        public string Address { get; set; }
        public decimal TotalPurchases { get; set; }
        public DateTime LastVisit { get; set; }
    }

    /// <summary>
    /// نموذج مورد للبحث السريع
    /// </summary>
    public class QuickSearchSupplier
    {
        public int SupplierID { get; set; }
        public string SupplierName { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
        public int TotalOrders { get; set; }
        public DateTime LastOrder { get; set; }
    }

    /// <summary>
    /// نموذج فاتورة للبحث السريع
    /// </summary>
    public class QuickSearchInvoice
    {
        public int InvoiceID { get; set; }
        public string InvoiceNumber { get; set; }
        public string CustomerName { get; set; }
        public DateTime InvoiceDate { get; set; }
        public decimal TotalAmount { get; set; }
        public string Status { get; set; }
    }

    /// <summary>
    /// نموذج مخزون للبحث السريع
    /// </summary>
    public class QuickSearchStock
    {
        public string DrugName { get; set; }
        public int CurrentStock { get; set; }
        public int MinimumStock { get; set; }
        public int MaximumStock { get; set; }
        public DateTime LastUpdate { get; set; }
        public string Status { get; set; }
    }

    /// <summary>
    /// نموذج تحويل للبحث السريع
    /// </summary>
    public class QuickSearchTransfer
    {
        public int TransferID { get; set; }
        public string TransferNumber { get; set; }
        public string FromBranch { get; set; }
        public string ToBranch { get; set; }
        public DateTime TransferDate { get; set; }
        public string Status { get; set; }
        public int ItemsCount { get; set; }
    }

    #endregion
}
