using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using PharmacyManagement.Models;

namespace PharmacyManagement.Classes
{
    /// <summary>
    /// مدير الفروع المحسن - Enhanced Branch Manager
    /// تم إزالة جميع التكرارات وتحسين الأداء
    /// </summary>
    public static class BranchManager
    {
        #region Branch Management - إدارة الفروع

        /// <summary>
        /// إضافة فرع جديد
        /// </summary>
        /// <param name="branch">بيانات الفرع</param>
        /// <returns>معرف الفرع الجديد</returns>
        public static int AddBranch(Branch branch)
        {
            try
            {
                // التحقق من صحة البيانات
                ValidateBranchData(branch);

                // التحقق من عدم تكرار كود الفرع
                if (IsBranchCodeExists(branch.BranchCode))
                {
                    throw new Exception("كود الفرع موجود مسبقاً");
                }

                // التحقق من وجود فرع رئيسي واحد فقط
                if (branch.IsMainBranch && HasMainBranch())
                {
                    throw new Exception("يوجد فرع رئيسي بالفعل. لا يمكن وجود أكثر من فرع رئيسي واحد");
                }

                string query = @"
                    INSERT INTO Branches (BranchCode, BranchName, Address, Phone, Email, City, 
                                        Manager, IsActive, CreatedDate, CreatedBy)
                    VALUES (@BranchCode, @BranchName, @Address, @Phone, @Email, @City, 
                           @Manager, @IsActive, @CreatedDate, @CreatedBy);
                    SELECT SCOPE_IDENTITY();";

                var parameters = new[]
                {
                    DatabaseHelper.CreateParameter("@BranchCode", branch.BranchCode),
                    DatabaseHelper.CreateParameter("@BranchName", branch.BranchName),
                    DatabaseHelper.CreateParameter("@Address", branch.Address ?? ""),
                    DatabaseHelper.CreateParameter("@Phone", branch.Phone ?? ""),
                    DatabaseHelper.CreateParameter("@Email", branch.Email ?? ""),
                    DatabaseHelper.CreateParameter("@City", branch.City ?? ""),
                    DatabaseHelper.CreateParameter("@Manager", branch.ManagerName ?? ""),
                    DatabaseHelper.CreateParameter("@IsActive", branch.IsActive),
                    DatabaseHelper.CreateParameter("@CreatedDate", DateTime.Now),
                    DatabaseHelper.CreateParameter("@CreatedBy", UserManager.CurrentUser?.UserID)
                };

                var result = DatabaseHelper.ExecuteScalar(query, parameters);
                int branchId = Convert.ToInt32(result);

                if (branchId > 0)
                {
                    LogManager.LogInfo($"تم إضافة فرع جديد: {branch.BranchCode} - {branch.BranchName}");
                }

                return branchId;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في إضافة الفرع: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// تحديث فرع موجود
        /// </summary>
        /// <param name="branch">بيانات الفرع المحدثة</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public static bool UpdateBranch(Branch branch)
        {
            try
            {
                // التحقق من صحة البيانات
                ValidateBranchData(branch);

                // التحقق من عدم تكرار كود الفرع
                if (IsBranchCodeExists(branch.BranchCode, branch.BranchID))
                {
                    throw new Exception("كود الفرع موجود مسبقاً");
                }

                // التحقق من وجود فرع رئيسي واحد فقط
                if (branch.IsMainBranch && HasMainBranch(branch.BranchID))
                {
                    throw new Exception("يوجد فرع رئيسي بالفعل. لا يمكن وجود أكثر من فرع رئيسي واحد");
                }

                string query = @"
                    UPDATE Branches SET 
                        BranchCode = @BranchCode,
                        BranchName = @BranchName,
                        Address = @Address,
                        Phone = @Phone,
                        Email = @Email,
                        City = @City,
                        Manager = @Manager,
                        IsActive = @IsActive,
                        ModifiedDate = @ModifiedDate,
                        ModifiedBy = @ModifiedBy
                    WHERE BranchID = @BranchID";

                var parameters = new[]
                {
                    DatabaseHelper.CreateParameter("@BranchID", branch.BranchID),
                    DatabaseHelper.CreateParameter("@BranchCode", branch.BranchCode),
                    DatabaseHelper.CreateParameter("@BranchName", branch.BranchName),
                    DatabaseHelper.CreateParameter("@Address", branch.Address ?? ""),
                    DatabaseHelper.CreateParameter("@Phone", branch.Phone ?? ""),
                    DatabaseHelper.CreateParameter("@Email", branch.Email ?? ""),
                    DatabaseHelper.CreateParameter("@City", branch.City ?? ""),
                    DatabaseHelper.CreateParameter("@Manager", branch.ManagerName ?? ""),
                    DatabaseHelper.CreateParameter("@IsActive", branch.IsActive),
                    DatabaseHelper.CreateParameter("@ModifiedDate", DateTime.Now),
                    DatabaseHelper.CreateParameter("@ModifiedBy", UserManager.CurrentUser?.UserID)
                };

                int rowsAffected = DatabaseHelper.ExecuteNonQuery(query, parameters);

                if (rowsAffected > 0)
                {
                    LogManager.LogInfo($"تم تحديث الفرع: {branch.BranchCode} - {branch.BranchName}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تحديث الفرع: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// حذف فرع
        /// </summary>
        /// <param name="branchId">معرف الفرع</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public static bool DeleteBranch(int branchId)
        {
            try
            {
                // التحقق من عدم حذف الفرع الرئيسي
                var branch = GetBranch(branchId);
                if (branch != null && branch.IsMainBranch)
                {
                    throw new Exception("لا يمكن حذف الفرع الرئيسي");
                }

                // التحقق من عدم وجود مخازن مرتبطة
                if (HasRelatedWarehouses(branchId))
                {
                    throw new Exception("لا يمكن حذف الفرع لوجود مخازن مرتبطة به");
                }

                // التحقق من عدم وجود مستخدمين مرتبطين
                if (HasRelatedUsers(branchId))
                {
                    throw new Exception("لا يمكن حذف الفرع لوجود مستخدمين مرتبطين به");
                }

                string query = "DELETE FROM Branches WHERE BranchID = @BranchID";
                var parameters = new[] { DatabaseHelper.CreateParameter("@BranchID", branchId) };

                int rowsAffected = DatabaseHelper.ExecuteNonQuery(query, parameters);

                if (rowsAffected > 0)
                {
                    LogManager.LogInfo($"تم حذف الفرع رقم: {branchId}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في حذف الفرع: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Data Retrieval - استرجاع البيانات

        /// <summary>
        /// الحصول على فرع بالمعرف
        /// </summary>
        /// <param name="branchId">معرف الفرع</param>
        /// <returns>بيانات الفرع</returns>
        public static Branch GetBranch(int branchId)
        {
            try
            {
                string query = @"
                    SELECT BranchID, BranchCode, BranchName, Address, Phone, Email, 
                           City, Manager, IsActive, CreatedDate, CreatedBy
                    FROM Branches 
                    WHERE BranchID = @BranchID";

                var parameters = new[] { DatabaseHelper.CreateParameter("@BranchID", branchId) };

                using (var reader = DatabaseHelper.ExecuteReader(query, parameters))
                {
                    if (reader.Read())
                    {
                        return MapBranchFromReader(reader);
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على الفرع {branchId}: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// الحصول على جميع الفروع
        /// </summary>
        /// <returns>قائمة جميع الفروع</returns>
        public static List<Branch> GetAllBranches()
        {
            try
            {
                string query = @"
                    SELECT BranchID, BranchCode, BranchName, Address, Phone, Email, 
                           City, Manager, IsActive, CreatedDate, CreatedBy
                    FROM Branches 
                    ORDER BY BranchName";

                return ExecuteBranchQuery(query);
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على جميع الفروع: {ex.Message}");
                return new List<Branch>();
            }
        }

        /// <summary>
        /// الحصول على الفروع النشطة فقط
        /// </summary>
        /// <returns>قائمة الفروع النشطة</returns>
        public static List<Branch> GetActiveBranches()
        {
            try
            {
                string query = @"
                    SELECT BranchID, BranchCode, BranchName, Address, Phone, Email, 
                           City, Manager, IsActive, CreatedDate, CreatedBy
                    FROM Branches 
                    WHERE IsActive = 1
                    ORDER BY BranchName";

                var branches = ExecuteBranchQuery(query);
                LogManager.LogInfo($"تم استرجاع {branches.Count} فرع نشط");
                return branches;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على الفروع النشطة: {ex.Message}");
                return new List<Branch>();
            }
        }

        /// <summary>
        /// البحث في الفروع
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <returns>قائمة الفروع المطابقة للبحث</returns>
        public static List<Branch> SearchBranches(string searchTerm)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                {
                    return GetActiveBranches();
                }

                string query = @"
                    SELECT BranchID, BranchCode, BranchName, Address, Phone, Email, 
                           City, Manager, IsActive, CreatedDate, CreatedBy
                    FROM Branches 
                    WHERE (BranchCode LIKE @SearchTerm OR 
                           BranchName LIKE @SearchTerm OR 
                           City LIKE @SearchTerm OR 
                           Manager LIKE @SearchTerm)
                    ORDER BY BranchName";

                var parameters = new[] { DatabaseHelper.CreateParameter("@SearchTerm", $"%{searchTerm}%") };
                var branches = ExecuteBranchQuery(query, parameters);
                
                LogManager.LogInfo($"تم العثور على {branches.Count} فرع مطابق للبحث: {searchTerm}");
                return branches;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في البحث في الفروع: {ex.Message}");
                return new List<Branch>();
            }
        }

        /// <summary>
        /// الحصول على الفرع الرئيسي
        /// </summary>
        /// <returns>الفرع الرئيسي</returns>
        public static Branch GetMainBranch()
        {
            try
            {
                string query = @"
                    SELECT BranchID, BranchCode, BranchName, Address, Phone, Email, 
                           City, Manager, IsActive, CreatedDate, CreatedBy
                    FROM Branches 
                    WHERE IsActive = 1
                    ORDER BY BranchID
                    OFFSET 0 ROWS FETCH NEXT 1 ROWS ONLY";

                using (var reader = DatabaseHelper.ExecuteReader(query))
                {
                    if (reader.Read())
                    {
                        return MapBranchFromReader(reader);
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على الفرع الرئيسي: {ex.Message}");
                return null;
            }
        }

        #endregion

        #region Utility Methods - الطرق المساعدة

        /// <summary>
        /// توليد كود فرع جديد تلقائياً
        /// </summary>
        /// <returns>كود الفرع الجديد</returns>
        public static string GenerateBranchCode()
        {
            try
            {
                string query = "SELECT COUNT(*) FROM Branches";
                var result = DatabaseHelper.ExecuteScalar(query);
                int count = Convert.ToInt32(result) + 1;
                
                string branchCode = $"BR{count:D3}";
                LogManager.LogInfo($"تم توليد كود فرع جديد: {branchCode}");
                return branchCode;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في توليد كود الفرع: {ex.Message}");
                // استخدام timestamp كبديل في حالة الخطأ
                return $"BR{DateTime.Now.Ticks.ToString().Substring(10)}";
            }
        }

        /// <summary>
        /// التحقق من وجود كود الفرع
        /// </summary>
        /// <param name="branchCode">كود الفرع</param>
        /// <param name="excludeBranchId">معرف الفرع المستثنى (للتحديث)</param>
        /// <returns>true إذا كان الكود موجود</returns>
        public static bool IsBranchCodeExists(string branchCode, int? excludeBranchId = null)
        {
            try
            {
                string query = "SELECT COUNT(*) FROM Branches WHERE BranchCode = @BranchCode";
                var parameters = new List<SqlParameter>
                {
                    DatabaseHelper.CreateParameter("@BranchCode", branchCode)
                };

                if (excludeBranchId.HasValue)
                {
                    query += " AND BranchID != @ExcludeBranchID";
                    parameters.Add(DatabaseHelper.CreateParameter("@ExcludeBranchID", excludeBranchId.Value));
                }

                var result = DatabaseHelper.ExecuteScalar(query, parameters.ToArray());
                return Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في التحقق من كود الفرع: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Private Helper Methods - الطرق المساعدة الخاصة

        /// <summary>
        /// التحقق من صحة بيانات الفرع
        /// </summary>
        /// <param name="branch">بيانات الفرع</param>
        private static void ValidateBranchData(Branch branch)
        {
            if (branch == null)
                throw new ArgumentNullException(nameof(branch), "بيانات الفرع مطلوبة");

            if (string.IsNullOrWhiteSpace(branch.BranchCode))
                throw new ArgumentException("كود الفرع مطلوب", nameof(branch.BranchCode));

            if (string.IsNullOrWhiteSpace(branch.BranchName))
                throw new ArgumentException("اسم الفرع مطلوب", nameof(branch.BranchName));

            if (branch.BranchCode.Length > 10)
                throw new ArgumentException("كود الفرع يجب أن يكون أقل من 10 أحرف", nameof(branch.BranchCode));

            if (branch.BranchName.Length > 100)
                throw new ArgumentException("اسم الفرع يجب أن يكون أقل من 100 حرف", nameof(branch.BranchName));
        }

        /// <summary>
        /// التحقق من وجود فرع رئيسي
        /// </summary>
        /// <param name="excludeBranchId">معرف الفرع المستثنى</param>
        /// <returns>true إذا كان يوجد فرع رئيسي</returns>
        private static bool HasMainBranch(int? excludeBranchId = null)
        {
            try
            {
                string query = "SELECT COUNT(*) FROM Branches WHERE IsActive = 1";
                var parameters = new List<SqlParameter>();

                if (excludeBranchId.HasValue)
                {
                    query += " AND BranchID != @ExcludeBranchID";
                    parameters.Add(DatabaseHelper.CreateParameter("@ExcludeBranchID", excludeBranchId.Value));
                }

                var result = DatabaseHelper.ExecuteScalar(query, parameters.ToArray());
                return Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في التحقق من الفرع الرئيسي: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// التحقق من وجود مخازن مرتبطة بالفرع
        /// </summary>
        /// <param name="branchId">معرف الفرع</param>
        /// <returns>true إذا كان يوجد مخازن مرتبطة</returns>
        private static bool HasRelatedWarehouses(int branchId)
        {
            try
            {
                string query = "SELECT COUNT(*) FROM Warehouses WHERE BranchID = @BranchID";
                var parameters = new[] { DatabaseHelper.CreateParameter("@BranchID", branchId) };

                var result = DatabaseHelper.ExecuteScalar(query, parameters);
                return Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في التحقق من المخازن المرتبطة: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// التحقق من وجود مستخدمين مرتبطين بالفرع
        /// </summary>
        /// <param name="branchId">معرف الفرع</param>
        /// <returns>true إذا كان يوجد مستخدمين مرتبطين</returns>
        private static bool HasRelatedUsers(int branchId)
        {
            try
            {
                string query = "SELECT COUNT(*) FROM Users WHERE BranchID = @BranchID";
                var parameters = new[] { DatabaseHelper.CreateParameter("@BranchID", branchId) };

                var result = DatabaseHelper.ExecuteScalar(query, parameters);
                return Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في التحقق من المستخدمين المرتبطين: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تنفيذ استعلام الفروع وإرجاع النتائج
        /// </summary>
        /// <param name="query">الاستعلام</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>قائمة الفروع</returns>
        private static List<Branch> ExecuteBranchQuery(string query, SqlParameter[] parameters = null)
        {
            var branches = new List<Branch>();
            try
            {
                using (var reader = DatabaseHelper.ExecuteReader(query, parameters))
                {
                    while (reader.Read())
                    {
                        branches.Add(MapBranchFromReader(reader));
                    }
                }
                return branches;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تنفيذ استعلام الفروع: {ex.Message}");
                return branches;
            }
        }

        /// <summary>
        /// تحويل بيانات القارئ إلى كائن فرع
        /// </summary>
        /// <param name="reader">قارئ البيانات</param>
        /// <returns>كائن الفرع</returns>
        private static Branch MapBranchFromReader(SqlDataReader reader)
        {
            return new Branch
            {
                BranchID = Convert.ToInt32(reader["BranchID"]),
                BranchCode = reader["BranchCode"].ToString(),
                BranchName = reader["BranchName"].ToString(),
                Address = reader["Address"] == DBNull.Value ? null : reader["Address"].ToString(),
                Phone = reader["Phone"] == DBNull.Value ? null : reader["Phone"].ToString(),
                Email = reader["Email"] == DBNull.Value ? null : reader["Email"].ToString(),
                City = reader["City"] == DBNull.Value ? null : reader["City"].ToString(),
                ManagerName = reader["Manager"] == DBNull.Value ? null : reader["Manager"].ToString(),
                IsActive = Convert.ToBoolean(reader["IsActive"]),
                CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                CreatedBy = reader["CreatedBy"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["CreatedBy"])
            };
        }

        #endregion

        #region Statistics and Reports - الإحصائيات والتقارير

        /// <summary>
        /// الحصول على إحصائيات الفروع
        /// </summary>
        /// <returns>إحصائيات الفروع</returns>
        public static BranchStatistics GetBranchStatistics()
        {
            try
            {
                string query = @"
                    SELECT
                        COUNT(*) as TotalBranches,
                        SUM(CASE WHEN IsActive = 1 THEN 1 ELSE 0 END) as ActiveBranches,
                        SUM(CASE WHEN IsActive = 0 THEN 1 ELSE 0 END) as InactiveBranches
                    FROM Branches";

                using (var reader = DatabaseHelper.ExecuteReader(query))
                {
                    if (reader.Read())
                    {
                        return new BranchStatistics
                        {
                            TotalBranches = Convert.ToInt32(reader["TotalBranches"]),
                            ActiveBranches = Convert.ToInt32(reader["ActiveBranches"]),
                            InactiveBranches = Convert.ToInt32(reader["InactiveBranches"])
                        };
                    }
                }

                return new BranchStatistics();
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على إحصائيات الفروع: {ex.Message}");
                return new BranchStatistics();
            }
        }

        /// <summary>
        /// الحصول على الفروع حسب المدينة
        /// </summary>
        /// <param name="city">اسم المدينة</param>
        /// <returns>قائمة الفروع في المدينة</returns>
        public static List<Branch> GetBranchesByCity(string city)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(city))
                    return new List<Branch>();

                string query = @"
                    SELECT BranchID, BranchCode, BranchName, Address, Phone, Email,
                           City, Manager, IsActive, CreatedDate, CreatedBy
                    FROM Branches
                    WHERE City = @City AND IsActive = 1
                    ORDER BY BranchName";

                var parameters = new[] { DatabaseHelper.CreateParameter("@City", city) };
                return ExecuteBranchQuery(query, parameters);
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على فروع المدينة {city}: {ex.Message}");
                return new List<Branch>();
            }
        }

        #endregion
    }

    #region Helper Models - النماذج المساعدة

    /// <summary>
    /// إحصائيات الفروع
    /// </summary>
    public class BranchStatistics
    {
        public int TotalBranches { get; set; }
        public int ActiveBranches { get; set; }
        public int InactiveBranches { get; set; }
    }

    #endregion
}
