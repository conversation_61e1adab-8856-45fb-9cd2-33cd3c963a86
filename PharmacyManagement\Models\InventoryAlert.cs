using System;

namespace PharmacyManagement.Models
{
    /// <summary>
    /// تنبيه المخزون
    /// </summary>
    public class InventoryAlert
    {
        /// <summary>
        /// معرف التنبيه
        /// </summary>
        public int AlertID { get; set; }

        /// <summary>
        /// معرف الدواء
        /// </summary>
        public int DrugID { get; set; }

        /// <summary>
        /// كود الدواء
        /// </summary>
        public string DrugCode { get; set; }

        /// <summary>
        /// اسم الدواء
        /// </summary>
        public string DrugName { get; set; }

        /// <summary>
        /// نوع التنبيه
        /// </summary>
        public string AlertType { get; set; }

        /// <summary>
        /// رسالة التنبيه
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// مستوى الخطورة
        /// </summary>
        public string Severity { get; set; }

        /// <summary>
        /// تاريخ التنبيه
        /// </summary>
        public DateTime AlertDate { get; set; }

        /// <summary>
        /// هل تم حل التنبيه
        /// </summary>
        public bool IsResolved { get; set; }

        /// <summary>
        /// هل تم قراءة التنبيه
        /// </summary>
        public bool IsRead { get; set; }

        /// <summary>
        /// تاريخ الحل
        /// </summary>
        public DateTime? ResolvedDate { get; set; }

        /// <summary>
        /// ملاحظات
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// معرف المستخدم الذي أنشأ التنبيه
        /// </summary>
        public int CreatedBy { get; set; }

        /// <summary>
        /// معرف المستخدم الذي حل التنبيه
        /// </summary>
        public int? ResolvedBy { get; set; }

        /// <summary>
        /// الكمية الحالية
        /// </summary>
        public decimal CurrentQuantity { get; set; }

        /// <summary>
        /// الحد الأدنى
        /// </summary>
        public decimal MinimumQuantity { get; set; }

        /// <summary>
        /// تاريخ انتهاء الصلاحية
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// الأيام المتبقية
        /// </summary>
        public int DaysRemaining { get; set; }
    }
}

