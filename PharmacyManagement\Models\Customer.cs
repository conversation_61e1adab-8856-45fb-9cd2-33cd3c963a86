using System;

namespace PharmacyManagement.Models
{
    /// <summary>
    /// نموذج العميل - Customer Model
    /// يمثل بيانات العملاء في النظام
    /// </summary>
    public class Customer
    {
        #region Properties - الخصائص

        /// <summary>
        /// معرف العميل الفريد
        /// </summary>
        public int CustomerID { get; set; }

        /// <summary>
        /// كود العميل
        /// </summary>
        public string CustomerCode { get; set; }

        /// <summary>
        /// اسم العميل
        /// </summary>
        public string CustomerName { get; set; }

        /// <summary>
        /// رقم الهاتف
        /// </summary>
        public string Phone { get; set; }

        /// <summary>
        /// رقم الجوال
        /// </summary>
        public string Mobile { get; set; }

        public decimal Balance { get; set; }
        /// <summary>
        /// العنوان
        /// </summary>
        public string Address { get; set; }

        /// <summary>
        /// البريد الإلكتروني
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// تاريخ الميلاد
        /// </summary>
        public DateTime? DateOfBirth { get; set; }

        /// <summary>
        /// تاريخ الميلاد (اسم بديل)
        /// </summary>
        public DateTime? BirthDate { get; set; }

        /// <summary>
        /// رقم الهوية الوطنية
        /// </summary>
        public string NationalID { get; set; }

        /// <summary>
        /// الجنس
        /// </summary>
        public string Gender { get; set; }

        /// <summary>
        /// نوع العميل (عادي، VIP، جملة، مؤسسة)
        /// </summary>
        public string CustomerType { get; set; }

        /// <summary>
        /// حد الائتمان المسموح
        /// </summary>
        public decimal CreditLimit { get; set; }

        /// <summary>
        /// الرصيد الحالي (المبلغ المستحق على العميل)
        /// </summary>
        public decimal CurrentBalance { get; set; }

        /// <summary>
        /// حالة العميل (نشط/غير نشط)
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// ملاحظات
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// تاريخ الإضافة
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// تاريخ التسجيل
        /// </summary>
        public DateTime RegistrationDate { get; set; }

        /// <summary>
        /// تاريخ آخر تعديل
        /// </summary>
        public DateTime? ModifiedDate { get; set; }

        /// <summary>
        /// معرف المستخدم الذي أضاف العميل
        /// </summary>
        public int? CreatedBy { get; set; }

        /// <summary>
        /// إجمالي المشتريات
        /// </summary>
        public decimal TotalPurchases { get; set; }

        /// <summary>
        /// تاريخ آخر شراء
        /// </summary>
        public DateTime? LastPurchaseDate { get; set; }

        #endregion

        #region Calculated Properties - الخصائص المحسوبة

        /// <summary>
        /// العمر
        /// </summary>
        public int? Age
        {
            get
            {
                if (!DateOfBirth.HasValue) return null;
                var today = DateTime.Today;
                var age = today.Year - DateOfBirth.Value.Year;
                if (DateOfBirth.Value.Date > today.AddYears(-age)) age--;
                return age;
            }
        }

        /// <summary>
        /// الرصيد المتاح للشراء
        /// </summary>
        public decimal AvailableCredit
        {
            get { return CreditLimit - CurrentBalance; }
        }

        /// <summary>
        /// حالة الائتمان
        /// </summary>
        public string CreditStatus
        {
            get
            {
                if (CurrentBalance == 0) return "لا توجد مستحقات";
                if (CurrentBalance > CreditLimit) return "تجاوز حد الائتمان";
                if (AvailableCredit < (CreditLimit * 0.1m)) return "قريب من حد الائتمان";
                return "ائتمان جيد";
            }
        }

        /// <summary>
        /// معلومات الاتصال الكاملة
        /// </summary>
        public string ContactInfo
        {
            get
            {
                var contact = "";
                if (!string.IsNullOrEmpty(Phone))
                    contact += "هاتف: " + Phone;
                if (!string.IsNullOrEmpty(Mobile))
                {
                    if (!string.IsNullOrEmpty(contact)) contact += " | ";
                    contact += "جوال: " + Mobile;
                }
                if (!string.IsNullOrEmpty(Email))
                {
                    if (!string.IsNullOrEmpty(contact)) contact += " | ";
                    contact += "إيميل: " + Email;
                }
                return contact;
            }
        }

        /// <summary>
        /// معلومات العميل الكاملة
        /// </summary>
        public string FullInfo
        {
            get
            {
                var info = CustomerName;
                if (Age.HasValue)
                    info += " (" + Age.ToString() + " سنة)";
                if (!string.IsNullOrEmpty(Gender))
                    info += " - " + Gender;
                return info;
            }
        }

        #endregion

        #region Constructors - المنشئات

        /// <summary>
        /// منشئ افتراضي
        /// </summary>
        public Customer()
        {
            IsActive = true;
            CreatedDate = DateTime.Now;
            CreditLimit = 0;
            CurrentBalance = 0;
            Gender = "ذكر";
        }

        /// <summary>
        /// منشئ مع المعاملات الأساسية
        /// </summary>
        /// <param name="customerName">اسم العميل</param>
        /// <param name="phone">رقم الهاتف</param>
        public Customer(string customerName, string phone)
        {
            CustomerName = customerName;
            Phone = phone;
            IsActive = true;
            CreatedDate = DateTime.Now;
            CreditLimit = 0;
            CurrentBalance = 0;
            Gender = "ذكر";
        }

        #endregion

        #region Methods - الطرق

        /// <summary>
        /// التحقق من صحة بيانات العميل
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(CustomerName) &&
                   CreditLimit >= 0 &&
                   CurrentBalance >= 0;
        }

        /// <summary>
        /// التحقق من إمكانية الشراء بمبلغ معين
        /// </summary>
        /// <param name="amount">المبلغ المطلوب</param>
        /// <returns>true إذا كان المبلغ ضمن حد الائتمان</returns>
        public bool CanPurchase(decimal amount)
        {
            return (CurrentBalance + amount) <= CreditLimit;
        }

        /// <summary>
        /// إضافة مبلغ للرصيد الحالي
        /// </summary>
        /// <param name="amount">المبلغ</param>
        public void AddToBalance(decimal amount)
        {
            CurrentBalance += amount;
        }

        /// <summary>
        /// خصم مبلغ من الرصيد الحالي (دفع)
        /// </summary>
        /// <param name="amount">المبلغ</param>
        public void SubtractFromBalance(decimal amount)
        {
            CurrentBalance = Math.Max(0, CurrentBalance - amount);
        }

        /// <summary>
        /// إرجاع تمثيل نصي للعميل
        /// </summary>
        /// <returns>اسم العميل</returns>
        public override string ToString()
        {
            return CustomerName;
        }

        #endregion
    }
}
