-- ===================================================================
-- إصلاح الجداول الموجودة للتقارير - Fix Existing Tables for Reports
-- تاريخ الإنشاء: 2025-01-28
-- الغرض: استخدام الجداول الموجودة وإضافة ما ينقصها فقط
-- ===================================================================

USE PharmacyDB
GO

PRINT '🔧 بدء إصلاح الجداول الموجودة للتقارير...'
PRINT '📅 التاريخ: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT ''

-- ===================================================================
-- 1. التحقق من الجداول الموجودة
-- ===================================================================

PRINT '🔍 التحقق من الجداول الموجودة...'

-- التحقق من جدول SalesInvoices
IF EXISTS (SELECT * FROM sysobjects WHERE name='SalesInvoices' AND xtype='U')
    PRINT '✅ جدول SalesInvoices موجود'
ELSE
    PRINT '❌ جدول SalesInvoices غير موجود'

-- التحقق من جدول SalesInvoiceDetails
IF EXISTS (SELECT * FROM sysobjects WHERE name='SalesInvoiceDetails' AND xtype='U')
    PRINT '✅ جدول SalesInvoiceDetails موجود'
ELSE
    PRINT '❌ جدول SalesInvoiceDetails غير موجود'

-- التحقق من جدول Customers
IF EXISTS (SELECT * FROM sysobjects WHERE name='Customers' AND xtype='U')
    PRINT '✅ جدول Customers موجود'
ELSE
    PRINT '❌ جدول Customers غير موجود'

-- التحقق من جدول Suppliers
IF EXISTS (SELECT * FROM sysobjects WHERE name='Suppliers' AND xtype='U')
    PRINT '✅ جدول Suppliers موجود'
ELSE
    PRINT '❌ جدول Suppliers غير موجود'

PRINT ''

-- ===================================================================
-- 2. إنشاء جدول المستخدمين إذا لم يكن موجوداً
-- ===================================================================

IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Users' AND xtype='U')
BEGIN
    CREATE TABLE Users (
        UserID INT IDENTITY(1,1) PRIMARY KEY,
        Username NVARCHAR(50) NOT NULL UNIQUE,
        FullName NVARCHAR(100) NOT NULL,
        Email NVARCHAR(100) NULL,
        Phone NVARCHAR(20) NULL,
        IsActive BIT NOT NULL DEFAULT 1,
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE()
    )
    
    -- إدراج مستخدم افتراضي
    INSERT INTO Users (Username, FullName, Email, IsActive)
    VALUES ('admin', 'مدير النظام', '<EMAIL>', 1)
    
    PRINT '✅ تم إنشاء جدول Users'
END
ELSE
    PRINT '✅ جدول Users موجود'

-- ===================================================================
-- 3. إنشاء جدول العملاء إذا لم يكن موجوداً
-- ===================================================================

IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Customers' AND xtype='U')
BEGIN
    CREATE TABLE Customers (
        CustomerID INT IDENTITY(1,1) PRIMARY KEY,
        CustomerName NVARCHAR(100) NOT NULL,
        Phone NVARCHAR(20) NULL,
        Email NVARCHAR(100) NULL,
        Address NVARCHAR(200) NULL,
        Balance DECIMAL(10,2) NOT NULL DEFAULT 0,
        CreditLimit DECIMAL(10,2) NOT NULL DEFAULT 0,
        IsActive BIT NOT NULL DEFAULT 1,
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE()
    )
    PRINT '✅ تم إنشاء جدول Customers'
END

-- ===================================================================
-- 4. إنشاء جدول الموردين إذا لم يكن موجوداً
-- ===================================================================

IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Suppliers' AND xtype='U')
BEGIN
    CREATE TABLE Suppliers (
        SupplierID INT IDENTITY(1,1) PRIMARY KEY,
        SupplierName NVARCHAR(100) NOT NULL,
        ContactPerson NVARCHAR(100) NULL,
        Phone NVARCHAR(20) NULL,
        Email NVARCHAR(100) NULL,
        Address NVARCHAR(200) NULL,
        Balance DECIMAL(10,2) NOT NULL DEFAULT 0,
        CreditLimit DECIMAL(10,2) NOT NULL DEFAULT 0,
        IsActive BIT NOT NULL DEFAULT 1,
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE()
    )
    PRINT '✅ تم إنشاء جدول Suppliers'
END

-- ===================================================================
-- 5. إنشاء جدول الشركات المصنعة إذا لم يكن موجوداً
-- ===================================================================

IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Manufacturers' AND xtype='U')
BEGIN
    CREATE TABLE Manufacturers (
        ManufacturerID INT IDENTITY(1,1) PRIMARY KEY,
        ManufacturerCode NVARCHAR(20) NOT NULL UNIQUE,
        ManufacturerName NVARCHAR(100) NOT NULL,
        Country NVARCHAR(50) NULL,
        IsActive BIT NOT NULL DEFAULT 1,
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        CreatedBy INT NOT NULL DEFAULT 1
    )
    PRINT '✅ تم إنشاء جدول Manufacturers'
END

-- ===================================================================
-- 6. إنشاء جدول المشتريات إذا لم يكن موجوداً
-- ===================================================================

IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Purchases' AND xtype='U')
BEGIN
    CREATE TABLE Purchases (
        PurchaseID INT IDENTITY(1,1) PRIMARY KEY,
        PurchaseDate DATETIME NOT NULL DEFAULT GETDATE(),
        SupplierID INT NOT NULL,
        TotalAmount DECIMAL(10,2) NOT NULL DEFAULT 0,
        DiscountAmount DECIMAL(10,2) NOT NULL DEFAULT 0,
        TaxAmount DECIMAL(10,2) NOT NULL DEFAULT 0,
        NetAmount DECIMAL(10,2) NOT NULL DEFAULT 0,
        PaymentMethod NVARCHAR(50) NOT NULL DEFAULT 'آجل',
        Notes NVARCHAR(200) NULL,
        UserID INT NOT NULL DEFAULT 1,
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        FOREIGN KEY (SupplierID) REFERENCES Suppliers(SupplierID),
        FOREIGN KEY (UserID) REFERENCES Users(UserID)
    )
    PRINT '✅ تم إنشاء جدول Purchases'
END

-- إنشاء جدول تفاصيل المشتريات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='PurchaseDetails' AND xtype='U')
BEGIN
    CREATE TABLE PurchaseDetails (
        PurchaseDetailID INT IDENTITY(1,1) PRIMARY KEY,
        PurchaseID INT NOT NULL,
        DrugID INT NOT NULL,
        Quantity INT NOT NULL,
        UnitPrice DECIMAL(10,2) NOT NULL,
        TotalPrice DECIMAL(10,2) NOT NULL,
        DiscountAmount DECIMAL(10,2) NOT NULL DEFAULT 0,
        FOREIGN KEY (PurchaseID) REFERENCES Purchases(PurchaseID),
        FOREIGN KEY (DrugID) REFERENCES Drugs(DrugID)
    )
    PRINT '✅ تم إنشاء جدول PurchaseDetails'
END

-- ===================================================================
-- 7. إضافة أعمدة مفقودة للجداول الموجودة
-- ===================================================================

PRINT '🔧 التحقق من الأعمدة المطلوبة...'

-- إضافة عمود UserID لجدول SalesInvoices إذا لم يكن موجوداً
IF EXISTS (SELECT * FROM sysobjects WHERE name='SalesInvoices' AND xtype='U')
BEGIN
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('SalesInvoices') AND name = 'UserID')
    BEGIN
        ALTER TABLE SalesInvoices ADD UserID INT NOT NULL DEFAULT 1
        PRINT '✅ تم إضافة عمود UserID لجدول SalesInvoices'
    END
    
    -- إضافة عمود Notes إذا لم يكن موجوداً
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('SalesInvoices') AND name = 'Notes')
    BEGIN
        ALTER TABLE SalesInvoices ADD Notes NVARCHAR(200) NULL
        PRINT '✅ تم إضافة عمود Notes لجدول SalesInvoices'
    END
END

-- ===================================================================
-- 8. إنشاء الفهارس المطلوبة
-- ===================================================================

PRINT '🚀 إنشاء الفهارس...'

-- فهارس جدول SalesInvoices
IF EXISTS (SELECT * FROM sysobjects WHERE name='SalesInvoices' AND xtype='U')
BEGIN
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_SalesInvoices_InvoiceDate')
        CREATE INDEX IX_SalesInvoices_InvoiceDate ON SalesInvoices(InvoiceDate)
    
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_SalesInvoices_CustomerID')
        CREATE INDEX IX_SalesInvoices_CustomerID ON SalesInvoices(CustomerID)
END

-- فهارس جدول SalesInvoiceDetails
IF EXISTS (SELECT * FROM sysobjects WHERE name='SalesInvoiceDetails' AND xtype='U')
BEGIN
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_SalesInvoiceDetails_InvoiceID')
        CREATE INDEX IX_SalesInvoiceDetails_InvoiceID ON SalesInvoiceDetails(InvoiceID)
    
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_SalesInvoiceDetails_DrugID')
        CREATE INDEX IX_SalesInvoiceDetails_DrugID ON SalesInvoiceDetails(DrugID)
END

-- فهارس جدول المشتريات
IF EXISTS (SELECT * FROM sysobjects WHERE name='Purchases' AND xtype='U')
BEGIN
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Purchases_PurchaseDate')
        CREATE INDEX IX_Purchases_PurchaseDate ON Purchases(PurchaseDate)
    
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Purchases_SupplierID')
        CREATE INDEX IX_Purchases_SupplierID ON Purchases(SupplierID)
END

PRINT '✅ تم إنشاء جميع الفهارس'

-- ===================================================================
-- 9. إنشاء Views للتقارير
-- ===================================================================

PRINT '👁️ إنشاء Views للتقارير...'

-- View للمبيعات (استخدام الجداول الموجودة)
IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_SalesReport')
    DROP VIEW vw_SalesReport
GO

CREATE VIEW vw_SalesReport AS
SELECT 
    si.InvoiceID as SaleID,
    si.InvoiceDate as SaleDate,
    si.CustomerID,
    ISNULL(c.CustomerName, 'عميل نقدي') as CustomerName,
    si.TotalAmount,
    si.DiscountAmount,
    si.TaxAmount,
    si.NetAmount,
    si.PaymentMethod,
    si.Notes,
    ISNULL(u.FullName, 'غير محدد') as SalesPersonName
FROM SalesInvoices si
LEFT JOIN Customers c ON si.CustomerID = c.CustomerID
LEFT JOIN Users u ON si.UserID = u.UserID
GO

PRINT '✅ تم إنشاء View vw_SalesReport'

PRINT ''
PRINT '✅ تم إصلاح جميع الجداول بنجاح!'
PRINT '📅 تاريخ الانتهاء: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT ''
PRINT '📝 ملخص العمل:'
PRINT '   - تم استخدام الجداول الموجودة: SalesInvoices, SalesInvoiceDetails'
PRINT '   - تم إنشاء الجداول المفقودة: Users, Customers, Suppliers, Purchases'
PRINT '   - تم إضافة الأعمدة المطلوبة'
PRINT '   - تم إنشاء الفهارس والـ Views'
PRINT ''
PRINT '🎯 الآن يمكن تشغيل ملف البيانات التجريبية المحدث!'
