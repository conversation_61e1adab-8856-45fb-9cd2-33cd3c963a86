using System;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نموذج إدارة الدليل المحاسبي - Chart of Accounts Management Form
    /// </summary>
    public partial class ChartOfAccountsForm : Form
    {
        #region Fields

        private BindingSource _bindingSource;
        private int? _selectedAccountId;

        #endregion

        #region Constructor

        public ChartOfAccountsForm()
        {
            InitializeComponent();
            SetupForm();
            LoadData();
        }

        #endregion

        #region Setup Methods

        private void SetupForm()
        {
            this.Text = "إدارة الدليل المحاسبي";
            this.WindowState = FormWindowState.Maximized;
            
            // إعداد التصميم المسطح
            SetupFlatDesign();
            
            // إعداد الشبكة
            SetupDataGrid();
            
            // إعداد الأحداث
            SetupEvents();
        }

        private void SetupFlatDesign()
        {
            // إعدادات النموذج
            this.BackColor = Color.White;
            this.Font = new Font("Segoe UI", 9F);
            
            // إعداد الأزرار
            foreach (Control control in this.Controls)
            {
                if (control is Button btn)
                {
                    btn.FlatStyle = FlatStyle.Flat;
                    btn.FlatAppearance.BorderSize = 1;
                    btn.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
                }
                else if (control is TextBox txt)
                {
                    txt.BorderStyle = BorderStyle.FixedSingle;
                    txt.Font = new Font("Segoe UI", 10F);
                }
                else if (control is ComboBox cmb)
                {
                    cmb.FlatStyle = FlatStyle.Flat;
                    cmb.Font = new Font("Segoe UI", 10F);
                }
            }

            // ألوان الأزرار
            btnAdd.BackColor = Color.FromArgb(46, 204, 113);
            btnAdd.ForeColor = Color.White;
            btnEdit.BackColor = Color.FromArgb(52, 152, 219);
            btnEdit.ForeColor = Color.White;
            btnDelete.BackColor = Color.FromArgb(231, 76, 60);
            btnDelete.ForeColor = Color.White;
            btnSearch.BackColor = Color.FromArgb(155, 89, 182);
            btnSearch.ForeColor = Color.White;
        }

        private void SetupDataGrid()
        {
            // إعداد الشبكة
            dgvAccounts.AutoGenerateColumns = false;
            dgvAccounts.AllowUserToAddRows = false;
            dgvAccounts.AllowUserToDeleteRows = false;
            dgvAccounts.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvAccounts.MultiSelect = false;
            dgvAccounts.BackgroundColor = Color.White;
            dgvAccounts.BorderStyle = BorderStyle.FixedSingle;
            dgvAccounts.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 152, 219);
            dgvAccounts.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvAccounts.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            dgvAccounts.DefaultCellStyle.Font = new Font("Segoe UI", 9F);
            dgvAccounts.RowHeadersVisible = false;
            dgvAccounts.EnableHeadersVisualStyles = false;

            // إضافة الأعمدة
            dgvAccounts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "AccountCode",
                HeaderText = "كود الحساب",
                DataPropertyName = "AccountCode",
                Width = 120
            });

            dgvAccounts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "AccountName",
                HeaderText = "اسم الحساب",
                DataPropertyName = "AccountName",
                Width = 250
            });

            dgvAccounts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "AccountType",
                HeaderText = "نوع الحساب",
                DataPropertyName = "AccountType",
                Width = 120
            });

            dgvAccounts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Level",
                HeaderText = "المستوى",
                DataPropertyName = "Level",
                Width = 80,
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });

            dgvAccounts.Columns.Add(new DataGridViewCheckBoxColumn
            {
                Name = "IsActive",
                HeaderText = "نشط",
                DataPropertyName = "IsActive",
                Width = 80
            });

            dgvAccounts.Columns.Add(new DataGridViewCheckBoxColumn
            {
                Name = "IsSystemAccount",
                HeaderText = "حساب نظامي",
                DataPropertyName = "IsSystemAccount",
                Width = 100
            });

            dgvAccounts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Description",
                HeaderText = "الوصف",
                DataPropertyName = "Description",
                Width = 200
            });

            // إعداد مصدر البيانات
            _bindingSource = new BindingSource();
            dgvAccounts.DataSource = _bindingSource;
        }

        private void SetupEvents()
        {
            // ربط الأحداث
            btnAdd.Click += BtnAdd_Click;
            btnEdit.Click += BtnEdit_Click;
            btnDelete.Click += BtnDelete_Click;
            btnSearch.Click += BtnSearch_Click;
            btnRefresh.Click += BtnRefresh_Click;
            
            dgvAccounts.SelectionChanged += DgvAccounts_SelectionChanged;
            dgvAccounts.CellFormatting += DgvAccounts_CellFormatting;
            dgvAccounts.CellDoubleClick += DgvAccounts_CellDoubleClick;
            
            txtSearch.KeyPress += TxtSearch_KeyPress;
            cmbAccountType.SelectedIndexChanged += CmbAccountType_SelectedIndexChanged;
        }

        #endregion

        #region Load Data Methods

        private void LoadData()
        {
            try
            {
                var accounts = ChartOfAccountsManager.GetAllAccounts();
                _bindingSource.DataSource = accounts;
                
                LoadAccountTypes();
                UpdateButtonStates();
                UpdateStatusLabel();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadAccountTypes()
        {
            var accountTypes = new[]
            {
                new { Value = "", Text = "جميع الأنواع" },
                new { Value = "Asset", Text = "أصول" },
                new { Value = "Liability", Text = "خصوم" },
                new { Value = "Equity", Text = "حقوق ملكية" },
                new { Value = "Revenue", Text = "إيرادات" },
                new { Value = "Expense", Text = "مصروفات" }
            };

            cmbAccountType.DataSource = accountTypes;
            cmbAccountType.DisplayMember = "Text";
            cmbAccountType.ValueMember = "Value";
            cmbAccountType.SelectedIndex = 0;
        }

        #endregion

        #region Event Handlers

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                // TODO: Create ChartOfAccountsAddEditForm
                MessageBox.Show("إضافة حساب - قيد التطوير", "معلومات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                // using (var addForm = new ChartOfAccountsAddEditForm())
                // {
                //     if (addForm.ShowDialog() == DialogResult.OK)
                //     {
                //         LoadData();
                //     }
                // }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة الحساب: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            try
            {
                // TODO: Create ChartOfAccountsAddEditForm
                MessageBox.Show("تعديل حساب - قيد التطوير", "معلومات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعديل الحساب: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            try
            {
                if (_selectedAccountId.HasValue)
                {
                    var result = MessageBox.Show("هل تريد حذف الحساب المحدد؟", "تأكيد الحذف",
                        MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    
                    if (result == DialogResult.Yes)
                    {
                        if (ChartOfAccountsManager.DeleteAccount(_selectedAccountId.Value))
                        {
                            MessageBox.Show("تم حذف الحساب بنجاح", "نجح",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                            LoadData();
                        }
                        else
                        {
                            MessageBox.Show("فشل في حذف الحساب", "خطأ",
                                MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف الحساب: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnSearch_Click(object sender, EventArgs e)
        {
            PerformSearch();
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            txtSearch.Clear();
            cmbAccountType.SelectedIndex = 0;
            LoadData();
        }

        private void DgvAccounts_SelectionChanged(object sender, EventArgs e)
        {
            if (dgvAccounts.SelectedRows.Count > 0)
            {
                var selectedRow = dgvAccounts.SelectedRows[0];
                if (selectedRow.DataBoundItem is ChartOfAccount account)
                {
                    _selectedAccountId = account.AccountID;
                    UpdateButtonStates();
                }
            }
            else
            {
                _selectedAccountId = null;
                UpdateButtonStates();
            }
        }

        private void DgvAccounts_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (dgvAccounts.Rows[e.RowIndex].DataBoundItem is ChartOfAccount account)
            {
                // تلوين الصف حسب النوع
                switch (account.AccountType)
                {
                    case "Asset":
                        dgvAccounts.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.LightBlue;
                        break;
                    case "Liability":
                        dgvAccounts.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.LightCoral;
                        break;
                    case "Equity":
                        dgvAccounts.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.LightGreen;
                        break;
                    case "Revenue":
                        dgvAccounts.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.LightYellow;
                        break;
                    case "Expense":
                        dgvAccounts.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.LightPink;
                        break;
                }

                // تنسيق عمود نوع الحساب
                if (e.ColumnIndex == dgvAccounts.Columns["AccountType"].Index)
                {
                    switch (account.AccountType)
                    {
                        case "Asset":
                            e.Value = "أصول";
                            break;
                        case "Liability":
                            e.Value = "خصوم";
                            break;
                        case "Equity":
                            e.Value = "حقوق ملكية";
                            break;
                        case "Revenue":
                            e.Value = "إيرادات";
                            break;
                        case "Expense":
                            e.Value = "مصروفات";
                            break;
                    }
                }

                // إضافة مسافات للمستويات الفرعية
                if (e.ColumnIndex == dgvAccounts.Columns["AccountName"].Index && account.Level > 1)
                {
                    string indent = new string(' ', (account.Level - 1) * 4);
                    e.Value = indent + account.AccountName;
                }
            }
        }

        private void DgvAccounts_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                BtnEdit_Click(sender, e);
            }
        }

        private void TxtSearch_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                PerformSearch();
                e.Handled = true;
            }
        }

        private void CmbAccountType_SelectedIndexChanged(object sender, EventArgs e)
        {
            FilterByAccountType();
        }

        #endregion

        #region Helper Methods

        private void UpdateButtonStates()
        {
            bool hasSelection = _selectedAccountId.HasValue;
            
            btnEdit.Enabled = hasSelection;
            btnDelete.Enabled = hasSelection;

            if (hasSelection && dgvAccounts.SelectedRows.Count > 0)
            {
                var selectedRow = dgvAccounts.SelectedRows[0];
                if (selectedRow.DataBoundItem is ChartOfAccount account)
                {
                    btnEdit.Enabled = !account.IsSystemAccount;
                    btnDelete.Enabled = !account.IsSystemAccount;
                }
            }
        }

        private void UpdateStatusLabel()
        {
            var totalAccounts = _bindingSource.Count;
            var activeAccounts = 0;
            var systemAccounts = 0;

            foreach (ChartOfAccount account in _bindingSource)
            {
                if (account.IsActive)
                    activeAccounts++;
                if (account.IsSystemAccount)
                    systemAccounts++;
            }

            lblStatus.Text = $"إجمالي الحسابات: {totalAccounts} | نشط: {activeAccounts} | نظامي: {systemAccounts}";
        }

        private void PerformSearch()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtSearch.Text))
                {
                    LoadData();
                    return;
                }

                var searchResults = ChartOfAccountsManager.SearchAccounts(txtSearch.Text.Trim());
                _bindingSource.DataSource = searchResults;
                UpdateStatusLabel();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void FilterByAccountType()
        {
            try
            {
                var selectedType = cmbAccountType.SelectedValue?.ToString();
                
                if (string.IsNullOrEmpty(selectedType))
                {
                    LoadData();
                    return;
                }

                var filteredAccounts = ChartOfAccountsManager.GetAccountsByType(selectedType);
                _bindingSource.DataSource = filteredAccounts;
                UpdateStatusLabel();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التصفية: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        private void btnAdd_Click_1(object sender, EventArgs e)
        {

        }
    }
}
