using System;
using System.Drawing;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نافذة إضافة/تعديل المستخدمين - User Add/Edit Form
    /// </summary>
    public partial class UserAddEditForm : Form
    {
        #region Fields - الحقول

        private User _currentUser;
        private bool _isEditMode;
        
       
        #endregion

        #region Constructors - المنشئات

        /// <summary>
        /// منشئ لإضافة مستخدم جديد
        /// </summary>
        public UserAddEditForm()
        {
            InitializeComponent();
            _isEditMode = false;
            _currentUser = new User();
            SetupForm();
        }

        /// <summary>
        /// منشئ لتعديل مستخدم موجود
        /// </summary>
        /// <param name="user">المستخدم المراد تعديله</param>
        public UserAddEditForm(User user)
        {
            InitializeComponent();
            _isEditMode = true;
            _currentUser = user;
            SetupForm();
            LoadUserData();
        }

        #endregion

        #region Form Setup - إعداد النافذة

       
        private void SetupForm()
        {
            try
            {
                this.Text = _isEditMode ? "تعديل مستخدم" : "إضافة مستخدم جديد";
                lblTitle.Text = _isEditMode ? "✏️ تعديل مستخدم" : "➕ إضافة مستخدم جديد";

                // تعيين القيم الافتراضية
                cmbRole.SelectedIndex = 3; // موظف مبيعات

                LogManager.LogInfo($"تم فتح نافذة {(_isEditMode ? "تعديل" : "إضافة")} مستخدم");
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في إعداد نافذة المستخدم: {ex.Message}");
                MessageBox.Show("حدث خطأ في إعداد النافذة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحميل بيانات المستخدم للتعديل
        /// </summary>
        private void LoadUserData()
        {
            try
            {
                if (_currentUser != null)
                {
                    txtUsername.Text = _currentUser.Username;
                    txtFullName.Text = _currentUser.FullName;
                    txtEmail.Text = _currentUser.Email;
                    txtPhone.Text = _currentUser.Phone;
                    cmbRole.Text = _currentUser.Role;
                    chkIsActive.Checked = _currentUser.IsActive;
                    chkCanAddUsers.Checked = _currentUser.CanAddUsers;
                    chkCanEditUsers.Checked = _currentUser.CanEditUsers;
                    chkCanDeleteUsers.Checked = _currentUser.CanDeleteUsers;
                    chkCanViewReports.Checked = _currentUser.CanViewReports;
                    chkCanManageSettings.Checked = _currentUser.CanPrint;

                    // إخفاء حقول كلمة المرور في وضع التعديل
                    lblPassword.Text = "كلمة المرور الجديدة (اتركها فارغة للاحتفاظ بالحالية):";
                    lblConfirmPassword.Text = "تأكيد كلمة المرور الجديدة:";
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تحميل بيانات المستخدم: {ex.Message}");
                MessageBox.Show("حدث خطأ في تحميل بيانات المستخدم", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Event Handlers - معالجات الأحداث

        /// <summary>
        /// حفظ المستخدم
        /// </summary>
        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (ValidateInput())
                {
                    SaveUser();
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في حفظ المستخدم: {ex.Message}");
                MessageBox.Show("حدث خطأ في حفظ المستخدم", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إلغاء العملية
        /// </summary>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        #endregion

        #region Private Methods - الطرق الخاصة

        /// <summary>
        /// التحقق من صحة البيانات المدخلة
        /// </summary>
        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المستخدم", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtUsername.Focus();
                return false;
            }

            if (!_isEditMode && string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                MessageBox.Show("يرجى إدخال كلمة المرور", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPassword.Focus();
                return false;
            }

            if (!string.IsNullOrWhiteSpace(txtPassword.Text) && txtPassword.Text != txtConfirmPassword.Text)
            {
                MessageBox.Show("كلمة المرور وتأكيدها غير متطابقتين", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtConfirmPassword.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtFullName.Text))
            {
                MessageBox.Show("يرجى إدخال الاسم الكامل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtFullName.Focus();
                return false;
            }

            if (!string.IsNullOrWhiteSpace(txtEmail.Text) && !IsValidEmail(txtEmail.Text))
            {
                MessageBox.Show("يرجى إدخال بريد إلكتروني صحيح", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtEmail.Focus();
                return false;
            }

            if (cmbRole.SelectedIndex == -1)
            {
                MessageBox.Show("يرجى اختيار دور المستخدم", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbRole.Focus();
                return false;
            }

            // التحقق من عدم وجود اسم المستخدم مسبقاً
            if (!_isEditMode || (_isEditMode && txtUsername.Text.Trim() != _currentUser.Username))
            {
                if (UserManager.UsernameExists(txtUsername.Text.Trim(), _isEditMode ? _currentUser.UserID : 0))
                {
                    MessageBox.Show("اسم المستخدم موجود مسبقاً، يرجى اختيار اسم آخر", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtUsername.Focus();
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// التحقق من صحة البريد الإلكتروني
        /// </summary>
        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// حفظ بيانات المستخدم
        /// </summary>
        private void SaveUser()
        {
            try
            {
                // تحديث خصائص المستخدم
                _currentUser.Username = txtUsername.Text.Trim();
                _currentUser.FullName = txtFullName.Text.Trim();
                _currentUser.Email = txtEmail.Text.Trim();
                _currentUser.Phone = txtPhone.Text.Trim();
                _currentUser.Role = cmbRole.Text;
                _currentUser.IsActive = chkIsActive.Checked;
                _currentUser.CanAddUsers = chkCanAddUsers.Checked;
                _currentUser.CanEditUsers = chkCanEditUsers.Checked;
                _currentUser.CanDeleteUsers = chkCanDeleteUsers.Checked;
                _currentUser.CanViewReports = chkCanViewReports.Checked;
                _currentUser.CanPrint = chkCanManageSettings.Checked;

                // تحديث كلمة المرور فقط إذا تم إدخالها
                if (!string.IsNullOrWhiteSpace(txtPassword.Text))
                {
                    _currentUser.Password = txtPassword.Text; // سيتم تشفيرها في UserManager
                }

                bool success = false;

                if (_isEditMode)
                {
                    // تحديث مستخدم موجود
                    _currentUser.ModifiedDate = DateTime.Now;
                    success = UserManager.UpdateUser(_currentUser);

                    // تحديث كلمة المرور إذا تم إدخالها
                    if (!string.IsNullOrWhiteSpace(txtPassword.Text))
                    {
                        UserManager.ChangePassword(_currentUser.UserID, txtPassword.Text);
                    }
                }
                else
                {
                    // إضافة مستخدم جديد
                    _currentUser.CreatedDate = DateTime.Now;
                    int newUserId = UserManager.AddUser(_currentUser);
                    success = newUserId > 0;

                    if (success)
                    {
                        _currentUser.UserID = newUserId;
                    }
                }

                if (success)
                {
                    MessageBox.Show($"تم {(_isEditMode ? "تحديث" : "إضافة")} المستخدم بنجاح",
                                  "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    LogManager.LogInfo($"تم {(_isEditMode ? "تعديل" : "إضافة")} المستخدم: {_currentUser.Username}");
                }
                else
                {
                    MessageBox.Show($"فشل في {(_isEditMode ? "تحديث" : "إضافة")} المستخدم",
                                  "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في حفظ المستخدم: {ex.Message}");
                MessageBox.Show($"حدث خطأ في {(_isEditMode ? "تحديث" : "إضافة")} المستخدم: {ex.Message}",
                              "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                throw;
            }
        }

        #endregion

        #region Properties - الخصائص

        /// <summary>
        /// المستخدم الحالي
        /// </summary>
        public User CurrentUser
        {
            get { return _currentUser; }
        }

        #endregion
    }
}
