using System;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نموذج إدارة السندات المحاسبية - Voucher Management Form
    /// </summary>
    public partial class VoucherForm : Form
    {
        #region Fields

        private BindingSource _bindingSource;
        private int? _selectedVoucherId;

        #endregion

        #region Constructor

        public VoucherForm()
        {
            InitializeComponent();
            SetupForm();
            LoadData();
        }

        #endregion

        #region Setup Methods

        private void SetupForm()
        {
            this.Text = "إدارة السندات المحاسبية";
            this.WindowState = FormWindowState.Maximized;
            
            // إعداد التصميم المسطح
            SetupFlatDesign();
            
            // إعداد الشبكة
            SetupDataGrid();
            
            // إعداد الأحداث
            SetupEvents();
        }

        private void SetupFlatDesign()
        {
            // إعدادات النموذج
            this.BackColor = Color.White;
            this.Font = new Font("Segoe UI", 9F);
            
            // إعداد الأزرار
            foreach (Control control in this.Controls)
            {
                if (control is Button btn)
                {
                    btn.FlatStyle = FlatStyle.Flat;
                    btn.FlatAppearance.BorderSize = 1;
                    btn.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
                }
            }

            // ألوان الأزرار
            btnAdd.BackColor = Color.FromArgb(46, 204, 113);
            btnAdd.ForeColor = Color.White;
            btnEdit.BackColor = Color.FromArgb(52, 152, 219);
            btnEdit.ForeColor = Color.White;
            btnDelete.BackColor = Color.FromArgb(231, 76, 60);
            btnDelete.ForeColor = Color.White;
            btnPost.BackColor = Color.FromArgb(155, 89, 182);
            btnPost.ForeColor = Color.White;
            btnUnpost.BackColor = Color.FromArgb(243, 156, 18);
            btnUnpost.ForeColor = Color.White;
            btnPrint.BackColor = Color.FromArgb(26, 188, 156);
            btnPrint.ForeColor = Color.White;
        }

        private void SetupDataGrid()
        {
            // إعداد الشبكة
            dgvVouchers.AutoGenerateColumns = false;
            dgvVouchers.AllowUserToAddRows = false;
            dgvVouchers.AllowUserToDeleteRows = false;
            dgvVouchers.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvVouchers.MultiSelect = false;
            dgvVouchers.BackgroundColor = Color.White;
            dgvVouchers.BorderStyle = BorderStyle.FixedSingle;
            dgvVouchers.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 152, 219);
            dgvVouchers.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvVouchers.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            dgvVouchers.DefaultCellStyle.Font = new Font("Segoe UI", 9F);
            dgvVouchers.RowHeadersVisible = false;
            dgvVouchers.EnableHeadersVisualStyles = false;

            // إضافة الأعمدة
            dgvVouchers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "VoucherNumber",
                HeaderText = "رقم السند",
                DataPropertyName = "VoucherNumber",
                Width = 120
            });

            dgvVouchers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "VoucherDate",
                HeaderText = "التاريخ",
                DataPropertyName = "VoucherDate",
                Width = 100,
                DefaultCellStyle = { Format = "dd/MM/yyyy" }
            });

            dgvVouchers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Description",
                HeaderText = "البيان",
                DataPropertyName = "Description",
                Width = 250
            });

            dgvVouchers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TotalDebit",
                HeaderText = "إجمالي المدين",
                DataPropertyName = "TotalDebit",
                Width = 120,
                DefaultCellStyle = { 
                    Alignment = DataGridViewContentAlignment.MiddleRight,
                    Format = "F2"
                }
            });

            dgvVouchers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TotalCredit",
                HeaderText = "إجمالي الدائن",
                DataPropertyName = "TotalCredit",
                Width = 120,
                DefaultCellStyle = { 
                    Alignment = DataGridViewContentAlignment.MiddleRight,
                    Format = "F2"
                }
            });

            dgvVouchers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Status",
                HeaderText = "الحالة",
                DataPropertyName = "Status",
                Width = 100
            });

            dgvVouchers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CreatedDate",
                HeaderText = "تاريخ الإنشاء",
                DataPropertyName = "CreatedDate",
                Width = 120,
                DefaultCellStyle = { Format = "dd/MM/yyyy" }
            });

            // إعداد مصدر البيانات
            _bindingSource = new BindingSource();
            dgvVouchers.DataSource = _bindingSource;
        }

        private void SetupEvents()
        {
            // ربط الأحداث
            btnAdd.Click += BtnAdd_Click;
            btnEdit.Click += BtnEdit_Click;
            btnDelete.Click += BtnDelete_Click;
            btnPost.Click += BtnPost_Click;
            btnUnpost.Click += BtnUnpost_Click;
            btnPrint.Click += BtnPrint_Click;
            btnRefresh.Click += BtnRefresh_Click;
            
            dgvVouchers.SelectionChanged += DgvVouchers_SelectionChanged;
            dgvVouchers.CellFormatting += DgvVouchers_CellFormatting;
            dgvVouchers.CellDoubleClick += DgvVouchers_CellDoubleClick;
        }

        #endregion

        #region Load Data Methods

        private void LoadData()
        {
            try
            {
                var vouchers = VoucherManager.GetAllVouchers();
                _bindingSource.DataSource = vouchers;
                
                UpdateButtonStates();
                UpdateStatusLabel();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Event Handlers

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                using (var addForm = new VoucherAddEditForm())
                {
                    if (addForm.ShowDialog() == DialogResult.OK)
                    {
                        LoadData();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة السند: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvVouchers.SelectedRows.Count > 0)
                {
                    var selectedRow = dgvVouchers.SelectedRows[0];
                    if (selectedRow.DataBoundItem is Voucher voucher)
                    {
                        using (var editForm = new VoucherAddEditForm(voucher))
                        {
                            if (editForm.ShowDialog() == DialogResult.OK)
                            {
                                LoadData();
                            }
                        }
                    }
                }
                else
                {
                    MessageBox.Show("يرجى اختيار سند للتعديل", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تعديل السند: " + ex.Message, "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            try
            {
                if (_selectedVoucherId.HasValue)
                {
                    var result = MessageBox.Show("هل تريد حذف السند المحدد؟", "تأكيد الحذف",
                        MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    
                    if (result == DialogResult.Yes)
                    {
                        if (VoucherManager.DeleteVoucher(_selectedVoucherId.Value))
                        {
                            MessageBox.Show("تم حذف السند بنجاح", "نجح",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                            LoadData();
                        }
                        else
                        {
                            MessageBox.Show("فشل في حذف السند", "خطأ",
                                MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف السند: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnPost_Click(object sender, EventArgs e)
        {
            try
            {
                if (_selectedVoucherId.HasValue)
                {
                    var result = MessageBox.Show("هل تريد ترحيل السند المحدد؟", "تأكيد الترحيل",
                        MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    
                    if (result == DialogResult.Yes)
                    {
                        if (VoucherManager.PostVoucher(_selectedVoucherId.Value))
                        {
                            MessageBox.Show("تم ترحيل السند بنجاح", "نجح",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                            LoadData();
                        }
                        else
                        {
                            MessageBox.Show("فشل في ترحيل السند", "خطأ",
                                MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في ترحيل السند: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnUnpost_Click(object sender, EventArgs e)
        {
            try
            {
                if (_selectedVoucherId.HasValue)
                {
                    var result = MessageBox.Show("هل تريد إلغاء ترحيل السند المحدد؟", "تأكيد إلغاء الترحيل",
                        MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    
                    if (result == DialogResult.Yes)
                    {
                        if (VoucherManager.UnpostVoucher(_selectedVoucherId.Value))
                        {
                            MessageBox.Show("تم إلغاء ترحيل السند بنجاح", "نجح",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                            LoadData();
                        }
                        else
                        {
                            MessageBox.Show("فشل في إلغاء ترحيل السند", "خطأ",
                                MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إلغاء ترحيل السند: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                if (_selectedVoucherId.HasValue)
                {
                    ReportsManager.PrintVoucher(_selectedVoucherId.Value);
                }
                else
                {
                    MessageBox.Show("يرجى اختيار سند للطباعة", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة السند: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadData();
        }

        private void DgvVouchers_SelectionChanged(object sender, EventArgs e)
        {
            if (dgvVouchers.SelectedRows.Count > 0)
            {
                var selectedRow = dgvVouchers.SelectedRows[0];
                if (selectedRow.DataBoundItem is Voucher voucher)
                {
                    _selectedVoucherId = voucher.VoucherID;
                    UpdateButtonStates();
                }
            }
            else
            {
                _selectedVoucherId = null;
                UpdateButtonStates();
            }
        }

        private void DgvVouchers_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (dgvVouchers.Rows[e.RowIndex].DataBoundItem is Voucher voucher)
            {
                // تلوين الصف حسب الحالة
                if (voucher.Status == "Posted")
                {
                    dgvVouchers.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.LightGreen;
                }
                else if (voucher.Status == "Cancelled")
                {
                    dgvVouchers.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.LightCoral;
                }
                else
                {
                    dgvVouchers.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.White;
                }

                // تنسيق عمود الحالة
                if (e.ColumnIndex == dgvVouchers.Columns["Status"].Index)
                {
                    switch (voucher.Status)
                    {
                        case "Draft":
                            e.Value = "مسودة";
                            break;
                        case "Posted":
                            e.Value = "مرحل";
                            break;
                        case "Cancelled":
                            e.Value = "ملغي";
                            break;
                    }
                }
            }
        }

        private void DgvVouchers_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                BtnEdit_Click(sender, e);
            }
        }

        #endregion

        #region Helper Methods

        private void UpdateButtonStates()
        {
            bool hasSelection = _selectedVoucherId.HasValue;
            
            btnEdit.Enabled = hasSelection;
            btnDelete.Enabled = hasSelection;
            btnPost.Enabled = hasSelection;
            btnUnpost.Enabled = hasSelection;
            btnPrint.Enabled = hasSelection;

            if (hasSelection && dgvVouchers.SelectedRows.Count > 0)
            {
                var selectedRow = dgvVouchers.SelectedRows[0];
                if (selectedRow.DataBoundItem is Voucher voucher)
                {
                    btnPost.Enabled = voucher.Status == "Draft";
                    btnUnpost.Enabled = voucher.Status == "Posted";
                    btnEdit.Enabled = voucher.Status == "Draft";
                    btnDelete.Enabled = voucher.Status == "Draft";
                }
            }
        }

        private void UpdateStatusLabel()
        {
            var totalVouchers = _bindingSource.Count;
            var postedVouchers = 0;
            var draftVouchers = 0;

            foreach (Voucher voucher in _bindingSource)
            {
                if (voucher.Status == "Posted")
                    postedVouchers++;
                else if (voucher.Status == "Draft")
                    draftVouchers++;
            }

            lblStatus.Text = $"إجمالي السندات: {totalVouchers} | مرحل: {postedVouchers} | مسودة: {draftVouchers}";
        }

        #endregion
    }
}
