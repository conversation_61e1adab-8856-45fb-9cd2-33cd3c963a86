-- ===================================================================
-- إدراج بيانات تجريبية شاملة للتقارير - Insert Comprehensive Test Data
-- تاريخ الإنشاء: 2025-01-28
-- الغرض: إضافة بيانات تجريبية لجعل التقارير تعمل
-- ===================================================================

USE PharmacyDB
GO

PRINT '📦 بدء إدراج البيانات التجريبية الشاملة...'
PRINT '📅 التاريخ: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT ''

-- ===================================================================
-- 1. إضافة الشركات المصنعة
-- ===================================================================

PRINT '🏭 إضافة الشركات المصنعة...'

IF NOT EXISTS (SELECT 1 FROM Manufacturers WHERE ManufacturerCode = 'PFIZER')
BEGIN
    INSERT INTO Manufacturers (ManufacturerCode, ManufacturerName, Country, IsActive, CreatedDate, CreatedBy)
    VALUES 
    ('PFIZER', 'شركة فايزر', 'الولايات المتحدة', 1, GETDATE(), 1),
    ('NOVARTIS', 'شركة نوفارتيس', 'سويسرا', 1, GETDATE(), 1),
    ('ROCHE', 'شركة روش', 'سويسرا', 1, GETDATE(), 1),
    ('SANOFI', 'شركة سانوفي', 'فرنسا', 1, GETDATE(), 1),
    ('GSK', 'شركة جلاكسو سميث كلاين', 'المملكة المتحدة', 1, GETDATE(), 1),
    ('BAYER', 'شركة باير', 'ألمانيا', 1, GETDATE(), 1),
    ('ABBOTT', 'شركة أبوت', 'الولايات المتحدة', 1, GETDATE(), 1),
    ('MERCK', 'شركة مرك', 'الولايات المتحدة', 1, GETDATE(), 1)
    
    PRINT '✅ تم إضافة 8 شركات مصنعة'
END
ELSE
    PRINT '✅ الشركات المصنعة موجودة'

-- ===================================================================
-- 2. إضافة العملاء
-- ===================================================================

PRINT '👥 إضافة العملاء...'

IF NOT EXISTS (SELECT 1 FROM Customers WHERE CustomerName = 'أحمد محمد علي')
BEGIN
    INSERT INTO Customers (CustomerName, Phone, Email, Address, Balance, CreditLimit, IsActive, CreatedDate)
    VALUES 
    ('أحمد محمد علي', '0501234567', '<EMAIL>', 'صنعاء - شارع الزبيري', 0, 5000, 1, GETDATE()),
    ('فاطمة سالم أحمد', '0509876543', '<EMAIL>', 'عدن - كريتر', 0, 3000, 1, GETDATE()),
    ('محمد عبدالله سالم', '0512345678', '<EMAIL>', 'تعز - الجمهورية', 0, 4000, 1, GETDATE()),
    ('نورا علي حسن', '0556789012', '<EMAIL>', 'الحديدة - الكورنيش', 0, 2000, 1, GETDATE()),
    ('خالد عبدالرحمن', '0543210987', '<EMAIL>', 'إب - السوق', 0, 6000, 1, GETDATE()),
    ('مريم أحمد محمد', '0567890123', '<EMAIL>', 'المكلا - الشاطئ', 0, 3500, 1, GETDATE()),
    ('عبدالله سعيد علي', '0578901234', '<EMAIL>', 'صعدة - المدينة', 0, 2500, 1, GETDATE()),
    ('زينب محمد سالم', '0589012345', '<EMAIL>', 'مأرب - الوادي', 0, 4500, 1, GETDATE()),
    ('يوسف علي أحمد', '0590123456', '<EMAIL>', 'حضرموت - سيئون', 0, 5500, 1, GETDATE()),
    ('عائشة سالم محمد', '0501112233', '<EMAIL>', 'لحج - الحوطة', 0, 3000, 1, GETDATE())
    
    PRINT '✅ تم إضافة 10 عملاء'
END
ELSE
    PRINT '✅ العملاء موجودون'

-- ===================================================================
-- 3. إضافة الموردين
-- ===================================================================

PRINT '🏪 إضافة الموردين...'

IF NOT EXISTS (SELECT 1 FROM Suppliers WHERE SupplierName = 'شركة الأدوية المتقدمة')
BEGIN
    INSERT INTO Suppliers (SupplierName, ContactPerson, Phone, Email, Address, Balance, CreditLimit, IsActive, CreatedDate)
    VALUES 
    ('شركة الأدوية المتقدمة', 'أحمد السالم', '0112345678', '<EMAIL>', 'صنعاء - شارع الستين', 0, 50000, 1, GETDATE()),
    ('مؤسسة الدواء الشامل', 'محمد العلي', '0126789012', '<EMAIL>', 'عدن - المعلا', 0, 40000, 1, GETDATE()),
    ('شركة الصحة الذهبية', 'فاطمة أحمد', '0138901234', '<EMAIL>', 'تعز - صالة', 0, 35000, 1, GETDATE()),
    ('مستودع الشفاء الطبي', 'علي محمد', '0149012345', '<EMAIL>', 'الحديدة - الميناء', 0, 30000, 1, GETDATE()),
    ('شركة النور للأدوية', 'سالم عبدالله', '0150123456', '<EMAIL>', 'إب - المدينة', 0, 25000, 1, GETDATE())
    
    PRINT '✅ تم إضافة 5 موردين'
END
ELSE
    PRINT '✅ الموردين موجودون'

-- ===================================================================
-- 4. إضافة المبيعات التجريبية
-- ===================================================================

PRINT '💰 إضافة المبيعات التجريبية...'

-- إضافة 100 عملية بيع تجريبية
DECLARE @SaleCounter INT = 1
DECLARE @CustomerCount INT = (SELECT COUNT(*) FROM Customers WHERE IsActive = 1)
DECLARE @DrugCount INT = (SELECT COUNT(*) FROM Drugs WHERE IsActive = 1)

IF @CustomerCount > 0 AND @DrugCount > 0
BEGIN
    WHILE @SaleCounter <= 100
    BEGIN
        DECLARE @CustomerID INT, @SaleID INT
        DECLARE @SaleDate DATETIME = DATEADD(DAY, -CAST(RAND() * 90 AS INT), GETDATE())
        
        -- اختيار عميل عشوائي (أو عميل نقدي)
        IF RAND() > 0.3
            SELECT TOP 1 @CustomerID = CustomerID FROM Customers WHERE IsActive = 1 ORDER BY NEWID()
        ELSE
            SET @CustomerID = NULL -- عميل نقدي
        
        -- إنشاء عملية بيع
        DECLARE @TotalAmount DECIMAL(10,2) = CAST(RAND() * 800 + 50 AS DECIMAL(10,2))
        DECLARE @DiscountAmount DECIMAL(10,2) = CAST(@TotalAmount * RAND() * 0.1 AS DECIMAL(10,2))
        DECLARE @TaxAmount DECIMAL(10,2) = CAST(@TotalAmount * 0.15 AS DECIMAL(10,2))
        DECLARE @NetAmount DECIMAL(10,2) = @TotalAmount - @DiscountAmount + @TaxAmount
        
        INSERT INTO Sales (SaleDate, CustomerID, TotalAmount, DiscountAmount, TaxAmount, NetAmount, PaymentMethod, Notes, UserID)
        VALUES (@SaleDate, @CustomerID, @TotalAmount, @DiscountAmount, @TaxAmount, @NetAmount, 
                CASE WHEN RAND() > 0.7 THEN 'آجل' ELSE 'نقدي' END, 
                'بيع تجريبي رقم ' + CAST(@SaleCounter AS VARCHAR), 1)
        
        SET @SaleID = SCOPE_IDENTITY()
        
        -- إضافة تفاصيل البيع (1-5 أدوية لكل بيع)
        DECLARE @ItemCount INT = CAST(RAND() * 5 + 1 AS INT)
        DECLARE @ItemCounter INT = 1
        DECLARE @RemainingAmount DECIMAL(10,2) = @TotalAmount
        
        WHILE @ItemCounter <= @ItemCount AND @RemainingAmount > 0
        BEGIN
            DECLARE @DrugID INT
            SELECT TOP 1 @DrugID = DrugID FROM Drugs WHERE IsActive = 1 ORDER BY NEWID()
            
            DECLARE @Quantity INT = CAST(RAND() * 10 + 1 AS INT)
            DECLARE @UnitPrice DECIMAL(10,2) = CASE 
                WHEN @ItemCounter = @ItemCount THEN @RemainingAmount / @Quantity
                ELSE CAST(RAND() * 100 + 10 AS DECIMAL(10,2))
            END
            DECLARE @TotalPrice DECIMAL(10,2) = @Quantity * @UnitPrice
            
            IF @TotalPrice > @RemainingAmount
                SET @TotalPrice = @RemainingAmount
            
            INSERT INTO SaleDetails (SaleID, DrugID, Quantity, UnitPrice, TotalPrice, DiscountAmount)
            VALUES (@SaleID, @DrugID, @Quantity, @UnitPrice, @TotalPrice, 0)
            
            SET @RemainingAmount = @RemainingAmount - @TotalPrice
            SET @ItemCounter = @ItemCounter + 1
        END
        
        SET @SaleCounter = @SaleCounter + 1
    END
    
    PRINT '✅ تم إضافة 100 عملية بيع تجريبية'
END
ELSE
    PRINT '❌ لا يمكن إضافة المبيعات - لا توجد عملاء أو أدوية'

-- ===================================================================
-- 5. إضافة المشتريات التجريبية
-- ===================================================================

PRINT '📦 إضافة المشتريات التجريبية...'

DECLARE @PurchaseCounter INT = 1
DECLARE @SupplierCount INT = (SELECT COUNT(*) FROM Suppliers WHERE IsActive = 1)

IF @SupplierCount > 0 AND @DrugCount > 0
BEGIN
    WHILE @PurchaseCounter <= 50
    BEGIN
        DECLARE @SupplierID INT, @PurchaseID INT
        DECLARE @PurchaseDate DATETIME = DATEADD(DAY, -CAST(RAND() * 120 AS INT), GETDATE())
        
        -- اختيار مورد عشوائي
        SELECT TOP 1 @SupplierID = SupplierID FROM Suppliers WHERE IsActive = 1 ORDER BY NEWID()
        
        -- إنشاء عملية شراء
        DECLARE @PurchaseAmount DECIMAL(10,2) = CAST(RAND() * 3000 + 500 AS DECIMAL(10,2))
        DECLARE @PurchaseDiscount DECIMAL(10,2) = CAST(@PurchaseAmount * RAND() * 0.05 AS DECIMAL(10,2))
        DECLARE @PurchaseTax DECIMAL(10,2) = CAST(@PurchaseAmount * 0.15 AS DECIMAL(10,2))
        DECLARE @PurchaseNet DECIMAL(10,2) = @PurchaseAmount - @PurchaseDiscount + @PurchaseTax
        
        INSERT INTO Purchases (PurchaseDate, SupplierID, TotalAmount, DiscountAmount, TaxAmount, NetAmount, PaymentMethod, Notes, UserID)
        VALUES (@PurchaseDate, @SupplierID, @PurchaseAmount, @PurchaseDiscount, @PurchaseTax, @PurchaseNet, 
                CASE WHEN RAND() > 0.3 THEN 'آجل' ELSE 'نقدي' END, 
                'شراء تجريبي رقم ' + CAST(@PurchaseCounter AS VARCHAR), 1)
        
        SET @PurchaseID = SCOPE_IDENTITY()
        
        -- إضافة تفاصيل الشراء
        DECLARE @PurchaseItemCount INT = CAST(RAND() * 8 + 2 AS INT)
        DECLARE @PurchaseItemCounter INT = 1
        DECLARE @RemainingPurchaseAmount DECIMAL(10,2) = @PurchaseAmount
        
        WHILE @PurchaseItemCounter <= @PurchaseItemCount AND @RemainingPurchaseAmount > 0
        BEGIN
            DECLARE @PurchaseDrugID INT
            SELECT TOP 1 @PurchaseDrugID = DrugID FROM Drugs WHERE IsActive = 1 ORDER BY NEWID()
            
            DECLARE @PurchaseQuantity INT = CAST(RAND() * 50 + 10 AS INT)
            DECLARE @PurchaseUnitPrice DECIMAL(10,2) = CASE 
                WHEN @PurchaseItemCounter = @PurchaseItemCount THEN @RemainingPurchaseAmount / @PurchaseQuantity
                ELSE CAST(RAND() * 50 + 5 AS DECIMAL(10,2))
            END
            DECLARE @PurchaseTotalPrice DECIMAL(10,2) = @PurchaseQuantity * @PurchaseUnitPrice
            
            IF @PurchaseTotalPrice > @RemainingPurchaseAmount
                SET @PurchaseTotalPrice = @RemainingPurchaseAmount
            
            INSERT INTO PurchaseDetails (PurchaseID, DrugID, Quantity, UnitPrice, TotalPrice, DiscountAmount)
            VALUES (@PurchaseID, @PurchaseDrugID, @PurchaseQuantity, @PurchaseUnitPrice, @PurchaseTotalPrice, 0)
            
            SET @RemainingPurchaseAmount = @RemainingPurchaseAmount - @PurchaseTotalPrice
            SET @PurchaseItemCounter = @PurchaseItemCounter + 1
        END
        
        SET @PurchaseCounter = @PurchaseCounter + 1
    END
    
    PRINT '✅ تم إضافة 50 عملية شراء تجريبية'
END
ELSE
    PRINT '❌ لا يمكن إضافة المشتريات - لا توجد موردين أو أدوية'

PRINT ''
PRINT '✅ تم إدراج جميع البيانات التجريبية بنجاح!'
PRINT '📅 تاريخ الانتهاء: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT ''
PRINT '📊 ملخص البيانات المضافة:'
PRINT '   - 8 شركات مصنعة'
PRINT '   - 10 عملاء'
PRINT '   - 5 موردين'
PRINT '   - 100 عملية بيع'
PRINT '   - 50 عملية شراء'
PRINT '   - تفاصيل شاملة لجميع العمليات'
PRINT ''
PRINT '🎯 الآن التقارير ستعمل وتظهر البيانات!'
