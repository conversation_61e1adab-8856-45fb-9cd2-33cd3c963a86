-- ===================================================================
-- نظام إدارة الصيدلية الشامل - قاعدة البيانات الموحدة
-- Comprehensive Pharmacy Management System - Unified Database
-- يجمع جميع الجداول من جميع قواعد البيانات السابقة
-- ===================================================================

-- إنشاء قاعدة البيانات
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'PharmacyDB')
BEGIN
    CREATE DATABASE PharmacyDB
    COLLATE Arabic_CI_AS
END
GO

USE PharmacyDB
GO

PRINT '🗄️ بدء إنشاء قاعدة البيانات الشاملة...'
PRINT '🗄️ Starting comprehensive database creation...'

-- ===================================================================
-- 1. الجداول المالية والمحاسبية - Financial & Accounting Tables
-- ===================================================================

-- جدول السنوات المالية
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='FiscalYears' AND xtype='U')
BEGIN
    CREATE TABLE FiscalYears (
        FiscalYearID INT IDENTITY(1,1) PRIMARY KEY,
        YearName NVARCHAR(50) NOT NULL,
        StartDate DATE NOT NULL,
        EndDate DATE NOT NULL,
        IsActive BIT NOT NULL DEFAULT 0,
        IsClosed BIT NOT NULL DEFAULT 0,
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        CreatedBy INT,
        ClosedDate DATETIME,
        ClosedBy INT
    )
    PRINT '✅ تم إنشاء جدول السنوات المالية'
END

-- جدول مراكز التكاليف
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CostCenters' AND xtype='U')
BEGIN
    CREATE TABLE CostCenters (
        CostCenterID INT IDENTITY(1,1) PRIMARY KEY,
        CostCenterCode NVARCHAR(20) NOT NULL UNIQUE,
        CostCenterName NVARCHAR(100) NOT NULL,
        Description NVARCHAR(200),
        ParentCostCenterID INT,
        IsActive BIT NOT NULL DEFAULT 1,
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        CreatedBy INT,
        FOREIGN KEY (ParentCostCenterID) REFERENCES CostCenters(CostCenterID)
    )
    PRINT '✅ تم إنشاء جدول مراكز التكاليف'
END

-- جدول الدليل المحاسبي
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ChartOfAccounts' AND xtype='U')
BEGIN
    CREATE TABLE ChartOfAccounts (
        AccountID INT IDENTITY(1,1) PRIMARY KEY,
        AccountCode NVARCHAR(20) NOT NULL UNIQUE,
        AccountName NVARCHAR(100) NOT NULL,
        AccountType NVARCHAR(20) NOT NULL, -- Asset, Liability, Equity, Revenue, Expense
        ParentAccountID INT,
        Level INT NOT NULL DEFAULT 1,
        IsActive BIT NOT NULL DEFAULT 1,
        IsSystemAccount BIT NOT NULL DEFAULT 0,
        Description NVARCHAR(200),
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        CreatedBy INT,
        FOREIGN KEY (ParentAccountID) REFERENCES ChartOfAccounts(AccountID)
    )
    PRINT '✅ تم إنشاء جدول الدليل المحاسبي'
END

-- جدول العملات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Currencies' AND xtype='U')
BEGIN
    CREATE TABLE Currencies (
        CurrencyID INT IDENTITY(1,1) PRIMARY KEY,
        CurrencyCode NVARCHAR(3) NOT NULL UNIQUE,
        CurrencyName NVARCHAR(100) NOT NULL,
        CurrencySymbol NVARCHAR(10) NOT NULL,
        ExchangeRate DECIMAL(18,6) NOT NULL DEFAULT 1.0,
        IsBaseCurrency BIT NOT NULL DEFAULT 0,
        IsActive BIT NOT NULL DEFAULT 1,
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        ModifiedDate DATETIME,
        CreatedBy INT,
        ModifiedBy INT
    )
    PRINT '✅ تم إنشاء جدول العملات'
END

-- جدول القيود المحاسبية
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='JournalEntries' AND xtype='U')
BEGIN
    CREATE TABLE JournalEntries (
        JournalEntryID INT IDENTITY(1,1) PRIMARY KEY,
        EntryNumber NVARCHAR(20) NOT NULL UNIQUE,
        EntryDate DATE NOT NULL,
        Description NVARCHAR(500),
        Reference NVARCHAR(100),
        TotalDebit DECIMAL(18,2) NOT NULL DEFAULT 0,
        TotalCredit DECIMAL(18,2) NOT NULL DEFAULT 0,
        IsPosted BIT NOT NULL DEFAULT 0,
        PostedDate DATETIME,
        PostedBy INT,
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        CreatedBy INT,
        ModifiedDate DATETIME,
        ModifiedBy INT
    )
    PRINT '✅ تم إنشاء جدول القيود المحاسبية'
END

-- جدول تفاصيل القيود المحاسبية
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='JournalEntryDetails' AND xtype='U')
BEGIN
    CREATE TABLE JournalEntryDetails (
        DetailID INT IDENTITY(1,1) PRIMARY KEY,
        JournalEntryID INT NOT NULL,
        AccountID INT NOT NULL,
        CostCenterID INT,
        DebitAmount DECIMAL(18,2) NOT NULL DEFAULT 0,
        CreditAmount DECIMAL(18,2) NOT NULL DEFAULT 0,
        Description NVARCHAR(200),
        FOREIGN KEY (JournalEntryID) REFERENCES JournalEntries(JournalEntryID),
        FOREIGN KEY (AccountID) REFERENCES ChartOfAccounts(AccountID),
        FOREIGN KEY (CostCenterID) REFERENCES CostCenters(CostCenterID)
    )
    PRINT '✅ تم إنشاء جدول تفاصيل القيود المحاسبية'
END

-- ===================================================================
-- 2. جداول إدارة المستخدمين والصلاحيات - Users & Permissions
-- ===================================================================

-- جدول المستخدمين
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Users' AND xtype='U')
BEGIN
    CREATE TABLE Users (
        UserID INT IDENTITY(1,1) PRIMARY KEY,
        Username NVARCHAR(50) NOT NULL UNIQUE,
        PasswordHash NVARCHAR(255) NOT NULL,
        FullName NVARCHAR(100) NOT NULL,
        Email NVARCHAR(100),
        Phone NVARCHAR(20),
        Role NVARCHAR(20) NOT NULL DEFAULT 'Employee',
        BranchID INT,
        IsActive BIT NOT NULL DEFAULT 1,
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        LastLoginDate DATETIME,
        CreatedBy INT,
        ModifiedDate DATETIME,
        ModifiedBy INT
    )
    PRINT '✅ تم إنشاء جدول المستخدمين'
END

-- جدول صلاحيات المستخدمين
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='UserPermissions' AND xtype='U')
BEGIN
    CREATE TABLE UserPermissions (
        PermissionID INT IDENTITY(1,1) PRIMARY KEY,
        UserID INT NOT NULL,
        ModuleName NVARCHAR(50) NOT NULL,
        CanView BIT NOT NULL DEFAULT 0,
        CanAdd BIT NOT NULL DEFAULT 0,
        CanEdit BIT NOT NULL DEFAULT 0,
        CanDelete BIT NOT NULL DEFAULT 0,
        CanPrint BIT NOT NULL DEFAULT 0,
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        CreatedBy INT,
        FOREIGN KEY (UserID) REFERENCES Users(UserID)
    )
    PRINT '✅ تم إنشاء جدول صلاحيات المستخدمين'
END

-- جدول جلسات المستخدمين
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='UserSessions' AND xtype='U')
BEGIN
    CREATE TABLE UserSessions (
        SessionID INT IDENTITY(1,1) PRIMARY KEY,
        UserID INT NOT NULL,
        SessionToken NVARCHAR(255) NOT NULL UNIQUE,
        LoginTime DATETIME NOT NULL DEFAULT GETDATE(),
        LogoutTime DATETIME,
        IPAddress NVARCHAR(50),
        UserAgent NVARCHAR(500),
        IsActive BIT NOT NULL DEFAULT 1,
        FOREIGN KEY (UserID) REFERENCES Users(UserID)
    )
    PRINT '✅ تم إنشاء جدول جلسات المستخدمين'
END

-- ===================================================================
-- 3. جداول إدارة الفروع والمخازن - Branches & Warehouses
-- ===================================================================

-- جدول الفروع
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Branches' AND xtype='U')
BEGIN
    CREATE TABLE Branches (
        BranchID INT IDENTITY(1,1) PRIMARY KEY,
        BranchCode NVARCHAR(10) NOT NULL UNIQUE,
        BranchName NVARCHAR(100) NOT NULL,
        Address NVARCHAR(200),
        Phone NVARCHAR(20),
        Email NVARCHAR(100),
        Manager NVARCHAR(100),
        City NVARCHAR(50),
        Country NVARCHAR(50) DEFAULT 'اليمن',
        TaxNumber NVARCHAR(50),
        CommercialRegister NVARCHAR(50),
        IsActive BIT NOT NULL DEFAULT 1,
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        CreatedBy INT,
        ModifiedDate DATETIME,
        ModifiedBy INT
    )
    PRINT '✅ تم إنشاء جدول الفروع'
END

-- جدول المخازن
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Warehouses' AND xtype='U')
BEGIN
    CREATE TABLE Warehouses (
        WarehouseID INT IDENTITY(1,1) PRIMARY KEY,
        WarehouseCode NVARCHAR(10) NOT NULL UNIQUE,
        WarehouseName NVARCHAR(100) NOT NULL,
        BranchID INT NOT NULL,
        Location NVARCHAR(200),
        Capacity DECIMAL(10,2),
        Temperature NVARCHAR(20),
        Humidity NVARCHAR(20),
        ResponsiblePerson NVARCHAR(100),
        IsActive BIT NOT NULL DEFAULT 1,
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        CreatedBy INT,
        ModifiedDate DATETIME,
        ModifiedBy INT,
        FOREIGN KEY (BranchID) REFERENCES Branches(BranchID)
    )
    PRINT '✅ تم إنشاء جدول المخازن'
END

-- ===================================================================
-- 4. جداول إدارة الأدوية والمنتجات - Drugs & Products
-- ===================================================================

-- جدول الشركات المصنعة
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Manufacturers' AND xtype='U')
BEGIN
    CREATE TABLE Manufacturers (
        ManufacturerID INT IDENTITY(1,1) PRIMARY KEY,
        ManufacturerName NVARCHAR(100) NOT NULL,
        Country NVARCHAR(50),
        ContactInfo NVARCHAR(200),
        Phone NVARCHAR(20),
        Email NVARCHAR(100),
        Website NVARCHAR(100),
        IsActive BIT NOT NULL DEFAULT 1,
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        CreatedBy INT
    )
    PRINT '✅ تم إنشاء جدول الشركات المصنعة'
END

-- جدول فئات الأدوية
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='DrugCategories' AND xtype='U')
BEGIN
    CREATE TABLE DrugCategories (
        CategoryID INT IDENTITY(1,1) PRIMARY KEY,
        CategoryCode NVARCHAR(10) NOT NULL UNIQUE,
        CategoryName NVARCHAR(100) NOT NULL,
        Description NVARCHAR(200),
        ParentCategoryID INT,
        IsActive BIT NOT NULL DEFAULT 1,
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        CreatedBy INT,
        FOREIGN KEY (ParentCategoryID) REFERENCES DrugCategories(CategoryID)
    )
    PRINT '✅ تم إنشاء جدول فئات الأدوية'
END

-- جدول الأدوية
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Drugs' AND xtype='U')
BEGIN
    CREATE TABLE Drugs (
        DrugID INT IDENTITY(1,1) PRIMARY KEY,
        DrugCode NVARCHAR(20) NOT NULL UNIQUE,
        DrugName NVARCHAR(200) NOT NULL,
        ScientificName NVARCHAR(200),
        Barcode NVARCHAR(50) UNIQUE,
        CategoryID INT,
        ManufacturerID INT,
        Unit NVARCHAR(20) NOT NULL DEFAULT 'قرص',
        Strength NVARCHAR(50),
        Form NVARCHAR(50), -- قرص، كبسولة، شراب، حقن
        PackageSize INT DEFAULT 1,
        PurchasePrice DECIMAL(10,2) NOT NULL DEFAULT 0,
        SalePrice DECIMAL(10,2) NOT NULL DEFAULT 0,
        WholesalePrice DECIMAL(10,2) NOT NULL DEFAULT 0,
        MinStock INT NOT NULL DEFAULT 10,
        MaxStock INT NOT NULL DEFAULT 1000,
        ReorderLevel INT NOT NULL DEFAULT 20,
        ShelfLife INT, -- بالأشهر
        StorageConditions NVARCHAR(200),
        IsControlled BIT NOT NULL DEFAULT 0, -- دواء مراقب
        RequiresPrescription BIT NOT NULL DEFAULT 0,
        IsActive BIT NOT NULL DEFAULT 1,
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        CreatedBy INT,
        ModifiedDate DATETIME,
        ModifiedBy INT,
        FOREIGN KEY (CategoryID) REFERENCES DrugCategories(CategoryID),
        FOREIGN KEY (ManufacturerID) REFERENCES Manufacturers(ManufacturerID)
    )
    PRINT '✅ تم إنشاء جدول الأدوية'
END

-- جدول بدائل الأدوية
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='DrugAlternatives' AND xtype='U')
BEGIN
    CREATE TABLE DrugAlternatives (
        AlternativeID INT IDENTITY(1,1) PRIMARY KEY,
        DrugID INT NOT NULL,
        AlternativeDrugID INT NOT NULL,
        AlternativeType NVARCHAR(20) NOT NULL, -- Generic, Brand, Therapeutic
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        CreatedBy INT,
        FOREIGN KEY (DrugID) REFERENCES Drugs(DrugID),
        FOREIGN KEY (AlternativeDrugID) REFERENCES Drugs(DrugID)
    )
    PRINT '✅ تم إنشاء جدول بدائل الأدوية'
END

-- جدول أسعار الأدوية التاريخية
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='DrugPriceHistory' AND xtype='U')
BEGIN
    CREATE TABLE DrugPriceHistory (
        PriceHistoryID INT IDENTITY(1,1) PRIMARY KEY,
        DrugID INT NOT NULL,
        OldPurchasePrice DECIMAL(10,2),
        NewPurchasePrice DECIMAL(10,2),
        OldSalePrice DECIMAL(10,2),
        NewSalePrice DECIMAL(10,2),
        ChangeDate DATETIME NOT NULL DEFAULT GETDATE(),
        ChangedBy INT,
        Reason NVARCHAR(200),
        FOREIGN KEY (DrugID) REFERENCES Drugs(DrugID)
    )
    PRINT '✅ تم إنشاء جدول أسعار الأدوية التاريخية'
END

-- ===================================================================
-- 5. جداول إدارة العملاء والموردين - Customers & Suppliers
-- ===================================================================

-- جدول العملاء
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Customers' AND xtype='U')
BEGIN
    CREATE TABLE Customers (
        CustomerID INT IDENTITY(1,1) PRIMARY KEY,
        CustomerCode NVARCHAR(20) NOT NULL UNIQUE,
        CustomerName NVARCHAR(100) NOT NULL,
        Phone NVARCHAR(20),
        Mobile NVARCHAR(20),
        Email NVARCHAR(100),
        Address NVARCHAR(200),
        City NVARCHAR(50),
        DateOfBirth DATE,
        Gender NVARCHAR(10),
        CustomerType NVARCHAR(20) DEFAULT 'عادي', -- عادي، VIP، جملة، مؤسسة
        CreditLimit DECIMAL(10,2) DEFAULT 0,
        CurrentBalance DECIMAL(10,2) DEFAULT 0,
        TaxNumber NVARCHAR(50),
        DiscountPercent DECIMAL(5,2) DEFAULT 0,
        IsActive BIT NOT NULL DEFAULT 1,
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        CreatedBy INT,
        ModifiedDate DATETIME,
        ModifiedBy INT
    )
    PRINT '✅ تم إنشاء جدول العملاء'
END

-- جدول الموردين
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Suppliers' AND xtype='U')
BEGIN
    CREATE TABLE Suppliers (
        SupplierID INT IDENTITY(1,1) PRIMARY KEY,
        SupplierCode NVARCHAR(20) NOT NULL UNIQUE,
        SupplierName NVARCHAR(100) NOT NULL,
        ContactPerson NVARCHAR(100),
        Phone NVARCHAR(20),
        Mobile NVARCHAR(20),
        Email NVARCHAR(100),
        Address NVARCHAR(200),
        City NVARCHAR(50),
        Country NVARCHAR(50),
        TaxNumber NVARCHAR(50),
        CommercialRegister NVARCHAR(50),
        PaymentTerms NVARCHAR(100),
        CreditLimit DECIMAL(10,2) DEFAULT 0,
        CurrentBalance DECIMAL(10,2) DEFAULT 0,
        IsActive BIT NOT NULL DEFAULT 1,
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        CreatedBy INT,
        ModifiedDate DATETIME,
        ModifiedBy INT
    )
    PRINT '✅ تم إنشاء جدول الموردين'
END

-- ===================================================================
-- 6. جداول إدارة المخزون - Inventory Management
-- ===================================================================

-- جدول المخزون
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Inventory' AND xtype='U')
BEGIN
    CREATE TABLE Inventory (
        InventoryID INT IDENTITY(1,1) PRIMARY KEY,
        DrugID INT NOT NULL,
        WarehouseID INT NOT NULL,
        BatchNumber NVARCHAR(50),
        Quantity INT NOT NULL DEFAULT 0,
        ReservedQuantity INT NOT NULL DEFAULT 0,
        AvailableQuantity AS (Quantity - ReservedQuantity),
        ExpiryDate DATE,
        ManufacturingDate DATE,
        PurchasePrice DECIMAL(10,2),
        SalePrice DECIMAL(10,2),
        SupplierID INT,
        PurchaseInvoiceNumber NVARCHAR(50),
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        UpdatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        CreatedBy INT,
        UpdatedBy INT,
        FOREIGN KEY (DrugID) REFERENCES Drugs(DrugID),
        FOREIGN KEY (WarehouseID) REFERENCES Warehouses(WarehouseID),
        FOREIGN KEY (SupplierID) REFERENCES Suppliers(SupplierID)
    )
    PRINT '✅ تم إنشاء جدول المخزون'
END

-- جدول حركات المخزون
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='StockMovements' AND xtype='U')
BEGIN
    CREATE TABLE StockMovements (
        MovementID INT IDENTITY(1,1) PRIMARY KEY,
        DrugID INT NOT NULL,
        WarehouseID INT NOT NULL,
        MovementType NVARCHAR(20) NOT NULL, -- دخول، خروج، تحويل، تسوية، إتلاف
        Quantity INT NOT NULL,
        UnitCost DECIMAL(10,2),
        TotalCost DECIMAL(10,2),
        ReferenceNumber NVARCHAR(50),
        ReferenceType NVARCHAR(20), -- فاتورة_شراء، فاتورة_بيع، تحويل، تسوية
        Notes NVARCHAR(200),
        UserID INT NOT NULL,
        MovementDate DATETIME NOT NULL DEFAULT GETDATE(),
        FOREIGN KEY (DrugID) REFERENCES Drugs(DrugID),
        FOREIGN KEY (WarehouseID) REFERENCES Warehouses(WarehouseID),
        FOREIGN KEY (UserID) REFERENCES Users(UserID)
    )
    PRINT '✅ تم إنشاء جدول حركات المخزون'
END

-- جدول تحويلات المخزون
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='StockTransfers' AND xtype='U')
BEGIN
    CREATE TABLE StockTransfers (
        TransferID INT IDENTITY(1,1) PRIMARY KEY,
        TransferNumber NVARCHAR(20) NOT NULL UNIQUE,
        FromWarehouseID INT NOT NULL,
        ToWarehouseID INT NOT NULL,
        TransferDate DATETIME NOT NULL DEFAULT GETDATE(),
        Status NVARCHAR(20) NOT NULL DEFAULT 'معلق', -- معلق، مرسل، مستلم، ملغي
        Notes NVARCHAR(500),
        RequestedBy INT NOT NULL,
        ApprovedBy INT,
        SentBy INT,
        ReceivedBy INT,
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        FOREIGN KEY (FromWarehouseID) REFERENCES Warehouses(WarehouseID),
        FOREIGN KEY (ToWarehouseID) REFERENCES Warehouses(WarehouseID),
        FOREIGN KEY (RequestedBy) REFERENCES Users(UserID)
    )
    PRINT '✅ تم إنشاء جدول تحويلات المخزون'
END

-- جدول تفاصيل تحويلات المخزون
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='StockTransferDetails' AND xtype='U')
BEGIN
    CREATE TABLE StockTransferDetails (
        TransferDetailID INT IDENTITY(1,1) PRIMARY KEY,
        TransferID INT NOT NULL,
        DrugID INT NOT NULL,
        RequestedQuantity INT NOT NULL,
        SentQuantity INT DEFAULT 0,
        ReceivedQuantity INT DEFAULT 0,
        BatchNumber NVARCHAR(50),
        ExpiryDate DATE,
        UnitCost DECIMAL(10,2),
        Notes NVARCHAR(200),
        FOREIGN KEY (TransferID) REFERENCES StockTransfers(TransferID),
        FOREIGN KEY (DrugID) REFERENCES Drugs(DrugID)
    )
    PRINT '✅ تم إنشاء جدول تفاصيل تحويلات المخزون'
END

-- ===================================================================
-- 7. جداول المبيعات والفواتير - Sales & Invoices
-- ===================================================================

-- جدول فواتير البيع
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SalesInvoices' AND xtype='U')
BEGIN
    CREATE TABLE SalesInvoices (
        InvoiceID INT IDENTITY(1,1) PRIMARY KEY,
        InvoiceNumber NVARCHAR(20) NOT NULL UNIQUE,
        CustomerID INT,
        UserID INT NOT NULL,
        BranchID INT NOT NULL,
        WarehouseID INT NOT NULL,
        InvoiceDate DATETIME NOT NULL DEFAULT GETDATE(),
        DueDate DATE,
        SubTotal DECIMAL(10,2) NOT NULL DEFAULT 0,
        DiscountAmount DECIMAL(10,2) NOT NULL DEFAULT 0,
        DiscountPercent DECIMAL(5,2) NOT NULL DEFAULT 0,
        TaxAmount DECIMAL(10,2) NOT NULL DEFAULT 0,
        TaxPercent DECIMAL(5,2) NOT NULL DEFAULT 0,
        NetAmount DECIMAL(10,2) NOT NULL DEFAULT 0,
        PaidAmount DECIMAL(10,2) NOT NULL DEFAULT 0,
        RemainingAmount AS (NetAmount - PaidAmount),
        PaymentMethod NVARCHAR(20) NOT NULL DEFAULT 'نقدي',
        PaymentStatus NVARCHAR(20) NOT NULL DEFAULT 'غير مدفوع',
        InvoiceStatus NVARCHAR(20) NOT NULL DEFAULT 'مكتملة',
        Notes NVARCHAR(500),
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        CreatedBy INT,
        ModifiedDate DATETIME,
        ModifiedBy INT,
        FOREIGN KEY (CustomerID) REFERENCES Customers(CustomerID),
        FOREIGN KEY (UserID) REFERENCES Users(UserID),
        FOREIGN KEY (BranchID) REFERENCES Branches(BranchID),
        FOREIGN KEY (WarehouseID) REFERENCES Warehouses(WarehouseID)
    )
    PRINT '✅ تم إنشاء جدول فواتير البيع'
END

-- جدول تفاصيل فواتير البيع
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SalesInvoiceDetails' AND xtype='U')
BEGIN
    CREATE TABLE SalesInvoiceDetails (
        DetailID INT IDENTITY(1,1) PRIMARY KEY,
        InvoiceID INT NOT NULL,
        DrugID INT NOT NULL,
        BatchNumber NVARCHAR(50),
        Quantity INT NOT NULL,
        UnitPrice DECIMAL(10,2) NOT NULL,
        DiscountPercent DECIMAL(5,2) DEFAULT 0,
        DiscountAmount DECIMAL(10,2) DEFAULT 0,
        TaxPercent DECIMAL(5,2) DEFAULT 0,
        TaxAmount DECIMAL(10,2) DEFAULT 0,
        TotalPrice DECIMAL(10,2) NOT NULL,
        ExpiryDate DATE,
        FOREIGN KEY (InvoiceID) REFERENCES SalesInvoices(InvoiceID),
        FOREIGN KEY (DrugID) REFERENCES Drugs(DrugID)
    )
    PRINT '✅ تم إنشاء جدول تفاصيل فواتير البيع'
END

-- جدول فواتير الشراء
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='PurchaseInvoices' AND xtype='U')
BEGIN
    CREATE TABLE PurchaseInvoices (
        PurchaseInvoiceID INT IDENTITY(1,1) PRIMARY KEY,
        InvoiceNumber NVARCHAR(20) NOT NULL UNIQUE,
        SupplierInvoiceNumber NVARCHAR(50),
        SupplierID INT NOT NULL,
        UserID INT NOT NULL,
        BranchID INT NOT NULL,
        WarehouseID INT NOT NULL,
        InvoiceDate DATETIME NOT NULL DEFAULT GETDATE(),
        DueDate DATE,
        SubTotal DECIMAL(10,2) NOT NULL DEFAULT 0,
        DiscountAmount DECIMAL(10,2) NOT NULL DEFAULT 0,
        DiscountPercent DECIMAL(5,2) NOT NULL DEFAULT 0,
        TaxAmount DECIMAL(10,2) NOT NULL DEFAULT 0,
        TaxPercent DECIMAL(5,2) NOT NULL DEFAULT 0,
        NetAmount DECIMAL(10,2) NOT NULL DEFAULT 0,
        PaidAmount DECIMAL(10,2) NOT NULL DEFAULT 0,
        RemainingAmount AS (NetAmount - PaidAmount),
        PaymentStatus NVARCHAR(20) NOT NULL DEFAULT 'غير مدفوع',
        InvoiceStatus NVARCHAR(20) NOT NULL DEFAULT 'مكتملة',
        Notes NVARCHAR(500),
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        CreatedBy INT,
        ModifiedDate DATETIME,
        ModifiedBy INT,
        FOREIGN KEY (SupplierID) REFERENCES Suppliers(SupplierID),
        FOREIGN KEY (UserID) REFERENCES Users(UserID),
        FOREIGN KEY (BranchID) REFERENCES Branches(BranchID),
        FOREIGN KEY (WarehouseID) REFERENCES Warehouses(WarehouseID)
    )
    PRINT '✅ تم إنشاء جدول فواتير الشراء'
END

-- جدول تفاصيل فواتير الشراء
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='PurchaseInvoiceDetails' AND xtype='U')
BEGIN
    CREATE TABLE PurchaseInvoiceDetails (
        DetailID INT IDENTITY(1,1) PRIMARY KEY,
        PurchaseInvoiceID INT NOT NULL,
        DrugID INT NOT NULL,
        BatchNumber NVARCHAR(50),
        Quantity INT NOT NULL,
        UnitCost DECIMAL(10,2) NOT NULL,
        DiscountPercent DECIMAL(5,2) DEFAULT 0,
        DiscountAmount DECIMAL(10,2) DEFAULT 0,
        TaxPercent DECIMAL(5,2) DEFAULT 0,
        TaxAmount DECIMAL(10,2) DEFAULT 0,
        TotalCost DECIMAL(10,2) NOT NULL,
        ExpiryDate DATE,
        ManufacturingDate DATE,
        FOREIGN KEY (PurchaseInvoiceID) REFERENCES PurchaseInvoices(PurchaseInvoiceID),
        FOREIGN KEY (DrugID) REFERENCES Drugs(DrugID)
    )
    PRINT '✅ تم إنشاء جدول تفاصيل فواتير الشراء'
END

-- جدول المدفوعات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Payments' AND xtype='U')
BEGIN
    CREATE TABLE Payments (
        PaymentID INT IDENTITY(1,1) PRIMARY KEY,
        PaymentNumber NVARCHAR(20) NOT NULL UNIQUE,
        PaymentType NVARCHAR(20) NOT NULL, -- دفع، استلام
        ReferenceType NVARCHAR(20) NOT NULL, -- فاتورة_بيع، فاتورة_شراء
        ReferenceID INT NOT NULL,
        CustomerID INT,
        SupplierID INT,
        Amount DECIMAL(10,2) NOT NULL,
        PaymentMethod NVARCHAR(20) NOT NULL, -- نقدي، شيك، تحويل، بطاقة
        PaymentDate DATETIME NOT NULL DEFAULT GETDATE(),
        CheckNumber NVARCHAR(50),
        CheckDate DATE,
        BankName NVARCHAR(100),
        Notes NVARCHAR(500),
        UserID INT NOT NULL,
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        FOREIGN KEY (CustomerID) REFERENCES Customers(CustomerID),
        FOREIGN KEY (SupplierID) REFERENCES Suppliers(SupplierID),
        FOREIGN KEY (UserID) REFERENCES Users(UserID)
    )
    PRINT '✅ تم إنشاء جدول المدفوعات'
END

-- ===================================================================
-- 8. جداول التقارير والإحصائيات - Reports & Statistics
-- ===================================================================

-- جدول تقارير المبيعات اليومية
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='DailySalesReports' AND xtype='U')
BEGIN
    CREATE TABLE DailySalesReports (
        ReportID INT IDENTITY(1,1) PRIMARY KEY,
        ReportDate DATE NOT NULL,
        BranchID INT NOT NULL,
        TotalInvoices INT NOT NULL DEFAULT 0,
        TotalQuantity INT NOT NULL DEFAULT 0,
        TotalAmount DECIMAL(10,2) NOT NULL DEFAULT 0,
        TotalDiscount DECIMAL(10,2) NOT NULL DEFAULT 0,
        TotalTax DECIMAL(10,2) NOT NULL DEFAULT 0,
        NetAmount DECIMAL(10,2) NOT NULL DEFAULT 0,
        CashAmount DECIMAL(10,2) NOT NULL DEFAULT 0,
        CreditAmount DECIMAL(10,2) NOT NULL DEFAULT 0,
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        CreatedBy INT,
        FOREIGN KEY (BranchID) REFERENCES Branches(BranchID)
    )
    PRINT '✅ تم إنشاء جدول تقارير المبيعات اليومية'
END

-- جدول تنبيهات النظام
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SystemAlerts' AND xtype='U')
BEGIN
    CREATE TABLE SystemAlerts (
        AlertID INT IDENTITY(1,1) PRIMARY KEY,
        AlertType NVARCHAR(20) NOT NULL, -- مخزون_منخفض، انتهاء_صلاحية، دين_متأخر
        Title NVARCHAR(100) NOT NULL,
        Message NVARCHAR(500) NOT NULL,
        Severity NVARCHAR(10) NOT NULL DEFAULT 'متوسط', -- منخفض، متوسط، عالي، حرج
        RelatedTable NVARCHAR(50),
        RelatedID INT,
        IsRead BIT NOT NULL DEFAULT 0,
        IsResolved BIT NOT NULL DEFAULT 0,
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        ReadDate DATETIME,
        ResolvedDate DATETIME,
        ResolvedBy INT
    )
    PRINT '✅ تم إنشاء جدول تنبيهات النظام'
END

-- ===================================================================
-- 9. جداول إضافية متخصصة - Additional Specialized Tables
-- ===================================================================

-- جدول الوصفات الطبية
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Prescriptions' AND xtype='U')
BEGIN
    CREATE TABLE Prescriptions (
        PrescriptionID INT IDENTITY(1,1) PRIMARY KEY,
        PrescriptionNumber NVARCHAR(20) NOT NULL UNIQUE,
        CustomerID INT NOT NULL,
        DoctorName NVARCHAR(100),
        DoctorPhone NVARCHAR(20),
        PrescriptionDate DATETIME NOT NULL DEFAULT GETDATE(),
        Status NVARCHAR(20) NOT NULL DEFAULT 'جديد', -- جديد، جاري_التحضير، جاهز، مسلم
        TotalAmount DECIMAL(10,2) DEFAULT 0,
        Notes NVARCHAR(500),
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        CreatedBy INT,
        FOREIGN KEY (CustomerID) REFERENCES Customers(CustomerID)
    )
    PRINT '✅ تم إنشاء جدول الوصفات الطبية'
END

-- جدول تفاصيل الوصفات الطبية
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='PrescriptionDetails' AND xtype='U')
BEGIN
    CREATE TABLE PrescriptionDetails (
        DetailID INT IDENTITY(1,1) PRIMARY KEY,
        PrescriptionID INT NOT NULL,
        DrugID INT NOT NULL,
        Quantity INT NOT NULL,
        Dosage NVARCHAR(100),
        Instructions NVARCHAR(200),
        Duration NVARCHAR(50),
        IsDispensed BIT NOT NULL DEFAULT 0,
        DispensedQuantity INT DEFAULT 0,
        FOREIGN KEY (PrescriptionID) REFERENCES Prescriptions(PrescriptionID),
        FOREIGN KEY (DrugID) REFERENCES Drugs(DrugID)
    )
    PRINT '✅ تم إنشاء جدول تفاصيل الوصفات الطبية'
END

-- جدول الأدوية المنتهية الصلاحية
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ExpiredDrugs' AND xtype='U')
BEGIN
    CREATE TABLE ExpiredDrugs (
        ExpiredDrugID INT IDENTITY(1,1) PRIMARY KEY,
        DrugID INT NOT NULL,
        WarehouseID INT NOT NULL,
        BatchNumber NVARCHAR(50),
        Quantity INT NOT NULL,
        ExpiryDate DATE NOT NULL,
        OriginalCost DECIMAL(10,2),
        DisposalMethod NVARCHAR(50), -- إتلاف، إرجاع_للمورد، تبرع
        DisposalDate DATE,
        DisposalNotes NVARCHAR(200),
        Status NVARCHAR(20) NOT NULL DEFAULT 'منتهي_الصلاحية',
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        CreatedBy INT,
        FOREIGN KEY (DrugID) REFERENCES Drugs(DrugID),
        FOREIGN KEY (WarehouseID) REFERENCES Warehouses(WarehouseID)
    )
    PRINT '✅ تم إنشاء جدول الأدوية المنتهية الصلاحية'
END

-- جدول إعدادات النظام
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SystemSettings' AND xtype='U')
BEGIN
    CREATE TABLE SystemSettings (
        SettingID INT IDENTITY(1,1) PRIMARY KEY,
        SettingKey NVARCHAR(50) NOT NULL UNIQUE,
        SettingValue NVARCHAR(500),
        SettingType NVARCHAR(20) NOT NULL DEFAULT 'String', -- String, Number, Boolean, Date
        Description NVARCHAR(200),
        Category NVARCHAR(50),
        IsSystemSetting BIT NOT NULL DEFAULT 0,
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        ModifiedDate DATETIME,
        ModifiedBy INT
    )
    PRINT '✅ تم إنشاء جدول إعدادات النظام'
END

-- جدول سجل العمليات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='AuditLog' AND xtype='U')
BEGIN
    CREATE TABLE AuditLog (
        LogID INT IDENTITY(1,1) PRIMARY KEY,
        TableName NVARCHAR(50) NOT NULL,
        RecordID INT NOT NULL,
        Operation NVARCHAR(10) NOT NULL, -- INSERT, UPDATE, DELETE
        OldValues NVARCHAR(MAX),
        NewValues NVARCHAR(MAX),
        UserID INT,
        IPAddress NVARCHAR(50),
        UserAgent NVARCHAR(500),
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        FOREIGN KEY (UserID) REFERENCES Users(UserID)
    )
    PRINT '✅ تم إنشاء جدول سجل العمليات'
END

-- ===================================================================
-- 10. إنشاء الفهارس لتحسين الأداء - Create Indexes for Performance
-- ===================================================================

PRINT '📊 إنشاء الفهارس لتحسين الأداء...'

-- فهارس الأدوية
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Drugs_DrugName')
    CREATE INDEX IX_Drugs_DrugName ON Drugs(DrugName)

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Drugs_Barcode')
    CREATE INDEX IX_Drugs_Barcode ON Drugs(Barcode)

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Drugs_CategoryID')
    CREATE INDEX IX_Drugs_CategoryID ON Drugs(CategoryID)

-- فهارس المخزون
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Inventory_DrugID')
    CREATE INDEX IX_Inventory_DrugID ON Inventory(DrugID)

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Inventory_WarehouseID')
    CREATE INDEX IX_Inventory_WarehouseID ON Inventory(WarehouseID)

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Inventory_ExpiryDate')
    CREATE INDEX IX_Inventory_ExpiryDate ON Inventory(ExpiryDate)

-- فهارس الفواتير
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_SalesInvoices_InvoiceDate')
    CREATE INDEX IX_SalesInvoices_InvoiceDate ON SalesInvoices(InvoiceDate)

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_SalesInvoices_CustomerID')
    CREATE INDEX IX_SalesInvoices_CustomerID ON SalesInvoices(CustomerID)

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_PurchaseInvoices_InvoiceDate')
    CREATE INDEX IX_PurchaseInvoices_InvoiceDate ON PurchaseInvoices(InvoiceDate)

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_PurchaseInvoices_SupplierID')
    CREATE INDEX IX_PurchaseInvoices_SupplierID ON PurchaseInvoices(SupplierID)

-- فهارس حركات المخزون
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_StockMovements_MovementDate')
    CREATE INDEX IX_StockMovements_MovementDate ON StockMovements(MovementDate)

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_StockMovements_DrugID')
    CREATE INDEX IX_StockMovements_DrugID ON StockMovements(DrugID)

-- فهارس العملاء والموردين
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Customers_CustomerName')
    CREATE INDEX IX_Customers_CustomerName ON Customers(CustomerName)

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Suppliers_SupplierName')
    CREATE INDEX IX_Suppliers_SupplierName ON Suppliers(SupplierName)

PRINT '✅ تم إنشاء جميع الفهارس'

-- ===================================================================
-- 11. إدراج البيانات التجريبية الشاملة - Insert Comprehensive Test Data
-- ===================================================================

PRINT '📊 إدراج البيانات التجريبية...'

-- إدراج العملات
IF NOT EXISTS (SELECT * FROM Currencies WHERE CurrencyCode = 'YER')
BEGIN
    INSERT INTO Currencies (CurrencyCode, CurrencyName, CurrencySymbol, ExchangeRate, IsBaseCurrency) VALUES
    ('YER', 'ريال يمني', 'ر.ي', 1.0, 1),
    ('USD', 'دولار أمريكي', '$', 530.0, 0),
    ('SAR', 'ريال سعودي', 'ر.س', 141.0, 0),
    ('EUR', 'يورو', '€', 580.0, 0)
    PRINT '✅ تم إدراج العملات'
END

-- إدراج السنة المالية
IF NOT EXISTS (SELECT * FROM FiscalYears WHERE YearName = '2024')
BEGIN
    INSERT INTO FiscalYears (YearName, StartDate, EndDate, IsActive) VALUES
    ('2024', '2024-01-01', '2024-12-31', 1)
    PRINT '✅ تم إدراج السنة المالية'
END

-- إدراج المستخدمين
IF NOT EXISTS (SELECT * FROM Users WHERE Username = 'admin')
BEGIN
    INSERT INTO Users (Username, PasswordHash, FullName, Role) VALUES
    ('admin', '123456', 'مدير النظام', 'Admin'),
    ('manager', '123456', 'مدير الفرع', 'Manager'),
    ('pharmacist', '123456', 'صيدلي', 'Pharmacist'),
    ('cashier', '123456', 'أمين الصندوق', 'Cashier'),
    ('warehouse', '123456', 'أمين المخزن', 'Warehouse')
    PRINT '✅ تم إدراج المستخدمين'
END

-- إدراج الفروع
IF NOT EXISTS (SELECT * FROM Branches WHERE BranchCode = 'BR001')
BEGIN
    INSERT INTO Branches (BranchCode, BranchName, Address, Phone, Manager, City, Country) VALUES
    ('BR001', 'الفرع الرئيسي', 'شارع الزبيري - صنعاء', '+967-1-123456', 'أحمد محمد علي', 'صنعاء', 'اليمن'),
    ('BR002', 'فرع عدن', 'كريتر - عدن', '+967-2-789012', 'محمد أحمد سالم', 'عدن', 'اليمن'),
    ('BR003', 'فرع تعز', 'شارع جمال - تعز', '+967-4-345678', 'علي محمد حسن', 'تعز', 'اليمن')
    PRINT '✅ تم إدراج الفروع'
END

-- إدراج المخازن
IF NOT EXISTS (SELECT * FROM Warehouses WHERE WarehouseCode = 'WH001')
BEGIN
    INSERT INTO Warehouses (WarehouseCode, WarehouseName, BranchID, Location, ResponsiblePerson) VALUES
    ('WH001', 'المخزن الرئيسي', 1, 'الطابق الأرضي', 'سالم أحمد'),
    ('WH002', 'مخزن الأدوية المبردة', 1, 'الطابق الأول', 'فاطمة محمد'),
    ('WH003', 'مخزن عدن', 2, 'الطابق الأرضي', 'خالد سعيد'),
    ('WH004', 'مخزن تعز', 3, 'الطابق الأرضي', 'نادية علي')
    PRINT '✅ تم إدراج المخازن'
END

-- إدراج الشركات المصنعة
IF NOT EXISTS (SELECT * FROM Manufacturers WHERE ManufacturerName = 'شركة الدواء اليمنية')
BEGIN
    INSERT INTO Manufacturers (ManufacturerName, Country, ContactInfo, Phone) VALUES
    ('شركة الدواء اليمنية', 'اليمن', 'صنعاء - المنطقة الصناعية', '+967-1-111111'),
    ('مؤسسة الشفاء للأدوية', 'اليمن', 'عدن - كريتر', '+967-2-222222'),
    ('Pfizer', 'الولايات المتحدة', 'New York, USA', '******-733-2323'),
    ('Novartis', 'سويسرا', 'Basel, Switzerland', '+41-61-324-1111'),
    ('GSK', 'المملكة المتحدة', 'London, UK', '+44-20-8047-5000')
    PRINT '✅ تم إدراج الشركات المصنعة'
END

-- إدراج فئات الأدوية
IF NOT EXISTS (SELECT * FROM DrugCategories WHERE CategoryCode = 'CAT001')
BEGIN
    INSERT INTO DrugCategories (CategoryCode, CategoryName, Description) VALUES
    ('CAT001', 'مسكنات الألم', 'أدوية تسكين الألم والالتهابات'),
    ('CAT002', 'مضادات حيوية', 'أدوية مكافحة العدوى البكتيرية'),
    ('CAT003', 'فيتامينات ومكملات', 'الفيتامينات والمكملات الغذائية'),
    ('CAT004', 'أدوية القلب والأوعية', 'أدوية علاج أمراض القلب والدورة الدموية'),
    ('CAT005', 'أدوية الجهاز الهضمي', 'أدوية علاج مشاكل الجهاز الهضمي'),
    ('CAT006', 'أدوية الجهاز التنفسي', 'أدوية علاج أمراض الجهاز التنفسي'),
    ('CAT007', 'أدوية السكري', 'أدوية علاج مرض السكري'),
    ('CAT008', 'أدوية الأطفال', 'أدوية مخصصة للأطفال'),
    ('CAT009', 'مستحضرات التجميل', 'كريمات ومستحضرات العناية'),
    ('CAT010', 'أدوية نسائية', 'أدوية خاصة بالنساء')
    PRINT '✅ تم إدراج فئات الأدوية'
END

-- إدراج الأدوية الشاملة - مجموعة 1 (مسكنات الألم)
IF NOT EXISTS (SELECT * FROM Drugs WHERE DrugCode = 'DRG001')
BEGIN
    INSERT INTO Drugs (DrugCode, DrugName, ScientificName, Barcode, CategoryID, ManufacturerID, Unit, Strength, Form, PurchasePrice, SalePrice, WholesalePrice, MinStock, ReorderLevel) VALUES
    ('DRG001', 'باراسيتامول 500 مجم', 'Paracetamol', '6221001000001', 1, 1, 'قرص', '500mg', 'قرص', 0.50, 1.00, 0.80, 100, 50),
    ('DRG002', 'أسبرين 100 مجم', 'Aspirin', '6221001000002', 1, 1, 'قرص', '100mg', 'قرص', 0.30, 0.60, 0.45, 200, 100),
    ('DRG003', 'إيبوبروفين 400 مجم', 'Ibuprofen', '6221001000003', 1, 2, 'قرص', '400mg', 'قرص', 1.20, 2.50, 2.00, 150, 75),
    ('DRG004', 'ديكلوفيناك 50 مجم', 'Diclofenac', '6221001000004', 1, 3, 'قرص', '50mg', 'قرص', 1.50, 3.00, 2.40, 100, 50),
    ('DRG005', 'كيتوبروفين 100 مجم', 'Ketoprofen', '6221001000005', 1, 4, 'قرص', '100mg', 'قرص', 2.00, 4.00, 3.20, 80, 40)
    PRINT '✅ تم إدراج مسكنات الألم'
END

-- إدراج مضادات حيوية
IF NOT EXISTS (SELECT * FROM Drugs WHERE DrugCode = 'DRG006')
BEGIN
    INSERT INTO Drugs (DrugCode, DrugName, ScientificName, Barcode, CategoryID, ManufacturerID, Unit, Strength, Form, PurchasePrice, SalePrice, WholesalePrice, MinStock, ReorderLevel) VALUES
    ('DRG006', 'أموكسيسيلين 500 مجم', 'Amoxicillin', '6221002000001', 2, 3, 'كبسولة', '500mg', 'كبسولة', 2.00, 4.00, 3.20, 80, 40),
    ('DRG007', 'أزيثرومايسين 250 مجم', 'Azithromycin', '6221002000002', 2, 4, 'كبسولة', '250mg', 'كبسولة', 5.00, 10.00, 8.00, 50, 25),
    ('DRG008', 'سيفالكسين 500 مجم', 'Cephalexin', '6221002000003', 2, 5, 'كبسولة', '500mg', 'كبسولة', 3.50, 7.00, 5.60, 60, 30),
    ('DRG009', 'كلاريثرومايسين 500 مجم', 'Clarithromycin', '6221002000004', 2, 3, 'قرص', '500mg', 'قرص', 6.00, 12.00, 9.60, 40, 20),
    ('DRG010', 'دوكسيسايكلين 100 مجم', 'Doxycycline', '6221002000005', 2, 4, 'كبسولة', '100mg', 'كبسولة', 1.50, 3.00, 2.40, 60, 30)
    PRINT '✅ تم إدراج مضادات حيوية'
END

-- إدراج فيتامينات ومكملات
IF NOT EXISTS (SELECT * FROM Drugs WHERE DrugCode = 'DRG011')
BEGIN
    INSERT INTO Drugs (DrugCode, DrugName, ScientificName, Barcode, CategoryID, ManufacturerID, Unit, Strength, Form, PurchasePrice, SalePrice, WholesalePrice, MinStock, ReorderLevel) VALUES
    ('DRG011', 'فيتامين سي 1000 مجم', 'Vitamin C', '6221003000001', 3, 1, 'قرص', '1000mg', 'قرص فوار', 1.00, 2.00, 1.60, 200, 100),
    ('DRG012', 'فيتامين د 5000 وحدة', 'Vitamin D3', '6221003000002', 3, 2, 'كبسولة', '5000IU', 'كبسولة', 2.50, 5.00, 4.00, 100, 50),
    ('DRG013', 'أوميجا 3', 'Omega 3', '6221003000003', 3, 4, 'كبسولة', '1000mg', 'كبسولة', 3.00, 6.00, 4.80, 80, 40),
    ('DRG014', 'فيتامين ب المركب', 'Vitamin B Complex', '6221003000004', 3, 1, 'قرص', 'متعدد', 'قرص', 1.20, 2.40, 1.92, 150, 75),
    ('DRG015', 'كالسيوم + مغنيسيوم', 'Calcium Magnesium', '6221003000005', 3, 2, 'قرص', '500mg', 'قرص', 1.80, 3.60, 2.88, 120, 60),
    ('DRG016', 'حديد + فوليك أسيد', 'Iron + Folic Acid', '6221003000006', 3, 3, 'قرص', '65mg', 'قرص', 1.00, 2.00, 1.60, 100, 50)
    PRINT '✅ تم إدراج فيتامينات ومكملات'
END

-- إدراج أدوية القلب والأوعية الدموية
IF NOT EXISTS (SELECT * FROM Drugs WHERE DrugCode = 'DRG017')
BEGIN
    INSERT INTO Drugs (DrugCode, DrugName, ScientificName, Barcode, CategoryID, ManufacturerID, Unit, Strength, Form, PurchasePrice, SalePrice, WholesalePrice, MinStock, ReorderLevel) VALUES
    ('DRG017', 'أتينولول 50 مجم', 'Atenolol', '6221004000001', 4, 3, 'قرص', '50mg', 'قرص', 1.80, 3.60, 2.88, 120, 60),
    ('DRG018', 'أملوديبين 5 مجم', 'Amlodipine', '6221004000002', 4, 4, 'قرص', '5mg', 'قرص', 2.20, 4.40, 3.52, 100, 50),
    ('DRG019', 'إنالابريل 10 مجم', 'Enalapril', '6221004000003', 4, 5, 'قرص', '10mg', 'قرص', 2.50, 5.00, 4.00, 80, 40),
    ('DRG020', 'فوروسيمايد 40 مجم', 'Furosemide', '6221004000004', 4, 3, 'قرص', '40mg', 'قرص', 1.20, 2.40, 1.92, 100, 50),
    ('DRG021', 'ديجوكسين 0.25 مجم', 'Digoxin', '6221004000005', 4, 4, 'قرص', '0.25mg', 'قرص', 3.00, 6.00, 4.80, 60, 30)
    PRINT '✅ تم إدراج أدوية القلب والأوعية الدموية'
END

-- إدراج أدوية الجهاز الهضمي
IF NOT EXISTS (SELECT * FROM Drugs WHERE DrugCode = 'DRG022')
BEGIN
    INSERT INTO Drugs (DrugCode, DrugName, ScientificName, Barcode, CategoryID, ManufacturerID, Unit, Strength, Form, PurchasePrice, SalePrice, WholesalePrice, MinStock, ReorderLevel) VALUES
    ('DRG022', 'أوميبرازول 20 مجم', 'Omeprazole', '6221005000001', 5, 1, 'كبسولة', '20mg', 'كبسولة', 2.00, 4.00, 3.20, 100, 50),
    ('DRG023', 'رانيتيدين 150 مجم', 'Ranitidine', '6221005000002', 5, 2, 'قرص', '150mg', 'قرص', 1.50, 3.00, 2.40, 120, 60),
    ('DRG024', 'لوبيراميد 2 مجم', 'Loperamide', '6221005000003', 5, 3, 'كبسولة', '2mg', 'كبسولة', 1.00, 2.00, 1.60, 80, 40),
    ('DRG025', 'ميتوكلوبراميد 10 مجم', 'Metoclopramide', '6221005000004', 5, 4, 'قرص', '10mg', 'قرص', 0.80, 1.60, 1.28, 150, 75),
    ('DRG026', 'سيميثيكون 40 مجم', 'Simethicone', '6221005000005', 5, 5, 'قرص', '40mg', 'قرص مضغ', 0.60, 1.20, 0.96, 200, 100)
    PRINT '✅ تم إدراج أدوية الجهاز الهضمي'
END

-- إدراج أدوية الجهاز التنفسي
IF NOT EXISTS (SELECT * FROM Drugs WHERE DrugCode = 'DRG027')
BEGIN
    INSERT INTO Drugs (DrugCode, DrugName, ScientificName, Barcode, CategoryID, ManufacturerID, Unit, Strength, Form, PurchasePrice, SalePrice, WholesalePrice, MinStock, ReorderLevel) VALUES
    ('DRG027', 'سالبوتامول 4 مجم', 'Salbutamol', '6221006000001', 6, 1, 'قرص', '4mg', 'قرص', 1.20, 2.40, 1.92, 100, 50),
    ('DRG028', 'مونتيلوكاست 10 مجم', 'Montelukast', '6221006000002', 6, 2, 'قرص', '10mg', 'قرص', 8.00, 16.00, 12.80, 40, 20),
    ('DRG029', 'ديكستروميثورفان شراب', 'Dextromethorphan', '6221006000003', 6, 3, 'زجاجة', '15mg/5ml', 'شراب', 12.00, 24.00, 19.20, 30, 15),
    ('DRG030', 'أمبروكسول 30 مجم', 'Ambroxol', '6221006000004', 6, 4, 'قرص', '30mg', 'قرص', 1.50, 3.00, 2.40, 80, 40),
    ('DRG031', 'لوراتادين 10 مجم', 'Loratadine', '6221006000005', 6, 5, 'قرص', '10mg', 'قرص', 2.00, 4.00, 3.20, 60, 30)
    PRINT '✅ تم إدراج أدوية الجهاز التنفسي'
END

-- إدراج أدوية السكري
IF NOT EXISTS (SELECT * FROM Drugs WHERE DrugCode = 'DRG032')
BEGIN
    INSERT INTO Drugs (DrugCode, DrugName, ScientificName, Barcode, CategoryID, ManufacturerID, Unit, Strength, Form, PurchasePrice, SalePrice, WholesalePrice, MinStock, ReorderLevel) VALUES
    ('DRG032', 'ميتفورمين 500 مجم', 'Metformin', '6221007000001', 7, 5, 'قرص', '500mg', 'قرص', 1.50, 3.00, 2.40, 150, 75),
    ('DRG033', 'جليبنكلاميد 5 مجم', 'Glibenclamide', '6221007000002', 7, 3, 'قرص', '5mg', 'قرص', 1.20, 2.40, 1.92, 100, 50),
    ('DRG034', 'جليكلازيد 80 مجم', 'Gliclazide', '6221007000003', 7, 4, 'قرص', '80mg', 'قرص', 2.50, 5.00, 4.00, 80, 40),
    ('DRG035', 'إنسولين طويل المفعول', 'Insulin Glargine', '6221007000004', 7, 1, 'قلم', '100IU/ml', 'حقن', 45.00, 90.00, 72.00, 20, 10),
    ('DRG036', 'إنسولين سريع المفعول', 'Insulin Aspart', '6221007000005', 7, 2, 'قلم', '100IU/ml', 'حقن', 40.00, 80.00, 64.00, 25, 12)
    PRINT '✅ تم إدراج أدوية السكري'
END

-- إدراج أدوية الأطفال
IF NOT EXISTS (SELECT * FROM Drugs WHERE DrugCode = 'DRG037')
BEGIN
    INSERT INTO Drugs (DrugCode, DrugName, ScientificName, Barcode, CategoryID, ManufacturerID, Unit, Strength, Form, PurchasePrice, SalePrice, WholesalePrice, MinStock, ReorderLevel) VALUES
    ('DRG037', 'باراسيتامول شراب للأطفال', 'Paracetamol Syrup', '6221008000001', 8, 1, 'زجاجة', '120mg/5ml', 'شراب', 8.00, 16.00, 12.80, 50, 25),
    ('DRG038', 'إيبوبروفين شراب للأطفال', 'Ibuprofen Syrup', '6221008000002', 8, 2, 'زجاجة', '100mg/5ml', 'شراب', 10.00, 20.00, 16.00, 40, 20),
    ('DRG039', 'أموكسيسيلين شراب للأطفال', 'Amoxicillin Syrup', '6221008000003', 8, 3, 'زجاجة', '125mg/5ml', 'شراب', 15.00, 30.00, 24.00, 30, 15),
    ('DRG040', 'فيتامين د نقط للأطفال', 'Vitamin D3 Drops', '6221008000004', 8, 4, 'زجاجة', '400IU/ml', 'نقط', 18.00, 36.00, 28.80, 25, 12),
    ('DRG041', 'محلول الجفاف للأطفال', 'ORS Solution', '6221008000005', 8, 5, 'كيس', '20.5g', 'مسحوق', 2.00, 4.00, 3.20, 100, 50)
    PRINT '✅ تم إدراج أدوية الأطفال'
END

-- إدراج مستحضرات التجميل والعناية
IF NOT EXISTS (SELECT * FROM Drugs WHERE DrugCode = 'DRG042')
BEGIN
    INSERT INTO Drugs (DrugCode, DrugName, ScientificName, Barcode, CategoryID, ManufacturerID, Unit, Strength, Form, PurchasePrice, SalePrice, WholesalePrice, MinStock, ReorderLevel) VALUES
    ('DRG042', 'كريم مرطب للبشرة', 'Moisturizing Cream', '6221009000001', 9, 1, 'أنبوب', '50g', 'كريم', 15.00, 30.00, 24.00, 60, 30),
    ('DRG043', 'واقي الشمس SPF 50', 'Sunscreen SPF 50', '6221009000002', 9, 2, 'أنبوب', '100ml', 'كريم', 25.00, 50.00, 40.00, 40, 20),
    ('DRG044', 'شامبو طبي للقشرة', 'Anti-Dandruff Shampoo', '6221009000003', 9, 3, 'زجاجة', '200ml', 'شامبو', 20.00, 40.00, 32.00, 30, 15),
    ('DRG045', 'كريم مضاد للفطريات', 'Antifungal Cream', '6221009000004', 9, 4, 'أنبوب', '30g', 'كريم', 12.00, 24.00, 19.20, 50, 25),
    ('DRG046', 'مرهم للجروح والحروق', 'Wound Healing Ointment', '6221009000005', 9, 5, 'أنبوب', '20g', 'مرهم', 18.00, 36.00, 28.80, 40, 20)
    PRINT '✅ تم إدراج مستحضرات التجميل والعناية'
END

-- إدراج أدوية نسائية
IF NOT EXISTS (SELECT * FROM Drugs WHERE DrugCode = 'DRG047')
BEGIN
    INSERT INTO Drugs (DrugCode, DrugName, ScientificName, Barcode, CategoryID, ManufacturerID, Unit, Strength, Form, PurchasePrice, SalePrice, WholesalePrice, MinStock, ReorderLevel) VALUES
    ('DRG047', 'حمض الفوليك 5 مجم', 'Folic Acid', '6221010000001', 10, 1, 'قرص', '5mg', 'قرص', 0.80, 1.60, 1.28, 200, 100),
    ('DRG048', 'حديد + فيتامين سي للحوامل', 'Iron + Vitamin C', '6221010000002', 10, 2, 'قرص', '65mg', 'قرص', 1.50, 3.00, 2.40, 150, 75),
    ('DRG049', 'كالسيوم للحوامل', 'Calcium Carbonate', '6221010000003', 10, 3, 'قرص', '600mg', 'قرص', 2.00, 4.00, 3.20, 100, 50),
    ('DRG050', 'فيتامينات ما قبل الولادة', 'Prenatal Vitamins', '6221010000004', 10, 4, 'قرص', 'متعدد', 'قرص', 3.50, 7.00, 5.60, 80, 40),
    ('DRG051', 'كريم مهبلي مضاد للفطريات', 'Antifungal Vaginal Cream', '6221010000005', 10, 5, 'أنبوب', '30g', 'كريم', 22.00, 44.00, 35.20, 30, 15)
    PRINT '✅ تم إدراج أدوية نسائية'
END

-- إدراج العملاء
IF NOT EXISTS (SELECT * FROM Customers WHERE CustomerCode = 'CUST001')
BEGIN
    INSERT INTO Customers (CustomerCode, CustomerName, Phone, Mobile, Address, City, CustomerType, CreditLimit) VALUES
    ('CUST001', 'أحمد محمد علي', '01-123456', '777123456', 'شارع الزبيري - صنعاء', 'صنعاء', 'عادي', 0),
    ('CUST002', 'فاطمة أحمد سالم', '01-234567', '733987654', 'شارع الستين - صنعاء', 'صنعاء', 'VIP', 50000),
    ('CUST003', 'محمد عبدالله حسن', '01-345678', '770555444', 'شارع الثورة - صنعاء', 'صنعاء', 'عادي', 0),
    ('CUST004', 'مريم سالم أحمد', '02-456789', '734111222', 'كريتر - عدن', 'عدن', 'عادي', 0),
    ('CUST005', 'صيدلية الشفاء', '01-567890', '777333444', 'شارع الحصبة - صنعاء', 'صنعاء', 'جملة', 200000),
    ('CUST006', 'مستشفى الثورة', '01-678901', '733555666', 'شارع الثورة - صنعاء', 'صنعاء', 'مؤسسة', 500000),
    ('CUST007', 'علي محمد قاسم', '04-789012', '770777888', 'شارع جمال - تعز', 'تعز', 'عادي', 0),
    ('CUST008', 'نادية أحمد محمد', '01-890123', '734999000', 'شارع الرقاص - صنعاء', 'صنعاء', 'VIP', 30000)
    PRINT '✅ تم إدراج العملاء'
END

-- إدراج الموردين
IF NOT EXISTS (SELECT * FROM Suppliers WHERE SupplierCode = 'SUP001')
BEGIN
    INSERT INTO Suppliers (SupplierCode, SupplierName, ContactPerson, Phone, Email, Address, City, Country, PaymentTerms, CreditLimit) VALUES
    ('SUP001', 'شركة الدواء اليمنية', 'علي أحمد محمد', '01-111111', '<EMAIL>', 'المنطقة الصناعية - صنعاء', 'صنعاء', 'اليمن', '30 يوم', 1000000),
    ('SUP002', 'مؤسسة الشفاء للأدوية', 'محمد سالم حسن', '02-222222', '<EMAIL>', 'كريتر - عدن', 'عدن', 'اليمن', '45 يوم', 800000),
    ('SUP003', 'شركة فايزر الشرق الأوسط', 'أحمد خالد', '01-333333', '<EMAIL>', 'دبي - الإمارات', 'دبي', 'الإمارات', '60 يوم', 2000000),
    ('SUP004', 'نوفارتيس الخليج', 'سارة محمد', '01-444444', '<EMAIL>', 'الرياض - السعودية', 'الرياض', 'السعودية', '30 يوم', 1500000),
    ('SUP005', 'جلاكسو سميث كلاين', 'محمد علي', '01-555555', '<EMAIL>', 'القاهرة - مصر', 'القاهرة', 'مصر', '45 يوم', 1200000)
    PRINT '✅ تم إدراج الموردين'
END

-- إدراج المخزون التجريبي الشامل
IF NOT EXISTS (SELECT * FROM Inventory WHERE DrugID = 1)
BEGIN
    -- المخزن الرئيسي - مسكنات الألم
    INSERT INTO Inventory (DrugID, WarehouseID, BatchNumber, Quantity, ExpiryDate, PurchasePrice, SalePrice, SupplierID) VALUES
    (1, 1, 'B001-2024', 500, '2025-12-31', 0.50, 1.00, 1),
    (2, 1, 'B002-2024', 300, '2025-06-30', 0.30, 0.60, 1),
    (3, 1, 'B003-2024', 200, '2024-12-31', 1.20, 2.50, 2),
    (4, 1, 'B004-2024', 150, '2025-08-15', 1.50, 3.00, 3),
    (5, 1, 'B005-2024', 120, '2025-10-20', 2.00, 4.00, 4)
    PRINT '✅ تم إدراج مخزون مسكنات الألم'
END

-- إدراج مخزون مضادات حيوية
IF NOT EXISTS (SELECT * FROM Inventory WHERE DrugID = 6)
BEGIN
    INSERT INTO Inventory (DrugID, WarehouseID, BatchNumber, Quantity, ExpiryDate, PurchasePrice, SalePrice, SupplierID) VALUES
    (6, 1, 'B006-2024', 80, '2025-03-31', 2.00, 4.00, 3),
    (7, 1, 'B007-2024', 60, '2025-07-20', 5.00, 10.00, 4),
    (8, 1, 'B008-2024', 90, '2025-05-15', 3.50, 7.00, 5),
    (9, 1, 'B009-2024', 50, '2025-09-30', 6.00, 12.00, 3),
    (10, 1, 'B010-2024', 70, '2025-11-25', 1.50, 3.00, 4)
    PRINT '✅ تم إدراج مخزون مضادات حيوية'
END

-- إدراج مخزون فيتامينات ومكملات
IF NOT EXISTS (SELECT * FROM Inventory WHERE DrugID = 11)
BEGIN
    INSERT INTO Inventory (DrugID, WarehouseID, BatchNumber, Quantity, ExpiryDate, PurchasePrice, SalePrice, SupplierID) VALUES
    (11, 1, 'B011-2024', 250, '2026-01-15', 1.00, 2.00, 1),
    (12, 1, 'B012-2024', 180, '2025-09-30', 2.50, 5.00, 2),
    (13, 1, 'B013-2024', 120, '2025-05-25', 3.00, 6.00, 4),
    (14, 1, 'B014-2024', 200, '2025-12-10', 1.20, 2.40, 1),
    (15, 1, 'B015-2024', 150, '2025-08-20', 1.80, 3.60, 2),
    (16, 1, 'B016-2024', 130, '2025-07-15', 1.00, 2.00, 3)
    PRINT '✅ تم إدراج مخزون فيتامينات ومكملات'
END

-- إدراج مخزون أدوية القلب والأوعية الدموية
IF NOT EXISTS (SELECT * FROM Inventory WHERE DrugID = 17)
BEGIN
    INSERT INTO Inventory (DrugID, WarehouseID, BatchNumber, Quantity, ExpiryDate, PurchasePrice, SalePrice, SupplierID) VALUES
    (17, 2, 'B017-2024', 100, '2025-04-10', 1.80, 3.60, 3),
    (18, 2, 'B018-2024', 80, '2025-06-15', 2.20, 4.40, 4),
    (19, 2, 'B019-2024', 90, '2025-08-20', 2.50, 5.00, 5),
    (20, 2, 'B020-2024', 110, '2025-10-30', 1.20, 2.40, 3),
    (21, 2, 'B021-2024', 70, '2025-12-25', 3.00, 6.00, 4)
    PRINT '✅ تم إدراج مخزون أدوية القلب والأوعية الدموية'
END

-- إدراج مخزون أدوية الجهاز الهضمي
IF NOT EXISTS (SELECT * FROM Inventory WHERE DrugID = 22)
BEGIN
    INSERT INTO Inventory (DrugID, WarehouseID, BatchNumber, Quantity, ExpiryDate, PurchasePrice, SalePrice, SupplierID) VALUES
    (22, 1, 'B022-2024', 120, '2025-11-15', 2.00, 4.00, 1),
    (23, 1, 'B023-2024', 140, '2025-09-20', 1.50, 3.00, 2),
    (24, 1, 'B024-2024', 100, '2025-07-30', 1.00, 2.00, 3),
    (25, 1, 'B025-2024', 160, '2025-12-05', 0.80, 1.60, 4),
    (26, 1, 'B026-2024', 220, '2026-02-10', 0.60, 1.20, 5)
    PRINT '✅ تم إدراج مخزون أدوية الجهاز الهضمي'
END

-- إدراج مخزون أدوية الجهاز التنفسي
IF NOT EXISTS (SELECT * FROM Inventory WHERE DrugID = 27)
BEGIN
    INSERT INTO Inventory (DrugID, WarehouseID, BatchNumber, Quantity, ExpiryDate, PurchasePrice, SalePrice, SupplierID) VALUES
    (27, 1, 'B027-2024', 110, '2025-08-25', 1.20, 2.40, 1),
    (28, 1, 'B028-2024', 50, '2025-10-15', 8.00, 16.00, 2),
    (29, 1, 'B029-2024', 40, '2025-06-30', 12.00, 24.00, 3),
    (30, 1, 'B030-2024', 90, '2025-09-10', 1.50, 3.00, 4),
    (31, 1, 'B031-2024', 70, '2025-11-20', 2.00, 4.00, 5)
    PRINT '✅ تم إدراج مخزون أدوية الجهاز التنفسي'
END

-- إدراج مخزون أدوية السكري
IF NOT EXISTS (SELECT * FROM Inventory WHERE DrugID = 32)
BEGIN
    INSERT INTO Inventory (DrugID, WarehouseID, BatchNumber, Quantity, ExpiryDate, PurchasePrice, SalePrice, SupplierID) VALUES
    (32, 2, 'B032-2024', 180, '2025-12-15', 1.50, 3.00, 5),
    (33, 2, 'B033-2024', 120, '2025-08-30', 1.20, 2.40, 3),
    (34, 2, 'B034-2024', 90, '2025-10-25', 2.50, 5.00, 4),
    (35, 2, 'B035-2024', 25, '2025-07-20', 45.00, 90.00, 1),
    (36, 2, 'B036-2024', 30, '2025-09-15', 40.00, 80.00, 2)
    PRINT '✅ تم إدراج مخزون أدوية السكري'
END

-- إدراج مخزون أدوية الأطفال
IF NOT EXISTS (SELECT * FROM Inventory WHERE DrugID = 37)
BEGIN
    INSERT INTO Inventory (DrugID, WarehouseID, BatchNumber, Quantity, ExpiryDate, PurchasePrice, SalePrice, SupplierID) VALUES
    (37, 1, 'B037-2024', 60, '2025-12-25', 8.00, 16.00, 1),
    (38, 1, 'B038-2024', 50, '2025-11-30', 10.00, 20.00, 2),
    (39, 1, 'B039-2024', 40, '2025-08-15', 15.00, 30.00, 3),
    (40, 1, 'B040-2024', 35, '2025-10-20', 18.00, 36.00, 4),
    (41, 1, 'B041-2024', 120, '2026-01-10', 2.00, 4.00, 5)
    PRINT '✅ تم إدراج مخزون أدوية الأطفال'
END

-- إدراج مخزون مستحضرات التجميل والعناية
IF NOT EXISTS (SELECT * FROM Inventory WHERE DrugID = 42)
BEGIN
    INSERT INTO Inventory (DrugID, WarehouseID, BatchNumber, Quantity, ExpiryDate, PurchasePrice, SalePrice, SupplierID) VALUES
    (42, 1, 'B042-2024', 70, '2025-09-30', 15.00, 30.00, 1),
    (43, 1, 'B043-2024', 50, '2025-08-25', 25.00, 50.00, 2),
    (44, 1, 'B044-2024', 40, '2025-07-15', 20.00, 40.00, 3),
    (45, 1, 'B045-2024', 60, '2025-11-10', 12.00, 24.00, 4),
    (46, 1, 'B046-2024', 50, '2025-10-05', 18.00, 36.00, 5)
    PRINT '✅ تم إدراج مخزون مستحضرات التجميل والعناية'
END

-- إدراج مخزون أدوية نسائية
IF NOT EXISTS (SELECT * FROM Inventory WHERE DrugID = 47)
BEGIN
    INSERT INTO Inventory (DrugID, WarehouseID, BatchNumber, Quantity, ExpiryDate, PurchasePrice, SalePrice, SupplierID) VALUES
    (47, 1, 'B047-2024', 250, '2026-03-15', 0.80, 1.60, 1),
    (48, 1, 'B048-2024', 180, '2025-12-20', 1.50, 3.00, 2),
    (49, 1, 'B049-2024', 130, '2025-11-25', 2.00, 4.00, 3),
    (50, 1, 'B050-2024', 100, '2025-10-30', 3.50, 7.00, 4),
    (51, 1, 'B051-2024', 40, '2025-09-15', 22.00, 44.00, 5)
    PRINT '✅ تم إدراج مخزون أدوية نسائية'
END

PRINT ''
PRINT '📊 تم إدراج مخزون شامل لجميع الأدوية!'
PRINT '📊 Comprehensive inventory for all drugs added!'
PRINT '💊 إجمالي الأدوية: 51 دواء'
PRINT '📦 إجمالي عناصر المخزون: 51 عنصر'

-- إدراج إعدادات النظام
IF NOT EXISTS (SELECT * FROM SystemSettings WHERE SettingKey = 'CompanyName')
BEGIN
    INSERT INTO SystemSettings (SettingKey, SettingValue, SettingType, Description, Category) VALUES
    ('CompanyName', 'صيدليات الشفاء', 'String', 'اسم الشركة', 'عام'),
    ('TaxRate', '15', 'Number', 'معدل الضريبة %', 'مالي'),
    ('Currency', 'YER', 'String', 'العملة الأساسية', 'مالي'),
    ('LowStockAlert', '10', 'Number', 'تنبيه المخزون المنخفض', 'مخزون'),
    ('ExpiryAlertDays', '90', 'Number', 'تنبيه انتهاء الصلاحية (أيام)', 'مخزون'),
    ('BackupEnabled', 'true', 'Boolean', 'تفعيل النسخ الاحتياطي', 'نظام'),
    ('AutoBackupHour', '2', 'Number', 'ساعة النسخ الاحتياطي التلقائي', 'نظام'),
    ('InvoicePrefix', 'INV', 'String', 'بادئة رقم الفاتورة', 'مبيعات'),
    ('ReceiptFooter', 'شكراً لزيارتكم - نتمنى لكم الشفاء العاجل', 'String', 'تذييل الفاتورة', 'مبيعات'),
    ('AllowNegativeStock', 'false', 'Boolean', 'السماح بالمخزون السالب', 'مخزون')
    PRINT '✅ تم إدراج إعدادات النظام'
END

-- إنشاء تنبيهات تجريبية
IF NOT EXISTS (SELECT * FROM SystemAlerts WHERE AlertType = 'مخزون_منخفض')
BEGIN
    INSERT INTO SystemAlerts (AlertType, Title, Message, Severity, RelatedTable, RelatedID) VALUES
    ('مخزون_منخفض', 'مخزون منخفض - باراسيتامول', 'كمية باراسيتامول 500 مجم أقل من الحد الأدنى', 'متوسط', 'Drugs', 1),
    ('انتهاء_صلاحية', 'انتهاء صلاحية قريب', 'أموكسيسيلين 500 مجم ينتهي خلال 30 يوم', 'عالي', 'Inventory', 5),
    ('مخزون_منخفض', 'مخزون منخفض - أوميجا 3', 'كمية أوميجا 3 أقل من الحد الأدنى', 'متوسط', 'Drugs', 10),
    ('انتهاء_صلاحية', 'انتهاء صلاحية قريب', 'إيبوبروفين 400 مجم ينتهي خلال 60 يوم', 'متوسط', 'Inventory', 3)
    PRINT '✅ تم إدراج التنبيهات التجريبية'
END

PRINT ''
PRINT '🎉 تم إنشاء قاعدة البيانات الشاملة بنجاح!'
PRINT '🎉 Comprehensive database created successfully!'
PRINT ''
PRINT '📊 إحصائيات قاعدة البيانات الشاملة:'
PRINT '📊 Comprehensive Database Statistics:'
PRINT '   📋 عدد الجداول: 25+ جدول'
PRINT '   👤 المستخدمين: 5 مستخدمين'
PRINT '   🏢 الفروع: 3 فروع'
PRINT '   🏪 المخازن: 4 مخازن'
PRINT '   🏭 الشركات المصنعة: 5 شركات'
PRINT '   📋 فئات الأدوية: 10 فئات'
PRINT '   💊 الأدوية: 51 دواء شامل'
PRINT '   👥 العملاء: 8 عملاء'
PRINT '   🏭 الموردين: 5 موردين'
PRINT '   📦 عناصر المخزون: 51 عنصر'
PRINT '   💰 العملات: 4 عملات'
PRINT '   ⚙️ إعدادات النظام: 10 إعدادات'
PRINT '   ⚠️ التنبيهات: 4 تنبيهات'
PRINT ''
PRINT '🔑 بيانات الدخول الافتراضية:'
PRINT '🔑 Default Login Credentials:'
PRINT '   👤 المدير: admin / 123456'
PRINT '   👤 مدير الفرع: manager / 123456'
PRINT '   👤 الصيدلي: pharmacist / 123456'
PRINT '   👤 أمين الصندوق: cashier / 123456'
PRINT '   👤 أمين المخزن: warehouse / 123456'
PRINT ''
PRINT '✅ قاعدة البيانات جاهزة للاستخدام!'
PRINT '✅ Database ready for use!'
