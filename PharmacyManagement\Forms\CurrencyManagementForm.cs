using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نافذة إدارة العملات - Currency Management Form
    /// </summary>
    public partial class CurrencyManagementForm : Form
    {
        #region Constructor - المنشئ

        public CurrencyManagementForm()
        {
            InitializeComponent();
            SetupForm();
            LoadData();
        }

        #endregion

        #region Form Setup - إعداد النافذة

        /// <summary>
        /// إعداد النافذة
        /// </summary>
        private void SetupForm()
        {
            // إعداد DataGridView
            SetupDataGridView();
            
            // إعداد التصميم المسطح
            ApplyFlatDesign();
        }

        /// <summary>
        /// إعداد DataGridView
        /// </summary>
        private void SetupDataGridView()
        {
            dgvCurrencies.AutoGenerateColumns = false;
            dgvCurrencies.AllowUserToAddRows = false;
            dgvCurrencies.AllowUserToDeleteRows = false;
            dgvCurrencies.ReadOnly = true;
            dgvCurrencies.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvCurrencies.MultiSelect = false;
            
            // تنسيق الألوان
            dgvCurrencies.BackgroundColor = Color.White;
            dgvCurrencies.GridColor = Color.FromArgb(189, 195, 199);
            dgvCurrencies.DefaultCellStyle.SelectionBackColor = Color.FromArgb(52, 152, 219);
            dgvCurrencies.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvCurrencies.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 73, 94);
            dgvCurrencies.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvCurrencies.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10, FontStyle.Bold);
        }

        /// <summary>
        /// تطبيق التصميم المسطح
        /// </summary>
        private void ApplyFlatDesign()
        {
            // إزالة حدود الأزرار
            foreach (Control control in this.Controls)
            {
                ApplyFlatStyleToControl(control);
            }
        }

        /// <summary>
        /// تطبيق التصميم المسطح على عنصر
        /// </summary>
        private void ApplyFlatStyleToControl(Control control)
        {
            if (control is Button button)
            {
                button.FlatAppearance.BorderSize = 0;
                button.Cursor = Cursors.Hand;
            }
            
            // تطبيق على العناصر الفرعية
            foreach (Control child in control.Controls)
            {
                ApplyFlatStyleToControl(child);
            }
        }

        #endregion

        #region Data Loading - تحميل البيانات

        /// <summary>
        /// تحميل بيانات العملات
        /// </summary>
        private void LoadData()
        {
            try
            {
                var currencies = Classes.CurrencyManager.GetAllCurrencies();
                dgvCurrencies.DataSource = currencies;

                // تحديث عدد السجلات
                lblRecordsCount.Text = "عدد العملات: " + currencies.Count.ToString();
                
                // تحديث الإحصائيات
                UpdateStatistics(currencies);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحديث الإحصائيات
        /// </summary>
        private void UpdateStatistics(System.Collections.Generic.List<Currency> currencies)
        {
            try
            {
                var baseCurrency = currencies.FirstOrDefault(c => c.IsBaseCurrency);
                var activeCurrencies = currencies.Where(c => c.IsActive).ToList();
                
                lblBaseCurrency.Text = baseCurrency != null ? 
                    $"العملة الأساسية: {baseCurrency.CurrencyName} ({baseCurrency.CurrencySymbol})" : 
                    "لا توجد عملة أساسية";
                
                lblActiveCurrencies.Text = $"العملات النشطة: {activeCurrencies.Count}";
                
                // تحديث آخر تحديث لأسعار الصرف
                lblLastUpdate.Text = $"آخر تحديث: {DateTime.Now:yyyy/MM/dd HH:mm}";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث الإحصائيات: {ex.Message}");
            }
        }

        #endregion

        #region Event Handlers - معالجات الأحداث

        /// <summary>
        /// تحديث أسعار الصرف
        /// </summary>
        private void btnUpdateRates_Click(object sender, EventArgs e)
        {
            try
            {
                var updateForm = new ExchangeRateUpdateForm();
                if (updateForm.ShowDialog() == DialogResult.OK)
                {
                    LoadData();
                    MessageBox.Show("تم تحديث أسعار الصرف بنجاح", "نجح", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث أسعار الصرف: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إضافة عملة جديدة
        /// </summary>
        private void btnAddCurrency_Click(object sender, EventArgs e)
        {
            try
            {
                var addForm = new CurrencyAddEditForm();
                if (addForm.ShowDialog() == DialogResult.OK)
                {
                    LoadData();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة العملة: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تعديل عملة
        /// </summary>
        private void btnEditCurrency_Click(object sender, EventArgs e)
        {
            if (dgvCurrencies.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار عملة للتعديل", "تنبيه", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                var selectedCurrency = dgvCurrencies.SelectedRows[0].DataBoundItem as Models.Currency;
                if (selectedCurrency != null)
                {
                    var editForm = new CurrencyAddEditForm(selectedCurrency);
                    if (editForm.ShowDialog() == DialogResult.OK)
                    {
                        LoadData();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعديل العملة: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تعطيل/تفعيل عملة
        /// </summary>
        private void btnToggleStatus_Click(object sender, EventArgs e)
        {
            if (dgvCurrencies.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار عملة", "تنبيه", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                var selectedCurrency = dgvCurrencies.SelectedRows[0].DataBoundItem as Currency;
                if (selectedCurrency != null)
                {
                    if (selectedCurrency.IsBaseCurrency)
                    {
                        MessageBox.Show("لا يمكن تعطيل العملة الأساسية", "تنبيه", 
                                      MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    string action = selectedCurrency.IsActive ? "تعطيل" : "تفعيل";
                    var result = MessageBox.Show($"هل تريد {action} هذه العملة؟", "تأكيد", 
                                               MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    
                    if (result == DialogResult.Yes)
                    {
                        bool newStatus = !selectedCurrency.IsActive;
                        if (Classes.CurrencyManager.ToggleCurrencyStatus(selectedCurrency.CurrencyCode, newStatus))
                        {
                            LoadData();
                            MessageBox.Show($"تم {action} العملة بنجاح", "نجح",
                                          MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            MessageBox.Show($"فشل في {action} العملة", "خطأ",
                                          MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تغيير حالة العملة: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحديث البيانات
        /// </summary>
        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadData();
        }

        /// <summary>
        /// تصدير البيانات
        /// </summary>
        private void btnExport_Click(object sender, EventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "Excel Files|*.xlsx|CSV Files|*.csv",
                    Title = "تصدير بيانات العملات",
                    FileName = $"Currencies_{DateTime.Now:yyyyMMdd}"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    var currencies = dgvCurrencies.DataSource as System.Collections.Generic.List<Currency>;
                    if (currencies != null)
                    {
                        ExportManager.ExportCurrencies(currencies, saveDialog.FileName);
                        MessageBox.Show("تم تصدير البيانات بنجاح", "نجح", 
                                      MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير البيانات: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// النقر المزدوج لتعديل
        /// </summary>
        private void dgvCurrencies_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                btnEditCurrency_Click(sender, e);
            }
        }

        /// <summary>
        /// تحديد صف في الجدول
        /// </summary>
        private void dgvCurrencies_SelectionChanged(object sender, EventArgs e)
        {
            bool hasSelection = dgvCurrencies.SelectedRows.Count > 0;
            btnEditCurrency.Enabled = hasSelection;
            btnToggleStatus.Enabled = hasSelection;
            
            if (hasSelection)
            {
                var selectedCurrency = dgvCurrencies.SelectedRows[0].DataBoundItem as Currency;
                if (selectedCurrency != null)
                {
                    btnToggleStatus.Text = selectedCurrency.IsActive ? "🔴 تعطيل" : "🟢 تفعيل";
                }
            }
        }

        #endregion
    }
}
