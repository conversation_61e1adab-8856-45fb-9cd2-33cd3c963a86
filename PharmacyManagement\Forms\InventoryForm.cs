using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using InventoryItem = PharmacyManagement.Models.InventoryItem;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نافذة إدارة المخزون - Inventory Management Form
    /// </summary>
    public partial class InventoryForm : Form
    {
        #region Constructor - المنشئ

        public InventoryForm()
        {
            InitializeComponent();
            SetupForm();
            LoadData();
        }

        #endregion

        #region Form Setup - إعداد النافذة

        /// <summary>
        /// إعداد النافذة
        /// </summary>
        private void SetupForm()
        {
            // إعداد DataGridView
            SetupDataGridView();
            
            // إعداد ComboBoxes
            LoadCategories();
            LoadStockStatus();
            
            // إعداد التصميم المسطح
            ApplyFlatDesign();
        }

        /// <summary>
        /// إعداد DataGridView
        /// </summary>
        private void SetupDataGridView()
        {
            dgvInventory.AutoGenerateColumns = false;
            dgvInventory.AllowUserToAddRows = false;
            dgvInventory.AllowUserToDeleteRows = false;
            dgvInventory.ReadOnly = true;
            dgvInventory.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvInventory.MultiSelect = false;
            
            // تنسيق الألوان
            dgvInventory.BackgroundColor = Color.White;
            dgvInventory.GridColor = Color.FromArgb(189, 195, 199);
            dgvInventory.DefaultCellStyle.SelectionBackColor = Color.FromArgb(52, 152, 219);
            dgvInventory.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvInventory.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 73, 94);
            dgvInventory.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvInventory.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10, FontStyle.Bold);
        }

        /// <summary>
        /// تحميل فئات الأدوية
        /// </summary>
        private void LoadCategories()
        {
            try
            {
                string query = "SELECT CategoryID, CategoryName FROM DrugCategories ORDER BY CategoryName";
                var dataTable = DatabaseHelper.ExecuteQuery(query);
                
                cmbCategory.DisplayMember = "CategoryName";
                cmbCategory.ValueMember = "CategoryID";
                cmbCategory.DataSource = dataTable;
                cmbCategory.SelectedIndex = -1;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الفئات: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل حالات المخزون
        /// </summary>
        private void LoadStockStatus()
        {
            var statusList = new[]
            {
                new { Value = "", Text = "جميع الحالات" },
                new { Value = "متوفر", Text = "متوفر" },
                new { Value = "منخفض", Text = "منخفض" },
                new { Value = "نفد", Text = "نفد" },
                new { Value = "زائد", Text = "زائد عن الحد" }
            };
            
            cmbStockStatus.DisplayMember = "Text";
            cmbStockStatus.ValueMember = "Value";
            cmbStockStatus.DataSource = statusList;
            cmbStockStatus.SelectedIndex = 0;
        }

        /// <summary>
        /// تطبيق التصميم المسطح
        /// </summary>
        private void ApplyFlatDesign()
        {
            // إزالة حدود الأزرار
            foreach (Control control in this.Controls)
            {
                ApplyFlatStyleToControl(control);
            }
        }

        /// <summary>
        /// تطبيق التصميم المسطح على عنصر
        /// </summary>
        private void ApplyFlatStyleToControl(Control control)
        {
            if (control is Button button)
            {
                button.FlatAppearance.BorderSize = 0;
                button.Cursor = Cursors.Hand;
            }
            
            // تطبيق على العناصر الفرعية
            foreach (Control child in control.Controls)
            {
                ApplyFlatStyleToControl(child);
            }
        }

        #endregion

        #region Data Loading - تحميل البيانات

        /// <summary>
        /// تحميل بيانات المخزون
        /// </summary>
        private void LoadData()
        {
            LoadInventoryData();
        }

        /// <summary>
        /// تحميل بيانات المخزون
        /// </summary>
        private void LoadInventoryData()
        {
            try
            {
                LogManager.LogInfo("بدء تحميل بيانات المخزون");

                var inventory = InventoryManager.GetAllInventoryItems();

                // تسجيل عينة من البيانات للتحقق
                if (inventory.Count > 0)
                {
                    var firstItem = inventory[0];
                    LogManager.LogInfo($"عينة من البيانات - الدواء: {firstItem.TradeName}, الفئة: {firstItem.CategoryName ?? "NULL"}, المصنع: {firstItem.ManufacturerName ?? "NULL"}");
                }

                dgvInventory.DataSource = inventory;

                // تنسيق الجدول
                FormatDataGridView();

                // تحديث عدد السجلات
                lblRecordsCount.Text = $"عدد السجلات: {inventory.Count}";

                // تحديث الإحصائيات
                UpdateStatistics(inventory);

                LogManager.LogInfo($"تم تحميل {inventory.Count} عنصر من المخزون");
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تحميل بيانات المخزون: {ex.Message}");
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تنسيق الجدول
        /// </summary>
        private void FormatDataGridView()
        {
            try
            {
                if (dgvInventory.Columns.Count == 0) return;

                // إخفاء الأعمدة غير المرغوب فيها
                var columnsToHide = new[] { "DrugID", "InventoryID", "WarehouseID", "IsActive",
                                          "CreatedDate", "UpdatedDate", "ReservedQuantity",
                                          "AvailableQuantity", "ManufacturingDate", "BatchNumber" };

                foreach (string columnName in columnsToHide)
                {
                    if (dgvInventory.Columns.Contains(columnName))
                    {
                        dgvInventory.Columns[columnName].Visible = false;
                    }
                }

                // تعيين عناوين الأعمدة بالعربية
                var columnHeaders = new Dictionary<string, string>
                {
                    { "DrugCode", "كود الدواء" },
                    { "TradeName", "اسم الدواء" },
                    { "DrugName", "اسم الدواء" },
                    { "Category", "الفئة" },
                    { "CategoryName", "الفئة" },
                    { "Manufacturer", "الشركة المصنعة" },
                    { "ManufacturerName", "الشركة المصنعة" },
                    { "Unit", "الوحدة" },
                    { "CurrentStock", "المخزون الحالي" },
                    { "MinStockLevel", "الحد الأدنى" },
                    { "MaxStockLevel", "الحد الأقصى" },
                    { "MinimumStock", "الحد الأدنى" },
                    { "MaximumStock", "الحد الأقصى" },
                    { "UnitPrice", "سعر الشراء" },
                    { "PurchasePrice", "سعر الشراء" },
                    { "SellingPrice", "سعر البيع" },
                    { "SalePrice", "سعر البيع" },
                    { "ExpiryDate", "تاريخ الانتهاء" },
                    { "StockStatus", "حالة المخزون" }
                };

                foreach (var header in columnHeaders)
                {
                    if (dgvInventory.Columns.Contains(header.Key))
                    {
                        dgvInventory.Columns[header.Key].HeaderText = header.Value;
                        dgvInventory.Columns[header.Key].Visible = true;
                    }
                }

                // تنسيق الأعمدة الرقمية
                if (dgvInventory.Columns.Contains("CurrentStock"))
                    dgvInventory.Columns["CurrentStock"].DefaultCellStyle.Format = "N0";

                if (dgvInventory.Columns.Contains("PurchasePrice"))
                    dgvInventory.Columns["PurchasePrice"].DefaultCellStyle.Format = "N2";

                if (dgvInventory.Columns.Contains("SalePrice"))
                    dgvInventory.Columns["SalePrice"].DefaultCellStyle.Format = "N2";

                if (dgvInventory.Columns.Contains("ExpiryDate"))
                    dgvInventory.Columns["ExpiryDate"].DefaultCellStyle.Format = "yyyy/MM/dd";

                // تعيين عرض الأعمدة
                dgvInventory.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;

                LogManager.LogInfo("تم تنسيق الجدول بنجاح");
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تنسيق الجدول: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث الإحصائيات
        /// </summary>
        private void UpdateStatistics(List<InventoryItem> inventory)
        {
            if (inventory == null || inventory.Count == 0)
            {
                lblTotalItems.Text = "إجمالي الأصناف: 0";
                lblLowStock.Text = "منخفض المخزون: 0";
                lblOutOfStock.Text = "نفد المخزون: 0";
                lblTotalValue.Text = "إجمالي القيمة: 0 ر.ي";
                return;
            }

            int totalItems = inventory.Count();
            int lowStock = inventory.Count(i => i.StockStatus == "منخفض");
            int outOfStock = inventory.Count(i => i.StockStatus == "نفد");
            decimal totalValue = inventory.Sum(i => i.CurrentStock * i.PurchasePrice);

            lblTotalItems.Text = $"إجمالي الأصناف: {totalItems}";
            lblLowStock.Text = $"منخفض المخزون: {lowStock}";
            lblOutOfStock.Text = $"نفد المخزون: {outOfStock}";
            lblTotalValue.Text = $"إجمالي القيمة: {totalValue:N2} ر.ي";
        }

        #endregion

        #region Event Handlers - معالجات الأحداث

        /// <summary>
        /// البحث في المخزون
        /// </summary>
        private void btnSearch_Click(object sender, EventArgs e)
        {
            try
            {
                string searchTerm = txtSearch.Text.Trim();
                int? categoryId = cmbCategory.SelectedValue as int?;
                string stockStatus = cmbStockStatus.SelectedValue?.ToString();

                // جلب جميع عناصر المخزون
                var allInventory = InventoryManager.GetAllInventoryItems();
                var filteredInventory = allInventory.AsEnumerable();

                // تطبيق فلتر البحث النصي
                if (!string.IsNullOrEmpty(searchTerm))
                {
                    filteredInventory = filteredInventory.Where(item =>
                        (item.DrugCode?.Contains(searchTerm) == true) ||
                        (item.TradeName?.Contains(searchTerm) == true) ||
                        (item.DrugName?.Contains(searchTerm) == true) ||
                        (item.CategoryName?.Contains(searchTerm) == true) ||
                        (item.ManufacturerName?.Contains(searchTerm) == true));
                }

                // تطبيق فلتر الفئة
                if (categoryId.HasValue && categoryId.Value > 0)
                {
                    filteredInventory = filteredInventory.Where(item =>
                        item.CategoryName != null &&
                        item.CategoryName.Contains(GetCategoryNameById(categoryId.Value)));
                }

                // تطبيق فلتر حالة المخزون
                if (!string.IsNullOrEmpty(stockStatus) && stockStatus != "الكل")
                {
                    filteredInventory = filteredInventory.Where(item =>
                    {
                        switch (stockStatus)
                        {
                            case "نفد المخزون":
                                return item.CurrentStock <= 0;
                            case "مخزون منخفض":
                                return item.CurrentStock > 0 && item.CurrentStock <= item.MinStockLevel;
                            case "مخزون طبيعي":
                                return item.CurrentStock > item.MinStockLevel && item.CurrentStock < item.MaxStockLevel;
                            case "مخزون مرتفع":
                                return item.CurrentStock >= item.MaxStockLevel;
                            default:
                                return true;
                        }
                    });
                }

                var result = filteredInventory.ToList();
                dgvInventory.DataSource = result;

                lblRecordsCount.Text = $"عدد السجلات: {result.Count}";
                UpdateStatistics(result);

                LogManager.LogInfo($"تم البحث في المخزون - النتائج: {result.Count} عنصر");
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في البحث في المخزون: {ex.Message}");
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// الحصول على اسم الفئة بالمعرف
        /// </summary>
        private string GetCategoryNameById(int categoryId)
        {
            try
            {
                string query = "SELECT CategoryName FROM DrugCategories WHERE CategoryID = @CategoryID";
                var parameters = new[] { DatabaseHelper.CreateParameter("@CategoryID", categoryId) };
                using (var reader = DatabaseHelper.ExecuteReader(query, parameters))
                {
                    if (reader.Read())
                    {
                        return reader["CategoryName"].ToString();
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في جلب اسم الفئة: {ex.Message}");
            }
            return "";
        }

        /// <summary>
        /// تحديث المخزون
        /// </summary>
        private void btnUpdateStock_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvInventory.SelectedRows.Count == 0)
                {
                    MessageBox.Show("يرجى اختيار صنف لتحديث المخزون", "تنبيه",
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var selectedItem = dgvInventory.SelectedRows[0].DataBoundItem as InventoryItem;
                if (selectedItem != null)
                {
                    LogManager.LogInfo($"بدء تحديث مخزون الدواء: {selectedItem.DrugCode} - {selectedItem.TradeName}");

                    var drug = DrugManager.GetDrugById(selectedItem.DrugID);
                    if (drug != null)
                    {
                        // استخدام النافذة المبسطة لتحديث المخزون
                        var updateForm = new StockUpdateForm(drug);
                        if (updateForm.ShowDialog() == DialogResult.OK)
                        {
                            // تحديث البيانات بعد التحديث
                            LoadInventoryData();
                            LogManager.LogInfo($"تم تحديث مخزون الدواء بنجاح: {selectedItem.DrugCode}");
                        }
                    }
                    else
                    {
                        MessageBox.Show("لم يتم العثور على بيانات الدواء", "خطأ",
                                      MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تحديث المخزون: {ex.Message}");
                MessageBox.Show($"خطأ في تحديث المخزون: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تقرير المخزون
        /// </summary>
        private void btnReport_Click(object sender, EventArgs e)
        {
            try
            {
                LogManager.LogInfo("بدء إنشاء تقرير المخزون");

                var inventory = dgvInventory.DataSource as List<InventoryItem>;
                if (inventory == null || inventory.Count == 0)
                {
                    MessageBox.Show("لا توجد بيانات لإنشاء التقرير", "تنبيه",
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // إنشاء تقرير سريع
                var reportDialog = new SaveFileDialog
                {
                    Filter = "HTML Files (*.html)|*.html",
                    Title = "حفظ تقرير المخزون",
                    FileName = $"تقرير_المخزون_التفصيلي_{DateTime.Now:yyyyMMdd_HHmmss}.html"
                };

                if (reportDialog.ShowDialog() == DialogResult.OK)
                {
                    GenerateInventoryReport(inventory, reportDialog.FileName);

                    MessageBox.Show("تم إنشاء التقرير بنجاح", "نجح",
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // فتح التقرير
                    if (MessageBox.Show("هل تريد فتح التقرير؟", "فتح التقرير",
                                      MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                    {
                        System.Diagnostics.Process.Start(reportDialog.FileName);
                    }

                    LogManager.LogInfo($"تم إنشاء تقرير المخزون: {reportDialog.FileName}");
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في إنشاء تقرير المخزون: {ex.Message}");
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إنشاء تقرير المخزون التفصيلي
        /// </summary>
        private void GenerateInventoryReport(List<InventoryItem> inventory, string filePath)
        {
            try
            {
                var html = new System.Text.StringBuilder();

                html.AppendLine("<!DOCTYPE html>");
                html.AppendLine("<html dir='rtl' lang='ar'>");
                html.AppendLine("<head>");
                html.AppendLine("<meta charset='UTF-8'>");
                html.AppendLine("<title>تقرير المخزون التفصيلي</title>");
                html.AppendLine("<style>");
                html.AppendLine("body { font-family: 'Segoe UI', Tahoma, Arial, sans-serif; margin: 20px; background-color: #f8f9fa; }");
                html.AppendLine(".container { max-width: 1200px; margin: 0 auto; background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }");
                html.AppendLine("h1 { color: #2c3e50; text-align: center; border-bottom: 3px solid #3498db; padding-bottom: 10px; }");
                html.AppendLine("h2 { color: #34495e; margin-top: 30px; }");
                html.AppendLine(".stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }");
                html.AppendLine(".stat-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px; border-radius: 8px; text-align: center; }");
                html.AppendLine(".stat-card h3 { margin: 0 0 10px 0; font-size: 16px; }");
                html.AppendLine(".stat-card p { margin: 0; font-size: 24px; font-weight: bold; }");
                html.AppendLine("table { width: 100%; border-collapse: collapse; margin-top: 20px; font-size: 14px; }");
                html.AppendLine("th, td { border: 1px solid #bdc3c7; padding: 12px 8px; text-align: right; }");
                html.AppendLine("th { background-color: #34495e; color: white; font-weight: bold; }");
                html.AppendLine("tr:nth-child(even) { background-color: #f8f9fa; }");
                html.AppendLine("tr:hover { background-color: #e8f4f8; }");
                html.AppendLine(".low-stock { background-color: #fff3cd !important; }");
                html.AppendLine(".out-of-stock { background-color: #f8d7da !important; }");
                html.AppendLine(".high-stock { background-color: #d1ecf1 !important; }");
                html.AppendLine(".footer { margin-top: 40px; padding-top: 20px; border-top: 1px solid #bdc3c7; color: #7f8c8d; font-size: 12px; text-align: center; }");
                html.AppendLine("</style>");
                html.AppendLine("</head>");
                html.AppendLine("<body>");
                html.AppendLine("<div class='container'>");

                // عنوان التقرير
                html.AppendLine("<h1>📦 تقرير المخزون التفصيلي</h1>");
                html.AppendLine($"<p style='text-align: center; color: #7f8c8d;'>تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm}</p>");

                // الإحصائيات
                var totalItems = inventory.Count;
                var outOfStock = inventory.Count(i => i.CurrentStock <= 0);
                var lowStock = inventory.Count(i => i.CurrentStock > 0 && i.CurrentStock <= i.MinStockLevel);
                var normalStock = inventory.Count(i => i.CurrentStock > i.MinStockLevel && i.CurrentStock < i.MaxStockLevel);
                var highStock = inventory.Count(i => i.CurrentStock >= i.MaxStockLevel);
                var totalValue = inventory.Sum(i => i.CurrentStock * i.PurchasePrice);

                html.AppendLine("<h2>📊 الإحصائيات العامة</h2>");
                html.AppendLine("<div class='stats'>");
                html.AppendLine($"<div class='stat-card'><h3>إجمالي الأصناف</h3><p>{totalItems}</p></div>");
                html.AppendLine($"<div class='stat-card'><h3>نفد المخزون</h3><p>{outOfStock}</p></div>");
                html.AppendLine($"<div class='stat-card'><h3>مخزون منخفض</h3><p>{lowStock}</p></div>");
                html.AppendLine($"<div class='stat-card'><h3>مخزون طبيعي</h3><p>{normalStock}</p></div>");
                html.AppendLine($"<div class='stat-card'><h3>مخزون مرتفع</h3><p>{highStock}</p></div>");
                html.AppendLine($"<div class='stat-card'><h3>قيمة المخزون</h3><p>{totalValue:N2} ر.ي</p></div>");
                html.AppendLine("</div>");

                // جدول البيانات
                html.AppendLine("<h2>📋 تفاصيل المخزون</h2>");
                html.AppendLine("<table>");
                html.AppendLine("<thead>");
                html.AppendLine("<tr>");
                html.AppendLine("<th>كود الدواء</th>");
                html.AppendLine("<th>اسم الدواء</th>");
                html.AppendLine("<th>الفئة</th>");
                html.AppendLine("<th>الشركة المصنعة</th>");
                html.AppendLine("<th>المخزون الحالي</th>");
                html.AppendLine("<th>الحد الأدنى</th>");
                html.AppendLine("<th>الحد الأقصى</th>");
                html.AppendLine("<th>سعر الشراء</th>");
                html.AppendLine("<th>قيمة المخزون</th>");
                html.AppendLine("<th>حالة المخزون</th>");
                html.AppendLine("</tr>");
                html.AppendLine("</thead>");
                html.AppendLine("<tbody>");

                foreach (var item in inventory.OrderBy(i => i.StockStatus).ThenBy(i => i.TradeName))
                {
                    string rowClass = "";
                    if (item.CurrentStock <= 0)
                        rowClass = "out-of-stock";
                    else if (item.CurrentStock <= item.MinStockLevel)
                        rowClass = "low-stock";
                    else if (item.CurrentStock >= item.MaxStockLevel)
                        rowClass = "high-stock";

                    var stockValue = item.CurrentStock * item.PurchasePrice;

                    html.AppendLine($"<tr class='{rowClass}'>");
                    html.AppendLine($"<td>{item.DrugCode}</td>");
                    html.AppendLine($"<td>{item.TradeName}</td>");
                    html.AppendLine($"<td>{item.CategoryName ?? "غير محدد"}</td>");
                    html.AppendLine($"<td>{item.ManufacturerName ?? "غير محدد"}</td>");
                    html.AppendLine($"<td>{item.CurrentStock:N0}</td>");
                    html.AppendLine($"<td>{item.MinStockLevel:N0}</td>");
                    html.AppendLine($"<td>{item.MaxStockLevel:N0}</td>");
                    html.AppendLine($"<td>{item.PurchasePrice:N2}</td>");
                    html.AppendLine($"<td>{stockValue:N2}</td>");
                    html.AppendLine($"<td>{item.StockStatus}</td>");
                    html.AppendLine("</tr>");
                }

                html.AppendLine("</tbody>");
                html.AppendLine("</table>");

                // التذييل
                html.AppendLine("<div class='footer'>");
                html.AppendLine($"<p>تم إنشاء هذا التقرير بواسطة نظام إدارة الصيدلية</p>");
                html.AppendLine($"<p>المستخدم: {UserManager.CurrentUser?.FullName ?? "غير محدد"}</p>");
                html.AppendLine("</div>");

                html.AppendLine("</div>");
                html.AppendLine("</body>");
                html.AppendLine("</html>");

                System.IO.File.WriteAllText(filePath, html.ToString(), System.Text.Encoding.UTF8);
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في إنشاء تقرير HTML: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// تصدير البيانات
        /// </summary>
        private void btnExport_Click(object sender, EventArgs e)
        {
            try
            {
                var inventory = dgvInventory.DataSource as List<InventoryItem>;
                if (inventory == null || inventory.Count == 0)
                {
                    MessageBox.Show("لا توجد بيانات للتصدير", "تنبيه",
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var saveDialog = new SaveFileDialog
                {
                    Filter = "CSV Files (*.csv)|*.csv|HTML Files (*.html)|*.html",
                    Title = "تصدير بيانات المخزون",
                    FileName = $"تقرير_المخزون_{DateTime.Now:yyyyMMdd_HHmmss}"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    LogManager.LogInfo($"بدء تصدير بيانات المخزون - {inventory.Count} عنصر");

                    if (saveDialog.FileName.EndsWith(".csv"))
                    {
                        ExportInventoryToCSV(inventory, saveDialog.FileName);
                    }
                    else if (saveDialog.FileName.EndsWith(".html"))
                    {
                        ExportInventoryToHTML(inventory, saveDialog.FileName);
                    }

                    MessageBox.Show("تم تصدير البيانات بنجاح", "نجح",
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // سؤال المستخدم إذا كان يريد فتح الملف
                    if (MessageBox.Show("هل تريد فتح الملف المصدر؟", "فتح الملف",
                                      MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                    {
                        System.Diagnostics.Process.Start(saveDialog.FileName);
                    }

                    LogManager.LogInfo($"تم تصدير بيانات المخزون بنجاح: {saveDialog.FileName}");
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تصدير بيانات المخزون: {ex.Message}");
                MessageBox.Show($"خطأ في تصدير البيانات: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إعادة تعيين البحث
        /// </summary>
        private void btnReset_Click(object sender, EventArgs e)
        {
            txtSearch.Clear();
            cmbCategory.SelectedIndex = -1;
            cmbStockStatus.SelectedIndex = 0;
            LoadData();
        }

        /// <summary>
        /// البحث عند الكتابة
        /// </summary>
        private void txtSearch_TextChanged(object sender, EventArgs e)
        {
            // البحث التلقائي بعد توقف الكتابة
            searchTimer.Stop();
            searchTimer.Start();
        }

        /// <summary>
        /// البحث التلقائي
        /// </summary>
        private void searchTimer_Tick(object sender, EventArgs e)
        {
            searchTimer.Stop();
            btnSearch_Click(sender, e);
        }

        /// <summary>
        /// تغيير الفئة
        /// </summary>
        private void cmbCategory_SelectedIndexChanged(object sender, EventArgs e)
        {
            btnSearch_Click(sender, e);
        }

        /// <summary>
        /// تغيير حالة المخزون
        /// </summary>
        private void cmbStockStatus_SelectedIndexChanged(object sender, EventArgs e)
        {
            btnSearch_Click(sender, e);
        }

        /// <summary>
        /// النقر المزدوج لتحديث المخزون
        /// </summary>
        private void dgvInventory_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                btnUpdateStock_Click(sender, e);
            }
        }

        /// <summary>
        /// تصدير المخزون إلى CSV
        /// </summary>
        private void ExportInventoryToCSV(List<InventoryItem> inventory, string filePath)
        {
            try
            {
                var csv = new System.Text.StringBuilder();
                csv.Append('\uFEFF'); // BOM للدعم العربي

                // رأس الجدول
                csv.AppendLine("كود الدواء,اسم الدواء,الفئة,الشركة المصنعة,الوحدة,المخزون الحالي,الحد الأدنى,الحد الأقصى,سعر الشراء,سعر البيع,تاريخ الانتهاء,حالة المخزون");

                // البيانات
                foreach (var item in inventory)
                {
                    csv.AppendLine($"{item.DrugCode}," +
                                 $"{item.TradeName}," +
                                 $"{item.CategoryName ?? "غير محدد"}," +
                                 $"{item.ManufacturerName ?? "غير محدد"}," +
                                 $"{item.Unit}," +
                                 $"{item.CurrentStock}," +
                                 $"{item.MinStockLevel}," +
                                 $"{item.MaxStockLevel}," +
                                 $"{item.PurchasePrice:F2}," +
                                 $"{item.SalePrice:F2}," +
                                 $"{item.ExpiryDate?.ToString("yyyy/MM/dd") ?? "غير محدد"}," +
                                 $"{item.StockStatus}");
                }

                System.IO.File.WriteAllText(filePath, csv.ToString(), System.Text.Encoding.UTF8);
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تصدير CSV: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// تصدير المخزون إلى HTML
        /// </summary>
        private void ExportInventoryToHTML(List<InventoryItem> inventory, string filePath)
        {
            try
            {
                var html = new System.Text.StringBuilder();

                html.AppendLine("<!DOCTYPE html>");
                html.AppendLine("<html dir='rtl' lang='ar'>");
                html.AppendLine("<head>");
                html.AppendLine("<meta charset='UTF-8'>");
                html.AppendLine("<title>تقرير المخزون</title>");
                html.AppendLine("<style>");
                html.AppendLine("body { font-family: 'Segoe UI', Tahoma, Arial, sans-serif; margin: 20px; }");
                html.AppendLine("h1 { color: #2c3e50; text-align: center; }");
                html.AppendLine("table { width: 100%; border-collapse: collapse; margin-top: 20px; }");
                html.AppendLine("th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }");
                html.AppendLine("th { background-color: #f2f2f2; font-weight: bold; }");
                html.AppendLine("tr:nth-child(even) { background-color: #f9f9f9; }");
                html.AppendLine(".low-stock { background-color: #ffebee; }");
                html.AppendLine(".out-of-stock { background-color: #ffcdd2; }");
                html.AppendLine("</style>");
                html.AppendLine("</head>");
                html.AppendLine("<body>");

                html.AppendLine("<h1>📦 تقرير المخزون</h1>");
                html.AppendLine($"<p>تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm}</p>");
                html.AppendLine($"<p>إجمالي العناصر: {inventory.Count}</p>");

                html.AppendLine("<table>");
                html.AppendLine("<tr>");
                html.AppendLine("<th>كود الدواء</th>");
                html.AppendLine("<th>اسم الدواء</th>");
                html.AppendLine("<th>الفئة</th>");
                html.AppendLine("<th>الشركة المصنعة</th>");
                html.AppendLine("<th>المخزون الحالي</th>");
                html.AppendLine("<th>الحد الأدنى</th>");
                html.AppendLine("<th>سعر الشراء</th>");
                html.AppendLine("<th>سعر البيع</th>");
                html.AppendLine("<th>حالة المخزون</th>");
                html.AppendLine("</tr>");

                foreach (var item in inventory)
                {
                    string rowClass = "";
                    if (item.CurrentStock <= 0)
                        rowClass = "out-of-stock";
                    else if (item.CurrentStock <= item.MinStockLevel)
                        rowClass = "low-stock";

                    html.AppendLine($"<tr class='{rowClass}'>");
                    html.AppendLine($"<td>{item.DrugCode}</td>");
                    html.AppendLine($"<td>{item.TradeName}</td>");
                    html.AppendLine($"<td>{item.CategoryName ?? "غير محدد"}</td>");
                    html.AppendLine($"<td>{item.ManufacturerName ?? "غير محدد"}</td>");
                    html.AppendLine($"<td>{item.CurrentStock}</td>");
                    html.AppendLine($"<td>{item.MinStockLevel}</td>");
                    html.AppendLine($"<td>{item.PurchasePrice:F2}</td>");
                    html.AppendLine($"<td>{item.SalePrice:F2}</td>");
                    html.AppendLine($"<td>{item.StockStatus}</td>");
                    html.AppendLine("</tr>");
                }

                html.AppendLine("</table>");
                html.AppendLine("</body>");
                html.AppendLine("</html>");

                System.IO.File.WriteAllText(filePath, html.ToString(), System.Text.Encoding.UTF8);
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تصدير HTML: {ex.Message}");
                throw;
            }
        }

        #endregion

        
    }
}


