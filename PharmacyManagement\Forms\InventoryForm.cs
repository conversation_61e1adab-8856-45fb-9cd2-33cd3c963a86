using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using InventoryItem = PharmacyManagement.Models.InventoryItem;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نافذة إدارة المخزون - Inventory Management Form
    /// </summary>
    public partial class InventoryForm : Form
    {
        #region Constructor - المنشئ

        public InventoryForm()
        {
            InitializeComponent();
            SetupForm();
            LoadData();
        }

        #endregion

        #region Form Setup - إعداد النافذة

        /// <summary>
        /// إعداد النافذة
        /// </summary>
        private void SetupForm()
        {
            // إعداد DataGridView
            SetupDataGridView();
            
            // إعداد ComboBoxes
            LoadCategories();
            LoadStockStatus();
            
            // إعداد التصميم المسطح
            ApplyFlatDesign();
        }

        /// <summary>
        /// إعداد DataGridView
        /// </summary>
        private void SetupDataGridView()
        {
            dgvInventory.AutoGenerateColumns = false;
            dgvInventory.AllowUserToAddRows = false;
            dgvInventory.AllowUserToDeleteRows = false;
            dgvInventory.ReadOnly = true;
            dgvInventory.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvInventory.MultiSelect = false;
            
            // تنسيق الألوان
            dgvInventory.BackgroundColor = Color.White;
            dgvInventory.GridColor = Color.FromArgb(189, 195, 199);
            dgvInventory.DefaultCellStyle.SelectionBackColor = Color.FromArgb(52, 152, 219);
            dgvInventory.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvInventory.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 73, 94);
            dgvInventory.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvInventory.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10, FontStyle.Bold);
        }

        /// <summary>
        /// تحميل فئات الأدوية
        /// </summary>
        private void LoadCategories()
        {
            try
            {
                string query = "SELECT CategoryID, CategoryName FROM DrugCategories ORDER BY CategoryName";
                var dataTable = DatabaseHelper.ExecuteQuery(query);
                
                cmbCategory.DisplayMember = "CategoryName";
                cmbCategory.ValueMember = "CategoryID";
                cmbCategory.DataSource = dataTable;
                cmbCategory.SelectedIndex = -1;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الفئات: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل حالات المخزون
        /// </summary>
        private void LoadStockStatus()
        {
            var statusList = new[]
            {
                new { Value = "", Text = "جميع الحالات" },
                new { Value = "متوفر", Text = "متوفر" },
                new { Value = "منخفض", Text = "منخفض" },
                new { Value = "نفد", Text = "نفد" },
                new { Value = "زائد", Text = "زائد عن الحد" }
            };
            
            cmbStockStatus.DisplayMember = "Text";
            cmbStockStatus.ValueMember = "Value";
            cmbStockStatus.DataSource = statusList;
            cmbStockStatus.SelectedIndex = 0;
        }

        /// <summary>
        /// تطبيق التصميم المسطح
        /// </summary>
        private void ApplyFlatDesign()
        {
            // إزالة حدود الأزرار
            foreach (Control control in this.Controls)
            {
                ApplyFlatStyleToControl(control);
            }
        }

        /// <summary>
        /// تطبيق التصميم المسطح على عنصر
        /// </summary>
        private void ApplyFlatStyleToControl(Control control)
        {
            if (control is Button button)
            {
                button.FlatAppearance.BorderSize = 0;
                button.Cursor = Cursors.Hand;
            }
            
            // تطبيق على العناصر الفرعية
            foreach (Control child in control.Controls)
            {
                ApplyFlatStyleToControl(child);
            }
        }

        #endregion

        #region Data Loading - تحميل البيانات

        /// <summary>
        /// تحميل بيانات المخزون
        /// </summary>
        private void LoadData()
        {
            try
            {
                var inventory = InventoryManager.GetAllInventoryItems();
                dgvInventory.DataSource = inventory;
                
                // تحديث عدد السجلات
                lblRecordsCount.Text = $"عدد السجلات: {inventory.Count}";
                
                // تحديث الإحصائيات
                UpdateStatistics(inventory);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحديث الإحصائيات
        /// </summary>
        private void UpdateStatistics(List<InventoryItem> inventory)
        {
            if (inventory == null || inventory.Count == 0)
            {
                lblTotalItems.Text = "إجمالي الأصناف: 0";
                lblLowStock.Text = "منخفض المخزون: 0";
                lblOutOfStock.Text = "نفد المخزون: 0";
                lblTotalValue.Text = "إجمالي القيمة: 0 ر.ي";
                return;
            }

            int totalItems = inventory.Count();
            int lowStock = inventory.Count(i => i.StockStatus == "منخفض");
            int outOfStock = inventory.Count(i => i.StockStatus == "نفد");
            decimal totalValue = inventory.Sum(i => i.CurrentStock * i.PurchasePrice);

            lblTotalItems.Text = $"إجمالي الأصناف: {totalItems}";
            lblLowStock.Text = $"منخفض المخزون: {lowStock}";
            lblOutOfStock.Text = $"نفد المخزون: {outOfStock}";
            lblTotalValue.Text = $"إجمالي القيمة: {totalValue:N2} ر.ي";
        }

        #endregion

        #region Event Handlers - معالجات الأحداث

        /// <summary>
        /// البحث في المخزون
        /// </summary>
        private void btnSearch_Click(object sender, EventArgs e)
        {
            try
            {
                string searchTerm = txtSearch.Text.Trim();
                int? categoryId = cmbCategory.SelectedValue as int?;
                string stockStatus = cmbStockStatus.SelectedValue?.ToString();
                
                var inventory = InventoryManager.GetAllInventoryItems();
                dgvInventory.DataSource = inventory;
                
                lblRecordsCount.Text = $"عدد السجلات: {inventory.Count}";
                UpdateStatistics(inventory);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحديث المخزون
        /// </summary>
        private void btnUpdateStock_Click(object sender, EventArgs e)
        {
            if (dgvInventory.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار صنف لتحديث المخزون", "تنبيه", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedItem = dgvInventory.SelectedRows[0].DataBoundItem as InventoryItem;
            if (selectedItem != null)
            {
                var drug = DrugManager.GetDrugById(selectedItem.DrugID); // Get Drug object first
                if (drug != null)
                {
                    // Use the drug object instead of just the ID
                    var updateForm = new StockUpdateForm(drug);
                    updateForm.ShowDialog();
                }
            }
        }

        /// <summary>
        /// تقرير المخزون
        /// </summary>
        private void btnReport_Click(object sender, EventArgs e)
        {
            try
            {
                var reportForm = new InventoryReportForm();
                reportForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح التقرير: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تصدير البيانات
        /// </summary>
        private void btnExport_Click(object sender, EventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "Excel Files|*.xlsx|CSV Files|*.csv",
                    Title = "تصدير بيانات المخزون",
                    FileName = $"Inventory_Report_{DateTime.Now:yyyyMMdd}"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    var inventory = dgvInventory.DataSource as List<InventoryItem>;
                    if (inventory != null)
                    {
                        ExportManager.ExportInventory(inventory, saveDialog.FileName);
                        MessageBox.Show("تم تصدير البيانات بنجاح", "نجح", 
                                      MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير البيانات: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إعادة تعيين البحث
        /// </summary>
        private void btnReset_Click(object sender, EventArgs e)
        {
            txtSearch.Clear();
            cmbCategory.SelectedIndex = -1;
            cmbStockStatus.SelectedIndex = 0;
            LoadData();
        }

        /// <summary>
        /// البحث عند الكتابة
        /// </summary>
        private void txtSearch_TextChanged(object sender, EventArgs e)
        {
            // البحث التلقائي بعد توقف الكتابة
            searchTimer.Stop();
            searchTimer.Start();
        }

        /// <summary>
        /// البحث التلقائي
        /// </summary>
        private void searchTimer_Tick(object sender, EventArgs e)
        {
            searchTimer.Stop();
            btnSearch_Click(sender, e);
        }

        /// <summary>
        /// تغيير الفئة
        /// </summary>
        private void cmbCategory_SelectedIndexChanged(object sender, EventArgs e)
        {
            btnSearch_Click(sender, e);
        }

        /// <summary>
        /// تغيير حالة المخزون
        /// </summary>
        private void cmbStockStatus_SelectedIndexChanged(object sender, EventArgs e)
        {
            btnSearch_Click(sender, e);
        }

        /// <summary>
        /// النقر المزدوج لتحديث المخزون
        /// </summary>
        private void dgvInventory_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                btnUpdateStock_Click(sender, e);
            }
        }

        #endregion
    }
}


