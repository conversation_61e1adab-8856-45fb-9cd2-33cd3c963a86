using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نافذة تحديث المخزون - Stock Update Form
    /// </summary>
    public partial class StockUpdateForm : Form
    {
        #region Fields - الحقول

        private Drug _drug;

        #endregion

        #region Constructor - المنشئ

        /// <summary>
        /// منشئ نافذة تحديث المخزون
        /// </summary>
        /// <param name="drug">الدواء</param>
        public StockUpdateForm(Drug drug)
        {
            InitializeComponent();
            _drug = drug;
            SetupForm();
            LoadDrugInfo();
        }

        #endregion

        #region Form Setup - إعداد النافذة

        /// <summary>
        /// إعداد النافذة
        /// </summary>
        private void SetupForm()
        {
            this.Text = $"تحديث مخزون - {_drug.DrugName}";
            //this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            ApplyFlatDesign();
        }

        /// <summary>
        /// تطبيق التصميم المسطح
        /// </summary>
        private void ApplyFlatDesign()
        {
            this.BackColor = Color.White;
            
            foreach (Control control in this.Controls)
            {
                if (control is Button button)
                {
                    button.FlatStyle = FlatStyle.Flat;
                    button.FlatAppearance.BorderSize = 0;
                }
            }
        }

        /// <summary>
        /// تحميل معلومات الدواء
        /// </summary>
        private void LoadDrugInfo()
        {
            // جلب المخزون الحالي من جدول Inventory
            decimal currentStock = GetCurrentStockFromInventory(_drug.DrugID);

            lblDrugName.Text = $"الدواء: {_drug.DrugName}";
            lblDrugCode.Text = $"الكود: {_drug.DrugCode}";
            lblCurrentStock.Text = $"المخزون الحالي: {currentStock}";
            lblUnit.Text = $"الوحدة: {_drug.Unit}";

            // تحديث المخزون في كائن الدواء
            _drug.CurrentStock = (int)currentStock;

            // تعيين القيم الافتراضية
            numNewQuantity.Value = 0;
            cmbOperationType.SelectedIndex = 0; // إضافة
            txtNotes.Clear();
        }

        /// <summary>
        /// جلب المخزون الحالي من جدول Inventory
        /// </summary>
        private decimal GetCurrentStockFromInventory(int drugId)
        {
            try
            {
                string query = @"
                    SELECT ISNULL(SUM(Quantity), 0) as TotalStock
                    FROM Inventory
                    WHERE DrugID = @DrugID AND Quantity > 0";

                var parameters = new[] { DatabaseHelper.CreateParameter("@DrugID", drugId) };

                using (var reader = DatabaseHelper.ExecuteReader(query, parameters))
                {
                    if (reader.Read())
                    {
                        return Convert.ToDecimal(reader["TotalStock"]);
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في جلب المخزون الحالي: {ex.Message}");
            }

            return 0;
        }

        #endregion

        #region Event Handlers - معالجات الأحداث

        /// <summary>
        /// حفظ تحديث المخزون
        /// </summary>
        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (ValidateInput())
                {
                    UpdateStock();
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث المخزون: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إلغاء العملية
        /// </summary>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// تغيير نوع العملية
        /// </summary>
        private void cmbOperationType_SelectedIndexChanged(object sender, EventArgs e)
        {
            UpdateLabels();
        }

        /// <summary>
        /// تغيير الكمية
        /// </summary>
        private void numNewQuantity_ValueChanged(object sender, EventArgs e)
        {
            UpdateLabels();
        }

        #endregion

        #region Private Methods - الطرق الخاصة

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        private bool ValidateInput()
        {
            if (numNewQuantity.Value <= 0)
            {
                MessageBox.Show("يرجى إدخال كمية صحيحة", "تنبيه",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                numNewQuantity.Focus();
                return false;
            }

            if (cmbOperationType.SelectedIndex == 1) // خصم
            {
                if (numNewQuantity.Value > _drug.CurrentStock)
                {
                    MessageBox.Show("لا يمكن خصم كمية أكبر من المخزون الحالي", "تنبيه",
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    numNewQuantity.Focus();
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// تحديث المخزون
        /// </summary>
        private void UpdateStock()
        {
            decimal quantity = numNewQuantity.Value;
            bool isAddition = cmbOperationType.SelectedIndex == 0;
            string notes = txtNotes.Text.Trim();

            // جلب المخزون الحالي الفعلي
            decimal currentStock = GetCurrentStockFromInventory(_drug.DrugID);

            // حساب المخزون الجديد
            decimal newStock = currentStock;
            if (isAddition)
            {
                newStock += quantity;
            }
            else
            {
                newStock -= quantity;
                if (newStock < 0)
                {
                    MessageBox.Show("لا يمكن أن يكون المخزون أقل من الصفر", "خطأ",
                                  MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
            }

            try
            {
                // تحديث المخزون في قاعدة البيانات
                bool success = UpdateInventoryStock(_drug.DrugID, quantity, isAddition, notes);

                if (success)
                {
                    string operation = isAddition ? "إضافة" : "خصم";
                    MessageBox.Show($"تم {operation} {quantity} {_drug.Unit} بنجاح\n" +
                                  $"المخزون السابق: {currentStock}\n" +
                                  $"المخزون الجديد: {newStock}",
                                  "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    LogManager.LogInfo($"تم تحديث مخزون الدواء {_drug.DrugName}: {operation} {quantity} - من {currentStock} إلى {newStock}");
                }
                else
                {
                    throw new Exception("فشل في تحديث المخزون");
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تحديث المخزون: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// تحديث المخزون في جدول Inventory
        /// </summary>
        private bool UpdateInventoryStock(int drugId, decimal quantity, bool isAddition, string notes)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            if (isAddition)
                            {
                                // إضافة دفعة جديدة للمخزون
                                string insertQuery = @"
                                    INSERT INTO Inventory (DrugID, WarehouseID, BatchNumber, Quantity,
                                                         ExpiryDate, PurchasePrice, SalePrice, CreatedDate)
                                    VALUES (@DrugID, 1, @BatchNumber, @Quantity,
                                           DATEADD(YEAR, 2, GETDATE()), @PurchasePrice, @SalePrice, GETDATE())";

                                using (var cmd = new SqlCommand(insertQuery, connection, transaction))
                                {
                                    cmd.Parameters.AddWithValue("@DrugID", drugId);
                                    cmd.Parameters.AddWithValue("@BatchNumber", $"BATCH_{DateTime.Now:yyyyMMddHHmmss}");
                                    cmd.Parameters.AddWithValue("@Quantity", quantity);
                                    cmd.Parameters.AddWithValue("@PurchasePrice", _drug.PurchasePrice);
                                    cmd.Parameters.AddWithValue("@SalePrice", _drug.SalePrice);
                                    cmd.ExecuteNonQuery();
                                }
                            }
                            else
                            {
                                // خصم من أقدم دفعة متاحة
                                decimal remainingToDeduct = quantity;

                                string selectQuery = @"
                                    SELECT InventoryID, Quantity
                                    FROM Inventory
                                    WHERE DrugID = @DrugID AND Quantity > 0
                                    ORDER BY ExpiryDate ASC, CreatedDate ASC";

                                using (var selectCmd = new SqlCommand(selectQuery, connection, transaction))
                                {
                                    selectCmd.Parameters.AddWithValue("@DrugID", drugId);

                                    using (var reader = selectCmd.ExecuteReader())
                                    {
                                        var inventoriesToUpdate = new List<(int InventoryID, decimal CurrentQuantity, decimal DeductAmount)>();

                                        while (reader.Read() && remainingToDeduct > 0)
                                        {
                                            int inventoryId = Convert.ToInt32(reader["InventoryID"]);
                                            decimal currentQuantity = Convert.ToDecimal(reader["Quantity"]);

                                            decimal deductFromThis = Math.Min(remainingToDeduct, currentQuantity);
                                            inventoriesToUpdate.Add((inventoryId, currentQuantity, deductFromThis));
                                            remainingToDeduct -= deductFromThis;
                                        }

                                        reader.Close();

                                        // تطبيق التحديثات
                                        foreach (var update in inventoriesToUpdate)
                                        {
                                            string updateQuery = @"
                                                UPDATE Inventory
                                                SET Quantity = Quantity - @DeductAmount, UpdatedDate = GETDATE()
                                                WHERE InventoryID = @InventoryID";

                                            using (var updateCmd = new SqlCommand(updateQuery, connection, transaction))
                                            {
                                                updateCmd.Parameters.AddWithValue("@InventoryID", update.InventoryID);
                                                updateCmd.Parameters.AddWithValue("@DeductAmount", update.DeductAmount);
                                                updateCmd.ExecuteNonQuery();
                                            }
                                        }
                                    }
                                }
                            }

                            // تسجيل حركة المخزون
                            string movementQuery = @"
                                INSERT INTO StockMovements (DrugID, MovementType, Quantity, MovementDate, Notes, UserID)
                                VALUES (@DrugID, @MovementType, @Quantity, GETDATE(), @Notes, @UserID)";

                            using (var movementCmd = new SqlCommand(movementQuery, connection, transaction))
                            {
                                movementCmd.Parameters.AddWithValue("@DrugID", drugId);
                                movementCmd.Parameters.AddWithValue("@MovementType", isAddition ? "إضافة" : "خصم");
                                movementCmd.Parameters.AddWithValue("@Quantity", quantity);
                                movementCmd.Parameters.AddWithValue("@Notes", notes ?? "");
                                movementCmd.Parameters.AddWithValue("@UserID", UserManager.CurrentUser?.UserID ?? 0);
                                movementCmd.ExecuteNonQuery();
                            }

                            transaction.Commit();
                            return true;
                        }
                        catch
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تحديث المخزون في قاعدة البيانات: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تحديث التسميات
        /// </summary>
        private void UpdateLabels()
        {
            // جلب المخزون الحالي الفعلي
            decimal currentStock = GetCurrentStockFromInventory(_drug.DrugID);

            if (cmbOperationType.SelectedIndex == 0) // إضافة
            {
                decimal newStock = currentStock + numNewQuantity.Value;
                lblNewStock.Text = $"المخزون الجديد: {newStock}";
                lblNewStock.ForeColor = Color.Green;
            }
            else // خصم
            {
                decimal newStock = currentStock - numNewQuantity.Value;
                lblNewStock.Text = $"المخزون الجديد: {newStock}";
                lblNewStock.ForeColor = newStock < 0 ? Color.Red : Color.Orange;
            }
        }

        #endregion
    }
}




