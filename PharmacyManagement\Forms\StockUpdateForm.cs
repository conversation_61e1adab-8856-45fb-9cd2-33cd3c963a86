using System;
using System.Drawing;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نافذة تحديث المخزون - Stock Update Form
    /// </summary>
    public partial class StockUpdateForm : Form
    {
        #region Fields - الحقول

        private Drug _drug;

        #endregion

        #region Constructor - المنشئ

        /// <summary>
        /// منشئ نافذة تحديث المخزون
        /// </summary>
        /// <param name="drug">الدواء</param>
        public StockUpdateForm(Drug drug)
        {
            InitializeComponent();
            _drug = drug;
            SetupForm();
            LoadDrugInfo();
        }

        #endregion

        #region Form Setup - إعداد النافذة

        /// <summary>
        /// إعداد النافذة
        /// </summary>
        private void SetupForm()
        {
            this.Text = $"تحديث مخزون - {_drug.DrugName}";
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            ApplyFlatDesign();
        }

        /// <summary>
        /// تطبيق التصميم المسطح
        /// </summary>
        private void ApplyFlatDesign()
        {
            this.BackColor = Color.White;
            
            foreach (Control control in this.Controls)
            {
                if (control is Button button)
                {
                    button.FlatStyle = FlatStyle.Flat;
                    button.FlatAppearance.BorderSize = 0;
                }
            }
        }

        /// <summary>
        /// تحميل معلومات الدواء
        /// </summary>
        private void LoadDrugInfo()
        {
            lblDrugName.Text = $"الدواء: {_drug.DrugName}";
            lblDrugCode.Text = $"الكود: {_drug.DrugCode}";
            lblCurrentStock.Text = $"المخزون الحالي: {_drug.StockQuantity}";
            lblUnit.Text = $"الوحدة: {_drug.Unit}";
            
            // تعيين القيم الافتراضية
            numNewQuantity.Value = 0;
            cmbOperationType.SelectedIndex = 0; // إضافة
            txtNotes.Clear();
        }

        #endregion

        #region Event Handlers - معالجات الأحداث

        /// <summary>
        /// حفظ تحديث المخزون
        /// </summary>
        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (ValidateInput())
                {
                    UpdateStock();
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث المخزون: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إلغاء العملية
        /// </summary>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// تغيير نوع العملية
        /// </summary>
        private void cmbOperationType_SelectedIndexChanged(object sender, EventArgs e)
        {
            UpdateLabels();
        }

        /// <summary>
        /// تغيير الكمية
        /// </summary>
        private void numNewQuantity_ValueChanged(object sender, EventArgs e)
        {
            UpdateLabels();
        }

        #endregion

        #region Private Methods - الطرق الخاصة

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        private bool ValidateInput()
        {
            if (numNewQuantity.Value <= 0)
            {
                MessageBox.Show("يرجى إدخال كمية صحيحة", "تنبيه", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                numNewQuantity.Focus();
                return false;
            }

            if (cmbOperationType.SelectedIndex == 1) // خصم
            {
                if (numNewQuantity.Value > _drug.StockQuantity)
                {
                    MessageBox.Show("لا يمكن خصم كمية أكبر من المخزون الحالي", "تنبيه", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    numNewQuantity.Focus();
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// تحديث المخزون
        /// </summary>
        private void UpdateStock()
        {
            decimal quantity = numNewQuantity.Value;
            bool isAddition = cmbOperationType.SelectedIndex == 0;
            string notes = txtNotes.Text.Trim();

            // حساب المخزون الجديد
            decimal newStock = _drug.StockQuantity;
            if (isAddition)
            {
                newStock += quantity;
            }
            else
            {
                newStock -= quantity;
            }

            // تحديث المخزون في قاعدة البيانات
            bool success = DrugManager.UpdateDrugStock(_drug.DrugID, newStock);

            if (success)
            {
                // تسجيل حركة المخزون
                var stockMovement = new StockMovement
                {
                    DrugID = _drug.DrugID,
                    MovementType = isAddition ? "إضافة" : "خصم",
                    Quantity = quantity,
                    PreviousStock = _drug.StockQuantity,
                    NewStock = newStock,
                    MovementDate = DateTime.Now,
                    Notes = notes,
                    UserID = SessionManager.GetValue("CurrentUserID") != null ? Convert.ToInt32(SessionManager.GetValue("CurrentUserID")) : 0
                };

                UnifiedInventoryManager.AddStockMovement(_drug.DrugID, 1, // assuming warehouse ID 1
                    isAddition ? "دخول" : "خروج", 
                    (int)quantity, 
                    "تحديث مخزون", 
                    notes);

                string operation = isAddition ? "إضافة" : "خصم";
                MessageBox.Show($"تم {operation} {quantity} {_drug.Unit} بنجاح\nالمخزون الجديد: {newStock}", 
                              "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);

                LogManager.LogInfo($"تم تحديث مخزون الدواء {_drug.DrugName}: {operation} {quantity}");
            }
            else
            {
                throw new Exception("فشل في تحديث المخزون");
            }
        }

        /// <summary>
        /// تحديث التسميات
        /// </summary>
        private void UpdateLabels()
        {
            if (cmbOperationType.SelectedIndex == 0) // إضافة
            {
                decimal newStock = _drug.StockQuantity + numNewQuantity.Value;
                lblNewStock.Text = $"المخزون الجديد: {newStock}";
                lblNewStock.ForeColor = Color.Green;
            }
            else // خصم
            {
                decimal newStock = _drug.StockQuantity - numNewQuantity.Value;
                lblNewStock.Text = $"المخزون الجديد: {newStock}";
                lblNewStock.ForeColor = newStock < 0 ? Color.Red : Color.Orange;
            }
        }

        #endregion
    }
}




