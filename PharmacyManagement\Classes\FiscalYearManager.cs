using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using PharmacyManagement.Models;

namespace PharmacyManagement.Classes
{
    /// <summary>
    /// مدير السنوات المالية - Fiscal Year Manager
    /// </summary>
    public static class FiscalYearManager
    {
        #region Fiscal Year Management

        /// <summary>
        /// إضافة سنة مالية جديدة
        /// </summary>
        /// <param name="fiscalYear">السنة المالية</param>
        /// <returns>معرف السنة المالية الجديدة</returns>
        public static int AddFiscalYear(FiscalYear fiscalYear)
        {
            try
            {
                // التحقق من عدم تداخل السنوات المالية
                if (IsFiscalYearOverlapping(fiscalYear.StartDate, fiscalYear.EndDate))
                {
                    throw new Exception("تتداخل السنة المالية مع سنة مالية أخرى موجودة");
                }

                string query = @"
                    INSERT INTO FiscalYears (YearName, StartDate, EndDate, IsActive, IsClosed, CreatedBy, CreatedDate)
                    VALUES (@YearName, @StartDate, @EndDate, @IsActive, @IsClosed, @CreatedBy, @CreatedDate);
                    SELECT SCOPE_IDENTITY();";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // إذا كانت السنة الجديدة نشطة، إلغاء تفعيل السنوات الأخرى
                            if (fiscalYear.IsActive)
                            {
                                DeactivateAllFiscalYears(connection, transaction);
                            }

                            using (var command = new SqlCommand(query, connection, transaction))
                            {
                                command.Parameters.AddWithValue("@YearName", fiscalYear.YearName);
                                command.Parameters.AddWithValue("@StartDate", fiscalYear.StartDate);
                                command.Parameters.AddWithValue("@EndDate", fiscalYear.EndDate);
                                command.Parameters.AddWithValue("@IsActive", fiscalYear.IsActive);
                                command.Parameters.AddWithValue("@IsClosed", fiscalYear.IsClosed);
                                command.Parameters.AddWithValue("@CreatedBy", fiscalYear.CreatedBy);
                                command.Parameters.AddWithValue("@CreatedDate", DateTime.Now);

                                int fiscalYearId = Convert.ToInt32(command.ExecuteScalar());
                                transaction.Commit();

                                LogManager.LogActivity("إضافة سنة مالية", $"تم إضافة السنة المالية: {fiscalYear.YearName}");
                                return fiscalYearId;
                            }
                        }
                        catch
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في إضافة السنة المالية: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// تحديث سنة مالية
        /// </summary>
        /// <param name="fiscalYear">السنة المالية</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public static bool UpdateFiscalYear(FiscalYear fiscalYear)
        {
            try
            {
                // التحقق من عدم تداخل السنوات المالية
                if (IsFiscalYearOverlapping(fiscalYear.StartDate, fiscalYear.EndDate, fiscalYear.FiscalYearID))
                {
                    throw new Exception("تتداخل السنة المالية مع سنة مالية أخرى موجودة");
                }

                string query = @"
                    UPDATE FiscalYears SET 
                        YearName = @YearName,
                        StartDate = @StartDate,
                        EndDate = @EndDate,
                        IsActive = @IsActive
                    WHERE FiscalYearID = @FiscalYearID AND IsClosed = 0";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // إذا كانت السنة الجديدة نشطة، إلغاء تفعيل السنوات الأخرى
                            if (fiscalYear.IsActive)
                            {
                                DeactivateAllFiscalYears(connection, transaction, fiscalYear.FiscalYearID);
                            }

                            using (var command = new SqlCommand(query, connection, transaction))
                            {
                                command.Parameters.AddWithValue("@FiscalYearID", fiscalYear.FiscalYearID);
                                command.Parameters.AddWithValue("@YearName", fiscalYear.YearName);
                                command.Parameters.AddWithValue("@StartDate", fiscalYear.StartDate);
                                command.Parameters.AddWithValue("@EndDate", fiscalYear.EndDate);
                                command.Parameters.AddWithValue("@IsActive", fiscalYear.IsActive);

                                int rowsAffected = command.ExecuteNonQuery();
                                transaction.Commit();

                                if (rowsAffected > 0)
                                {
                                    LogManager.LogActivity("تحديث سنة مالية", $"تم تحديث السنة المالية: {fiscalYear.YearName}");
                                    return true;
                                }
                            }
                        }
                        catch
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تحديث السنة المالية: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// حذف سنة مالية
        /// </summary>
        /// <param name="fiscalYearId">معرف السنة المالية</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public static bool DeleteFiscalYear(int fiscalYearId)
        {
            try
            {
                // التحقق من عدم وجود معاملات في هذه السنة
                if (HasTransactions(fiscalYearId))
                {
                    throw new Exception("لا يمكن حذف السنة المالية لوجود معاملات مرتبطة بها");
                }

                string query = "DELETE FROM FiscalYears WHERE FiscalYearID = @FiscalYearID AND IsClosed = 0";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@FiscalYearID", fiscalYearId);
                        int rowsAffected = command.ExecuteNonQuery();

                        if (rowsAffected > 0)
                        {
                            LogManager.LogActivity("حذف سنة مالية" , $"تم حذف السنة المالية رقم: {fiscalYearId}");
                            return true;
                        }
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في حذف السنة المالية: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// الحصول على سنة مالية
        /// </summary>
        /// <param name="fiscalYearId">معرف السنة المالية</param>
        /// <returns>السنة المالية</returns>
        public static FiscalYear GetFiscalYear(int fiscalYearId)
        {
            try
            {
                string query = "SELECT * FROM FiscalYears WHERE FiscalYearID = @FiscalYearID";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@FiscalYearID", fiscalYearId);
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return new FiscalYear
                                {
                                    FiscalYearID = Convert.ToInt32(reader["FiscalYearID"]),
                                    YearName = reader["YearName"].ToString(),
                                    StartDate = Convert.ToDateTime(reader["StartDate"]),
                                    EndDate = Convert.ToDateTime(reader["EndDate"]),
                                    IsActive = Convert.ToBoolean(reader["IsActive"]),
                                    IsClosed = Convert.ToBoolean(reader["IsClosed"]),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                                    CreatedBy = reader["CreatedBy"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["CreatedBy"]),
                                    ClosedDate = reader["ClosedDate"] == DBNull.Value ? null : (DateTime?)Convert.ToDateTime(reader["ClosedDate"]),
                                    ClosedBy = reader["ClosedBy"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["ClosedBy"])
                                };
                            }
                        }
                    }
                }
                return null;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على السنة المالية: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// الحصول على جميع السنوات المالية
        /// </summary>
        /// <returns>قائمة السنوات المالية</returns>
        public static List<FiscalYear> GetAllFiscalYears()
        {
            try
            {
                var fiscalYears = new List<FiscalYear>();
                string query = "SELECT * FROM FiscalYears ORDER BY StartDate DESC";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                fiscalYears.Add(new FiscalYear
                                {
                                    FiscalYearID = Convert.ToInt32(reader["FiscalYearID"]),
                                    YearName = reader["YearName"].ToString(),
                                    StartDate = Convert.ToDateTime(reader["StartDate"]),
                                    EndDate = Convert.ToDateTime(reader["EndDate"]),
                                    IsActive = Convert.ToBoolean(reader["IsActive"]),
                                    IsClosed = Convert.ToBoolean(reader["IsClosed"]),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                                    CreatedBy = reader["CreatedBy"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["CreatedBy"]),
                                    ClosedDate = reader["ClosedDate"] == DBNull.Value ? null : (DateTime?)Convert.ToDateTime(reader["ClosedDate"]),
                                    ClosedBy = reader["ClosedBy"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["ClosedBy"])
                                });
                            }
                        }
                    }
                }
                return fiscalYears;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على السنوات المالية: {ex.Message}");
                return new List<FiscalYear>();
            }
        }

        /// <summary>
        /// الحصول على السنة المالية النشطة
        /// </summary>
        /// <returns>السنة المالية النشطة</returns>
        public static FiscalYear GetActiveFiscalYear()
        {
            try
            {
                string query = "SELECT * FROM FiscalYears WHERE IsActive = 1";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return new FiscalYear
                                {
                                    FiscalYearID = Convert.ToInt32(reader["FiscalYearID"]),
                                    YearName = reader["YearName"].ToString(),
                                    StartDate = Convert.ToDateTime(reader["StartDate"]),
                                    EndDate = Convert.ToDateTime(reader["EndDate"]),
                                    IsActive = Convert.ToBoolean(reader["IsActive"]),
                                    IsClosed = Convert.ToBoolean(reader["IsClosed"]),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                                    CreatedBy = reader["CreatedBy"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["CreatedBy"]),
                                    ClosedDate = reader["ClosedDate"] == DBNull.Value ? null : (DateTime?)Convert.ToDateTime(reader["ClosedDate"]),
                                    ClosedBy = reader["ClosedBy"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["ClosedBy"])
                                };
                            }
                        }
                    }
                }
                return null;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على السنة المالية النشطة: {ex.Message}");
                return null;
            }
        }

        #endregion

        #region Fiscal Year Operations

        /// <summary>
        /// إقفال السنة المالية
        /// </summary>
        /// <param name="fiscalYearId">معرف السنة المالية</param>
        /// <returns>true إذا تم الإقفال بنجاح</returns>
        public static bool CloseFiscalYear(int fiscalYearId)
        {
            try
            {
                string query = @"
                    UPDATE FiscalYears 
                    SET IsClosed = 1, ClosedDate = @ClosedDate, ClosedBy = @ClosedBy, IsActive = 0
                    WHERE FiscalYearID = @FiscalYearID AND IsClosed = 0";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@FiscalYearID", fiscalYearId);
                        command.Parameters.AddWithValue("@ClosedDate", DateTime.Now);
                        command.Parameters.AddWithValue("@ClosedBy", UserManager.CurrentUser.UserID);

                        int rowsAffected = command.ExecuteNonQuery();
                        if (rowsAffected > 0)
                        {
                            LogManager.LogActivity("إقفال سنة مالية", $"تم إقفال السنة المالية رقم: {fiscalYearId}");
                            return true;
                        }
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في إقفال السنة المالية: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إعادة فتح السنة المالية
        /// </summary>
        /// <param name="fiscalYearId">معرف السنة المالية</param>
        /// <returns>true إذا تم إعادة الفتح بنجاح</returns>
        public static bool ReopenFiscalYear(int fiscalYearId)
        {
            try
            {
                string query = @"
                    UPDATE FiscalYears 
                    SET IsClosed = 0, ClosedDate = NULL, ClosedBy = NULL
                    WHERE FiscalYearID = @FiscalYearID AND IsClosed = 1";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@FiscalYearID", fiscalYearId);

                        int rowsAffected = command.ExecuteNonQuery();
                        if (rowsAffected > 0)
                        {
                            LogManager.LogActivity("إعادة فتح سنة مالية", $"تم إعادة فتح السنة المالية رقم: {fiscalYearId}");
                            return true;
                        }
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في إعادة فتح السنة المالية: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تفعيل السنة المالية
        /// </summary>
        /// <param name="fiscalYearId">معرف السنة المالية</param>
        /// <returns>true إذا تم التفعيل بنجاح</returns>
        public static bool ActivateFiscalYear(int fiscalYearId)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // إلغاء تفعيل جميع السنوات المالية
                            DeactivateAllFiscalYears(connection, transaction);

                            // تفعيل السنة المحددة
                            string query = @"
                                UPDATE FiscalYears 
                                SET IsActive = 1
                                WHERE FiscalYearID = @FiscalYearID AND IsClosed = 0";

                            using (var command = new SqlCommand(query, connection, transaction))
                            {
                                command.Parameters.AddWithValue("@FiscalYearID", fiscalYearId);
                                int rowsAffected = command.ExecuteNonQuery();

                                transaction.Commit();

                                if (rowsAffected > 0)
                                {
                                    LogManager.LogActivity("تفعيل سنة مالية", $"تم تفعيل السنة المالية رقم: {fiscalYearId}");
                                    return true;
                                }
                            }
                        }
                        catch
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تفعيل السنة المالية: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// التحقق من تداخل السنوات المالية
        /// </summary>
        private static bool IsFiscalYearOverlapping(DateTime startDate, DateTime endDate, int? excludeFiscalYearId = null)
        {
            try
            {
                string query = @"
                    SELECT COUNT(*) FROM FiscalYears 
                    WHERE ((@StartDate BETWEEN StartDate AND EndDate) OR 
                           (@EndDate BETWEEN StartDate AND EndDate) OR 
                           (StartDate BETWEEN @StartDate AND @EndDate))";

                if (excludeFiscalYearId.HasValue)
                {
                    query += " AND FiscalYearID != @ExcludeFiscalYearID";
                }

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@StartDate", startDate);
                        command.Parameters.AddWithValue("@EndDate", endDate);
                        if (excludeFiscalYearId.HasValue)
                        {
                            command.Parameters.AddWithValue("@ExcludeFiscalYearID", excludeFiscalYearId.Value);
                        }

                        int count = Convert.ToInt32(command.ExecuteScalar());
                        return count > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في التحقق من تداخل السنوات المالية: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// التحقق من وجود معاملات في السنة المالية
        /// </summary>
        private static bool HasTransactions(int fiscalYearId)
        {
            try
            {
                string query = @"
                    SELECT COUNT(*) FROM (
                        SELECT 1 FROM Invoices WHERE FiscalYearID = @FiscalYearID
                        UNION ALL
                        SELECT 1 FROM Vouchers WHERE FiscalYearID = @FiscalYearID
                        UNION ALL
                        SELECT 1 FROM ReceiptVouchers WHERE FiscalYearID = @FiscalYearID
                        UNION ALL
                        SELECT 1 FROM PaymentVouchers WHERE FiscalYearID = @FiscalYearID
                        UNION ALL
                        SELECT 1 FROM Expenses WHERE FiscalYearID = @FiscalYearID
                    ) AS Transactions";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@FiscalYearID", fiscalYearId);
                        int count = Convert.ToInt32(command.ExecuteScalar());
                        return count > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في التحقق من وجود معاملات: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إلغاء تفعيل جميع السنوات المالية
        /// </summary>
        private static void DeactivateAllFiscalYears(SqlConnection connection, SqlTransaction transaction, int? excludeFiscalYearId = null)
        {
            string query = "UPDATE FiscalYears SET IsActive = 0";
            if (excludeFiscalYearId.HasValue)
            {
                query += " WHERE FiscalYearID != @ExcludeFiscalYearID";
            }

            using (var command = new SqlCommand(query, connection, transaction))
            {
                if (excludeFiscalYearId.HasValue)
                {
                    command.Parameters.AddWithValue("@ExcludeFiscalYearID", excludeFiscalYearId.Value);
                }
                command.ExecuteNonQuery();
            }
        }

        /// <summary>
        /// الحصول على معرف السنة المالية النشطة
        /// </summary>
        /// <returns>معرف السنة المالية النشطة</returns>
        public static int GetActiveFiscalYearId()
        {
            var activeFiscalYear = GetActiveFiscalYear();
            return activeFiscalYear?.FiscalYearID ?? 1;
        }

        #endregion
    }
}
