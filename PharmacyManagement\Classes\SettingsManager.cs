using System;
using System.Configuration;
using System.Globalization;
using System.Collections.Generic;
using System.IO;
using System.Windows.Forms;
using System.Xml;

namespace PharmacyManagement.Classes
{
    /// <summary>
    /// مدير الإعدادات - Settings Manager
    /// </summary>
    public static class SettingsManager
    {
        /// <summary>
        /// الحصول على إعداد نصي
        /// </summary>
        /// <param name="key">المفتاح</param>
        /// <param name="defaultValue">القيمة الافتراضية</param>
        /// <returns>القيمة</returns>
        public static string GetSetting(string key, string defaultValue = "")
        {
            try
            {
                var value = ConfigurationManager.AppSettings[key];
                return string.IsNullOrEmpty(value) ? defaultValue : value;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على الإعداد {key}: {ex.Message}");
                return defaultValue;
            }
        }

        /// <summary>
        /// الحصول على إعداد منطقي
        /// </summary>
        /// <param name="key">المفتاح</param>
        /// <param name="defaultValue">القيمة الافتراضية</param>
        /// <returns>القيمة</returns>
        public static bool GetBoolSetting(string key, bool defaultValue = false)
        {
            try
            {
                var value = GetSetting(key, defaultValue.ToString());
                return bool.TryParse(value, out bool result) ? result : defaultValue;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على الإعداد المنطقي {key}: {ex.Message}");
                return defaultValue;
            }
        }

        /// <summary>
        /// الحصول على إعداد رقمي
        /// </summary>
        /// <param name="key">المفتاح</param>
        /// <param name="defaultValue">القيمة الافتراضية</param>
        /// <returns>القيمة</returns>
        public static int GetIntSetting(string key, int defaultValue = 0)
        {
            try
            {
                var value = GetSetting(key, defaultValue.ToString());
                return int.TryParse(value, out int result) ? result : defaultValue;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على الإعداد الرقمي {key}: {ex.Message}");
                return defaultValue;
            }
        }

        /// <summary>
        /// الحصول على إعداد تاريخ
        /// </summary>
        /// <param name="key">المفتاح</param>
        /// <param name="defaultValue">القيمة الافتراضية</param>
        /// <returns>القيمة</returns>
        public static DateTime GetDateTimeSetting(string key, DateTime defaultValue)
        {
            try
            {
                var value = GetSetting(key, defaultValue.ToString(CultureInfo.InvariantCulture));
                return DateTime.TryParse(value, out DateTime result) ? result : defaultValue;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على إعداد التاريخ {key}: {ex.Message}");
                return defaultValue;
            }
        }

        /// <summary>
        /// حفظ إعداد
        /// </summary>
        /// <param name="key">المفتاح</param>
        /// <param name="value">القيمة</param>
        public static void SetSetting(string key, string value)
        {
            try
            {
                var config = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);
                
                if (config.AppSettings.Settings[key] != null)
                {
                    config.AppSettings.Settings[key].Value = value;
                }
                else
                {
                    config.AppSettings.Settings.Add(key, value);
                }
                
                config.Save(ConfigurationSaveMode.Modified);
                ConfigurationManager.RefreshSection("appSettings");
                
                LogManager.LogInfo("تم حفظ الإعداد " + key + " = " + value);
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في حفظ الإعداد " + key + ": " + ex.Message);
            }
        }

        /// <summary>
        /// حذف إعداد
        /// </summary>
        /// <param name="key">المفتاح</param>
        public static void RemoveSetting(string key)
        {
            try
            {
                var config = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);
                
                if (config.AppSettings.Settings[key] != null)
                {
                    config.AppSettings.Settings.Remove(key);
                    config.Save(ConfigurationSaveMode.Modified);
                    ConfigurationManager.RefreshSection("appSettings");
                    
                    LogManager.LogInfo("تم حذف الإعداد " + key);
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في حذف الإعداد " + key + ": " + ex.Message);
            }
        }

        /// <summary>
        /// التحقق من وجود إعداد
        /// </summary>
        /// <param name="key">المفتاح</param>
        /// <returns>موجود أم لا</returns>
        public static bool HasSetting(string key)
        {
            try
            {
                return ConfigurationManager.AppSettings[key] != null;
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في التحقق من الإعداد " + key + ": " + ex.Message);
                return false;
            }
        }

        /// <summary>
        /// إعادة تعيين جميع الإعدادات
        /// </summary>
        public static void ResetAllSettings()
        {
            try
            {
                var config = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);
                config.AppSettings.Settings.Clear();
                config.Save(ConfigurationSaveMode.Modified);
                ConfigurationManager.RefreshSection("appSettings");
                
                LogManager.LogInfo("تم إعادة تعيين جميع الإعدادات");
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في إعادة تعيين الإعدادات: " + ex.Message);
            }
        }

        /// <summary>
        /// إعادة تعيين جميع الإعدادات للقيم الافتراضية
        /// </summary>
        public static void ResetToDefaults()
        {
            try
            {
                // إعدادات عامة
                SetSetting("PharmacyName", "صيدلية الشفاء");
                SetSetting("PharmacyAddress", "");
                SetSetting("PharmacyPhone", "");
                SetSetting("PharmacyEmail", "");
                SetSetting("LicenseNumber", "");

                // إعدادات النظام
                SetSetting("AutoBackup", "True");
                SetSetting("BackupInterval", "24");
                SetSetting("LowStockAlert", "True");
                SetSetting("LowStockThreshold", "10");

                // إعدادات الأمان
                SetSetting("PasswordExpiry", "False");
                SetSetting("PasswordExpiryDays", "90");
                SetSetting("LimitLoginAttempts", "True");
                SetSetting("MaxLoginAttempts", "3");

                // إعدادات الطباعة
                SetSetting("AutoPrintInvoice", "False");
                SetSetting("ReceiptHeader", "");
                SetSetting("ReceiptFooter", "شكراً لزيارتكم");

                // إعدادات الضرائب
                SetSetting("EnableTax", "False");
                SetSetting("TaxRate", "0");
                SetSetting("TaxNumber", "");

                // إعدادات العملات
                SetSetting("BaseCurrency", "YER");
                SetSetting("EnableMultiCurrency", "False");

                LogManager.LogInfo("تم إعادة تعيين جميع الإعدادات للقيم الافتراضية");
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في إعادة تعيين الإعدادات: " + ex.Message);
                throw;
            }
        }

        /// <summary>
        /// التحقق من صحة الإعدادات
        /// </summary>
        /// <returns>true إذا كانت الإعدادات صحيحة</returns>
        public static bool ValidateSettings()
        {
            try
            {
                // التحقق من الإعدادات الأساسية
                var pharmacyName = GetSetting("PharmacyName");
                if (string.IsNullOrWhiteSpace(pharmacyName))
                {
                    return false;
                }

                // التحقق من إعدادات النظام
                var backupInterval = GetIntSetting("BackupInterval", 24);
                if (backupInterval < 1 || backupInterval > 168) // من ساعة إلى أسبوع
                {
                    return false;
                }

                var lowStockThreshold = GetIntSetting("LowStockThreshold", 10);
                if (lowStockThreshold < 0)
                {
                    return false;
                }

                // التحقق من إعدادات الأمان
                var maxAttempts = GetIntSetting("MaxLoginAttempts", 3);
                if (maxAttempts < 1 || maxAttempts > 10)
                {
                    return false;
                }

                var passwordDays = GetIntSetting("PasswordExpiryDays", 90);
                if (passwordDays < 1 || passwordDays > 365)
                {
                    return false;
                }

                // التحقق من إعدادات الضرائب
                var taxRate = GetDecimalSetting("TaxRate", 0);
                if (taxRate < 0 || taxRate > 100)
                {
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في التحقق من صحة الإعدادات: " + ex.Message);
                return false;
            }
        }

        /// <summary>
        /// تصدير الإعدادات إلى ملف
        /// </summary>
        /// <param name="filePath">مسار الملف</param>
        public static void ExportSettings(string filePath)
        {
            try
            {
                var settings = new System.Text.StringBuilder();
                settings.AppendLine("# إعدادات نظام إدارة الصيدلية");
                settings.AppendLine("# تم التصدير في: " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                settings.AppendLine();

                // إعدادات عامة
                settings.AppendLine("[General]");
                settings.AppendLine("PharmacyName=" + GetSetting("PharmacyName"));
                settings.AppendLine("PharmacyAddress=" + GetSetting("PharmacyAddress"));
                settings.AppendLine("PharmacyPhone=" + GetSetting("PharmacyPhone"));
                settings.AppendLine("PharmacyEmail=" + GetSetting("PharmacyEmail"));
                settings.AppendLine("LicenseNumber=" + GetSetting("LicenseNumber"));
                settings.AppendLine();

                // إعدادات النظام
                settings.AppendLine("[System]");
                settings.AppendLine("AutoBackup=" + GetSetting("AutoBackup"));
                settings.AppendLine("BackupInterval=" + GetSetting("BackupInterval"));
                settings.AppendLine("LowStockAlert=" + GetSetting("LowStockAlert"));
                settings.AppendLine("LowStockThreshold=" + GetSetting("LowStockThreshold"));
                settings.AppendLine();

                // إعدادات الأمان
                settings.AppendLine("[Security]");
                settings.AppendLine("PasswordExpiry=" + GetSetting("PasswordExpiry"));
                settings.AppendLine("PasswordExpiryDays=" + GetSetting("PasswordExpiryDays"));
                settings.AppendLine("LimitLoginAttempts=" + GetSetting("LimitLoginAttempts"));
                settings.AppendLine("MaxLoginAttempts=" + GetSetting("MaxLoginAttempts"));
                settings.AppendLine();

                // إعدادات الطباعة
                settings.AppendLine("[Printing]");
                settings.AppendLine("AutoPrintInvoice=" + GetSetting("AutoPrintInvoice"));
                settings.AppendLine("ReceiptHeader=" + GetSetting("ReceiptHeader"));
                settings.AppendLine("ReceiptFooter=" + GetSetting("ReceiptFooter"));
                settings.AppendLine();

                // إعدادات الضرائب
                settings.AppendLine("[Tax]");
                settings.AppendLine("EnableTax=" + GetSetting("EnableTax"));
                settings.AppendLine("TaxRate=" + GetSetting("TaxRate"));
                settings.AppendLine("TaxNumber=" + GetSetting("TaxNumber"));
                settings.AppendLine();

                // إعدادات العملات
                settings.AppendLine("[Currency]");
                settings.AppendLine("BaseCurrency=" + GetSetting("BaseCurrency"));
                settings.AppendLine("EnableMultiCurrency=" + GetSetting("EnableMultiCurrency"));

                System.IO.File.WriteAllText(filePath, settings.ToString(), System.Text.Encoding.UTF8);
                LogManager.LogInfo("تم تصدير الإعدادات إلى: " + filePath);
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في تصدير الإعدادات: " + ex.Message);
                throw;
            }
        }

        /// <summary>
        /// تعيين قيمة إعداد مؤقت (للإعدادات المؤقتة)
        /// </summary>
        /// <param name="key">المفتاح</param>
        /// <param name="value">القيمة</param>
        public static void SetTempSetting(string key, string value)
        {
            try
            {
                // حفظ مؤقت في ملف XML منفصل
                var settingsPath = Path.Combine(Application.StartupPath, "Data", "UserSettings.xml");
                var dataDir = Path.GetDirectoryName(settingsPath);

                if (!Directory.Exists(dataDir))
                {
                    Directory.CreateDirectory(dataDir);
                }

                XmlDocument doc = new XmlDocument();

                if (File.Exists(settingsPath))
                {
                    doc.Load(settingsPath);
                }
                else
                {
                    var declaration = doc.CreateXmlDeclaration("1.0", "UTF-8", null);
                    doc.AppendChild(declaration);
                    var root = doc.CreateElement("UserSettings");
                    doc.AppendChild(root);
                }

                var settingsNode = doc.SelectSingleNode("UserSettings");
                var existingNode = settingsNode.SelectSingleNode($"Setting[@key='{key}']");

                if (existingNode != null)
                {
                    existingNode.Attributes["value"].Value = value;
                }
                else
                {
                    var newNode = doc.CreateElement("Setting");
                    newNode.SetAttribute("key", key);
                    newNode.SetAttribute("value", value);
                    settingsNode.AppendChild(newNode);
                }

                doc.Save(settingsPath);
                LogManager.LogInfo("تم حفظ الإعداد المؤقت " + key + " = " + value);
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في حفظ الإعداد المؤقت " + key + ": " + ex.Message);
            }
        }

        /// <summary>
        /// الحصول على إعداد محفوظ مؤقتاً
        /// </summary>
        /// <param name="key">المفتاح</param>
        /// <param name="defaultValue">القيمة الافتراضية</param>
        /// <returns>القيمة</returns>
        public static string GetUserSetting(string key, string defaultValue = "")
        {
            try
            {
                var settingsPath = Path.Combine(Application.StartupPath, "Data", "UserSettings.xml");

                if (!File.Exists(settingsPath))
                {
                    return defaultValue;
                }

                var doc = new XmlDocument();
                doc.Load(settingsPath);

                var node = doc.SelectSingleNode("UserSettings/Setting[@key='" + key + "']");
                return node != null && node.Attributes["value"] != null ? node.Attributes["value"].Value : defaultValue;
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في قراءة الإعداد المؤقت " + key + ": " + ex.Message);
                return defaultValue;
            }
        }

        /// <summary>
        /// الحصول على إعداد رقمي عشري
        /// </summary>
        /// <param name="key">المفتاح</param>
        /// <param name="defaultValue">القيمة الافتراضية</param>
        /// <returns>القيمة</returns>
        public static decimal GetDecimalSetting(string key, decimal defaultValue = 0)
        {
            try
            {
                string value = GetSetting(key);
                decimal result;
                if (decimal.TryParse(value, out result))
                {
                    return result;
                }
                return defaultValue;
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في قراءة الإعداد العشري " + key + ": " + ex.Message);
                return defaultValue;
            }
        }
    }
}
