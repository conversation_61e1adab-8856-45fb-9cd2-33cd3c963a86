using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using PharmacyManagement.Models;

namespace PharmacyManagement.Classes
{
    /// <summary>
    /// مدير التصدير - Export Manager
    /// </summary>
    public static class ExportManager
    {
        /// <summary>
        /// تصدير العملات إلى CSV
        /// </summary>
        /// <param name="currencies">قائمة العملات</param>
        /// <param name="filePath">مسار الملف</param>
        public static void ExportCurrencies(List<Currency> currencies, string filePath)
        {
            try
            {
                var csv = new StringBuilder();
                
                // إضافة العناوين
                csv.AppendLine("كود العملة,اسم العملة,رمز العملة,سعر الصرف,عملة أساسية,نشط,تاريخ الإنشاء");
                
                // إضافة البيانات
                foreach (var currency in currencies)
                {
                    csv.AppendLine($"{currency.CurrencyCode},{currency.CurrencyName},{currency.CurrencySymbol}," +
                                 $"{currency.ExchangeRate},{(currency.IsBaseCurrency ? "نعم" : "لا")}," +
                                 $"{(currency.IsActive ? "نعم" : "لا")},{currency.CreatedDate:yyyy-MM-dd}");
                }
                
                File.WriteAllText(filePath, csv.ToString(), Encoding.UTF8);
                LogManager.LogInfo($"تم تصدير {currencies.Count} عملة إلى {filePath}");
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تصدير العملات: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// تصدير الأدوية إلى CSV
        /// </summary>
        /// <param name="drugs">قائمة الأدوية</param>
        /// <param name="filePath">مسار الملف</param>
        public static void ExportDrugs(List<Drug> drugs, string filePath)
        {
            try
            {
                var csv = new StringBuilder();
                
                // إضافة العناوين
                csv.AppendLine("كود الدواء,الاسم التجاري,الاسم العلمي,الفئة,الشركة المصنعة,سعر الشراء,سعر البيع,المخزون الحالي,الحد الأدنى,الحد الأقصى,تاريخ الانتهاء");
                
                // إضافة البيانات
                foreach (var drug in drugs)
                {
                    csv.AppendLine($"{drug.DrugCode},{drug.DrugName},{drug.ScientificName}," +
                                 $"{drug.CategoryName},{drug.ManufacturerName},{drug.PurchasePrice}," +
                                 $"{drug.SalePrice},{drug.CurrentStock},{drug.MinStockLevel}," +
                                 $"{drug.MaxStockLevel}");
                }
                
                File.WriteAllText(filePath, csv.ToString(), Encoding.UTF8);
                LogManager.LogInfo($"تم تصدير {drugs.Count} دواء إلى {filePath}");
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تصدير الأدوية: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// تصدير العملاء إلى CSV
        /// </summary>
        /// <param name="customers">قائمة العملاء</param>
        /// <param name="filePath">مسار الملف</param>
        public static void ExportCustomers(List<Customer> customers, string filePath)
        {
            try
            {
                var csv = new StringBuilder();
                
                // إضافة العناوين
                csv.AppendLine("كود العميل,الاسم,الهاتف,البريد الإلكتروني,العنوان,تاريخ التسجيل,إجمالي المشتريات");
                
                // إضافة البيانات
                foreach (var customer in customers)
                {
                    csv.AppendLine($"{customer.CustomerCode},{customer.CustomerName},{customer.Phone}," +
                                 $"{customer.Email},{customer.Address},{customer.RegistrationDate:yyyy-MM-dd}," +
                                 $"{customer.TotalPurchases}");
                }
                
                File.WriteAllText(filePath, csv.ToString(), Encoding.UTF8);
                LogManager.LogInfo($"تم تصدير {customers.Count} عميل إلى {filePath}");
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تصدير العملاء: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// تصدير البيانات العامة إلى CSV
        /// </summary>
        /// <param name="data">البيانات</param>
        /// <param name="headers">العناوين</param>
        /// <param name="filePath">مسار الملف</param>
        public static void ExportToCSV(List<string[]> data, string[] headers, string filePath)
        {
            try
            {
                var csv = new StringBuilder();

                // إضافة العناوين
                if (headers != null && headers.Length > 0)
                {
                    csv.AppendLine(string.Join(",", headers));
                }

                // إضافة البيانات
                foreach (var row in data)
                {
                    csv.AppendLine(string.Join(",", row));
                }

                File.WriteAllText(filePath, csv.ToString(), Encoding.UTF8);
                LogManager.LogInfo($"تم تصدير {data.Count} سجل إلى {filePath}");
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في التصدير: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// تصدير البيانات المالية إلى CSV
        /// </summary>
        /// <param name="financialData">البيانات المالية</param>
        /// <param name="filePath">مسار الملف</param>
        /// <returns>true إذا تم التصدير بنجاح</returns>
        public static bool ExportFinancialData(object financialData, string filePath)
        {
            try
            {
                if (financialData == null)
                    return false;

                var csv = new StringBuilder();

                // إضافة عنوان التقرير
                csv.AppendLine("التقرير المالي");
                csv.AppendLine($"تاريخ التقرير: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                csv.AppendLine("");

                // تحديد نوع البيانات وتصديرها
                if (financialData is FinancialSummary summary)
                {
                    ExportFinancialSummary(summary, csv);
                }
                else if (financialData is List<FinancialTransaction> transactions)
                {
                    ExportFinancialTransactions(transactions, csv);
                }
                else if (financialData is List<MonthlyCashFlow> cashFlow)
                {
                    ExportMonthlyCashFlow(cashFlow, csv);
                }
                else
                {
                    csv.AppendLine("نوع البيانات غير مدعوم");
                }

                File.WriteAllText(filePath, csv.ToString(), Encoding.UTF8);
                LogManager.LogInfo($"تم تصدير البيانات المالية إلى {filePath}");
                return true;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تصدير البيانات المالية: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تصدير الملخص المالي
        /// </summary>
        private static void ExportFinancialSummary(FinancialSummary summary, StringBuilder csv)
        {
            csv.AppendLine("الملخص المالي");
            csv.AppendLine("البند,المبلغ");
            csv.AppendLine($"إجمالي المبيعات,{summary.TotalSales:F2}");
            csv.AppendLine($"إجمالي المشتريات,{summary.TotalPurchases:F2}");
            csv.AppendLine($"إجمالي المصروفات,{summary.TotalExpenses:F2}");
            csv.AppendLine($"صافي الربح,{summary.NetProfit:F2}");
            // Properties removed - not available in FinancialSummary model
        }

        /// <summary>
        /// تصدير المعاملات المالية
        /// </summary>
        private static void ExportFinancialTransactions(List<FinancialTransaction> transactions, StringBuilder csv)
        {
            csv.AppendLine("المعاملات المالية");
            csv.AppendLine("التاريخ,النوع,الوصف,المبلغ,الرصيد,المرجع");

            foreach (var transaction in transactions)
            {
                var reference = "";
                if (transaction.ReferenceNumber != null)
                    reference = transaction.ReferenceNumber;

                csv.AppendLine(transaction.TransactionDate.ToString("yyyy-MM-dd") + "," + transaction.TransactionType + "," +
                             transaction.Description + "," + transaction.Amount.ToString("F2") + "," +
                             transaction.Balance.ToString("F2") + "," + reference);
            }
        }

        /// <summary>
        /// تصدير التدفق النقدي الشهري
        /// </summary>
        private static void ExportMonthlyCashFlow(List<MonthlyCashFlow> cashFlow, StringBuilder csv)
        {
            csv.AppendLine("التدفق النقدي الشهري");
            csv.AppendLine("الشهر,السنة,المبيعات,المشتريات,المصروفات,صافي التدفق");

            foreach (var flow in cashFlow)
            {
                csv.AppendLine(flow.Month.ToString() + "," + flow.Year.ToString() + "," + flow.Sales.ToString("F2") + "," +
                             flow.Purchases.ToString("F2") + "," + flow.Expenses.ToString("F2") + "," + flow.NetCashFlow.ToString("F2"));
            }
        }

        /// <summary>
        /// تصدير المخزون إلى CSV
        /// </summary>
        /// <param name="inventory">قائمة المخزون</param>
        /// <param name="filePath">مسار الملف</param>
        public static void ExportInventory(List<InventoryItem> inventory, string filePath)
        {
            try
            {
                var csv = new StringBuilder();

                // إضافة العناوين
                csv.AppendLine("كود الدواء,اسم الدواء,الفئة,الشركة المصنعة,سعر الشراء,سعر البيع,المخزون الحالي,الحد الأدنى,الحد الأقصى,تاريخ الانتهاء,حالة المخزون,حالة الصلاحية");

                // إضافة البيانات
                foreach (var item in inventory)
                {
                    csv.AppendLine($"{item.DrugCode},{item.DrugName},{item.Category},{item.Manufacturer}," +
                                 $"{item.UnitPrice},{item.SellingPrice},{item.CurrentStock}," +
                                 $"{item.MinimumStock},{item.MaximumStock}," +
                                 $"{item.ExpiryDate?.ToString("yyyy-MM-dd") ?? "غير محدد"}," +
                                 $"{item.StockStatus},{item.ExpiryStatus}");
                }

                File.WriteAllText(filePath, csv.ToString(), Encoding.UTF8);
                LogManager.LogInfo($"تم تصدير {inventory.Count} عنصر مخزون إلى {filePath}");
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تصدير المخزون: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// تصدير بيانات المبيعات
        /// </summary>
        /// <param name="salesData">بيانات المبيعات</param>
        /// <param name="filePath">مسار الملف</param>
        public static void ExportSales(object salesData, string filePath)
        {
            try
            {
                // منطق تصدير المبيعات
                LogManager.LogInfo("تم تصدير بيانات المبيعات إلى: " + filePath);
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في تصدير المبيعات: " + ex.Message);
            }
        }
    }
}
