using System;
using System.Collections.Generic;
using System.IO;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement
{
    /// <summary>
    /// فئة اختبار لوظائف التصدير في تاريخ العميل
    /// </summary>
    public static class TestCustomerHistoryExport
    {
        /// <summary>
        /// اختبار تصدير تاريخ العميل إلى CSV
        /// </summary>
        public static void TestExportToCSV()
        {
            try
            {
                Console.WriteLine("بدء اختبار تصدير CSV...");
                
                // إنشاء عميل تجريبي
                var customer = new Customer
                {
                    CustomerID = 1,
                    CustomerCode = "CUST001",
                    CustomerName = "أحمد محمد علي",
                    Phone = "01234567890",
                    Mobile = "01987654321",
                    Email = "<EMAIL>",
                    Address = "الرياض، المملكة العربية السعودية"
                };
                
                // إنشاء فواتير تجريبية
                var invoices = new List<Invoice>
                {
                    new Invoice
                    {
                        InvoiceID = 1,
                        InvoiceNumber = "INV001",
                        InvoiceDate = DateTime.Now.AddDays(-30),
                        TotalAmount = 1500.00m,
                        PaidAmount = 1500.00m,
                        RemainingAmount = 0,
                        PaymentMethod = "نقدي",
                        Status = "مدفوعة",
                        Notes = "فاتورة مدفوعة بالكامل"
                    },
                    new Invoice
                    {
                        InvoiceID = 2,
                        InvoiceNumber = "INV002",
                        InvoiceDate = DateTime.Now.AddDays(-15),
                        TotalAmount = 2250.00m,
                        PaidAmount = 1000.00m,
                        RemainingAmount = 1250.00m,
                        PaymentMethod = "آجل",
                        Status = "معلقة",
                        Notes = "دفع جزئي"
                    }
                };
                
                // إحصائيات تجريبية
                var statistics = new Dictionary<string, string>
                {
                    { "إجمالي الفواتير", "2" },
                    { "إجمالي المبلغ", "3,750.00 ر.ي" },
                    { "أول فاتورة", "2024/11/28" },
                    { "آخر فاتورة", "2024/12/13" }
                };
                
                // مسار الملف التجريبي
                string filePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), 
                                             $"اختبار_تصدير_CSV_{DateTime.Now.ToString("yyyyMMdd_HHmmss")}.csv");
                
                // تصدير البيانات
                ExportHelper.ExportCustomerHistoryToCSV(customer, invoices, statistics, filePath);
                
                Console.WriteLine($"تم تصدير الملف بنجاح: {filePath}");
                Console.WriteLine("تم اختبار تصدير CSV بنجاح!");
                
                // فتح الملف
                if (File.Exists(filePath))
                {
                    Console.WriteLine("فتح الملف...");
                    ExportHelper.OpenExportedFile(filePath);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في اختبار تصدير CSV: {ex.Message}");
            }
        }
        
        /// <summary>
        /// اختبار تصدير تاريخ العميل إلى HTML
        /// </summary>
        public static void TestExportToHTML()
        {
            try
            {
                Console.WriteLine("بدء اختبار تصدير HTML...");
                
                // إنشاء عميل تجريبي
                var customer = new Customer
                {
                    CustomerID = 1,
                    CustomerCode = "CUST001",
                    CustomerName = "فاطمة أحمد محمد",
                    Phone = "01234567890",
                    Mobile = "01987654321",
                    Email = "<EMAIL>",
                    Address = "جدة، المملكة العربية السعودية"
                };
                
                // إنشاء فواتير تجريبية
                var invoices = new List<Invoice>
                {
                    new Invoice
                    {
                        InvoiceID = 1,
                        InvoiceNumber = "INV001",
                        InvoiceDate = DateTime.Now.AddDays(-45),
                        TotalAmount = 850.00m,
                        PaidAmount = 850.00m,
                        RemainingAmount = 0,
                        PaymentMethod = "بطاقة ائتمان",
                        Status = "مدفوعة"
                    },
                    new Invoice
                    {
                        InvoiceID = 2,
                        InvoiceNumber = "INV002",
                        InvoiceDate = DateTime.Now.AddDays(-20),
                        TotalAmount = 1200.00m,
                        PaidAmount = 600.00m,
                        RemainingAmount = 600.00m,
                        PaymentMethod = "آجل",
                        Status = "معلقة"
                    },
                    new Invoice
                    {
                        InvoiceID = 3,
                        InvoiceNumber = "INV003",
                        InvoiceDate = DateTime.Now.AddDays(-5),
                        TotalAmount = 750.00m,
                        PaidAmount = 0,
                        RemainingAmount = 750.00m,
                        PaymentMethod = "آجل",
                        Status = "غير مدفوعة"
                    }
                };
                
                // إحصائيات تجريبية
                var statistics = new Dictionary<string, string>
                {
                    { "إجمالي الفواتير", "3" },
                    { "إجمالي المبلغ", "2,800.00 ر.ي" },
                    { "أول فاتورة", "2024/11/13" },
                    { "آخر فاتورة", "2024/12/23" }
                };
                
                // مسار الملف التجريبي
                string filePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), 
                                             $"اختبار_تصدير_HTML_{DateTime.Now.ToString("yyyyMMdd_HHmmss")}.html");
                
                // تصدير البيانات
                ExportHelper.ExportCustomerHistoryToHTML(customer, invoices, statistics, filePath);
                
                Console.WriteLine($"تم تصدير الملف بنجاح: {filePath}");
                Console.WriteLine("تم اختبار تصدير HTML بنجاح!");
                
                // فتح الملف
                if (File.Exists(filePath))
                {
                    Console.WriteLine("فتح الملف...");
                    ExportHelper.OpenExportedFile(filePath);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في اختبار تصدير HTML: {ex.Message}");
            }
        }
        
        /// <summary>
        /// تشغيل جميع اختبارات التصدير
        /// </summary>
        public static void RunAllExportTests()
        {
            try
            {
                Console.WriteLine("=== بدء اختبارات التصدير ===");
                
                // اختبار تصدير CSV
                TestExportToCSV();
                
                Console.WriteLine("\n" + new string('-', 50) + "\n");
                
                // اختبار تصدير HTML
                TestExportToHTML();
                
                Console.WriteLine("\n=== انتهت جميع اختبارات التصدير ===");
                
                MessageBox.Show("تم تشغيل جميع اختبارات التصدير بنجاح!\nتحقق من سطح المكتب للملفات المصدرة.", 
                              "اختبارات التصدير", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تشغيل اختبارات التصدير: {ex.Message}");
                MessageBox.Show($"خطأ في اختبارات التصدير: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
