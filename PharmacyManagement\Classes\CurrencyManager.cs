
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using PharmacyManagement.Models;

namespace PharmacyManagement.Classes
{
    /// <summary>
    /// مدير العملات - Currency Manager
    /// </summary>
    public static class CurrencyManager
    {
        #region Currency Operations - عمليات العملات

        /// <summary>
        /// الحصول على جميع العملات
        /// </summary>
        /// <returns>قائمة العملات</returns>
        public static List<Currency> GetAllCurrencies()
        {
            var currencies = new List<Currency>();
            
            try
            {
                string query = @"
                    SELECT CurrencyID, CurrencyCode, CurrencyName, CurrencySymbol, 
                           ExchangeRate, IsBaseCurrency, IsActive, CreatedDate
                    FROM Currencies 
                    WHERE IsActive = 1
                    ORDER BY IsBaseCurrency DESC, CurrencyName";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                currencies.Add(new Currency
                                {
                                    CurrencyID = Convert.ToInt32(reader["CurrencyID"]),
                                    CurrencyCode = reader["CurrencyCode"].ToString(),
                                    CurrencyName = reader["CurrencyName"].ToString(),
                                    CurrencySymbol = reader["CurrencySymbol"].ToString(),
                                    ExchangeRate = Convert.ToDecimal(reader["ExchangeRate"]),
                                    IsBaseCurrency = Convert.ToBoolean(reader["IsBaseCurrency"]),
                                    IsActive = Convert.ToBoolean(reader["IsActive"]),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"])
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على العملات: {ex.Message}");
            }
            
            return currencies;
        }

        /// <summary>
        /// الحصول على العملة الأساسية
        /// </summary>
        /// <returns>العملة الأساسية</returns>
        public static Currency GetBaseCurrency()
        {
            try
            {
                string query = @"
                    SELECT CurrencyID, CurrencyCode, CurrencyName, CurrencySymbol, 
                           ExchangeRate, IsBaseCurrency, IsActive, CreatedDate
                    FROM Currencies 
                    WHERE IsBaseCurrency = 1 AND IsActive = 1";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return new Currency
                                {
                                    CurrencyID = Convert.ToInt32(reader["CurrencyID"]),
                                    CurrencyCode = reader["CurrencyCode"].ToString(),
                                    CurrencyName = reader["CurrencyName"].ToString(),
                                    CurrencySymbol = reader["CurrencySymbol"].ToString(),
                                    ExchangeRate = Convert.ToDecimal(reader["ExchangeRate"]),
                                    IsBaseCurrency = Convert.ToBoolean(reader["IsBaseCurrency"]),
                                    IsActive = Convert.ToBoolean(reader["IsActive"]),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"])
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على العملة الأساسية: {ex.Message}");
            }
            
            // إرجاع الريال اليمني كعملة افتراضية
            return new Currency
            {
                CurrencyID = 1,
                CurrencyCode = "YER",
                CurrencyName = "ريال يمني",
                CurrencySymbol = "ر.ي",
                ExchangeRate = 1.0m,
                IsBaseCurrency = true,
                IsActive = true
            };
        }

        /// <summary>
        /// الحصول على عملة حسب الكود
        /// </summary>
        /// <param name="currencyCode">كود العملة</param>
        /// <returns>العملة</returns>
        public static Currency GetCurrencyByCode(string currencyCode)
        {
            try
            {
                string query = @"
                    SELECT CurrencyID, CurrencyCode, CurrencyName, CurrencySymbol, 
                           ExchangeRate, IsBaseCurrency, IsActive, CreatedDate
                    FROM Currencies 
                    WHERE CurrencyCode = @CurrencyCode AND IsActive = 1";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@CurrencyCode", currencyCode);
                        
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return new Currency
                                {
                                    CurrencyID = Convert.ToInt32(reader["CurrencyID"]),
                                    CurrencyCode = reader["CurrencyCode"].ToString(),
                                    CurrencyName = reader["CurrencyName"].ToString(),
                                    CurrencySymbol = reader["CurrencySymbol"].ToString(),
                                    ExchangeRate = Convert.ToDecimal(reader["ExchangeRate"]),
                                    IsBaseCurrency = Convert.ToBoolean(reader["IsBaseCurrency"]),
                                    IsActive = Convert.ToBoolean(reader["IsActive"]),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"])
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على العملة: {ex.Message}");
            }
            
            return null;
        }

        #endregion

        #region Currency Conversion - تحويل العملات

        /// <summary>
        /// تحويل مبلغ من عملة إلى أخرى
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <param name="fromCurrencyCode">عملة المصدر</param>
        /// <param name="toCurrencyCode">عملة الهدف</param>
        /// <returns>المبلغ المحول</returns>
        public static decimal ConvertCurrency(decimal amount, string fromCurrencyCode, string toCurrencyCode)
        {
            try
            {
                if (fromCurrencyCode == toCurrencyCode)
                    return amount;

                var fromCurrency = GetCurrencyByCode(fromCurrencyCode);
                var toCurrency = GetCurrencyByCode(toCurrencyCode);

                if (fromCurrency == null || toCurrency == null)
                    return amount;

                // تحويل إلى العملة الأساسية أولاً
                decimal baseAmount = amount / fromCurrency.ExchangeRate;
                
                // ثم تحويل إلى العملة المطلوبة
                return baseAmount * toCurrency.ExchangeRate;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تحويل العملة: {ex.Message}");
                return amount;
            }
        }

        /// <summary>
        /// تحويل مبلغ إلى العملة الأساسية
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <param name="fromCurrencyCode">عملة المصدر</param>
        /// <returns>المبلغ بالعملة الأساسية</returns>
        public static decimal ConvertToBaseCurrency(decimal amount, string fromCurrencyCode)
        {
            var baseCurrency = GetBaseCurrency();
            return ConvertCurrency(amount, fromCurrencyCode, baseCurrency.CurrencyCode);
        }

        /// <summary>
        /// تحويل مبلغ من العملة الأساسية
        /// </summary>
        /// <param name="amount">المبلغ بالعملة الأساسية</param>
        /// <param name="toCurrencyCode">عملة الهدف</param>
        /// <returns>المبلغ المحول</returns>
        public static decimal ConvertFromBaseCurrency(decimal amount, string toCurrencyCode)
        {
            var baseCurrency = GetBaseCurrency();
            return ConvertCurrency(amount, baseCurrency.CurrencyCode, toCurrencyCode);
        }

        #endregion

        #region Currency Formatting - تنسيق العملات

        /// <summary>
        /// تنسيق مبلغ مع رمز العملة
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <param name="currencyCode">كود العملة</param>
        /// <returns>المبلغ منسق</returns>
        public static string FormatCurrency(decimal amount, string currencyCode = null)
        {
            try
            {
                if (string.IsNullOrEmpty(currencyCode))
                {
                    var baseCurrency = GetBaseCurrency();
                    currencyCode = baseCurrency.CurrencyCode;
                }

                var currency = GetCurrencyByCode(currencyCode);
                if (currency != null)
                {
                    return amount.ToString("N2") + " " + currency.CurrencySymbol;
                }

                return amount.ToString("N2");
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في تنسيق العملة: " + ex.Message);
                return amount.ToString("N2");
            }
        }

        /// <summary>
        /// تنسيق مبلغ بالعملة الأساسية
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <returns>المبلغ منسق بالعملة الأساسية</returns>
        public static string FormatBaseCurrency(decimal amount)
        {
            var baseCurrency = GetBaseCurrency();
            return FormatCurrency(amount, baseCurrency.CurrencyCode);
        }

        #endregion

        #region Exchange Rate Management - إدارة أسعار الصرف

        /// <summary>
        /// تحديث سعر صرف العملة
        /// </summary>
        /// <param name="currencyCode">كود العملة</param>
        /// <param name="newRate">السعر الجديد</param>
        /// <returns>نجح التحديث</returns>
        public static bool UpdateExchangeRate(string currencyCode, decimal newRate)
        {
            try
            {
                string query = @"
                    UPDATE Currencies 
                    SET ExchangeRate = @ExchangeRate, 
                        ModifiedDate = GETDATE()
                    WHERE CurrencyCode = @CurrencyCode";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@CurrencyCode", currencyCode);
                        command.Parameters.AddWithValue("@ExchangeRate", newRate);
                        
                        int rowsAffected = command.ExecuteNonQuery();
                        
                        if (rowsAffected > 0)
                        {
                            LogManager.LogInfo($"تم تحديث سعر صرف العملة {currencyCode} إلى {newRate}");
                            return true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تحديث سعر الصرف: {ex.Message}");
            }
            
            return false;
        }

        /// <summary>
        /// إنشاء العملات الافتراضية
        /// </summary>
        public static void InitializeDefaultCurrencies()
        {
            try
            {
                var defaultCurrencies = new List<Currency>
                {
                    new Currency { CurrencyCode = "YER", CurrencyName = "ريال يمني", CurrencySymbol = "ر.ي", ExchangeRate = 1.0m, IsBaseCurrency = true },
                    new Currency { CurrencyCode = "USD", CurrencyName = "دولار أمريكي", CurrencySymbol = "$", ExchangeRate = 250.0m, IsBaseCurrency = false },
                    new Currency { CurrencyCode = "SAR", CurrencyName = "ريال سعودي", CurrencySymbol = "ر.س", ExchangeRate = 66.67m, IsBaseCurrency = false },
                    new Currency { CurrencyCode = "EUR", CurrencyName = "يورو", CurrencySymbol = "€", ExchangeRate = 275.0m, IsBaseCurrency = false }
                };

                foreach (var currency in defaultCurrencies)
                {
                    AddCurrency(currency);
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في إنشاء العملات الافتراضية: {ex.Message}");
            }
        }

        /// <summary>
        /// إضافة عملة جديدة (عامة)
        /// </summary>
        /// <param name="currency">العملة</param>
        /// <returns>نجح الإضافة</returns>
        public static bool AddNewCurrency(Currency currency)
        {
            return AddCurrency(currency);
        }

        /// <summary>
        /// تحديث عملة موجودة
        /// </summary>
        /// <param name="currency">العملة</param>
        /// <returns>نجح التحديث</returns>
        public static bool UpdateCurrency(Currency currency)
        {
            try
            {
                string query = @"
                    UPDATE Currencies
                    SET CurrencyName = @CurrencyName,
                        CurrencySymbol = @CurrencySymbol,
                        ExchangeRate = @ExchangeRate,
                        IsBaseCurrency = @IsBaseCurrency,
                        IsActive = @IsActive,
                        ModifiedDate = GETDATE()
                    WHERE CurrencyCode = @CurrencyCode";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@CurrencyCode", currency.CurrencyCode);
                        command.Parameters.AddWithValue("@CurrencyName", currency.CurrencyName);
                        command.Parameters.AddWithValue("@CurrencySymbol", currency.CurrencySymbol);
                        command.Parameters.AddWithValue("@ExchangeRate", currency.ExchangeRate);
                        command.Parameters.AddWithValue("@IsBaseCurrency", currency.IsBaseCurrency);
                        command.Parameters.AddWithValue("@IsActive", currency.IsActive);

                        int result = command.ExecuteNonQuery();
                        if (result > 0)
                        {
                            LogManager.LogInfo($"تم تحديث العملة: {currency.CurrencyCode}");
                            return true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تحديث العملة: {ex.Message}");
            }

            return false;
        }

        /// <summary>
        /// تعطيل/تفعيل عملة
        /// </summary>
        /// <param name="currencyCode">كود العملة</param>
        /// <param name="isActive">حالة التفعيل</param>
        /// <returns>نجح التحديث</returns>
        public static bool ToggleCurrencyStatus(string currencyCode, bool isActive)
        {
            try
            {
                string query = @"
                    UPDATE Currencies
                    SET IsActive = @IsActive
                    WHERE CurrencyCode = @CurrencyCode";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@CurrencyCode", currencyCode);
                        command.Parameters.AddWithValue("@IsActive", isActive);

                        int result = command.ExecuteNonQuery();
                        if (result > 0)
                        {
                            string status = isActive ? "تفعيل" : "تعطيل";
                            LogManager.LogInfo($"تم {status} العملة: {currencyCode}");
                            return true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تغيير حالة العملة: {ex.Message}");
            }

            return false;
        }

        /// <summary>
        /// إضافة عملة جديدة (خاصة)
        /// </summary>
        /// <param name="currency">العملة</param>
        /// <returns>نجح الإضافة</returns>
        private static bool AddCurrency(Currency currency)
        {
            try
            {
                string query = @"
                    IF NOT EXISTS (SELECT 1 FROM Currencies WHERE CurrencyCode = @CurrencyCode)
                    BEGIN
                        INSERT INTO Currencies (CurrencyCode, CurrencyName, CurrencySymbol, ExchangeRate, IsBaseCurrency, IsActive, CreatedDate)
                        VALUES (@CurrencyCode, @CurrencyName, @CurrencySymbol, @ExchangeRate, @IsBaseCurrency, 1, GETDATE())
                    END";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@CurrencyCode", currency.CurrencyCode);
                        command.Parameters.AddWithValue("@CurrencyName", currency.CurrencyName);
                        command.Parameters.AddWithValue("@CurrencySymbol", currency.CurrencySymbol);
                        command.Parameters.AddWithValue("@ExchangeRate", currency.ExchangeRate);
                        command.Parameters.AddWithValue("@IsBaseCurrency", currency.IsBaseCurrency);
                        
                        command.ExecuteNonQuery();
                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في إضافة العملة: {ex.Message}");
                return false;
            }
        }

        #endregion
    }


}

