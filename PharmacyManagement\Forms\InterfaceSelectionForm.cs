using System;
using System.Drawing;
using System.Windows.Forms;
using PharmacyManagement.Classes;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نافذة اختيار نوع الواجهة
    /// </summary>
    public partial class InterfaceSelectionForm : Form
    {
        #region Fields

        private readonly Color _primaryColor = Color.FromArgb(41, 128, 185);
        private readonly Color _secondaryColor = Color.FromArgb(52, 73, 94);
        private readonly Color _accentColor = Color.FromArgb(46, 204, 113);
        private readonly Color _lightGray = Color.FromArgb(236, 240, 241);

        public bool UseModernInterface { get; private set; }

        #endregion

        #region Constructor

        public InterfaceSelectionForm()
        {
            InitializeComponent();
            SetupInterface();
        }

        #endregion

        #region Interface Setup

        /// <summary>
        /// إعداد واجهة النافذة
        /// </summary>
        private void SetupInterface()
        {
            // إعداد النافذة
            this.Text = "اختيار نوع الواجهة";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = _lightGray;
            this.Font = new Font("Segoe UI", 10F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            CreateHeader();
            CreateInterfaceOptions();
            CreateFooter();
        }

        /// <summary>
        /// إنشاء رأس النافذة
        /// </summary>
        private void CreateHeader()
        {
            var headerPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 100,
                BackColor = _primaryColor
            };

            var titleLabel = new Label
            {
                Text = "🏥 نظام إدارة الصيدلية اليمنية",
                Font = new Font("Segoe UI", 20F, FontStyle.Bold),
                ForeColor = Color.White,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };

            headerPanel.Controls.Add(titleLabel);
            this.Controls.Add(headerPanel);
        }

        /// <summary>
        /// إنشاء خيارات الواجهة
        /// </summary>
        private void CreateInterfaceOptions()
        {
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(50, 30, 50, 30),
                BackColor = _lightGray
            };

            // عنوان الاختيار
            var instructionLabel = new Label
            {
                Text = "اختر نوع الواجهة المفضل لديك:",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                ForeColor = _secondaryColor,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Top,
                Height = 50
            };

            // لوحة الخيارات
            var optionsPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            // الواجهة التقليدية
            var classicPanel = CreateInterfaceOption(
                "الواجهة التقليدية",
                "واجهة كلاسيكية مع قوائم وشريط أدوات تقليدي\nمناسبة للمستخدمين المعتادين على الواجهات التقليدية",
                "📋",
                false
            );
            classicPanel.Location = new Point(20, 20);

            // الواجهة الحديثة
            var modernPanel = CreateInterfaceOption(
                "الواجهة الحديثة",
                "واجهة عصرية بتصميم Flat مع قائمة جانبية وأزرار حديثة\nتصميم بسيط وأنيق مع ألوان متناسقة",
                "🎨",
                true
            );
            modernPanel.Location = new Point(400, 20);

            optionsPanel.Controls.AddRange(new Control[] { classicPanel, modernPanel });
            mainPanel.Controls.AddRange(new Control[] { instructionLabel, optionsPanel });
            this.Controls.Add(mainPanel);
        }

        /// <summary>
        /// إنشاء خيار واجهة
        /// </summary>
        private Panel CreateInterfaceOption(string title, string description, string icon, bool isModern)
        {
            var panel = new Panel
            {
                Size = new Size(320, 350),
                BackColor = Color.White,
                BorderStyle = BorderStyle.None,
                Cursor = Cursors.Hand
            };

            // إضافة ظل
            panel.Paint += (s, e) =>
            {
                var rect = panel.ClientRectangle;
                rect.Width -= 1;
                rect.Height -= 1;
                e.Graphics.DrawRectangle(new Pen(Color.FromArgb(200, 200, 200)), rect);
            };

            // أيقونة
            var iconLabel = new Label
            {
                Text = icon,
                Font = new Font("Segoe UI Emoji", 48F),
                TextAlign = ContentAlignment.MiddleCenter,
                Size = new Size(320, 80),
                Location = new Point(0, 20)
            };

            // العنوان
            var titleLabel = new Label
            {
                Text = title,
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                ForeColor = _secondaryColor,
                TextAlign = ContentAlignment.MiddleCenter,
                Size = new Size(320, 30),
                Location = new Point(0, 110)
            };

            // الوصف
            var descLabel = new Label
            {
                Text = description,
                Font = new Font("Segoe UI", 11F),
                ForeColor = _secondaryColor,
                TextAlign = ContentAlignment.MiddleCenter,
                Size = new Size(300, 80),
                Location = new Point(10, 150)
            };

            // زر الاختيار
            var selectButton = new Button
            {
                Text = "اختيار هذه الواجهة",
                Size = new Size(200, 40),
                Location = new Point(60, 280),
                BackColor = isModern ? _accentColor : _primaryColor,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                Cursor = Cursors.Hand
            };

            selectButton.FlatAppearance.BorderSize = 0;
            selectButton.Click += (s, e) => SelectInterface(isModern);

            // تأثير الماوس على اللوحة
            panel.MouseEnter += (s, e) =>
            {
                panel.BackColor = Color.FromArgb(250, 250, 250);
                selectButton.BackColor = ControlPaint.Light(selectButton.BackColor, 0.1f);
            };

            panel.MouseLeave += (s, e) =>
            {
                panel.BackColor = Color.White;
                selectButton.BackColor = isModern ? _accentColor : _primaryColor;
            };

            panel.Click += (s, e) => SelectInterface(isModern);

            panel.Controls.AddRange(new Control[] { iconLabel, titleLabel, descLabel, selectButton });
            return panel;
        }

        /// <summary>
        /// إنشاء تذييل النافذة
        /// </summary>
        private void CreateFooter()
        {
            var footerPanel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 60,
                BackColor = _secondaryColor
            };

            var noteLabel = new Label
            {
                Text = "يمكنك تغيير نوع الواجهة لاحقاً من الإعدادات",
                Font = new Font("Segoe UI", 10F),
                ForeColor = Color.White,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };

            footerPanel.Controls.Add(noteLabel);
            this.Controls.Add(footerPanel);
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// اختيار نوع الواجهة
        /// </summary>
        private void SelectInterface(bool useModern)
        {
            UseModernInterface = useModern;
            
            // حفظ الاختيار في الإعدادات
            try
            {
                SettingsManager.SetSetting("UseModernInterface", useModern.ToString());
                LogManager.LogInfo($"تم اختيار الواجهة: {(useModern ? "الحديثة" : "التقليدية")}");
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في حفظ إعدادات الواجهة: {ex.Message}");
            }

            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        #endregion
    }
}
