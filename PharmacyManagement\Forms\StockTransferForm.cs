using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using InventoryItem = PharmacyManagement.Models.InventoryItem;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نموذج تحويل المخزون المحسن - Enhanced Stock Transfer Form
    /// تصميم مسطح حديث مع بيانات تجريبية شاملة
    /// </summary>
    public partial class StockTransferForm : Form
    {
        #region Fields

        private BindingSource _transfersBindingSource;
        private BindingSource _detailsBindingSource;
        private int? _selectedTransferId;
        private int? _defaultFromWarehouseId;
        private List<StockTransferData> _sampleTransfers;
        private List<StockTransferDetail> _sampleDetails;
        private List<WarehouseData> _sampleWarehouses;

        #endregion

        #region Constructors

        /// <summary>
        /// منشئ عام
        /// </summary>
        public StockTransferForm()
        {
            InitializeComponent();
            SetupForm();
            LoadWarehouses();
            LoadData();
        }

        /// <summary>
        /// منشئ مع تحديد المخزن المرسل
        /// </summary>
        /// <param name="fromWarehouseId">معرف المخزن المرسل</param>
        public StockTransferForm(int fromWarehouseId)
        {
            InitializeComponent();
            _defaultFromWarehouseId = fromWarehouseId;
            SetupForm();
            LoadWarehouses();
            LoadData();
        }

        #endregion

        #region Setup Methods

        private void SetupForm()
        {
            // إعداد النموذج الأساسي
            this.Text = "🔄 إدارة تحويل المخزون";
            this.WindowState = FormWindowState.Maximized;
            this.BackColor = Color.White;
            this.Font = new Font("Segoe UI", 9F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // إعداد التصميم المسطح
            SetupFlatDesign();

            // إعداد الشبكات
            SetupDataGrids();

            // إعداد الأحداث
            SetupEvents();

            // تحميل البيانات التجريبية
            LoadSampleData();
        }

        private void SetupFlatDesign()
        {
            // تنسيق الأزرار المحسن
            btnNewTransfer.BackColor = Color.FromArgb(46, 204, 113);
            btnNewTransfer.ForeColor = Color.White;
            btnNewTransfer.FlatStyle = FlatStyle.Flat;
            btnNewTransfer.FlatAppearance.BorderSize = 0;
            btnNewTransfer.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            btnNewTransfer.Cursor = Cursors.Hand;

            btnApprove.BackColor = Color.FromArgb(52, 152, 219);
            btnApprove.ForeColor = Color.White;
            btnApprove.FlatStyle = FlatStyle.Flat;
            btnApprove.FlatAppearance.BorderSize = 0;
            btnApprove.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            btnApprove.Cursor = Cursors.Hand;

            btnShip.BackColor = Color.FromArgb(243, 156, 18);
            btnShip.ForeColor = Color.White;
            btnShip.FlatStyle = FlatStyle.Flat;
            btnShip.FlatAppearance.BorderSize = 0;
            btnShip.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            btnShip.Cursor = Cursors.Hand;

            btnReceive.BackColor = Color.FromArgb(26, 188, 156);
            btnReceive.ForeColor = Color.White;
            btnReceive.FlatStyle = FlatStyle.Flat;
            btnReceive.FlatAppearance.BorderSize = 0;
            btnReceive.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            btnReceive.Cursor = Cursors.Hand;

            btnCancel.BackColor = Color.FromArgb(231, 76, 60);
            btnCancel.ForeColor = Color.White;
            btnCancel.FlatStyle = FlatStyle.Flat;
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            btnCancel.Cursor = Cursors.Hand;

            btnPrint.BackColor = Color.FromArgb(155, 89, 182);
            btnPrint.ForeColor = Color.White;
            btnPrint.FlatStyle = FlatStyle.Flat;
            btnPrint.FlatAppearance.BorderSize = 0;
            btnPrint.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            btnPrint.Cursor = Cursors.Hand;

            btnRefresh.BackColor = Color.FromArgb(52, 152, 219);
            btnRefresh.ForeColor = Color.White;
            btnRefresh.FlatStyle = FlatStyle.Flat;
            btnRefresh.FlatAppearance.BorderSize = 0;
            btnRefresh.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            btnRefresh.Cursor = Cursors.Hand;
        }

        private void SetupDataGrids()
        {
            // إعداد شبكة التحويلات
            dgvTransfers.AutoGenerateColumns = false;
            dgvTransfers.AllowUserToAddRows = false;
            dgvTransfers.AllowUserToDeleteRows = false;
            dgvTransfers.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvTransfers.MultiSelect = false;
            dgvTransfers.BackgroundColor = Color.White;
            dgvTransfers.BorderStyle = BorderStyle.FixedSingle;
            dgvTransfers.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(243, 156, 18);
            dgvTransfers.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvTransfers.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            dgvTransfers.DefaultCellStyle.Font = new Font("Segoe UI", 9F);
            dgvTransfers.RowHeadersVisible = false;
            dgvTransfers.EnableHeadersVisualStyles = false;

            // أعمدة شبكة التحويلات
            dgvTransfers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TransferNumber",
                HeaderText = "رقم التحويل",
                DataPropertyName = "TransferNumber",
                Width = 120
            });

            dgvTransfers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TransferDate",
                HeaderText = "تاريخ التحويل",
                DataPropertyName = "TransferDate",
                Width = 100
            });

            dgvTransfers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "FromWarehouseName",
                HeaderText = "من مخزن",
                DataPropertyName = "FromWarehouseName",
                Width = 150
            });

            dgvTransfers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ToWarehouseName",
                HeaderText = "إلى مخزن",
                DataPropertyName = "ToWarehouseName",
                Width = 150
            });

            dgvTransfers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TransferType",
                HeaderText = "نوع التحويل",
                DataPropertyName = "TransferType",
                Width = 100
            });

            dgvTransfers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Status",
                HeaderText = "الحالة",
                DataPropertyName = "Status",
                Width = 100
            });

            dgvTransfers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "RequestedByName",
                HeaderText = "طلب بواسطة",
                DataPropertyName = "RequestedByName",
                Width = 120
            });

            // إعداد شبكة التفاصيل
            dgvDetails.AutoGenerateColumns = false;
            dgvDetails.AllowUserToAddRows = false;
            dgvDetails.AllowUserToDeleteRows = false;
            dgvDetails.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvDetails.MultiSelect = false;
            dgvDetails.BackgroundColor = Color.White;
            dgvDetails.BorderStyle = BorderStyle.FixedSingle;
            dgvDetails.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(26, 188, 156);
            dgvDetails.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvDetails.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            dgvDetails.DefaultCellStyle.Font = new Font("Segoe UI", 9F);
            dgvDetails.RowHeadersVisible = false;
            dgvDetails.EnableHeadersVisualStyles = false;

            // أعمدة شبكة التفاصيل
            dgvDetails.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "DrugName",
                HeaderText = "اسم الدواء",
                DataPropertyName = "DrugName",
                Width = 200
            });

            dgvDetails.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "BatchNumber",
                HeaderText = "رقم الدفعة",
                DataPropertyName = "BatchNumber",
                Width = 100
            });

            dgvDetails.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "RequestedQuantity",
                HeaderText = "الكمية المطلوبة",
                DataPropertyName = "RequestedQuantity",
                Width = 100
            });

            dgvDetails.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ApprovedQuantity",
                HeaderText = "الكمية الموافق عليها",
                DataPropertyName = "ApprovedQuantity",
                Width = 120
            });

            dgvDetails.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ShippedQuantity",
                HeaderText = "الكمية المشحونة",
                DataPropertyName = "ShippedQuantity",
                Width = 100
            });

            dgvDetails.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ReceivedQuantity",
                HeaderText = "الكمية المستلمة",
                DataPropertyName = "ReceivedQuantity",
                Width = 100
            });

            dgvDetails.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "UnitPrice",
                HeaderText = "سعر الوحدة",
                DataPropertyName = "UnitPrice",
                Width = 100
            });

            // إعداد مصادر البيانات
            _transfersBindingSource = new BindingSource();
            _detailsBindingSource = new BindingSource();
            dgvTransfers.DataSource = _transfersBindingSource;
            dgvDetails.DataSource = _detailsBindingSource;
        }

        private void SetupEvents()
        {
            // ربط الأحداث
            btnNewTransfer.Click += BtnNewTransfer_Click;
            btnApprove.Click += BtnApprove_Click;
            btnShip.Click += BtnShip_Click;
            btnReceive.Click += BtnReceive_Click;
            btnCancel.Click += BtnCancel_Click;
            btnPrint.Click += BtnPrint_Click;
            btnRefresh.Click += BtnRefresh_Click;
            
            dgvTransfers.SelectionChanged += DgvTransfers_SelectionChanged;
            dgvTransfers.CellFormatting += DgvTransfers_CellFormatting;
            
            cmbStatusFilter.SelectedIndexChanged += CmbStatusFilter_SelectedIndexChanged;
        }

        #endregion

        #region Load Data Methods

        private void LoadWarehouses()
        {
            try
            {
                // استخدام البيانات التجريبية إذا لم تكن متوفرة من قاعدة البيانات
                if (_sampleWarehouses?.Any() == true)
                {
                    var warehouses = _sampleWarehouses.ToList();
                    warehouses.Insert(0, new WarehouseData { WarehouseID = 0, WarehouseName = "جميع المخازن" });

                    cmbWarehouseFilter.DataSource = warehouses;
                    cmbWarehouseFilter.DisplayMember = "WarehouseName";
                    cmbWarehouseFilter.ValueMember = "WarehouseID";
                    cmbWarehouseFilter.SelectedIndex = 0;
                }
                else
                {
                    // محاولة تحميل من قاعدة البيانات
                    try
                    {
                        var warehouses = WarehouseManager.GetActiveWarehouses();

                        // إضافة خيار "جميع المخازن"
                        var allWarehouses = new List<Warehouse>
                        {
                            new Warehouse { WarehouseID = 0, WarehouseName = "جميع المخازن" }
                        };
                        allWarehouses.AddRange(warehouses);

                        cmbWarehouseFilter.DataSource = allWarehouses;
                        cmbWarehouseFilter.DisplayMember = "WarehouseName";
                        cmbWarehouseFilter.ValueMember = "WarehouseID";
                        cmbWarehouseFilter.SelectedIndex = 0;
                    }
                    catch
                    {
                        // في حالة فشل قاعدة البيانات، استخدم البيانات التجريبية
                        LoadSampleWarehouses();
                        var warehouses = _sampleWarehouses.ToList();
                        warehouses.Insert(0, new WarehouseData { WarehouseID = 0, WarehouseName = "جميع المخازن" });

                        cmbWarehouseFilter.DataSource = warehouses;
                        cmbWarehouseFilter.DisplayMember = "WarehouseName";
                        cmbWarehouseFilter.ValueMember = "WarehouseID";
                        cmbWarehouseFilter.SelectedIndex = 0;
                    }
                }

                // إعداد فلتر الحالة
                var statuses = new[]
                {
                    "جميع الحالات",
                    "Pending",
                    "Approved", 
                    "Shipped",
                    "Received",
                    "Cancelled"
                };
                cmbStatusFilter.Items.AddRange(statuses);
                cmbStatusFilter.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المخازن: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadData()
        {
            try
            {
                // تحميل التحويلات
                var transfers = StockTransferManager.GetAllStockTransfers();
                _transfersBindingSource.DataSource = transfers;
                
                UpdateButtonStates();
                UpdateStatusLabel();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadTransferDetails(int transferId)
        {
            try
            {
                var details = StockTransferManager.GetTransferDetails(transferId);
                _detailsBindingSource.DataSource = details;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تفاصيل التحويل: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Event Handlers

        private void BtnNewTransfer_Click(object sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("إنشاء تحويل جديد قيد التطوير", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
                // using (var newTransferForm = new NewStockTransferForm())
                // {
                //     if (newTransferForm.ShowDialog() == DialogResult.OK)
                //     {
                //         LoadData();
                //     }
                // }
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في إنشاء تحويل جديد: " + ex.Message, "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnApprove_Click(object sender, EventArgs e)
        {
            try
            {
                if (_selectedTransferId.HasValue)
                {
                    MessageBox.Show("الموافقة على التحويل قيد التطوير", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    // // الحصول على بيانات التحويل
                    // var transfer = GetStockTransferById(_selectedTransferId.Value);
                    // if (transfer != null)
                    // {
                    //     using (var approveForm = new ApproveTransferForm(transfer))
                    //     {
                    //         if (approveForm.ShowDialog() == DialogResult.OK)
                    //         {
                    //             LoadData();
                    //             LoadTransferDetails(_selectedTransferId.Value);
                    //         }
                    //     }
                    // }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الموافقة على التحويل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnShip_Click(object sender, EventArgs e)
        {
            try
            {
                if (_selectedTransferId.HasValue)
                {
                    MessageBox.Show("شحن التحويل قيد التطوير", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    // // الحصول على بيانات التحويل
                    // var transfer = GetStockTransferById(_selectedTransferId.Value);
                    // if (transfer != null)
                    // {
                    //     using (var shipForm = new ShipTransferForm(transfer))
                    //     {
                    //         if (shipForm.ShowDialog() == DialogResult.OK)
                    //         {
                    //             LoadData();
                    //             LoadTransferDetails(_selectedTransferId.Value);
                    //         }
                    //     }
                    // }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في شحن التحويل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnReceive_Click(object sender, EventArgs e)
        {
            try
            {
                if (_selectedTransferId.HasValue)
                {
                    MessageBox.Show("استلام التحويل قيد التطوير", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    // // الحصول على بيانات التحويل
                    // var transfer = GetStockTransferById(_selectedTransferId.Value);
                    // if (transfer != null)
                    // {
                    //     using (var receiveForm = new ReceiveTransferForm(transfer))
                    //     {
                    //         if (receiveForm.ShowDialog() == DialogResult.OK)
                    //         {
                    //             LoadData();
                    //             LoadTransferDetails(_selectedTransferId.Value);
                    //         }
                    //     }
                    // }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في استلام التحويل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            try
            {
                if (_selectedTransferId.HasValue)
                {
                    var result = MessageBox.Show("هل تريد إلغاء التحويل المحدد؟", "تأكيد الإلغاء",
                        MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    
                    if (result == DialogResult.Yes)
                    {
                        string reason = "";
                        if (MessageBox.Show("هل تريد إلغاء هذا التحويل؟", "تأكيد الإلغاء", 
                            MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                        {
                            reason = "تم الإلغاء بواسطة المستخدم";
                            
                            // Fix StockTransfer parameter - get selected transfer and use its ID
                            var selectedTransfer = dgvTransfers.CurrentRow?.DataBoundItem as StockTransfer;
                            if (selectedTransfer != null)
                            {
                                if (StockTransferManager.CancelTransfer(selectedTransfer.TransferID, 
                                    UserManager.CurrentUser?.UserID ?? 0, reason))
                                {
                                    LoadData();
                                    MessageBox.Show("تم إلغاء التحويل بنجاح", "نجح", 
                                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إلغاء التحويل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                if (_selectedTransferId.HasValue)
                {
                    var transfer = StockTransferManager.GetStockTransfer(_selectedTransferId.Value);
                    var details = StockTransferManager.GetTransferDetails(_selectedTransferId.Value);
                    
                    ReportsManager.PrintStockTransferDocument(_selectedTransferId.Value);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التحويل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadData();
        }

        private void DgvTransfers_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                if (dgvTransfers.SelectedRows.Count > 0)
                {
                    var selectedRow = dgvTransfers.SelectedRows[0];

                    // التحقق من نوع البيانات
                    if (selectedRow.DataBoundItem is StockTransfer transfer)
                    {
                        _selectedTransferId = transfer.TransferID;
                        LoadTransferDetails(transfer.TransferID);
                        UpdateButtonStates();
                    }
                    else if (selectedRow.DataBoundItem is StockTransferData transferData)
                    {
                        _selectedTransferId = transferData.TransferID;
                        LoadSampleTransferDetails(transferData.TransferID);
                        UpdateButtonStates();
                    }
                }
                else
                {
                    _selectedTransferId = null;
                    _detailsBindingSource.DataSource = null;
                    UpdateButtonStates();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تحديد التحويل: {ex.Message}");
            }
        }

        private void LoadSampleTransferDetails(int transferId)
        {
            try
            {
                var details = _sampleDetails?.Where(d => d.TransferID == transferId).ToList();
                _detailsBindingSource.DataSource = details ?? new List<StockTransferDetail>();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تحميل تفاصيل التحويل: {ex.Message}");
            }
        }

        private void DgvTransfers_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (dgvTransfers.Rows[e.RowIndex].DataBoundItem is StockTransfer transfer)
            {
                // تلوين الصف حسب الحالة
                switch (transfer.Status)
                {
                    case "Pending":
                        dgvTransfers.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.LightYellow;
                        break;
                    case "Approved":
                        dgvTransfers.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.LightBlue;
                        break;
                    case "Shipped":
                        dgvTransfers.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.LightSkyBlue;
                        break;
                    case "Received":
                        dgvTransfers.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.LightGreen;
                        break;
                    case "Cancelled":
                        dgvTransfers.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.LightGray;
                        dgvTransfers.Rows[e.RowIndex].DefaultCellStyle.ForeColor = Color.DarkGray;
                        break;
                    default:
                        dgvTransfers.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.White;
                        break;
                }
            }
        }

        private void CmbStatusFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        #endregion

        #region Helper Methods

        private void UpdateButtonStates()
        {
            bool hasSelection = _selectedTransferId.HasValue;
            
            if (hasSelection && dgvTransfers.SelectedRows.Count > 0)
            {
                var selectedRow = dgvTransfers.SelectedRows[0];
                if (selectedRow.DataBoundItem is StockTransfer transfer)
                {
                    btnApprove.Enabled = transfer.Status == "Pending";
                    btnShip.Enabled = transfer.Status == "Approved";
                    btnReceive.Enabled = transfer.Status == "Shipped";
                    btnCancel.Enabled = transfer.Status == "Pending" || transfer.Status == "Approved";
                    btnPrint.Enabled = true;
                }
            }
            else
            {
                btnApprove.Enabled = false;
                btnShip.Enabled = false;
                btnReceive.Enabled = false;
                btnCancel.Enabled = false;
                btnPrint.Enabled = false;
            }
        }

        private void UpdateStatusLabel()
        {
            var totalTransfers = _transfersBindingSource.Count;
            var pendingCount = 0;
            var approvedCount = 0;
            var shippedCount = 0;
            var receivedCount = 0;

            foreach (StockTransfer transfer in _transfersBindingSource)
            {
                switch (transfer.Status)
                {
                    case "Pending": pendingCount++; break;
                    case "Approved": approvedCount++; break;
                    case "Shipped": shippedCount++; break;
                    case "Received": receivedCount++; break;
                }
            }

            lblStatus.Text = $"إجمالي التحويلات: {totalTransfers} | معلق: {pendingCount} | موافق عليه: {approvedCount} | مشحون: {shippedCount} | مستلم: {receivedCount}";
        }

        private void ApplyFilters()
        {
            // تطبيق الفلاتر
            // سيتم تنفيذ هذا لاحق
        }

        /// <summary>
        /// الحصول على تحويل المخزون بالمعرف
        /// </summary>
        /// <param name="transferId">معرف التحويل</param>
        /// <returns>بيانات التحويل</returns>
        private Models.StockTransfer GetStockTransferById(int transferId)
        {
            try
            {
                // محاولة الحصول على البيانات من قاعدة البيانات
                return StockTransferManager.GetStockTransfer(transferId);
            }
            catch
            {
                // في حالة فشل قاعدة البيانات، إنشاء كائن افتراضي
                return new Models.StockTransfer
                {
                    TransferID = transferId,
                    TransferNumber = "TRF" + transferId.ToString("D6"),
                    TransferDate = DateTime.Now,
                    Status = "Pending",
                    CreatedDate = DateTime.Now
                };
            }
        }

        #endregion

        #region Sample Data Methods

        private void LoadSampleData()
        {
            try
            {
                LoadSampleWarehouses();
                LoadSampleTransfers();
                LoadSampleDetails();
                LoadStatusFilters();
                Console.WriteLine("تم تحميل البيانات التجريبية لتحويل المخزون بنجاح");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات التجريبية: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadSampleWarehouses()
        {
            _sampleWarehouses = new List<WarehouseData>
            {
                new WarehouseData { WarehouseID = 1, WarehouseName = "المخزن الرئيسي", Location = "الطابق الأول", IsActive = true },
                new WarehouseData { WarehouseID = 2, WarehouseName = "مخزن فرع الحصبة", Location = "شارع الحصبة", IsActive = true },
                new WarehouseData { WarehouseID = 3, WarehouseName = "مخزن فرع الستين", Location = "شارع الستين", IsActive = true },
                new WarehouseData { WarehouseID = 4, WarehouseName = "مخزن فرع الجامعة", Location = "شارع الجامعة", IsActive = true },
                new WarehouseData { WarehouseID = 5, WarehouseName = "مخزن الطوارئ", Location = "الطابق الثاني", IsActive = true }
            };
        }

        private void LoadSampleTransfers()
        {
            _sampleTransfers = new List<StockTransferData>
            {
                new StockTransferData { TransferID = 1001, TransferNumber = "TRF202401001", TransferDate = DateTime.Now.AddDays(-1), FromWarehouse = "المخزن الرئيسي", ToWarehouse = "مخزن فرع الحصبة", Status = "مكتملة", TotalItems = 5, CreatedBy = "أحمد محمد" },
                new StockTransferData { TransferID = 1002, TransferNumber = "TRF202401002", TransferDate = DateTime.Now.AddDays(-2), FromWarehouse = "مخزن فرع الحصبة", ToWarehouse = "مخزن فرع الستين", Status = "قيد الشحن", TotalItems = 3, CreatedBy = "فاطمة علي" },
                new StockTransferData { TransferID = 1003, TransferNumber = "TRF202401003", TransferDate = DateTime.Now.AddDays(-3), FromWarehouse = "المخزن الرئيسي", ToWarehouse = "مخزن فرع الجامعة", Status = "معتمدة", TotalItems = 8, CreatedBy = "محمد حسن" },
                new StockTransferData { TransferID = 1004, TransferNumber = "TRF202401004", TransferDate = DateTime.Now.AddDays(-5), FromWarehouse = "مخزن فرع الستين", ToWarehouse = "مخزن الطوارئ", Status = "قيد المراجعة", TotalItems = 2, CreatedBy = "عائشة أحمد" },
                new StockTransferData { TransferID = 1005, TransferNumber = "TRF202401005", TransferDate = DateTime.Now.AddDays(-7), FromWarehouse = "المخزن الرئيسي", ToWarehouse = "مخزن فرع الحصبة", Status = "ملغية", TotalItems = 4, CreatedBy = "يوسف إبراهيم" }
            };

            // تحميل البيانات في الشبكة
            _transfersBindingSource.DataSource = _sampleTransfers;
        }

        private void LoadSampleDetails()
        {
            _sampleDetails = new List<StockTransferDetail>
            {
                // تفاصيل التحويل 1001
                new StockTransferDetail { TransferID = 1001, DrugName = "باراسيتامول 500 مجم", RequestedQuantity = 100, ShippedQuantity = 100, ReceivedQuantity = 100, Status = "مكتملة" },
                new StockTransferDetail { TransferID = 1001, DrugName = "أموكسيسيلين 250 مجم", RequestedQuantity = 50, ShippedQuantity = 50, ReceivedQuantity = 50, Status = "مكتملة" },
                new StockTransferDetail { TransferID = 1001, DrugName = "إيبوبروفين 400 مجم", RequestedQuantity = 75, ShippedQuantity = 75, ReceivedQuantity = 75, Status = "مكتملة" },

                // تفاصيل التحويل 1002
                new StockTransferDetail { TransferID = 1002, DrugName = "أسبرين 100 مجم", RequestedQuantity = 200, ShippedQuantity = 200, ReceivedQuantity = 0, Status = "قيد الشحن" },
                new StockTransferDetail { TransferID = 1002, DrugName = "فيتامين د 1000 وحدة", RequestedQuantity = 30, ShippedQuantity = 30, ReceivedQuantity = 0, Status = "قيد الشحن" },

                // تفاصيل التحويل 1003
                new StockTransferDetail { TransferID = 1003, DrugName = "أوميجا 3 كبسولات", RequestedQuantity = 40, ShippedQuantity = 0, ReceivedQuantity = 0, Status = "معتمدة" },
                new StockTransferDetail { TransferID = 1003, DrugName = "كالسيوم + مغنيسيوم", RequestedQuantity = 60, ShippedQuantity = 0, ReceivedQuantity = 0, Status = "معتمدة" }
            };
        }

        private void LoadStatusFilters()
        {
            var statusList = new List<StatusFilter>
            {
                new StatusFilter { StatusID = 0, StatusName = "جميع الحالات" },
                new StatusFilter { StatusID = 1, StatusName = "قيد المراجعة" },
                new StatusFilter { StatusID = 2, StatusName = "معتمدة" },
                new StatusFilter { StatusID = 3, StatusName = "قيد الشحن" },
                new StatusFilter { StatusID = 4, StatusName = "مكتملة" },
                new StatusFilter { StatusID = 5, StatusName = "ملغية" }
            };

            cmbStatusFilter.DataSource = statusList;
            cmbStatusFilter.DisplayMember = "StatusName";
            cmbStatusFilter.ValueMember = "StatusID";
            cmbStatusFilter.SelectedIndex = 0;
        }

        #endregion
    }

    #region Stock Transfer Models

    /// <summary>
    /// نموذج بيانات تحويل المخزون
    /// </summary>
    public class StockTransferData
    {
        public int TransferID { get; set; }
        public string TransferNumber { get; set; }
        public DateTime TransferDate { get; set; }
        public string FromWarehouse { get; set; }
        public string ToWarehouse { get; set; }
        public string Status { get; set; }
        public int TotalItems { get; set; }
        public string CreatedBy { get; set; }
    }

    /// <summary>
    /// نموذج تفاصيل تحويل المخزون
    /// </summary>
    public class StockTransferDetail
    {
        public int TransferID { get; set; }
        public string DrugName { get; set; }
        public int RequestedQuantity { get; set; }
        public int ShippedQuantity { get; set; }
        public int ReceivedQuantity { get; set; }
        public string Status { get; set; }
    }

    /// <summary>
    /// نموذج بيانات المخزن
    /// </summary>
    public class WarehouseData
    {
        public int WarehouseID { get; set; }
        public string WarehouseName { get; set; }
        public string Location { get; set; }
        public bool IsActive { get; set; }
    }

    /// <summary>
    /// نموذج فلتر الحالة
    /// </summary>
    public class StatusFilter
    {
        public int StatusID { get; set; }
        public string StatusName { get; set; }
    }

    #endregion
}







