using System;
using System.Linq;
using System.Windows.Forms;
using PharmacyManagement.Classes;

namespace PharmacyManagement
{
    /// <summary>
    /// اختبار تحديث المخزون
    /// </summary>
    public static class TestStockUpdate
    {
        /// <summary>
        /// اختبار تحديث المخزون
        /// </summary>
        public static void TestStockUpdateProcess()
        {
            try
            {
                Console.WriteLine("=== اختبار عملية تحديث المخزون ===");
                
                // 1. جلب دواء للاختبار
                var drugs = DrugManager.GetAllDrugs();
                if (drugs.Count == 0)
                {
                    Console.WriteLine("❌ لا توجد أدوية للاختبار");
                    return;
                }
                
                var testDrug = drugs.First();
                Console.WriteLine($"الدواء المختار للاختبار: {testDrug.DrugName} (كود: {testDrug.DrugCode})");
                
                // 2. جلب المخزون الحالي
                decimal currentStock = GetCurrentStockFromInventory(testDrug.DrugID);
                Console.WriteLine($"المخزون الحالي: {currentStock}");
                
                // 3. محاكاة إضافة 100 وحدة
                Console.WriteLine("\n--- محاكاة إضافة 100 وحدة ---");
                Console.WriteLine($"المخزون قبل الإضافة: {currentStock}");
                Console.WriteLine($"الكمية المضافة: 100");
                Console.WriteLine($"المخزون المتوقع بعد الإضافة: {currentStock + 100}");
                
                // 4. محاكاة خصم 50 وحدة
                Console.WriteLine("\n--- محاكاة خصم 50 وحدة ---");
                decimal stockAfterAddition = currentStock + 100;
                Console.WriteLine($"المخزون قبل الخصم: {stockAfterAddition}");
                Console.WriteLine($"الكمية المخصومة: 50");
                Console.WriteLine($"المخزون المتوقع بعد الخصم: {stockAfterAddition - 50}");
                
                // 5. اختبار الحسابات
                Console.WriteLine("\n--- اختبار الحسابات ---");
                TestStockCalculations();
                
                Console.WriteLine("\n=== انتهى اختبار تحديث المخزون ===");
                
                MessageBox.Show("تم اختبار عملية تحديث المخزون بنجاح!\nتحقق من وحدة التحكم للتفاصيل.", 
                              "اختبار تحديث المخزون", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار تحديث المخزون: {ex.Message}");
                MessageBox.Show($"خطأ في الاختبار: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        /// <summary>
        /// جلب المخزون الحالي من جدول Inventory
        /// </summary>
        private static decimal GetCurrentStockFromInventory(int drugId)
        {
            try
            {
                string query = @"
                    SELECT ISNULL(SUM(Quantity), 0) as TotalStock
                    FROM Inventory 
                    WHERE DrugID = @DrugID AND Quantity > 0";
                
                var parameters = new[] { DatabaseHelper.CreateParameter("@DrugID", drugId) };
                
                using (var reader = DatabaseHelper.ExecuteReader(query, parameters))
                {
                    if (reader.Read())
                    {
                        return Convert.ToDecimal(reader["TotalStock"]);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في جلب المخزون: {ex.Message}");
            }
            
            return 0;
        }
        
        /// <summary>
        /// اختبار حسابات المخزون
        /// </summary>
        private static void TestStockCalculations()
        {
            Console.WriteLine("اختبار الحسابات الرياضية:");
            
            // حالات اختبار مختلفة
            var testCases = new[]
            {
                new { Current = 200m, Add = 100m, Expected = 300m, Operation = "إضافة" },
                new { Current = 150m, Add = 75m, Expected = 225m, Operation = "إضافة" },
                new { Current = 300m, Add = -50m, Expected = 250m, Operation = "خصم" },
                new { Current = 100m, Add = -25m, Expected = 75m, Operation = "خصم" }
            };
            
            foreach (var testCase in testCases)
            {
                decimal result;
                if (testCase.Add > 0)
                {
                    result = testCase.Current + testCase.Add;
                }
                else
                {
                    result = testCase.Current + testCase.Add; // Add is negative for subtraction
                }
                
                bool isCorrect = result == testCase.Expected;
                string status = isCorrect ? "✅" : "❌";
                
                Console.WriteLine($"{status} {testCase.Operation}: {testCase.Current} + ({testCase.Add}) = {result} (متوقع: {testCase.Expected})");
            }
        }
        
        /// <summary>
        /// اختبار سيناريوهات مختلفة
        /// </summary>
        public static void TestDifferentScenarios()
        {
            try
            {
                Console.WriteLine("=== اختبار سيناريوهات مختلفة ===");
                
                // سيناريو 1: إضافة إلى مخزون فارغ
                Console.WriteLine("\nسيناريو 1: إضافة إلى مخزون فارغ");
                TestScenario(0, 100, true, "إضافة 100 وحدة إلى مخزون فارغ");
                
                // سيناريو 2: إضافة إلى مخزون موجود
                Console.WriteLine("\nسيناريو 2: إضافة إلى مخزون موجود");
                TestScenario(200, 100, true, "إضافة 100 وحدة إلى مخزون 200");
                
                // سيناريو 3: خصم جزئي
                Console.WriteLine("\nسيناريو 3: خصم جزئي");
                TestScenario(300, 50, false, "خصم 50 وحدة من مخزون 300");
                
                // سيناريو 4: خصم كامل
                Console.WriteLine("\nسيناريو 4: خصم كامل");
                TestScenario(100, 100, false, "خصم 100 وحدة من مخزون 100");
                
                // سيناريو 5: محاولة خصم أكثر من المتاح (خطأ)
                Console.WriteLine("\nسيناريو 5: محاولة خصم أكثر من المتاح");
                TestScenario(50, 100, false, "محاولة خصم 100 وحدة من مخزون 50 (يجب أن يفشل)");
                
                Console.WriteLine("\n=== انتهت اختبارات السيناريوهات ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار السيناريوهات: {ex.Message}");
            }
        }
        
        /// <summary>
        /// اختبار سيناريو محدد
        /// </summary>
        private static void TestScenario(decimal currentStock, decimal quantity, bool isAddition, string description)
        {
            try
            {
                Console.WriteLine($"  {description}:");
                Console.WriteLine($"    المخزون الحالي: {currentStock}");
                Console.WriteLine($"    الكمية: {quantity}");
                Console.WriteLine($"    العملية: {(isAddition ? "إضافة" : "خصم")}");
                
                decimal expectedResult;
                bool shouldSucceed = true;
                
                if (isAddition)
                {
                    expectedResult = currentStock + quantity;
                }
                else
                {
                    expectedResult = currentStock - quantity;
                    shouldSucceed = expectedResult >= 0;
                }
                
                if (shouldSucceed)
                {
                    Console.WriteLine($"    النتيجة المتوقعة: {expectedResult} ✅");
                }
                else
                {
                    Console.WriteLine($"    النتيجة المتوقعة: فشل العملية (مخزون سالب) ❌");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"    خطأ في السيناريو: {ex.Message} ❌");
            }
        }
        
        /// <summary>
        /// اختبار شامل لتحديث المخزون
        /// </summary>
        public static void RunAllStockUpdateTests()
        {
            Console.WriteLine("=== بدء الاختبارات الشاملة لتحديث المخزون ===");
            
            TestStockUpdateProcess();
            Console.WriteLine();
            TestDifferentScenarios();
            
            Console.WriteLine("\n=== انتهت جميع اختبارات تحديث المخزون ===");
            
            MessageBox.Show("تم تشغيل جميع اختبارات تحديث المخزون بنجاح!\nتحقق من وحدة التحكم للتفاصيل.", 
                          "اختبارات تحديث المخزون", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
}
