using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using PharmacyManagement.Models;

namespace PharmacyManagement.Classes
{
    /// <summary>
    /// مدير السندات المحاسبية - Voucher Manager
    /// </summary>
    public static class VoucherManager
    {
        #region Voucher Management

        /// <summary>
        /// إضافة سند محاسبي جديد
        /// </summary>
        /// <param name="voucher">السند</param>
        /// <returns>معرف السند الجديد</returns>
        public static int AddVoucher(Voucher voucher)
        {
            try
            {
                string query = @"
                    INSERT INTO Vouchers (VoucherNumber, VoucherDate, FiscalYearID, VoucherTypeID, 
                                        Description, TotalDebit, TotalCredit, CostCenterID, 
                                        ReferenceType, ReferenceID, Status, CreatedBy, CreatedDate)
                    VALUES (@VoucherNumber, @VoucherDate, @FiscalYearID, @VoucherTypeID, 
                           @Description, @TotalDebit, @TotalCredit, @CostCenterID, 
                           @ReferenceType, @ReferenceID, @Status, @CreatedBy, @CreatedDate);
                    SELECT SCOPE_IDENTITY();";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // إدراج السند الرئيسي
                            using (var command = new SqlCommand(query, connection, transaction))
                            {
                                command.Parameters.AddWithValue("@VoucherNumber", voucher.VoucherNumber);
                                command.Parameters.AddWithValue("@VoucherDate", voucher.VoucherDate);
                                command.Parameters.AddWithValue("@FiscalYearID", voucher.FiscalYearID);
                                command.Parameters.AddWithValue("@VoucherTypeID", voucher.VoucherTypeID);
                                command.Parameters.AddWithValue("@Description", voucher.Description);
                                command.Parameters.AddWithValue("@TotalDebit", voucher.TotalDebit);
                                command.Parameters.AddWithValue("@TotalCredit", voucher.TotalCredit);
                                command.Parameters.AddWithValue("@CostCenterID", voucher.CostCenterID.HasValue ? (object)voucher.CostCenterID.Value : DBNull.Value);
                                command.Parameters.AddWithValue("@ReferenceType", voucher.ReferenceType ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@ReferenceID", voucher.ReferenceID ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Status", voucher.Status);
                                command.Parameters.AddWithValue("@CreatedBy", voucher.CreatedBy != 0 ? (object)voucher.CreatedBy : DBNull.Value);
                                command.Parameters.AddWithValue("@CreatedDate", DateTime.Now);

                                int voucherId = Convert.ToInt32(command.ExecuteScalar());

                                // إدراج تفاصيل السند
                                foreach (var detail in voucher.Details)
                                {
                                    AddVoucherDetail(voucherId, detail, connection, transaction);
                                }

                                transaction.Commit();
                                LogManager.LogActivity("إضافة سند محاسبي", $"تم إضافة السند رقم: {voucher.VoucherNumber}");
                                return voucherId;
                            }
                        }
                        catch
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في إضافة السند المحاسبي: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// تحديث سند محاسبي
        /// </summary>
        /// <param name="voucher">السند</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public static bool UpdateVoucher(Voucher voucher)
        {
            try
            {
                string query = @"
                    UPDATE Vouchers SET 
                        VoucherDate = @VoucherDate,
                        VoucherTypeID = @VoucherTypeID,
                        Description = @Description,
                        TotalDebit = @TotalDebit,
                        TotalCredit = @TotalCredit,
                        CostCenterID = @CostCenterID,
                        ReferenceType = @ReferenceType,
                        ReferenceID = @ReferenceID,
                        Status = @Status,
                        ModifiedBy = @ModifiedBy,
                        ModifiedDate = @ModifiedDate
                    WHERE VoucherID = @VoucherID";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // تحديث السند الرئيسي
                            using (var command = new SqlCommand(query, connection, transaction))
                            {
                                command.Parameters.AddWithValue("@VoucherID", voucher.VoucherID);
                                command.Parameters.AddWithValue("@VoucherDate", voucher.VoucherDate);
                                command.Parameters.AddWithValue("@VoucherTypeID", voucher.VoucherTypeID);
                                command.Parameters.AddWithValue("@Description", voucher.Description);
                                command.Parameters.AddWithValue("@TotalDebit", voucher.TotalDebit);
                                command.Parameters.AddWithValue("@TotalCredit", voucher.TotalCredit);
                                command.Parameters.AddWithValue("@CostCenterID", voucher.CostCenterID.HasValue ? (object)voucher.CostCenterID.Value : DBNull.Value);
                                command.Parameters.AddWithValue("@ReferenceType", voucher.ReferenceType ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@ReferenceID", voucher.ReferenceID ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Status", voucher.Status);
                                command.Parameters.AddWithValue("@ModifiedBy", voucher.ModifiedBy ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);

                                command.ExecuteNonQuery();
                            }

                            // حذف التفاصيل القديمة
                            DeleteVoucherDetails(voucher.VoucherID, connection, transaction);

                            // إدراج التفاصيل الجديدة
                            foreach (var detail in voucher.Details)
                            {
                                AddVoucherDetail(voucher.VoucherID, detail, connection, transaction);
                            }

                            transaction.Commit();
                            LogManager.LogActivity("تحديث سند محاسبي", $"تم تحديث السند رقم: {voucher.VoucherNumber}");
                            return true;
                        }
                        catch
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تحديث السند المحاسبي: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// حذف سند محاسبي
        /// </summary>
        /// <param name="voucherId">معرف السند</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public static bool DeleteVoucher(int voucherId)
        {
            try
            {
                string query = "DELETE FROM Vouchers WHERE VoucherID = @VoucherID";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // حذف التفاصيل أولاً
                            DeleteVoucherDetails(voucherId, connection, transaction);

                            // حذف السند الرئيسي
                            using (var command = new SqlCommand(query, connection, transaction))
                            {
                                command.Parameters.AddWithValue("@VoucherID", voucherId);
                                int rowsAffected = command.ExecuteNonQuery();

                                transaction.Commit();
                                LogManager.LogActivity("حذف سند محاسبي", $"تم حذف السند رقم: {voucherId}");
                                return rowsAffected > 0;
                            }
                        }
                        catch
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في حذف السند المحاسبي: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// الحصول على سند محاسبي
        /// </summary>
        /// <param name="voucherId">معرف السند</param>
        /// <returns>السند</returns>
        public static Voucher GetVoucher(int voucherId)
        {
            try
            {
                string query = @"
                    SELECT v.*, vt.VoucherTypeName, cc.CostCenterName
                    FROM Vouchers v
                    LEFT JOIN VoucherTypes vt ON v.VoucherTypeID = vt.VoucherTypeID
                    LEFT JOIN CostCenters cc ON v.CostCenterID = cc.CostCenterID
                    WHERE v.VoucherID = @VoucherID";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@VoucherID", voucherId);
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                var voucher = new Voucher
                                {
                                    VoucherID = Convert.ToInt32(reader["VoucherID"]),
                                    VoucherNumber = reader["VoucherNumber"].ToString(),
                                    VoucherDate = Convert.ToDateTime(reader["VoucherDate"]),
                                    FiscalYearID = Convert.ToInt32(reader["FiscalYearID"]),
                                    VoucherTypeID = Convert.ToInt32(reader["VoucherTypeID"]),
                                    Description = reader["Description"].ToString(),
                                    TotalDebit = Convert.ToDecimal(reader["TotalDebit"]),
                                    TotalCredit = Convert.ToDecimal(reader["TotalCredit"]),
                                    CostCenterID = reader["CostCenterID"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["CostCenterID"]),
                                    ReferenceType = reader["ReferenceType"].ToString(),
                                    ReferenceID = reader["ReferenceID"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["ReferenceID"]),
                                    Status = reader["Status"].ToString(),
                                    PostedDate = reader["PostedDate"] == DBNull.Value ? null : (DateTime?)Convert.ToDateTime(reader["PostedDate"]),
                                    PostedBy = reader["PostedBy"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["PostedBy"]),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                                    CreatedBy = reader["CreatedBy"] == DBNull.Value ? 0 : Convert.ToInt32(reader["CreatedBy"]),
                                    ModifiedDate = reader["ModifiedDate"] == DBNull.Value ? null : (DateTime?)Convert.ToDateTime(reader["ModifiedDate"]),
                                    ModifiedBy = reader["ModifiedBy"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["ModifiedBy"])
                                };

                                // تحميل التفاصيل
                                voucher.Details = GetVoucherDetails(voucherId);
                                return voucher;
                            }
                        }
                    }
                }
                return null;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على السند المحاسبي: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// الحصول على جميع السندات المحاسبية
        /// </summary>
        /// <returns>قائمة السندات</returns>
        public static List<Voucher> GetAllVouchers()
        {
            try
            {
                var vouchers = new List<Voucher>();
                string query = @"
                    SELECT v.*, vt.VoucherTypeName, cc.CostCenterName
                    FROM Vouchers v
                    LEFT JOIN VoucherTypes vt ON v.VoucherTypeID = vt.VoucherTypeID
                    LEFT JOIN CostCenters cc ON v.CostCenterID = cc.CostCenterID
                    ORDER BY v.VoucherDate DESC, v.VoucherNumber DESC";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                vouchers.Add(new Voucher
                                {
                                    VoucherID = Convert.ToInt32(reader["VoucherID"]),
                                    VoucherNumber = reader["VoucherNumber"].ToString(),
                                    VoucherDate = Convert.ToDateTime(reader["VoucherDate"]),
                                    FiscalYearID = Convert.ToInt32(reader["FiscalYearID"]),
                                    VoucherTypeID = Convert.ToInt32(reader["VoucherTypeID"]),
                                    Description = reader["Description"].ToString(),
                                    TotalDebit = Convert.ToDecimal(reader["TotalDebit"]),
                                    TotalCredit = Convert.ToDecimal(reader["TotalCredit"]),
                                    CostCenterID = reader["CostCenterID"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["CostCenterID"]),
                                    Status = reader["Status"].ToString(),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"])
                                });
                            }
                        }
                    }
                }
                return vouchers;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على السندات المحاسبية: {ex.Message}");
                return new List<Voucher>();
            }
        }

        #endregion

        #region Voucher Details Management

        /// <summary>
        /// إضافة تفاصيل السند
        /// </summary>
        private static void AddVoucherDetail(int voucherId, VoucherDetail detail, SqlConnection connection, SqlTransaction transaction)
        {
            string query = @"
                INSERT INTO VoucherDetails (VoucherID, AccountID, Description, DebitAmount, CreditAmount, CostCenterID)
                VALUES (@VoucherID, @AccountID, @Description, @DebitAmount, @CreditAmount, @CostCenterID)";

            using (var command = new SqlCommand(query, connection, transaction))
            {
                command.Parameters.AddWithValue("@VoucherID", voucherId);
                command.Parameters.AddWithValue("@AccountID", detail.AccountID);
                command.Parameters.AddWithValue("@Description", detail.Description ?? "");
                command.Parameters.AddWithValue("@DebitAmount", detail.DebitAmount);
                command.Parameters.AddWithValue("@CreditAmount", detail.CreditAmount);
                command.Parameters.AddWithValue("@CostCenterID", detail.CostCenterID ?? (object)DBNull.Value);

                command.ExecuteNonQuery();
            }
        }

        /// <summary>
        /// حذف تفاصيل السند
        /// </summary>
        private static void DeleteVoucherDetails(int voucherId, SqlConnection connection, SqlTransaction transaction)
        {
            string query = "DELETE FROM VoucherDetails WHERE VoucherID = @VoucherID";

            using (var command = new SqlCommand(query, connection, transaction))
            {
                command.Parameters.AddWithValue("@VoucherID", voucherId);
                command.ExecuteNonQuery();
            }
        }

        /// <summary>
        /// الحصول على تفاصيل السند
        /// </summary>
        public static List<VoucherDetail> GetVoucherDetails(int voucherId)
        {
            var details = new List<VoucherDetail>();
            string query = @"
                SELECT vd.*, coa.AccountName, cc.CostCenterName
                FROM VoucherDetails vd
                LEFT JOIN ChartOfAccounts coa ON vd.AccountID = coa.AccountID
                LEFT JOIN CostCenters cc ON vd.CostCenterID = cc.CostCenterID
                WHERE vd.VoucherID = @VoucherID
                ORDER BY vd.VoucherDetailID";

            using (var connection = DatabaseHelper.GetConnection())
            {
                connection.Open();
                using (var command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@VoucherID", voucherId);
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            details.Add(new VoucherDetail
                            {
                                VoucherDetailID = Convert.ToInt32(reader["VoucherDetailID"]),
                                VoucherID = Convert.ToInt32(reader["VoucherID"]),
                                AccountID = Convert.ToInt32(reader["AccountID"]),
                                Description = reader["Description"].ToString(),
                                DebitAmount = Convert.ToDecimal(reader["DebitAmount"]),
                                CreditAmount = Convert.ToDecimal(reader["CreditAmount"]),
                                CostCenterID = reader["CostCenterID"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["CostCenterID"])
                            });
                        }
                    }
                }
            }
            return details;
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// توليد رقم سند جديد
        /// </summary>
        /// <param name="voucherTypeCode">كود نوع السند</param>
        /// <returns>رقم السند</returns>
        public static string GenerateVoucherNumber(string voucherTypeCode)
        {
            try
            {
                string query = @"
                    SELECT COUNT(*) 
                    FROM Vouchers v
                    INNER JOIN VoucherTypes vt ON v.VoucherTypeID = vt.VoucherTypeID
                    WHERE vt.VoucherTypeCode = @VoucherTypeCode 
                    AND YEAR(v.VoucherDate) = YEAR(GETDATE())";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@VoucherTypeCode", voucherTypeCode);
                        int count = Convert.ToInt32(command.ExecuteScalar()) + 1;
                        return $"{voucherTypeCode}-{DateTime.Now.Year}-{count:D4}";
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في توليد رقم السند: {ex.Message}");
                return $"{voucherTypeCode}-{DateTime.Now.Year}-{DateTime.Now.Ticks.ToString().Substring(10)}";
            }
        }

        /// <summary>
        /// ترحيل السند
        /// </summary>
        /// <param name="voucherId">معرف السند</param>
        /// <returns>true إذا تم الترحيل بنجاح</returns>
        public static bool PostVoucher(int voucherId)
        {
            try
            {
                string query = @"
                    UPDATE Vouchers 
                    SET Status = 'Posted', PostedDate = @PostedDate, PostedBy = @PostedBy
                    WHERE VoucherID = @VoucherID AND Status = 'Draft'";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@VoucherID", voucherId);
                        command.Parameters.AddWithValue("@PostedDate", DateTime.Now);
                        command.Parameters.AddWithValue("@PostedBy", UserManager.CurrentUser?.UserID ?? 0);

                        int rowsAffected = command.ExecuteNonQuery();
                        if (rowsAffected > 0)
                        {
                            LogManager.LogActivity("ترحيل سند محاسبي", $"تم ترحيل السند رقم: {voucherId}");
                            return true;
                        }
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في ترحيل السند: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إلغاء ترحيل السند
        /// </summary>
        /// <param name="voucherId">معرف السند</param>
        /// <returns>true إذا تم إلغاء الترحيل بنجاح</returns>
        public static bool UnpostVoucher(int voucherId)
        {
            try
            {
                string query = @"
                    UPDATE Vouchers 
                    SET Status = 'Draft', PostedDate = NULL, PostedBy = NULL
                    WHERE VoucherID = @VoucherID AND Status = 'Posted'";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@VoucherID", voucherId);

                        int rowsAffected = command.ExecuteNonQuery();
                        if (rowsAffected > 0)
                        {
                            LogManager.LogActivity("إلغاء ترحيل سند محاسبي", $"تم إلغاء ترحيل السند رقم: {voucherId}");
                            return true;
                        }
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في إلغاء ترحيل السند: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// الحصول على أنواع السندات
        /// </summary>
        /// <returns>قائمة أنواع السندات</returns>
        public static List<VoucherType> GetVoucherTypes()
        {
            try
            {
                var voucherTypes = new List<VoucherType>();
                string query = "SELECT * FROM VoucherTypes WHERE IsActive = 1 ORDER BY VoucherTypeName";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                voucherTypes.Add(new VoucherType
                                {
                                    VoucherTypeID = Convert.ToInt32(reader["VoucherTypeID"]),
                                    VoucherTypeCode = reader["VoucherTypeCode"].ToString(),
                                    VoucherTypeName = reader["VoucherTypeName"].ToString(),
                                    Description = reader["Description"].ToString(),
                                    IsActive = Convert.ToBoolean(reader["IsActive"])
                                });
                            }
                        }
                    }
                }
                return voucherTypes;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على أنواع السندات: {ex.Message}");
                return new List<VoucherType>();
            }
        }

        #endregion
    }
}




