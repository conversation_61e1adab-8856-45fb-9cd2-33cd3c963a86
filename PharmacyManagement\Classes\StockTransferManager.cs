using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using PharmacyManagement.Models;

namespace PharmacyManagement.Classes
{
    /// <summary>
    /// مدير تحويلات المخزون - Stock Transfer Manager
    /// </summary>
    public static class StockTransferManager
    {
        #region Stock Transfer Management

        /// <summary>
        /// إنشاء طلب تحويل مخزون جديد
        /// </summary>
        /// <param name="transfer">طلب التحويل</param>
        /// <returns>معرف طلب التحويل الجديد</returns>
        public static int CreateStockTransfer(StockTransfer transfer)
        {
            try
            {
                // التحقق من صحة البيانات
                ValidateStockTransfer(transfer);

                // توليد رقم التحويل
                transfer.TransferNumber = GenerateTransferNumber();

                string query = @"
                    INSERT INTO StockTransfers (TransferNumber, TransferDate, FromWarehouseID, ToWarehouseID, 
                                              TransferType, Status, Notes, RequestedBy, RequestDate)
                    VALUES (@TransferNumber, @TransferDate, @FromWarehouseID, @ToWarehouseID, 
                           @TransferType, @Status, @Notes, @RequestedBy, @RequestDate);
                    SELECT SCOPE_IDENTITY();";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@TransferNumber", transfer.TransferNumber);
                        command.Parameters.AddWithValue("@TransferDate", transfer.TransferDate);
                        command.Parameters.AddWithValue("@FromWarehouseID", transfer.FromWarehouseID);
                        command.Parameters.AddWithValue("@ToWarehouseID", transfer.ToWarehouseID);
                        command.Parameters.AddWithValue("@TransferType", transfer.TransferType);
                        command.Parameters.AddWithValue("@Status", "Pending");
                        command.Parameters.AddWithValue("@Notes", transfer.Notes ?? "");
                        command.Parameters.AddWithValue("@RequestedBy", transfer.RequestedBy);
                        command.Parameters.AddWithValue("@RequestDate", DateTime.Now);

                        int transferId = Convert.ToInt32(command.ExecuteScalar());
                        LogManager.LogActivity("إنشاء طلب تحويل", $"تم إنشاء طلب التحويل: {transfer.TransferNumber}");
                        return transferId;
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في إنشاء طلب التحويل: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// إضافة تفاصيل التحويل
        /// </summary>
        /// <param name="transferDetails">تفاصيل التحويل</param>
        /// <returns>true إذا تم الإضافة بنجاح</returns>
        public static bool AddTransferDetails(List<StockTransferDetail> transferDetails)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            foreach (var detail in transferDetails)
                            {
                                string query = @"
                                    INSERT INTO StockTransferDetails (TransferID, DrugID, BatchNumber, 
                                                                     RequestedQuantity, UnitPrice, Notes)
                                    VALUES (@TransferID, @DrugID, @BatchNumber, @RequestedQuantity, @UnitPrice, @Notes)";

                                using (var command = new SqlCommand(query, connection, transaction))
                                {
                                    command.Parameters.AddWithValue("@TransferID", detail.TransferID);
                                    command.Parameters.AddWithValue("@DrugID", detail.DrugID);
                                    command.Parameters.AddWithValue("@BatchNumber", detail.BatchNumber ?? "");
                                    command.Parameters.AddWithValue("@RequestedQuantity", detail.RequestedQuantity);
                                    command.Parameters.AddWithValue("@UnitPrice", (object)detail.UnitPrice ?? DBNull.Value);
                                    command.Parameters.AddWithValue("@Notes", detail.Notes ?? "");

                                    command.ExecuteNonQuery();
                                }
                            }

                            transaction.Commit();
                            LogManager.LogActivity("إضافة تفاصيل التحويل", $"تم إضافة {transferDetails.Count} عنصر للتحويل");
                            return true;
                        }
                        catch
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في إضافة تفاصيل التحويل: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// الموافقة على طلب التحويل
        /// </summary>
        /// <param name="transferId">معرف طلب التحويل</param>
        /// <param name="approvedBy">معرف المستخدم الموافق</param>
        /// <param name="approvedDetails">التفاصيل الموافق عليها</param>
        /// <returns>true إذا تمت الموافقة بنجاح</returns>
        public static bool ApproveTransfer(int transferId, int approvedBy, List<StockTransferDetail> approvedDetails)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // تحديث حالة التحويل
                            string updateTransferQuery = @"
                                UPDATE StockTransfers SET 
                                    Status = 'Approved',
                                    ApprovedBy = @ApprovedBy,
                                    ApprovalDate = @ApprovalDate
                                WHERE TransferID = @TransferID";

                            using (var command = new SqlCommand(updateTransferQuery, connection, transaction))
                            {
                                command.Parameters.AddWithValue("@TransferID", transferId);
                                command.Parameters.AddWithValue("@ApprovedBy", approvedBy);
                                command.Parameters.AddWithValue("@ApprovalDate", DateTime.Now);
                                command.ExecuteNonQuery();
                            }

                            // تحديث الكميات الموافق عليها
                            foreach (var detail in approvedDetails)
                            {
                                string updateDetailQuery = @"
                                    UPDATE StockTransferDetails SET 
                                        ApprovedQuantity = @ApprovedQuantity
                                    WHERE TransferDetailID = @TransferDetailID";

                                using (var command = new SqlCommand(updateDetailQuery, connection, transaction))
                                {
                                    command.Parameters.AddWithValue("@TransferDetailID", detail.TransferDetailID);
                                    command.Parameters.AddWithValue("@ApprovedQuantity", detail.ApprovedQuantity);
                                    command.ExecuteNonQuery();
                                }
                            }

                            transaction.Commit();
                            LogManager.LogActivity("الموافقة على التحويل", $"تم الموافقة على طلب التحويل رقم: {transferId}");
                            return true;
                        }
                        catch
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الموافقة على التحويل: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// شحن التحويل
        /// </summary>
        /// <param name="transferId">معرف طلب التحويل</param>
        /// <param name="shippedBy">معرف المستخدم الشاحن</param>
        /// <param name="shippedDetails">التفاصيل المشحونة</param>
        /// <returns>true إذا تم الشحن بنجاح</returns>
        public static bool ShipTransfer(int transferId, int shippedBy, List<StockTransferDetail> shippedDetails)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // التحقق من توفر المخزون
                            foreach (var detail in shippedDetails)
                            {
                                if (!IsStockAvailable(detail.DrugID, GetFromWarehouseId(transferId), detail.ShippedQuantity ?? 0, detail.BatchNumber))
                                {
                                    throw new Exception($"المخزون غير متوفر للدواء: {detail.DrugName}");
                                }
                            }

                            // تحديث حالة التحويل
                            string updateTransferQuery = @"
                                UPDATE StockTransfers SET 
                                    Status = 'Shipped',
                                    ShippedBy = @ShippedBy,
                                    ShipmentDate = @ShipmentDate
                                WHERE TransferID = @TransferID";

                            using (var command = new SqlCommand(updateTransferQuery, connection, transaction))
                            {
                                command.Parameters.AddWithValue("@TransferID", transferId);
                                command.Parameters.AddWithValue("@ShippedBy", shippedBy);
                                command.Parameters.AddWithValue("@ShipmentDate", DateTime.Now);
                                command.ExecuteNonQuery();
                            }

                            // تحديث الكميات المشحونة وخصم المخزون
                            foreach (var detail in shippedDetails)
                            {
                                // تحديث تفاصيل التحويل
                                string updateDetailQuery = @"
                                    UPDATE StockTransferDetails SET 
                                        ShippedQuantity = @ShippedQuantity
                                    WHERE TransferDetailID = @TransferDetailID";

                                using (var command = new SqlCommand(updateDetailQuery, connection, transaction))
                                {
                                    command.Parameters.AddWithValue("@TransferDetailID", detail.TransferDetailID);
                                    command.Parameters.AddWithValue("@ShippedQuantity", detail.ShippedQuantity);
                                    command.ExecuteNonQuery();
                                }

                                // خصم المخزون من المخزن المرسل
                                var fromWarehouseId = GetFromWarehouseId(transferId);
                                UpdateStock(detail.DrugID, fromWarehouseId, detail.BatchNumber, -(detail.ShippedQuantity ?? 0), connection, transaction);

                                // تسجيل حركة المخزون
                                RecordStockMovement(detail.DrugID, fromWarehouseId, "Transfer_Out", "Transfer", transferId,
                                    detail.BatchNumber, -(detail.ShippedQuantity ?? 0), detail.UnitPrice, shippedBy, connection, transaction);
                            }

                            transaction.Commit();
                            LogManager.LogActivity("شحن التحويل", $"تم شحن طلب التحويل رقم: {transferId}");
                            return true;
                        }
                        catch
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في شحن التحويل: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// استلام التحويل
        /// </summary>
        /// <param name="transferId">معرف طلب التحويل</param>
        /// <param name="receivedBy">معرف المستخدم المستلم</param>
        /// <param name="receivedDetails">التفاصيل المستلمة</param>
        /// <returns>true إذا تم الاستلام بنجاح</returns>
        public static bool ReceiveTransfer(int transferId, int receivedBy, List<StockTransferDetail> receivedDetails)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // تحديث حالة التحويل
                            string updateTransferQuery = @"
                                UPDATE StockTransfers SET 
                                    Status = 'Received',
                                    ReceivedBy = @ReceivedBy,
                                    ReceiptDate = @ReceiptDate
                                WHERE TransferID = @TransferID";

                            using (var command = new SqlCommand(updateTransferQuery, connection, transaction))
                            {
                                command.Parameters.AddWithValue("@TransferID", transferId);
                                command.Parameters.AddWithValue("@ReceivedBy", receivedBy);
                                command.Parameters.AddWithValue("@ReceiptDate", DateTime.Now);
                                command.ExecuteNonQuery();
                            }

                            // تحديث الكميات المستلمة وإضافة المخزون
                            foreach (var detail in receivedDetails)
                            {
                                // تحديث تفاصيل التحويل
                                string updateDetailQuery = @"
                                    UPDATE StockTransferDetails SET 
                                        ReceivedQuantity = @ReceivedQuantity
                                    WHERE TransferDetailID = @TransferDetailID";

                                using (var command = new SqlCommand(updateDetailQuery, connection, transaction))
                                {
                                    command.Parameters.AddWithValue("@TransferDetailID", detail.TransferDetailID);
                                    command.Parameters.AddWithValue("@ReceivedQuantity", detail.ReceivedQuantity);
                                    command.ExecuteNonQuery();
                                }

                                // إضافة المخزون للمخزن المستقبل
                                var toWarehouseId = GetToWarehouseId(transferId);
                                UpdateStock(detail.DrugID, toWarehouseId, detail.BatchNumber, detail.ReceivedQuantity ?? 0, connection, transaction);

                                // تسجيل حركة المخزون
                                RecordStockMovement(detail.DrugID, toWarehouseId, "Transfer_In", "Transfer", transferId,
                                    detail.BatchNumber, detail.ReceivedQuantity ?? 0, detail.UnitPrice, receivedBy, connection, transaction);
                            }

                            transaction.Commit();
                            LogManager.LogActivity("استلام التحويل", $"تم استلام طلب التحويل رقم: {transferId}");
                            return true;
                        }
                        catch
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في استلام التحويل: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إلغاء طلب التحويل
        /// </summary>
        /// <param name="transferId">معرف طلب التحويل</param>
        /// <param name="cancelledBy">معرف المستخدم الملغي</param>
        /// <param name="reason">سبب الإلغاء</param>
        /// <returns>true إذا تم الإلغاء بنجاح</returns>
        public static bool CancelTransfer(int transferId, int cancelledBy, string reason)
        {
            try
            {
                var transfer = GetStockTransfer(transferId);
                if (transfer == null)
                {
                    throw new Exception("طلب التحويل غير موجود");
                }

                if (transfer.Status == "Shipped" || transfer.Status == "Received")
                {
                    throw new Exception("لا يمكن إلغاء طلب التحويل بعد الشحن أو الاستلام");
                }

                string query = @"
                    UPDATE StockTransfers SET 
                        Status = 'Cancelled',
                        Notes = CONCAT(ISNULL(Notes, ''), ' - ملغي: ', @Reason)
                    WHERE TransferID = @TransferID";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@TransferID", transferId);
                        command.Parameters.AddWithValue("@Reason", reason ?? "");
                        
                        int rowsAffected = command.ExecuteNonQuery();
                        if (rowsAffected > 0)
                        {
                            LogManager.LogActivity("إلغاء التحويل", $"تم إلغاء طلب التحويل رقم: {transferId} - السبب: {reason}");
                            return true;
                        }
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في إلغاء التحويل: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// الحصول على طلب تحويل
        /// </summary>
        /// <param name="transferId">معرف طلب التحويل</param>
        /// <returns>طلب التحويل</returns>
        public static StockTransfer GetStockTransfer(int transferId)
        {
            try
            {
                string query = @"
                    SELECT st.*, 
                           fw.WarehouseName AS FromWarehouseName, fb.BranchName AS FromBranchName,
                           tw.WarehouseName AS ToWarehouseName, tb.BranchName AS ToBranchName,
                           ru.FullName AS RequestedByName, au.FullName AS ApprovedByName,
                           su.FullName AS ShippedByName, reu.FullName AS ReceivedByName
                    FROM StockTransfers st
                    INNER JOIN Warehouses fw ON st.FromWarehouseID = fw.WarehouseID
                    INNER JOIN Branches fb ON fw.BranchID = fb.BranchID
                    INNER JOIN Warehouses tw ON st.ToWarehouseID = tw.WarehouseID
                    INNER JOIN Branches tb ON tw.BranchID = tb.BranchID
                    LEFT JOIN Users ru ON st.RequestedBy = ru.UserID
                    LEFT JOIN Users au ON st.ApprovedBy = au.UserID
                    LEFT JOIN Users su ON st.ShippedBy = su.UserID
                    LEFT JOIN Users reu ON st.ReceivedBy = reu.UserID
                    WHERE st.TransferID = @TransferID";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@TransferID", transferId);
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return new StockTransfer
                                {
                                    TransferID = Convert.ToInt32(reader["TransferID"]),
                                    TransferNumber = reader["TransferNumber"].ToString(),
                                    TransferDate = Convert.ToDateTime(reader["TransferDate"]),
                                    FromWarehouseID = Convert.ToInt32(reader["FromWarehouseID"]),
                                    FromWarehouseName = reader["FromWarehouseName"].ToString(),
                                    FromBranchName = reader["FromBranchName"].ToString(),
                                    ToWarehouseID = Convert.ToInt32(reader["ToWarehouseID"]),
                                    ToWarehouseName = reader["ToWarehouseName"].ToString(),
                                    ToBranchName = reader["ToBranchName"].ToString(),
                                    TransferType = reader["TransferType"].ToString(),
                                    Status = reader["Status"].ToString(),
                                    Notes = reader["Notes"].ToString(),
                                    RequestedBy = reader["RequestedBy"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["RequestedBy"]),
                                    RequestedByName = reader["RequestedByName"].ToString(),
                                    ApprovedBy = reader["ApprovedBy"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["ApprovedBy"]),
                                    ApprovedByName = reader["ApprovedByName"].ToString(),
                                    ShippedBy = reader["ShippedBy"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["ShippedBy"]),
                                    ShippedByName = reader["ShippedByName"].ToString(),
                                    ReceivedBy = reader["ReceivedBy"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["ReceivedBy"]),
                                    ReceivedByName = reader["ReceivedByName"].ToString(),
                                    RequestDate = Convert.ToDateTime(reader["RequestDate"]),
                                    ApprovalDate = reader["ApprovalDate"] == DBNull.Value ? null : (DateTime?)Convert.ToDateTime(reader["ApprovalDate"]),
                                    ShipmentDate = reader["ShipmentDate"] == DBNull.Value ? null : (DateTime?)Convert.ToDateTime(reader["ShipmentDate"]),
                                    ReceiptDate = reader["ReceiptDate"] == DBNull.Value ? null : (DateTime?)Convert.ToDateTime(reader["ReceiptDate"])
                                };
                            }
                        }
                    }
                }
                return null;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على طلب التحويل: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// الحصول على تفاصيل طلب التحويل
        /// </summary>
        /// <param name="transferId">معرف طلب التحويل</param>
        /// <returns>قائمة تفاصيل التحويل</returns>
        public static List<StockTransferDetail> GetTransferDetails(int transferId)
        {
            try
            {
                var details = new List<StockTransferDetail>();
                string query = @"
                    SELECT std.*, d.DrugName, d.DrugCode
                    FROM StockTransferDetails std
                    INNER JOIN Drugs d ON std.DrugID = d.DrugID
                    WHERE std.TransferID = @TransferID
                    ORDER BY d.DrugName";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@TransferID", transferId);
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                details.Add(new StockTransferDetail
                                {
                                    TransferDetailID = Convert.ToInt32(reader["TransferDetailID"]),
                                    TransferID = Convert.ToInt32(reader["TransferID"]),
                                    DrugID = Convert.ToInt32(reader["DrugID"]),
                                    DrugName = reader["DrugName"].ToString(),
                                    DrugCode = reader["DrugCode"].ToString(),
                                    BatchNumber = reader["BatchNumber"].ToString(),
                                    RequestedQuantity = Convert.ToInt32(reader["RequestedQuantity"]),
                                    ApprovedQuantity = reader["ApprovedQuantity"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["ApprovedQuantity"]),
                                    ShippedQuantity = reader["ShippedQuantity"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["ShippedQuantity"]),
                                    ReceivedQuantity = reader["ReceivedQuantity"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["ReceivedQuantity"]),
                                    UnitPrice = reader["UnitPrice"] == DBNull.Value ? null : (decimal?)Convert.ToDecimal(reader["UnitPrice"]),
                                    Notes = reader["Notes"].ToString()
                                });
                            }
                        }
                    }
                }
                return details;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على تفاصيل التحويل: {ex.Message}");
                return new List<StockTransferDetail>();
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// التحقق من صحة بيانات التحويل
        /// </summary>
        private static void ValidateStockTransfer(StockTransfer transfer)
        {
            if (transfer.FromWarehouseID == transfer.ToWarehouseID)
            {
                throw new Exception("لا يمكن التحويل من وإلى نفس المخزن");
            }

            var fromWarehouse = WarehouseManager.GetWarehouse(transfer.FromWarehouseID);
            var toWarehouse = WarehouseManager.GetWarehouse(transfer.ToWarehouseID);

            if (fromWarehouse == null || toWarehouse == null)
            {
                throw new Exception("المخزن المحدد غير موجود");
            }

            if (!fromWarehouse.IsActive || !toWarehouse.IsActive)
            {
                throw new Exception("المخزن المحدد غير نشط");
            }

            // تحديد نوع التحويل
            if (fromWarehouse.BranchID == toWarehouse.BranchID)
            {
                transfer.TransferType = "بين مخازن";
            }
            else
            {
                transfer.TransferType = "بين فروع";
            }
        }

        /// <summary>
        /// توليد رقم التحويل
        /// </summary>
        private static string GenerateTransferNumber()
        {
            try
            {
                string query = "SELECT COUNT(*) FROM StockTransfers WHERE YEAR(TransferDate) = YEAR(GETDATE())";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        int count = Convert.ToInt32(command.ExecuteScalar()) + 1;
                        return $"ST{DateTime.Now.Year}{count:D4}";
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في توليد رقم التحويل: {ex.Message}");
                return $"ST{DateTime.Now.Year}{DateTime.Now.Ticks.ToString().Substring(10)}";
            }
        }

        /// <summary>
        /// التحقق من توفر المخزون
        /// </summary>
        private static bool IsStockAvailable(int drugId, int warehouseId, int quantity, string batchNumber)
        {
            try
            {
                string query = @"
                    SELECT ISNULL(SUM(AvailableQuantity), 0) 
                    FROM DrugStock 
                    WHERE DrugID = @DrugID AND WarehouseID = @WarehouseID";

                if (!string.IsNullOrEmpty(batchNumber))
                {
                    query += " AND BatchNumber = @BatchNumber";
                }

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@DrugID", drugId);
                        command.Parameters.AddWithValue("@WarehouseID", warehouseId);
                        if (!string.IsNullOrEmpty(batchNumber))
                        {
                            command.Parameters.AddWithValue("@BatchNumber", batchNumber);
                        }

                        int availableQuantity = Convert.ToInt32(command.ExecuteScalar());
                        return availableQuantity >= quantity;
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في التحقق من توفر المخزون: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تحديث المخزون
        /// </summary>
        private static void UpdateStock(int drugId, int warehouseId, string batchNumber, int quantityChange, 
            SqlConnection connection, SqlTransaction transaction)
        {
            string query = @"
                UPDATE DrugStock SET 
                    Quantity = Quantity + @QuantityChange,
                    LastUpdated = GETDATE()
                WHERE DrugID = @DrugID AND WarehouseID = @WarehouseID AND BatchNumber = @BatchNumber";

            using (var command = new SqlCommand(query, connection, transaction))
            {
                command.Parameters.AddWithValue("@DrugID", drugId);
                command.Parameters.AddWithValue("@WarehouseID", warehouseId);
                command.Parameters.AddWithValue("@BatchNumber", batchNumber ?? "");
                command.Parameters.AddWithValue("@QuantityChange", quantityChange);
                command.ExecuteNonQuery();
            }
        }

        /// <summary>
        /// تسجيل حركة المخزون
        /// </summary>
        private static void RecordStockMovement(int drugId, int warehouseId, string movementType, string referenceType,
            int referenceId, string batchNumber, int quantity, decimal? unitPrice, int createdBy,
            SqlConnection connection, SqlTransaction transaction)
        {
            string query = @"
                INSERT INTO StockMovements (DrugID, WarehouseID, MovementType, ReferenceType, ReferenceID, 
                                          BatchNumber, Quantity, UnitPrice, TotalValue, MovementDate, CreatedBy)
                VALUES (@DrugID, @WarehouseID, @MovementType, @ReferenceType, @ReferenceID, 
                       @BatchNumber, @Quantity, @UnitPrice, @TotalValue, @MovementDate, @CreatedBy)";

            using (var command = new SqlCommand(query, connection, transaction))
            {
                command.Parameters.AddWithValue("@DrugID", drugId);
                command.Parameters.AddWithValue("@WarehouseID", warehouseId);
                command.Parameters.AddWithValue("@MovementType", movementType);
                command.Parameters.AddWithValue("@ReferenceType", referenceType);
                command.Parameters.AddWithValue("@ReferenceID", referenceId);
                command.Parameters.AddWithValue("@BatchNumber", batchNumber ?? "");
                command.Parameters.AddWithValue("@Quantity", quantity);
                command.Parameters.AddWithValue("@UnitPrice", (object)unitPrice ?? DBNull.Value);
                command.Parameters.AddWithValue("@TotalValue", (object)(quantity * (unitPrice ?? 0)) ?? DBNull.Value);
                command.Parameters.AddWithValue("@MovementDate", DateTime.Now);
                command.Parameters.AddWithValue("@CreatedBy", createdBy);
                command.ExecuteNonQuery();
            }
        }

        /// <summary>
        /// الحصول على معرف المخزن المرسل
        /// </summary>
        private static int GetFromWarehouseId(int transferId)
        {
            string query = "SELECT FromWarehouseID FROM StockTransfers WHERE TransferID = @TransferID";
            using (var connection = DatabaseHelper.GetConnection())
            {
                connection.Open();
                using (var command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@TransferID", transferId);
                    return Convert.ToInt32(command.ExecuteScalar());
                }
            }
        }

        /// <summary>
        /// الحصول على معرف المخزن المستقبل
        /// </summary>
        private static int GetToWarehouseId(int transferId)
        {
            string query = "SELECT ToWarehouseID FROM StockTransfers WHERE TransferID = @TransferID";
            using (var connection = DatabaseHelper.GetConnection())
            {
                connection.Open();
                using (var command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@TransferID", transferId);
                    return Convert.ToInt32(command.ExecuteScalar());
                }
            }
        }

        /// <summary>
        /// الحصول على جميع التحويلات
        /// </summary>
        /// <returns>قائمة التحويلات</returns>
        public static List<StockTransfer> GetAllStockTransfers()
        {
            try
            {
                var transfers = new List<StockTransfer>();
                string query = @"
                    SELECT st.*,
                           fw.WarehouseName AS FromWarehouseName, fb.BranchName AS FromBranchName,
                           tw.WarehouseName AS ToWarehouseName, tb.BranchName AS ToBranchName,
                           ru.FullName AS RequestedByName, au.FullName AS ApprovedByName,
                           su.FullName AS ShippedByName, reu.FullName AS ReceivedByName
                    FROM StockTransfers st
                    INNER JOIN Warehouses fw ON st.FromWarehouseID = fw.WarehouseID
                    INNER JOIN Branches fb ON fw.BranchID = fb.BranchID
                    INNER JOIN Warehouses tw ON st.ToWarehouseID = tw.WarehouseID
                    INNER JOIN Branches tb ON tw.BranchID = tb.BranchID
                    LEFT JOIN Users ru ON st.RequestedBy = ru.UserID
                    LEFT JOIN Users au ON st.ApprovedBy = au.UserID
                    LEFT JOIN Users su ON st.ShippedBy = su.UserID
                    LEFT JOIN Users reu ON st.ReceivedBy = reu.UserID
                    ORDER BY st.TransferDate DESC";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                transfers.Add(new StockTransfer
                                {
                                    TransferID = Convert.ToInt32(reader["TransferID"]),
                                    TransferNumber = reader["TransferNumber"].ToString(),
                                    TransferDate = Convert.ToDateTime(reader["TransferDate"]),
                                    FromWarehouseID = Convert.ToInt32(reader["FromWarehouseID"]),
                                    FromWarehouseName = reader["FromWarehouseName"].ToString(),
                                    FromBranchName = reader["FromBranchName"].ToString(),
                                    ToWarehouseID = Convert.ToInt32(reader["ToWarehouseID"]),
                                    ToWarehouseName = reader["ToWarehouseName"].ToString(),
                                    ToBranchName = reader["ToBranchName"].ToString(),
                                    TransferType = reader["TransferType"].ToString(),
                                    Status = reader["Status"].ToString(),
                                    Notes = reader["Notes"].ToString(),
                                    RequestedBy = reader["RequestedBy"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["RequestedBy"]),
                                    RequestedByName = reader["RequestedByName"].ToString(),
                                    ApprovedBy = reader["ApprovedBy"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["ApprovedBy"]),
                                    ApprovedByName = reader["ApprovedByName"].ToString(),
                                    ShippedBy = reader["ShippedBy"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["ShippedBy"]),
                                    ShippedByName = reader["ShippedByName"].ToString(),
                                    ReceivedBy = reader["ReceivedBy"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["ReceivedBy"]),
                                    ReceivedByName = reader["ReceivedByName"].ToString(),
                                    RequestDate = Convert.ToDateTime(reader["RequestDate"]),
                                    ApprovalDate = reader["ApprovalDate"] == DBNull.Value ? null : (DateTime?)Convert.ToDateTime(reader["ApprovalDate"]),
                                    ShipmentDate = reader["ShipmentDate"] == DBNull.Value ? null : (DateTime?)Convert.ToDateTime(reader["ShipmentDate"]),
                                    ReceiptDate = reader["ReceiptDate"] == DBNull.Value ? null : (DateTime?)Convert.ToDateTime(reader["ReceiptDate"])
                                });
                            }
                        }
                    }
                }
                return transfers;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على التحويلات: {ex.Message}");
                return new List<StockTransfer>();
            }
        }

        /// <summary>
        /// البحث في التحويلات
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <returns>قائمة التحويلات المطابقة</returns>
        public static List<StockTransfer> SearchTransfers(string searchTerm)
        {
            try
            {
                var transfers = new List<StockTransfer>();
                string query = @"
                    SELECT st.*,
                           fw.WarehouseName AS FromWarehouseName, fb.BranchName AS FromBranchName,
                           tw.WarehouseName AS ToWarehouseName, tb.BranchName AS ToBranchName,
                           ru.FullName AS RequestedByName
                    FROM StockTransfers st
                    INNER JOIN Warehouses fw ON st.FromWarehouseID = fw.WarehouseID
                    INNER JOIN Branches fb ON fw.BranchID = fb.BranchID
                    INNER JOIN Warehouses tw ON st.ToWarehouseID = tw.WarehouseID
                    INNER JOIN Branches tb ON tw.BranchID = tb.BranchID
                    LEFT JOIN Users ru ON st.RequestedBy = ru.UserID
                    WHERE (st.TransferNumber LIKE @SearchTerm OR
                           fw.WarehouseName LIKE @SearchTerm OR
                           tw.WarehouseName LIKE @SearchTerm OR
                           fb.BranchName LIKE @SearchTerm OR
                           tb.BranchName LIKE @SearchTerm)
                    ORDER BY st.TransferDate DESC";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@SearchTerm", $"%{searchTerm}%");
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                transfers.Add(new StockTransfer
                                {
                                    TransferID = Convert.ToInt32(reader["TransferID"]),
                                    TransferNumber = reader["TransferNumber"].ToString(),
                                    TransferDate = Convert.ToDateTime(reader["TransferDate"]),
                                    FromWarehouseID = Convert.ToInt32(reader["FromWarehouseID"]),
                                    FromWarehouseName = reader["FromWarehouseName"].ToString(),
                                    FromBranchName = reader["FromBranchName"].ToString(),
                                    ToWarehouseID = Convert.ToInt32(reader["ToWarehouseID"]),
                                    ToWarehouseName = reader["ToWarehouseName"].ToString(),
                                    ToBranchName = reader["ToBranchName"].ToString(),
                                    TransferType = reader["TransferType"].ToString(),
                                    Status = reader["Status"].ToString(),
                                    Notes = reader["Notes"].ToString(),
                                    RequestedBy = reader["RequestedBy"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["RequestedBy"]),
                                    RequestedByName = reader["RequestedByName"].ToString(),
                                    RequestDate = Convert.ToDateTime(reader["RequestDate"])
                                });
                            }
                        }
                    }
                }
                return transfers;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في البحث في التحويلات: {ex.Message}");
                return new List<StockTransfer>();
            }
        }

        #endregion
    }
}




