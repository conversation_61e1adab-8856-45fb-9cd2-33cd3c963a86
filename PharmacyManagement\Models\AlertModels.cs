using System;

namespace PharmacyManagement.Models
{
    /// <summary>
    /// تنبيه المخزون المنخفض
    /// </summary>
    public class LowStockAlert
    {
        /// <summary>
        /// معرف الدواء
        /// </summary>
        public int DrugID { get; set; }

        /// <summary>
        /// كود الدواء
        /// </summary>
        public string DrugCode { get; set; }

        /// <summary>
        /// اسم الدواء
        /// </summary>
        public string DrugName { get; set; }

        /// <summary>
        /// المخزون الحالي
        /// </summary>
        public decimal CurrentStock { get; set; }

        /// <summary>
        /// الحد الأدنى للمخزون
        /// </summary>
        public decimal MinStock { get; set; }

        /// <summary>
        /// اسم الفئة
        /// </summary>
        public string CategoryName { get; set; }

        /// <summary>
        /// اسم الشركة المصنعة
        /// </summary>
        public string ManufacturerName { get; set; }
    }

    /// <summary>
    /// تنبيه انتهاء الصلاحية
    /// </summary>
    public class ExpiryAlert
    {
        /// <summary>
        /// معرف المخزون
        /// </summary>
        public int InventoryID { get; set; }

        /// <summary>
        /// معرف الدواء
        /// </summary>
        public int DrugID { get; set; }

        /// <summary>
        /// كود الدواء
        /// </summary>
        public string DrugCode { get; set; }

        /// <summary>
        /// اسم الدواء
        /// </summary>
        public string DrugName { get; set; }

        /// <summary>
        /// رقم الدفعة
        /// </summary>
        public string BatchNumber { get; set; }

        /// <summary>
        /// الكمية
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// تاريخ انتهاء الصلاحية
        /// </summary>
        public DateTime ExpiryDate { get; set; }

        /// <summary>
        /// اسم المستودع
        /// </summary>
        public string WarehouseName { get; set; }

        /// <summary>
        /// اسم الفئة
        /// </summary>
        public string CategoryName { get; set; }

        /// <summary>
        /// الأيام المتبقية لانتهاء الصلاحية
        /// </summary>
        public int DaysToExpiry { get; set; }
    }
}
