using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نافذة تقرير العملاء - Customers Report Form
    /// </summary>
    public partial class CustomersReportForm : Form
    {
        #region Fields - الحقول

        private DateTime _fromDate;
        private DateTime _toDate;

        #endregion

        #region Constructor - المنشئ

        /// <summary>
        /// منشئ نافذة تقرير العملاء
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        public CustomersReportForm(DateTime fromDate, DateTime toDate)
        {
            InitializeComponent();
            _fromDate = fromDate;
            _toDate = toDate;
            SetupForm();
            LoadReport();
        }

        #endregion

        #region Form Setup - إعداد النافذة

        /// <summary>
        /// إعداد النافذة
        /// </summary>
        private void SetupForm()
        {
            this.Text = $"تقرير العملاء - من {_fromDate:yyyy/MM/dd} إلى {_toDate:yyyy/MM/dd}";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MinimumSize = new Size(800, 600);

            SetupDataGridView();
            ApplyFlatDesign();
        }

        /// <summary>
        /// إعداد DataGridView
        /// </summary>
        private void SetupDataGridView()
        {
            dgvCustomers.AutoGenerateColumns = false;
            dgvCustomers.AllowUserToAddRows = false;
            dgvCustomers.AllowUserToDeleteRows = false;
            dgvCustomers.ReadOnly = true;
            dgvCustomers.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvCustomers.MultiSelect = false;
        }

        /// <summary>
        /// تطبيق التصميم المسطح
        /// </summary>
        private void ApplyFlatDesign()
        {
            this.BackColor = Color.White;
            
            foreach (Control control in this.Controls)
            {
                if (control is Button button)
                {
                    button.FlatStyle = FlatStyle.Flat;
                    button.FlatAppearance.BorderSize = 0;
                }
            }
        }

        #endregion

        #region Data Loading - تحميل البيانات

        /// <summary>
        /// تحميل التقرير
        /// </summary>
        private void LoadReport()
        {
            try
            {
                var customersReport = ReportsManager.GetCustomersReport(_fromDate, _toDate);
                
                // تحديث الإحصائيات
                UpdateStatistics(customersReport);
                
                // تحميل بيانات العملاء
                var customersData = ReportsManager.GetCustomersData(_fromDate, _toDate);
                dgvCustomers.DataSource = customersData;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تقرير العملاء: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحديث الإحصائيات
        /// </summary>
        /// <param name="report">تقرير العملاء</param>
        private void UpdateStatistics(CustomerReport report)
        {
            if (report != null && report.Summary != null)
            {
                lblTotalCustomers.Text = $"إجمالي العملاء: {report.Summary.TotalCustomers}";
                lblActiveCustomers.Text = $"العملاء النشطون: {report.Summary.ActiveCustomers}";
                lblNewCustomers.Text = $"عملاء جدد: {report.Summary.NewCustomers}";
                lblTotalPurchases.Text = $"إجمالي المشتريات: {report.Summary.TotalSales:F2} ر.ي";
                lblAveragePurchase.Text = $"متوسط المشتريات: {report.Summary.AverageSalesPerCustomer:F2} ر.ي";
            }
            else
            {
                lblTotalCustomers.Text = "إجمالي العملاء: 0";
                lblActiveCustomers.Text = "العملاء النشطون: 0";
                lblNewCustomers.Text = "عملاء جدد: 0";
                lblTotalPurchases.Text = "إجمالي المشتريات: 0.00 ر.ي";
                lblAveragePurchase.Text = "متوسط المشتريات: 0.00 ر.ي";
            }
        }

        #endregion

        #region Event Handlers - معالجات الأحداث

        /// <summary>
        /// تصدير التقرير
        /// </summary>
        private void btnExport_Click(object sender, EventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "Excel Files|*.xlsx|PDF Files|*.pdf|CSV Files|*.csv",
                    Title = "تصدير تقرير العملاء",
                    FileName = $"Customers_Report_{_fromDate:yyyyMMdd}_{_toDate:yyyyMMdd}"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    var customersData = dgvCustomers.DataSource as System.Collections.Generic.List<Customer>;
                    if (customersData != null)
                    {
                        ExportManager.ExportCustomers(customersData, saveDialog.FileName);
                        MessageBox.Show("تم تصدير التقرير بنجاح", "نجح", 
                                      MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير التقرير: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// طباعة التقرير
        /// </summary>
        private void btnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                // يمكن إضافة وظيفة الطباعة هنا
                MessageBox.Show("وظيفة الطباعة ستكون متاحة قريباً", "معلومات", 
                              MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحديث التقرير
        /// </summary>
        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadReport();
        }

        /// <summary>
        /// إغلاق النافذة
        /// </summary>
        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// عرض تفاصيل العميل
        /// </summary>
        private void dgvCustomers_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                var selectedCustomer = dgvCustomers.SelectedRows[0].DataBoundItem as Customer;
                if (selectedCustomer != null)
                {
                    var historyForm = new CustomerHistoryForm(selectedCustomer);
                    historyForm.ShowDialog();
                }
            }
        }

        #endregion
    }

 
}
