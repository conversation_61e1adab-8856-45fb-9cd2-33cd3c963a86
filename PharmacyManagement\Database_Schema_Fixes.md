# إصلاح مشاكل قاعدة البيانات - Database Schema Fixes

## المشكلة الرئيسية
كان هناك عدم تطابق بين أسماء الحقول في الكود وأسماء الحقول الفعلية في قاعدة البيانات، مما تسبب في فشل جميع العمليات (البحث، الإضافة، التعديل، الحذف).

## الإصلاحات المطبقة

### 1. جدول المستخدمين (Users)

#### المشاكل:
- الكود يستخدم `Password` لكن قاعدة البيانات تحتوي على `PasswordHash`
- الكود يحاول الوصول لحقول الصلاحيات من جدول Users لكنها موجودة في جدول UserPermissions

#### الإصلاحات:
```sql
-- تم تصحيح استعلامات تسجيل الدخول
WHERE u.Username = @Username AND u.PasswordHash = @Password

-- تم إضافة JOIN مع جدول UserPermissions
LEFT JOIN UserPermissions up ON u.UserID = up.UserID

-- تم استخدام أسماء الحقول الصحيحة
MAX(CASE WHEN up.ModuleName = 'Users' AND up.CanAdd = 1 THEN 1 ELSE 0 END) as CanAddUsers
```

### 2. جدول الأدوية (Drugs)

#### المشاكل:
- الكود يستخدم `TradeName` لكن قاعدة البيانات تحتوي على `DrugName`
- الكود يستخدم `DosageForm` لكن قاعدة البيانات تحتوي على `Form`
- الكود يستخدم `PackSize` لكن قاعدة البيانات تحتوي على `PackageSize`
- الكود يستخدم `MinStockLevel/MaxStockLevel` لكن قاعدة البيانات تحتوي على `MinStock/MaxStock`
- الكود يحاول تحديث `CurrentStock` في جدول Drugs لكن المخزون في جدول Inventory منفصل

#### الإصلاحات:
```sql
-- استعلام الإضافة المصحح
INSERT INTO Drugs (DrugCode, DrugName, ScientificName, Barcode, CategoryID, ManufacturerID,
                   Form, Strength, PackageSize, Unit, PurchasePrice, SalePrice,
                   MinStock, MaxStock, RequiresPrescription, IsActive, CreatedDate, CreatedBy)

-- استعلام تحديث المخزون المصحح
UPDATE Inventory SET Quantity = Quantity + @Quantity WHERE DrugID = @DrugID

-- استعلام الحصول على المخزون المصحح
SELECT SUM(Quantity) FROM Inventory WHERE DrugID = @DrugID
```

### 3. جدول العملاء (Customers)

#### المشاكل:
- الكود يستخدم `BirthDate` لكن قاعدة البيانات تحتوي على `DateOfBirth`
- الكود يستخدم `NationalID` لكن هذا الحقل غير موجود في قاعدة البيانات
- الكود يستخدم `RegistrationDate` لكن قاعدة البيانات تحتوي على `CreatedDate`
- الكود لا يستخدم حقل `Mobile` الموجود في قاعدة البيانات

#### الإصلاحات:
```sql
-- استعلام الإضافة المصحح
INSERT INTO Customers (CustomerCode, CustomerName, Phone, Mobile, Email, Address, 
                       DateOfBirth, Gender, CustomerType, CreditLimit, 
                       IsActive, CreatedDate, CreatedBy)

-- استعلام التحديث المصحح
UPDATE Customers SET 
    CustomerCode = @CustomerCode,
    CustomerName = @CustomerName,
    Phone = @Phone,
    Mobile = @Mobile,
    Email = @Email,
    Address = @Address,
    DateOfBirth = @DateOfBirth,
    Gender = @Gender,
    CustomerType = @CustomerType,
    CreditLimit = @CreditLimit,
    IsActive = @IsActive,
    ModifiedDate = @ModifiedDate,
    ModifiedBy = @ModifiedBy
WHERE CustomerID = @CustomerID
```

### 4. جدول الموردين (Suppliers)

#### المشاكل:
- الكود يحاول إدراج حقل `Notes` لكنه غير موجود في قاعدة البيانات
- الكود لا يستخدم حقل `Mobile` الموجود في قاعدة البيانات

#### الإصلاحات:
```sql
-- استعلام الإضافة المصحح
INSERT INTO Suppliers (SupplierName, ContactPerson, Phone, Mobile, Email, Address,
                       SupplierCode, TaxNumber, CreditLimit, CurrentBalance,
                       IsActive, CreatedDate, CreatedBy)
```

## الملفات المصححة

1. **PharmacyManagement/Classes/UserManager.cs**
   - تصحيح استعلامات تسجيل الدخول
   - تصحيح استعلامات الحصول على المستخدمين
   - تصحيح استعلامات البحث
   - إضافة JOIN مع جدول UserPermissions

2. **PharmacyManagement/Classes/DrugManager.cs**
   - تصحيح استعلامات إضافة الأدوية
   - تصحيح استعلامات تحديث الأدوية
   - تصحيح استعلامات إدارة المخزون

3. **PharmacyManagement/Classes/CustomerManager.cs**
   - تصحيح استعلامات إضافة العملاء
   - تصحيح استعلامات تحديث العملاء

4. **PharmacyManagement/Classes/SupplierManager.cs**
   - تصحيح استعلامات إضافة الموردين

5. **PharmacyManagement/Forms/UserPermissionsForm.cs**
   - تحديث للعمل مع هيكل UserPermission الجديد

6. **PharmacyManagement/Classes/InvoiceManager.cs**
   - تصحيح جميع الاستعلامات لاستخدام `SalesInvoices` بدلاً من `Invoices`

7. **PharmacyManagement/Classes/ReportsManager.cs**
   - تصحيح استعلامات المخزون لاستخدام جدول `Inventory`
   - تصحيح استعلامات العملاء لاستخدام `CurrentBalance` بدلاً من `Balance`
   - تصحيح استعلامات الفواتير لاستخدام `SalesInvoices`

8. **PharmacyManagement/Classes/InventoryManager.cs**
   - تصحيح استعلامات المخزون المنخفض لاستخدام جدول `Inventory`
   - تصحيح استعلامات الأدوية منتهية الصلاحية

## مشاكل إضافية تم إصلاحها

### 5. جدول الفواتير (SalesInvoices)

#### المشاكل:
- الكود يستخدم جدول `Invoices` لكن قاعدة البيانات تحتوي على `SalesInvoices`

#### الإصلاحات:
```sql
-- تم تصحيح جميع الاستعلامات
SELECT i.*, c.CustomerName, u.FullName as UserName
FROM SalesInvoices i
LEFT JOIN Customers c ON i.CustomerID = c.CustomerID
LEFT JOIN Users u ON i.UserID = u.UserID
```

### 6. جدول المخزون (Inventory)

#### المشاكل:
- الكود يحاول الوصول لحقول المخزون من جدول `Drugs` لكنها في جدول `Inventory`
- الكود يستخدم `MinimumStock` لكن قاعدة البيانات تحتوي على `MinStock`
- الكود يستخدم `ExpiryDate` في جدول `Drugs` لكنه في جدول `Inventory`

#### الإصلاحات:
```sql
-- استعلام المخزون المنخفض المصحح
SELECT COUNT(*)
FROM (
    SELECT d.DrugID
    FROM Drugs d
    LEFT JOIN Inventory i ON d.DrugID = i.DrugID
    WHERE d.IsActive = 1
    GROUP BY d.DrugID, d.MinStock
    HAVING ISNULL(SUM(i.Quantity), 0) <= d.MinStock
) as LowStock

-- استعلام الأدوية منتهية الصلاحية المصحح
SELECT COUNT(*) FROM Inventory WHERE ExpiryDate <= GETDATE()
```

## التوصيات للمستقبل

1. **مراجعة شاملة**: يُنصح بمراجعة جميع الكلاسات الأخرى للتأكد من عدم وجود مشاكل مماثلة
2. **اختبار شامل**: اختبار جميع العمليات (إضافة، تعديل، حذف، بحث) لكل وحدة
3. **توثيق قاعدة البيانات**: إنشاء وثائق تحدد أسماء الحقول الصحيحة لكل جدول
4. **استخدام ORM**: النظر في استخدام Entity Framework أو ORM آخر لتجنب هذه المشاكل مستقبلاً

## إصلاحات إضافية - DrugManager

### المشكلة الجديدة:
كانت دالة `MapReaderToDrug()` تحاول قراءة حقول بأسماء خاطئة مما تسبب في `IndexOutOfRangeException`.

### الإصلاحات:
```csharp
// تم تصحيح أسماء الحقول في MapReaderToDrug()
TradeName = reader["DrugName"].ToString(), // بدلاً من TradeName
DosageForm = reader["Form"].ToString(), // بدلاً من DosageForm
PackSize = Convert.ToInt32(reader["PackageSize"]), // بدلاً من PackSize
MinStockLevel = Convert.ToInt32(reader["MinStock"]), // بدلاً من MinStockLevel
MaxStockLevel = Convert.ToInt32(reader["MaxStock"]), // بدلاً من MaxStockLevel

// تم تصحيح استعلامات البحث والترتيب
ORDER BY d.DrugName // بدلاً من d.TradeName
WHERE d.DrugName LIKE @SearchTerm // بدلاً من d.TradeName

// تم تصحيح معاملات الإضافة والتحديث
@DrugName بدلاً من @TradeName
@Form بدلاً من @DosageForm
@MinStock بدلاً من @MinStockLevel
@MaxStock بدلاً من @MaxStockLevel

// تم تصحيح استعلام المخزون المنخفض
LEFT JOIN (SELECT DrugID, SUM(Quantity) as TotalStock FROM Inventory GROUP BY DrugID) i ON d.DrugID = i.DrugID
WHERE d.IsActive = 1 AND ISNULL(i.TotalStock, 0) <= d.MinStock
```

## حالة الإصلاح
✅ **مكتمل** - تم إصلاح جميع المشاكل المكتشفة بما في ذلك مشاكل DrugManager وجميع العمليات يجب أن تعمل الآن بشكل صحيح.
