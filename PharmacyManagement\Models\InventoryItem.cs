using System;

namespace PharmacyManagement.Models
{
    /// <summary>
    /// نموذج عنصر المخزون - Inventory Item Model
    /// </summary>
    public class InventoryItem
    {
        #region Properties - الخصائص

        /// <summary>
        /// معرف الدواء
        /// </summary>
        public int DrugID { get; set; }

        /// <summary>
        /// كود الدواء
        /// </summary>
        public string DrugCode { get; set; }

        /// <summary>
        /// اسم الدواء
        /// </summary>
        public string DrugName { get; set; }

        /// <summary>
        /// الفئة
        /// </summary>
        public string Category { get; set; }

        /// <summary>
        /// الشركة المصنعة
        /// </summary>
        public string Manufacturer { get; set; }

        /// <summary>
        /// الوحدة
        /// </summary>
        public string Unit { get; set; }

        /// <summary>
        /// سعر الشراء
        /// </summary>
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// سعر الشراء (اسم بديل)
        /// </summary>
        public decimal PurchasePrice { get; set; }

        /// <summary>
        /// سعر البيع
        /// </summary>
        public decimal SellingPrice { get; set; }

        /// <summary>
        /// المخزون الحالي
        /// </summary>
        public decimal CurrentStock { get; set; }

        /// <summary>
        /// الحد الأدنى للمخزون
        /// </summary>
        public decimal MinimumStock { get; set; }

        /// <summary>
        /// الحد الأقصى للمخزون
        /// </summary>
        public decimal MaximumStock { get; set; }

        /// <summary>
        /// تاريخ الانتهاء
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// نشط
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// معرف المخزون
        /// </summary>
        public int InventoryID { get; set; }

        /// <summary>
        /// الاسم العلمي
        /// </summary>
        public string ScientificName { get; set; }

        /// <summary>
        /// الباركود
        /// </summary>
        public string Barcode { get; set; }

        /// <summary>
        /// اسم الفئة
        /// </summary>
        public string CategoryName { get; set; }

        /// <summary>
        /// اسم الشركة المصنعة
        /// </summary>
        public string ManufacturerName { get; set; }

        /// <summary>
        /// معرف المستودع
        /// </summary>
        public int WarehouseID { get; set; }

        /// <summary>
        /// اسم المستودع
        /// </summary>
        public string WarehouseName { get; set; }

        /// <summary>
        /// رقم الدفعة
        /// </summary>
        public string BatchNumber { get; set; }

        /// <summary>
        /// الكمية
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// الكمية المحجوزة
        /// </summary>
        public decimal ReservedQuantity { get; set; }

        /// <summary>
        /// الكمية المتاحة
        /// </summary>
        public decimal AvailableQuantity { get; set; }

        /// <summary>
        /// تاريخ التصنيع
        /// </summary>
        public DateTime? ManufacturingDate { get; set; }

        /// <summary>
        /// سعر البيع
        /// </summary>
        public decimal SalePrice { get; set; }

        /// <summary>
        /// الحد الأدنى للمخزون
        /// </summary>
        public decimal MinStock { get; set; }

        /// <summary>
        /// الحد الأقصى للمخزون
        /// </summary>
        public decimal MaxStock { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// تاريخ التحديث
        /// </summary>
        public DateTime? UpdatedDate { get; set; }

        /// <summary>
        /// الاسم التجاري (اسم بديل لـ DrugName)
        /// </summary>
        public string TradeName
        {
            get { return DrugName; }
            set { DrugName = value; }
        }

        /// <summary>
        /// الحد الأدنى للمخزون (اسم بديل)
        /// </summary>
        public decimal MinStockLevel
        {
            get { return MinimumStock; }
            set { MinimumStock = value; }
        }

        /// <summary>
        /// الحد الأقصى للمخزون (اسم بديل)
        /// </summary>
        public decimal MaxStockLevel
        {
            get { return MaximumStock; }
            set { MaximumStock = value; }
        }

        #endregion

        #region Calculated Properties - الخصائص المحسوبة

        /// <summary>
        /// حالة المخزون
        /// </summary>
        public string StockStatus
        {
            get
            {
                if (CurrentStock <= 0)
                    return "نفد المخزون";
                else if (CurrentStock <= MinimumStock)
                    return "مخزون منخفض";
                else if (CurrentStock >= MaximumStock)
                    return "مخزون مرتفع";
                else
                    return "مخزون طبيعي";
            }
        }

        /// <summary>
        /// قيمة المخزون الحالي
        /// </summary>
        public decimal StockValue
        {
            get { return CurrentStock * UnitPrice; }
        }

        /// <summary>
        /// هامش الربح
        /// </summary>
        public decimal ProfitMargin
        {
            get
            {
                if (UnitPrice == 0) return 0;
                return ((SellingPrice - UnitPrice) / UnitPrice) * 100;
            }
        }

        /// <summary>
        /// حالة انتهاء الصلاحية
        /// </summary>
        public string ExpiryStatus
        {
            get
            {
                if (!ExpiryDate.HasValue)
                    return "غير محدد";

                var daysToExpiry = (ExpiryDate.Value - DateTime.Now).Days;

                if (daysToExpiry < 0)
                    return "منتهي الصلاحية";
                else if (daysToExpiry <= 30)
                    return "قريب الانتهاء";
                else if (daysToExpiry <= 90)
                    return "تحذير";
                else
                    return "صالح";
            }
        }

        /// <summary>
        /// لون حالة المخزون
        /// </summary>
        public System.Drawing.Color StockStatusColor
        {
            get
            {
                switch (StockStatus)
                {
                    case "نفد المخزون":
                        return System.Drawing.Color.Red;
                    case "مخزون منخفض":
                        return System.Drawing.Color.Orange;
                    case "مخزون مرتفع":
                        return System.Drawing.Color.Blue;
                    default:
                        return System.Drawing.Color.Green;
                }
            }
        }

        /// <summary>
        /// لون حالة انتهاء الصلاحية
        /// </summary>
        public System.Drawing.Color ExpiryStatusColor
        {
            get
            {
                switch (ExpiryStatus)
                {
                    case "منتهي الصلاحية":
                        return System.Drawing.Color.Red;
                    case "قريب الانتهاء":
                        return System.Drawing.Color.Orange;
                    case "تحذير":
                        return System.Drawing.Color.Yellow;
                    default:
                        return System.Drawing.Color.Green;
                }
            }
        }

        #endregion

        #region Constructor - المنشئ

        /// <summary>
        /// منشئ افتراضي
        /// </summary>
        public InventoryItem()
        {
            IsActive = true;
        }

        #endregion

        #region Methods - الطرق

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(DrugCode) &&
                   !string.IsNullOrWhiteSpace(DrugName) &&
                   UnitPrice >= 0 &&
                   SellingPrice >= 0 &&
                   CurrentStock >= 0 &&
                   MinimumStock >= 0 &&
                   MaximumStock >= MinimumStock;
        }

        /// <summary>
        /// تحديث المخزون
        /// </summary>
        /// <param name="quantity">الكمية</param>
        /// <param name="isAddition">إضافة أم خصم</param>
        public void UpdateStock(decimal quantity, bool isAddition)
        {
            if (isAddition)
                CurrentStock += quantity;
            else
                CurrentStock = Math.Max(0, CurrentStock - quantity);
        }

        /// <summary>
        /// نسخ البيانات من دواء
        /// </summary>
        /// <param name="drug">الدواء</param>
        public void CopyFromDrug(Drug drug)
        {
            DrugID = drug.DrugID;
            DrugCode = drug.DrugCode;
            DrugName = drug.DrugName;
            Category = drug.CategoryName;
            Manufacturer = drug.ManufacturerName;
            Unit = drug.Unit;
            UnitPrice = drug.PurchasePrice;
            SellingPrice = drug.SalePrice;
            CurrentStock = drug.CurrentStock;
            MinimumStock = drug.MinStockLevel;
            MaximumStock = drug.MaxStockLevel;
            ExpiryDate = drug.ExpiryDate;
            IsActive = drug.IsActive;
        }

        #endregion
    }
}
