using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نافذة التقارير الشاملة - Universal Report Form
    /// </summary>
    public partial class UniversalReportForm : Form
    {
        #region Fields - الحقول

        private string _reportType;
        private DateTime _fromDate;
        private DateTime _toDate;

        #endregion

        #region Constructor - المنشئ

        /// <summary>
        /// منشئ نافذة التقارير الشاملة
        /// </summary>
        /// <param name="reportType">نوع التقرير</param>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        public UniversalReportForm(string reportType, DateTime fromDate, DateTime toDate)
        {
            InitializeComponent();
            _reportType = reportType;
            _fromDate = fromDate;
            _toDate = toDate;
            SetupForm();
            LoadReport();
        }

        #endregion

        #region Form Setup - إعداد النافذة

        /// <summary>
        /// إعداد النافذة
        /// </summary>
        private void SetupForm()
        {
            this.Text = GetReportTitle(_reportType);
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterParent;
            this.WindowState = FormWindowState.Maximized;
            
            SetupWebBrowser();
        }

        /// <summary>
        /// إعداد متصفح الويب
        /// </summary>
        private void SetupWebBrowser()
        {
            webBrowser.AllowWebBrowserDrop = false;
            webBrowser.IsWebBrowserContextMenuEnabled = false;
            webBrowser.WebBrowserShortcutsEnabled = false;
            webBrowser.ScrollBarsEnabled = true;
        }

        /// <summary>
        /// الحصول على عنوان التقرير
        /// </summary>
        private string GetReportTitle(string reportType)
        {
            switch (reportType.ToLower())
            {
                case "sales": return "تقرير المبيعات";
                case "inventory": return "تقرير المخزون";
                case "customers": return "تقرير العملاء";
                case "suppliers": return "تقرير الموردين";
                case "financial": return "التقرير المالي";
                case "topselling": return "تقرير الأدوية الأكثر مبيعاً";
                case "expired": return "تقرير الأدوية منتهية الصلاحية";
                case "profit": return "تقرير الأرباح";
                case "comprehensive": return "التقرير الشامل";
                default: return "تقرير";
            }
        }

        #endregion

        #region Report Loading - تحميل التقرير

        /// <summary>
        /// تحميل التقرير
        /// </summary>
        private void LoadReport()
        {
            try
            {
                string htmlContent = GenerateReportHTML();
                webBrowser.DocumentText = htmlContent;
                
                LogManager.LogInfo($"تم تحميل تقرير {_reportType} بنجاح");
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تحميل التقرير: {ex.Message}");
                MessageBox.Show($"خطأ في تحميل التقرير: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إنشاء HTML للتقرير
        /// </summary>
        private string GenerateReportHTML()
        {
            var html = new StringBuilder();
            
            // رأس HTML
            html.AppendLine("<!DOCTYPE html>");
            html.AppendLine("<html dir='rtl' lang='ar'>");
            html.AppendLine("<head>");
            html.AppendLine("<meta charset='UTF-8'>");
            html.AppendLine($"<title>{GetReportTitle(_reportType)}</title>");
            html.AppendLine(GetReportCSS());
            html.AppendLine("</head>");
            html.AppendLine("<body>");
            
            // محتوى التقرير
            html.AppendLine(GenerateReportContent());
            
            // تذييل HTML
            html.AppendLine("</body>");
            html.AppendLine("</html>");
            
            return html.ToString();
        }

        /// <summary>
        /// إنشاء CSS للتقرير
        /// </summary>
        private string GetReportCSS()
        {
            return @"
<style>
    body {
        font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
        margin: 0;
        padding: 20px;
        background-color: #f8f9fa;
        color: #333;
    }
    .container {
        max-width: 1200px;
        margin: 0 auto;
        background-color: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
    }
    .header {
        text-align: center;
        border-bottom: 3px solid #007bff;
        padding-bottom: 20px;
        margin-bottom: 30px;
    }
    .header h1 {
        color: #007bff;
        margin: 0;
        font-size: 28px;
    }
    .header .date-range {
        color: #666;
        margin-top: 10px;
        font-size: 16px;
    }
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 10px;
        text-align: center;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }
    .stat-card h3 {
        margin: 0 0 10px 0;
        font-size: 16px;
        opacity: 0.9;
    }
    .stat-card .value {
        font-size: 24px;
        font-weight: bold;
        margin: 0;
    }
    table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
        font-size: 14px;
    }
    th, td {
        border: 1px solid #dee2e6;
        padding: 12px 8px;
        text-align: right;
    }
    th {
        background-color: #f8f9fa;
        font-weight: bold;
        color: #495057;
    }
    tr:nth-child(even) {
        background-color: #f8f9fa;
    }
    tr:hover {
        background-color: #e9ecef;
    }
    .section {
        margin-bottom: 40px;
    }
    .section h2 {
        color: #495057;
        border-bottom: 2px solid #dee2e6;
        padding-bottom: 10px;
        margin-bottom: 20px;
    }
    .footer {
        margin-top: 40px;
        padding-top: 20px;
        border-top: 1px solid #dee2e6;
        text-align: center;
        color: #6c757d;
        font-size: 12px;
    }
    .no-data {
        text-align: center;
        color: #6c757d;
        font-style: italic;
        padding: 40px;
    }
    .highlight {
        background-color: #fff3cd !important;
    }
    .danger {
        background-color: #f8d7da !important;
    }
    .success {
        background-color: #d1ecf1 !important;
    }
</style>";
        }

        /// <summary>
        /// إنشاء محتوى التقرير
        /// </summary>
        private string GenerateReportContent()
        {
            var content = new StringBuilder();
            
            content.AppendLine("<div class='container'>");
            
            // رأس التقرير
            content.AppendLine("<div class='header'>");
            content.AppendLine($"<h1>📊 {GetReportTitle(_reportType)}</h1>");
            content.AppendLine($"<div class='date-range'>من {_fromDate:yyyy/MM/dd} إلى {_toDate:yyyy/MM/dd}</div>");
            content.AppendLine($"<div class='date-range'>تاريخ الإنشاء: {DateTime.Now:yyyy/MM/dd HH:mm}</div>");
            content.AppendLine("</div>");
            
            // محتوى التقرير حسب النوع
            switch (_reportType.ToLower())
            {
                case "sales":
                    content.AppendLine(GenerateSalesReportContent());
                    break;
                case "inventory":
                    content.AppendLine(GenerateInventoryReportContent());
                    break;
                case "customers":
                    content.AppendLine(GenerateCustomersReportContent());
                    break;
                case "suppliers":
                    content.AppendLine(GenerateSuppliersReportContent());
                    break;
                case "financial":
                    content.AppendLine(GenerateFinancialReportContent());
                    break;
                case "topselling":
                    content.AppendLine(GenerateTopSellingReportContent());
                    break;
                case "expired":
                    content.AppendLine(GenerateExpiredReportContent());
                    break;
                case "profit":
                    content.AppendLine(GenerateProfitReportContent());
                    break;
                case "comprehensive":
                    content.AppendLine(GenerateComprehensiveReportContent());
                    break;
                default:
                    content.AppendLine("<div class='no-data'>نوع التقرير غير مدعوم</div>");
                    break;
            }
            
            // تذييل التقرير
            content.AppendLine("<div class='footer'>");
            content.AppendLine("<p>تم إنشاء هذا التقرير بواسطة نظام إدارة الصيدلية</p>");
            content.AppendLine($"<p>المستخدم: {UserManager.CurrentUser?.FullName ?? "غير محدد"}</p>");
            content.AppendLine("</div>");
            
            content.AppendLine("</div>");
            
            return content.ToString();
        }

        #endregion

        #region Event Handlers - معالجات الأحداث

        /// <summary>
        /// طباعة التقرير
        /// </summary>
        private void btnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                webBrowser.ShowPrintDialog();
                LogManager.LogInfo($"تم طباعة تقرير {_reportType}");
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في طباعة التقرير: {ex.Message}");
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تصدير التقرير
        /// </summary>
        private void btnExport_Click(object sender, EventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "HTML Files (*.html)|*.html|PDF Files (*.pdf)|*.pdf",
                    Title = "تصدير التقرير",
                    FileName = $"{GetReportTitle(_reportType)}_{DateTime.Now:yyyyMMdd_HHmmss}"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    if (saveDialog.FileName.EndsWith(".html"))
                    {
                        File.WriteAllText(saveDialog.FileName, webBrowser.DocumentText, Encoding.UTF8);
                    }
                    else if (saveDialog.FileName.EndsWith(".pdf"))
                    {
                        // تصدير PDF (يتطلب مكتبة إضافية)
                        MessageBox.Show("تصدير PDF قيد التطوير", "معلومات", 
                                      MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }
                    
                    MessageBox.Show("تم تصدير التقرير بنجاح", "نجح", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                    
                    LogManager.LogInfo($"تم تصدير تقرير {_reportType} إلى {saveDialog.FileName}");
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تصدير التقرير: {ex.Message}");
                MessageBox.Show($"خطأ في تصدير التقرير: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إغلاق النافذة
        /// </summary>
        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        #endregion

        #region Report Content Generation - إنشاء محتوى التقارير

        /// <summary>
        /// إنشاء محتوى تقرير المبيعات
        /// </summary>
        private string GenerateSalesReportContent()
        {
            var content = new StringBuilder();

            try
            {
                var salesReport = ReportsManager.GetSalesReport(_fromDate, _toDate);
                var salesData = ReportsManager.GetSalesData(_fromDate, _toDate);

                // إحصائيات المبيعات
                content.AppendLine("<div class='stats-grid'>");
                content.AppendLine($"<div class='stat-card'><h3>إجمالي المبيعات</h3><p class='value'>{salesReport.TotalAmount:N2} ر.ي</p></div>");
                content.AppendLine($"<div class='stat-card'><h3>عدد الفواتير</h3><p class='value'>{salesReport.InvoiceCount}</p></div>");
                content.AppendLine($"<div class='stat-card'><h3>إجمالي الخصومات</h3><p class='value'>{salesReport.TotalDiscount:N2} ر.ي</p></div>");
                content.AppendLine($"<div class='stat-card'><h3>إجمالي الضرائب</h3><p class='value'>{salesReport.TotalTax:N2} ر.ي</p></div>");
                content.AppendLine("</div>");

                // جدول المبيعات
                content.AppendLine("<div class='section'>");
                content.AppendLine("<h2>📋 تفاصيل المبيعات</h2>");

                if (salesData.Count > 0)
                {
                    content.AppendLine("<table>");
                    content.AppendLine("<thead>");
                    content.AppendLine("<tr><th>رقم البيع</th><th>التاريخ</th><th>العميل</th><th>المبلغ الإجمالي</th><th>الخصم</th><th>الضريبة</th><th>الصافي</th><th>طريقة الدفع</th></tr>");
                    content.AppendLine("</thead>");
                    content.AppendLine("<tbody>");

                    foreach (var sale in salesData)
                    {
                        content.AppendLine("<tr>");
                        content.AppendLine($"<td>{sale.SaleID}</td>");
                        content.AppendLine($"<td>{sale.SaleDate:yyyy/MM/dd}</td>");
                        content.AppendLine($"<td>{sale.CustomerName}</td>");
                        content.AppendLine($"<td>{sale.TotalAmount:N2}</td>");
                        content.AppendLine($"<td>{sale.DiscountAmount:N2}</td>");
                        content.AppendLine($"<td>{sale.TaxAmount:N2}</td>");
                        content.AppendLine($"<td>{sale.NetAmount:N2}</td>");
                        content.AppendLine($"<td>{sale.PaymentMethod}</td>");
                        content.AppendLine("</tr>");
                    }

                    content.AppendLine("</tbody>");
                    content.AppendLine("</table>");
                }
                else
                {
                    content.AppendLine("<div class='no-data'>لا توجد مبيعات في الفترة المحددة</div>");
                }

                content.AppendLine("</div>");
            }
            catch (Exception ex)
            {
                content.AppendLine($"<div class='no-data'>خطأ في تحميل بيانات المبيعات: {ex.Message}</div>");
            }

            return content.ToString();
        }

        /// <summary>
        /// إنشاء محتوى تقرير المخزون
        /// </summary>
        private string GenerateInventoryReportContent()
        {
            var content = new StringBuilder();

            try
            {
                var inventoryReport = ReportsManager.GetInventoryReport();
                var inventoryData = InventoryManager.GetAllInventoryItems();

                // إحصائيات المخزون
                content.AppendLine("<div class='stats-grid'>");
                content.AppendLine($"<div class='stat-card'><h3>إجمالي الأدوية</h3><p class='value'>{inventoryReport.TotalDrugs}</p></div>");
                content.AppendLine($"<div class='stat-card'><h3>مخزون منخفض</h3><p class='value'>{inventoryReport.LowStockCount}</p></div>");
                content.AppendLine($"<div class='stat-card'><h3>منتهي الصلاحية</h3><p class='value'>{inventoryReport.ExpiredCount}</p></div>");
                content.AppendLine($"<div class='stat-card'><h3>قيمة المخزون</h3><p class='value'>{inventoryReport.TotalInventoryValue:N2} ر.ي</p></div>");
                content.AppendLine("</div>");

                // جدول المخزون
                content.AppendLine("<div class='section'>");
                content.AppendLine("<h2>📦 تفاصيل المخزون</h2>");

                if (inventoryData.Count > 0)
                {
                    content.AppendLine("<table>");
                    content.AppendLine("<thead>");
                    content.AppendLine("<tr><th>كود الدواء</th><th>اسم الدواء</th><th>الفئة</th><th>الشركة المصنعة</th><th>المخزون الحالي</th><th>الحد الأدنى</th><th>حالة المخزون</th></tr>");
                    content.AppendLine("</thead>");
                    content.AppendLine("<tbody>");

                    foreach (var item in inventoryData)
                    {
                        string rowClass = "";
                        if (item.CurrentStock <= 0)
                            rowClass = "danger";
                        else if (item.CurrentStock <= item.MinimumStock)
                            rowClass = "highlight";

                        content.AppendLine($"<tr class='{rowClass}'>");
                        content.AppendLine($"<td>{item.DrugCode}</td>");
                        content.AppendLine($"<td>{item.DrugName}</td>");
                        content.AppendLine($"<td>{item.Category ?? "غير محدد"}</td>");
                        content.AppendLine($"<td>{item.Manufacturer ?? "غير محدد"}</td>");
                        content.AppendLine($"<td>{item.CurrentStock:N0}</td>");
                        content.AppendLine($"<td>{item.MinimumStock:N0}</td>");
                        content.AppendLine($"<td>{item.StockStatus}</td>");
                        content.AppendLine("</tr>");
                    }

                    content.AppendLine("</tbody>");
                    content.AppendLine("</table>");
                }
                else
                {
                    content.AppendLine("<div class='no-data'>لا توجد بيانات مخزون</div>");
                }

                content.AppendLine("</div>");
            }
            catch (Exception ex)
            {
                content.AppendLine($"<div class='no-data'>خطأ في تحميل بيانات المخزون: {ex.Message}</div>");
            }

            return content.ToString();
        }

        /// <summary>
        /// إنشاء محتوى تقرير العملاء
        /// </summary>
        private string GenerateCustomersReportContent()
        {
            var content = new StringBuilder();

            try
            {
                var customersReport = ReportsManager.GetCustomersReport(_fromDate, _toDate);
                var customersData = ReportsManager.GetCustomersData(_fromDate, _toDate);

                // إحصائيات العملاء
                content.AppendLine("<div class='stats-grid'>");
                content.AppendLine($"<div class='stat-card'><h3>إجمالي العملاء</h3><p class='value'>{customersReport.Summary.TotalCustomers}</p></div>");
                content.AppendLine($"<div class='stat-card'><h3>العملاء النشطون</h3><p class='value'>{customersReport.Summary.ActiveCustomers}</p></div>");
                content.AppendLine($"<div class='stat-card'><h3>إجمالي الأرصدة</h3><p class='value'>{customersReport.Summary.TotalBalance:N2} ر.ي</p></div>");
                content.AppendLine($"<div class='stat-card'><h3>متوسط الحد الائتماني</h3><p class='value'>{customersReport.Summary.AverageCreditLimit:N2} ر.ي</p></div>");
                content.AppendLine("</div>");

                // جدول العملاء
                content.AppendLine("<div class='section'>");
                content.AppendLine("<h2>👥 تفاصيل العملاء</h2>");

                if (customersData.Count > 0)
                {
                    content.AppendLine("<table>");
                    content.AppendLine("<thead>");
                    content.AppendLine("<tr><th>اسم العميل</th><th>الهاتف</th><th>البريد الإلكتروني</th><th>الرصيد</th><th>الحد الائتماني</th><th>الحالة</th></tr>");
                    content.AppendLine("</thead>");
                    content.AppendLine("<tbody>");

                    foreach (var customer in customersData)
                    {
                        string statusClass = customer.IsActive ? "success" : "danger";

                        content.AppendLine($"<tr class='{statusClass}'>");
                        content.AppendLine($"<td>{customer.CustomerName}</td>");
                        content.AppendLine($"<td>{customer.Phone}</td>");
                        content.AppendLine($"<td>{customer.Email}</td>");
                        content.AppendLine($"<td>{customer.Balance:N2}</td>");
                        content.AppendLine($"<td>{customer.CreditLimit:N2}</td>");
                        content.AppendLine($"<td>{(customer.IsActive ? "نشط" : "غير نشط")}</td>");
                        content.AppendLine("</tr>");
                    }

                    content.AppendLine("</tbody>");
                    content.AppendLine("</table>");
                }
                else
                {
                    content.AppendLine("<div class='no-data'>لا توجد بيانات عملاء</div>");
                }

                content.AppendLine("</div>");
            }
            catch (Exception ex)
            {
                content.AppendLine($"<div class='no-data'>خطأ في تحميل بيانات العملاء: {ex.Message}</div>");
            }

            return content.ToString();
        }

        /// <summary>
        /// إنشاء محتوى تقرير الموردين
        /// </summary>
        private string GenerateSuppliersReportContent()
        {
            var content = new StringBuilder();

            try
            {
                var suppliersData = ReportsManager.GetSuppliersData(_fromDate, _toDate);

                // إحصائيات الموردين
                content.AppendLine("<div class='stats-grid'>");
                content.AppendLine($"<div class='stat-card'><h3>إجمالي الموردين</h3><p class='value'>{suppliersData.Count}</p></div>");
                content.AppendLine($"<div class='stat-card'><h3>الموردين النشطون</h3><p class='value'>{suppliersData.Count(s => s.IsActive)}</p></div>");
                content.AppendLine($"<div class='stat-card'><h3>إجمالي المشتريات</h3><p class='value'>{suppliersData.Sum(s => s.TotalPurchases):N2} ر.ي</p></div>");
                content.AppendLine($"<div class='stat-card'><h3>إجمالي الطلبات</h3><p class='value'>{suppliersData.Sum(s => s.TotalOrders)}</p></div>");
                content.AppendLine("</div>");

                // جدول الموردين
                content.AppendLine("<div class='section'>");
                content.AppendLine("<h2>🏭 تفاصيل الموردين</h2>");

                if (suppliersData.Count > 0)
                {
                    content.AppendLine("<table>");
                    content.AppendLine("<thead>");
                    content.AppendLine("<tr><th>اسم المورد</th><th>جهة الاتصال</th><th>الهاتف</th><th>إجمالي المشتريات</th><th>عدد الطلبات</th><th>الرصيد</th><th>الحالة</th></tr>");
                    content.AppendLine("</thead>");
                    content.AppendLine("<tbody>");

                    foreach (var supplier in suppliersData)
                    {
                        string statusClass = supplier.IsActive ? "success" : "danger";

                        content.AppendLine($"<tr class='{statusClass}'>");
                        content.AppendLine($"<td>{supplier.SupplierName}</td>");
                        content.AppendLine($"<td>{supplier.ContactPerson}</td>");
                        content.AppendLine($"<td>{supplier.Phone}</td>");
                        content.AppendLine($"<td>{supplier.TotalPurchases:N2}</td>");
                        content.AppendLine($"<td>{supplier.TotalOrders}</td>");
                        content.AppendLine($"<td>{supplier.Balance:N2}</td>");
                        content.AppendLine($"<td>{(supplier.IsActive ? "نشط" : "غير نشط")}</td>");
                        content.AppendLine("</tr>");
                    }

                    content.AppendLine("</tbody>");
                    content.AppendLine("</table>");
                }
                else
                {
                    content.AppendLine("<div class='no-data'>لا توجد بيانات موردين</div>");
                }

                content.AppendLine("</div>");
            }
            catch (Exception ex)
            {
                content.AppendLine($"<div class='no-data'>خطأ في تحميل بيانات الموردين: {ex.Message}</div>");
            }

            return content.ToString();
        }

        /// <summary>
        /// إنشاء محتوى التقرير المالي
        /// </summary>
        private string GenerateFinancialReportContent()
        {
            var content = new StringBuilder();

            try
            {
                var financialReport = ReportsManager.GetFinancialReport(_fromDate, _toDate);

                // إحصائيات مالية
                content.AppendLine("<div class='stats-grid'>");
                content.AppendLine($"<div class='stat-card'><h3>إجمالي المبيعات</h3><p class='value'>{financialReport.TotalSales:N2} ر.ي</p></div>");
                content.AppendLine($"<div class='stat-card'><h3>إجمالي المشتريات</h3><p class='value'>{financialReport.TotalPurchases:N2} ر.ي</p></div>");
                content.AppendLine($"<div class='stat-card'><h3>الربح الإجمالي</h3><p class='value'>{financialReport.GrossProfit:N2} ر.ي</p></div>");
                content.AppendLine($"<div class='stat-card'><h3>هامش الربح</h3><p class='value'>{financialReport.ProfitMargin:N1}%</p></div>");
                content.AppendLine("</div>");

                // تفاصيل مالية
                content.AppendLine("<div class='section'>");
                content.AppendLine("<h2>💰 التفاصيل المالية</h2>");
                content.AppendLine("<table>");
                content.AppendLine("<thead>");
                content.AppendLine("<tr><th>البيان</th><th>المبلغ (ر.ي)</th></tr>");
                content.AppendLine("</thead>");
                content.AppendLine("<tbody>");
                content.AppendLine($"<tr><td>إجمالي المبيعات</td><td>{financialReport.TotalSales:N2}</td></tr>");
                content.AppendLine($"<tr><td>إجمالي المشتريات</td><td>{financialReport.TotalPurchases:N2}</td></tr>");
                content.AppendLine($"<tr><td>إجمالي الخصومات</td><td>{financialReport.TotalDiscounts:N2}</td></tr>");
                content.AppendLine($"<tr><td>إجمالي الضرائب</td><td>{financialReport.TotalTax:N2}</td></tr>");
                content.AppendLine($"<tr class='highlight'><td><strong>الربح الإجمالي</strong></td><td><strong>{financialReport.GrossProfit:N2}</strong></td></tr>");
                content.AppendLine($"<tr class='success'><td><strong>صافي الربح</strong></td><td><strong>{financialReport.NetProfit:N2}</strong></td></tr>");
                content.AppendLine("</tbody>");
                content.AppendLine("</table>");
                content.AppendLine("</div>");
            }
            catch (Exception ex)
            {
                content.AppendLine($"<div class='no-data'>خطأ في تحميل البيانات المالية: {ex.Message}</div>");
            }

            return content.ToString();
        }

        /// <summary>
        /// إنشاء محتوى تقرير الأدوية الأكثر مبيعاً
        /// </summary>
        private string GenerateTopSellingReportContent()
        {
            var content = new StringBuilder();

            try
            {
                var topSellingDrugs = ReportsManager.GetTopSellingDrugs(_fromDate, _toDate, 20);

                // إحصائيات
                content.AppendLine("<div class='stats-grid'>");
                content.AppendLine($"<div class='stat-card'><h3>عدد الأدوية</h3><p class='value'>{topSellingDrugs.Count}</p></div>");
                content.AppendLine($"<div class='stat-card'><h3>إجمالي الكمية المباعة</h3><p class='value'>{topSellingDrugs.Sum(d => d.TotalQuantitySold):N0}</p></div>");
                content.AppendLine($"<div class='stat-card'><h3>إجمالي قيمة المبيعات</h3><p class='value'>{topSellingDrugs.Sum(d => d.TotalSalesAmount):N2} ر.ي</p></div>");
                content.AppendLine($"<div class='stat-card'><h3>متوسط السعر</h3><p class='value'>{(topSellingDrugs.Count > 0 ? topSellingDrugs.Average(d => d.AveragePrice) : 0):N2} ر.ي</p></div>");
                content.AppendLine("</div>");

                // جدول الأدوية الأكثر مبيعاً
                content.AppendLine("<div class='section'>");
                content.AppendLine("<h2>🏆 الأدوية الأكثر مبيعاً</h2>");

                if (topSellingDrugs.Count > 0)
                {
                    content.AppendLine("<table>");
                    content.AppendLine("<thead>");
                    content.AppendLine("<tr><th>الترتيب</th><th>كود الدواء</th><th>اسم الدواء</th><th>الفئة</th><th>الكمية المباعة</th><th>قيمة المبيعات</th><th>عدد المبيعات</th><th>متوسط السعر</th></tr>");
                    content.AppendLine("</thead>");
                    content.AppendLine("<tbody>");

                    foreach (var drug in topSellingDrugs)
                    {
                        string rankClass = drug.Rank <= 3 ? "highlight" : "";

                        content.AppendLine($"<tr class='{rankClass}'>");
                        content.AppendLine($"<td>{drug.Rank}</td>");
                        content.AppendLine($"<td>{drug.DrugCode}</td>");
                        content.AppendLine($"<td>{drug.DrugName}</td>");
                        content.AppendLine($"<td>{drug.CategoryName}</td>");
                        content.AppendLine($"<td>{drug.TotalQuantitySold:N0}</td>");
                        content.AppendLine($"<td>{drug.TotalSalesAmount:N2}</td>");
                        content.AppendLine($"<td>{drug.NumberOfSales}</td>");
                        content.AppendLine($"<td>{drug.AveragePrice:N2}</td>");
                        content.AppendLine("</tr>");
                    }

                    content.AppendLine("</tbody>");
                    content.AppendLine("</table>");
                }
                else
                {
                    content.AppendLine("<div class='no-data'>لا توجد مبيعات في الفترة المحددة</div>");
                }

                content.AppendLine("</div>");
            }
            catch (Exception ex)
            {
                content.AppendLine($"<div class='no-data'>خطأ في تحميل بيانات الأدوية الأكثر مبيعاً: {ex.Message}</div>");
            }

            return content.ToString();
        }

        /// <summary>
        /// إنشاء محتوى تقرير الأدوية منتهية الصلاحية
        /// </summary>
        private string GenerateExpiredReportContent()
        {
            var content = new StringBuilder();

            try
            {
                var expiredDrugs = ReportsManager.GetExpiredDrugs();

                // إحصائيات
                content.AppendLine("<div class='stats-grid'>");
                content.AppendLine($"<div class='stat-card'><h3>عدد الأدوية المنتهية</h3><p class='value'>{expiredDrugs.Count}</p></div>");
                content.AppendLine($"<div class='stat-card'><h3>إجمالي الكمية</h3><p class='value'>{expiredDrugs.Sum(d => d.Quantity):N0}</p></div>");
                content.AppendLine($"<div class='stat-card'><h3>إجمالي الخسارة</h3><p class='value'>{expiredDrugs.Sum(d => d.TotalLoss):N2} ر.ي</p></div>");
                content.AppendLine($"<div class='stat-card'><h3>متوسط أيام الانتهاء</h3><p class='value'>{(expiredDrugs.Count > 0 ? expiredDrugs.Average(d => d.DaysExpired) : 0):N0} يوم</p></div>");
                content.AppendLine("</div>");

                // جدول الأدوية منتهية الصلاحية
                content.AppendLine("<div class='section'>");
                content.AppendLine("<h2>⚠️ الأدوية منتهية الصلاحية</h2>");

                if (expiredDrugs.Count > 0)
                {
                    content.AppendLine("<table>");
                    content.AppendLine("<thead>");
                    content.AppendLine("<tr><th>كود الدواء</th><th>اسم الدواء</th><th>الفئة</th><th>رقم الدفعة</th><th>الكمية</th><th>تاريخ الانتهاء</th><th>أيام الانتهاء</th><th>الخسارة</th></tr>");
                    content.AppendLine("</thead>");
                    content.AppendLine("<tbody>");

                    foreach (var drug in expiredDrugs.OrderByDescending(d => d.DaysExpired))
                    {
                        content.AppendLine("<tr class='danger'>");
                        content.AppendLine($"<td>{drug.DrugCode}</td>");
                        content.AppendLine($"<td>{drug.DrugName}</td>");
                        content.AppendLine($"<td>{drug.CategoryName}</td>");
                        content.AppendLine($"<td>{drug.BatchNumber}</td>");
                        content.AppendLine($"<td>{drug.Quantity:N0}</td>");
                        content.AppendLine($"<td>{drug.ExpiryDate:yyyy/MM/dd}</td>");
                        content.AppendLine($"<td>{drug.DaysExpired}</td>");
                        content.AppendLine($"<td>{drug.TotalLoss:N2}</td>");
                        content.AppendLine("</tr>");
                    }

                    content.AppendLine("</tbody>");
                    content.AppendLine("</table>");
                }
                else
                {
                    content.AppendLine("<div class='no-data'>لا توجد أدوية منتهية الصلاحية</div>");
                }

                content.AppendLine("</div>");
            }
            catch (Exception ex)
            {
                content.AppendLine($"<div class='no-data'>خطأ في تحميل بيانات الأدوية منتهية الصلاحية: {ex.Message}</div>");
            }

            return content.ToString();
        }

        /// <summary>
        /// إنشاء محتوى تقرير الأرباح
        /// </summary>
        private string GenerateProfitReportContent()
        {
            var content = new StringBuilder();

            try
            {
                var financialReport = ReportsManager.GetFinancialReport(_fromDate, _toDate);

                // إحصائيات الأرباح
                content.AppendLine("<div class='stats-grid'>");
                content.AppendLine($"<div class='stat-card'><h3>الإيرادات</h3><p class='value'>{financialReport.TotalSales:N2} ر.ي</p></div>");
                content.AppendLine($"<div class='stat-card'><h3>التكاليف</h3><p class='value'>{financialReport.TotalPurchases:N2} ر.ي</p></div>");
                content.AppendLine($"<div class='stat-card'><h3>صافي الربح</h3><p class='value'>{financialReport.NetProfit:N2} ر.ي</p></div>");
                content.AppendLine($"<div class='stat-card'><h3>هامش الربح</h3><p class='value'>{financialReport.ProfitMargin:N1}%</p></div>");
                content.AppendLine("</div>");

                // تحليل الأرباح
                content.AppendLine("<div class='section'>");
                content.AppendLine("<h2>📈 تحليل الأرباح</h2>");
                content.AppendLine("<table>");
                content.AppendLine("<thead>");
                content.AppendLine("<tr><th>البيان</th><th>المبلغ (ر.ي)</th><th>النسبة من الإيرادات</th></tr>");
                content.AppendLine("</thead>");
                content.AppendLine("<tbody>");

                decimal totalRevenue = financialReport.TotalSales;

                content.AppendLine($"<tr class='success'><td>إجمالي الإيرادات</td><td>{totalRevenue:N2}</td><td>100.0%</td></tr>");
                content.AppendLine($"<tr><td>تكلفة البضاعة المباعة</td><td>{financialReport.TotalPurchases:N2}</td><td>{(totalRevenue > 0 ? (financialReport.TotalPurchases / totalRevenue * 100) : 0):N1}%</td></tr>");
                content.AppendLine($"<tr class='highlight'><td>الربح الإجمالي</td><td>{financialReport.GrossProfit:N2}</td><td>{(totalRevenue > 0 ? (financialReport.GrossProfit / totalRevenue * 100) : 0):N1}%</td></tr>");
                content.AppendLine($"<tr><td>الخصومات</td><td>{financialReport.TotalDiscounts:N2}</td><td>{(totalRevenue > 0 ? (financialReport.TotalDiscounts / totalRevenue * 100) : 0):N1}%</td></tr>");
                content.AppendLine($"<tr class='success'><td><strong>صافي الربح</strong></td><td><strong>{financialReport.NetProfit:N2}</strong></td><td><strong>{financialReport.ProfitMargin:N1}%</strong></td></tr>");

                content.AppendLine("</tbody>");
                content.AppendLine("</table>");
                content.AppendLine("</div>");
            }
            catch (Exception ex)
            {
                content.AppendLine($"<div class='no-data'>خطأ في تحميل بيانات الأرباح: {ex.Message}</div>");
            }

            return content.ToString();
        }

        /// <summary>
        /// إنشاء محتوى التقرير الشامل
        /// </summary>
        private string GenerateComprehensiveReportContent()
        {
            var content = new StringBuilder();

            content.AppendLine("<div class='section'>");
            content.AppendLine("<h2>📊 ملخص المبيعات</h2>");
            content.AppendLine(GenerateSalesReportContent());
            content.AppendLine("</div>");

            content.AppendLine("<div class='section'>");
            content.AppendLine("<h2>📦 ملخص المخزون</h2>");
            content.AppendLine(GenerateInventoryReportContent());
            content.AppendLine("</div>");

            content.AppendLine("<div class='section'>");
            content.AppendLine("<h2>💰 الملخص المالي</h2>");
            content.AppendLine(GenerateFinancialReportContent());
            content.AppendLine("</div>");

            return content.ToString();
        }

        #endregion
    }
}
