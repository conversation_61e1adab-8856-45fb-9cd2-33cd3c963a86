using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نافذة تقرير المبيعات - Sales Report Form
    /// </summary>
    public partial class SalesReportForm : Form
    {
        #region Fields - الحقول

        private DateTime _fromDate;
        private DateTime _toDate;

        #endregion

        #region Constructor - المنشئ

        /// <summary>
        /// منشئ نافذة تقرير المبيعات
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        public SalesReportForm(DateTime fromDate, DateTime toDate)
        {
            InitializeComponent();
            _fromDate = fromDate;
            _toDate = toDate;
            SetupForm();
            LoadReport();
        }

        #endregion

        #region Form Setup - إعداد النافذة

        /// <summary>
        /// إعداد النافذة
        /// </summary>
        private void SetupForm()
        {
            this.Text = $"تقرير المبيعات - من {_fromDate:yyyy/MM/dd} إلى {_toDate:yyyy/MM/dd}";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MinimumSize = new Size(800, 600);

            SetupDataGridView();
            ApplyFlatDesign();
        }

        /// <summary>
        /// إعداد DataGridView
        /// </summary>
        private void SetupDataGridView()
        {
            dgvSales.AutoGenerateColumns = false;
            dgvSales.AllowUserToAddRows = false;
            dgvSales.AllowUserToDeleteRows = false;
            dgvSales.ReadOnly = true;
            dgvSales.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvSales.MultiSelect = false;
        }

        /// <summary>
        /// تطبيق التصميم المسطح
        /// </summary>
        private void ApplyFlatDesign()
        {
            this.BackColor = Color.White;
            
            foreach (Control control in this.Controls)
            {
                if (control is Button button)
                {
                    button.FlatStyle = FlatStyle.Flat;
                    button.FlatAppearance.BorderSize = 0;
                }
            }
        }

        #endregion

        #region Data Loading - تحميل البيانات

        /// <summary>
        /// تحميل التقرير
        /// </summary>
        private void LoadReport()
        {
            try
            {
                var salesReport = ReportsManager.GetSalesReport(_fromDate, _toDate);
                
                // تحديث الإحصائيات
                UpdateStatistics(salesReport);
                
                // تحميل بيانات المبيعات
                var salesData = ReportsManager.GetSalesData(_fromDate, _toDate);
                dgvSales.DataSource = salesData;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تقرير المبيعات: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحديث الإحصائيات
        /// </summary>
        /// <param name="report">تقرير المبيعات</param>
        private void UpdateStatistics(SalesReport report)
        {
            if (report != null)
            {
                lblTotalInvoices.Text = "إجمالي الفواتير: " + report.InvoiceCount.ToString();
                lblTotalSales.Text = "إجمالي المبيعات: " + report.TotalAmount.ToString("F2") + " ر.ي";
                lblTotalDiscounts.Text = "إجمالي الخصومات: " + report.TotalDiscount.ToString("F2") + " ر.ي";
                lblTotalTax.Text = "إجمالي الضرائب: " + report.TotalTax.ToString("F2") + " ر.ي";
                decimal averageSale = report.InvoiceCount > 0 ? report.TotalAmount / report.InvoiceCount : 0;
                lblAverageSale.Text = "متوسط الفاتورة: " + averageSale.ToString("F2") + " ر.ي";
            }
            else
            {
                lblTotalInvoices.Text = "إجمالي الفواتير: 0";
                lblTotalSales.Text = "إجمالي المبيعات: 0.00 ر.ي";
                lblTotalDiscounts.Text = "إجمالي الخصومات: 0.00 ر.ي";
                lblTotalTax.Text = "إجمالي الضرائب: 0.00 ر.ي";
                lblAverageSale.Text = "متوسط الفاتورة: 0.00 ر.ي";
            }
        }

        #endregion

        #region Event Handlers - معالجات الأحداث

        /// <summary>
        /// تصدير التقرير
        /// </summary>
        private void btnExport_Click(object sender, EventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "Excel Files|*.xlsx|PDF Files|*.pdf|CSV Files|*.csv",
                    Title = "تصدير تقرير المبيعات",
                    FileName = $"Sales_Report_{_fromDate:yyyyMMdd}_{_toDate:yyyyMMdd}"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    var salesData = dgvSales.DataSource as System.Collections.Generic.List<Sale>;
                    if (salesData != null)
                    {
                        ExportManager.ExportSales(salesData, saveDialog.FileName);
                        MessageBox.Show("تم تصدير التقرير بنجاح", "نجح", 
                                      MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير التقرير: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// طباعة التقرير
        /// </summary>
        private void btnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                // يمكن إضافة وظيفة الطباعة هنا
                MessageBox.Show("وظيفة الطباعة ستكون متاحة قريباً", "معلومات", 
                              MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحديث التقرير
        /// </summary>
        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadReport();
        }

        /// <summary>
        /// إغلاق النافذة
        /// </summary>
        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// عرض تفاصيل الفاتورة
        /// </summary>
        private void dgvSales_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                var selectedSale = dgvSales.SelectedRows[0].DataBoundItem as Sale;
                if (selectedSale != null)
                {
                    MessageBox.Show($"عرض تفاصيل الفاتورة رقم: {selectedSale.SaleNumber}", "تفاصيل الفاتورة", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }

        #endregion
    }
}
