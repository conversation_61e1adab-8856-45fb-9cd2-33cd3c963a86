using System;

namespace PharmacyManagement.Models
{
    /// <summary>
    /// نموذج معاملة المورد
    /// </summary>
    public class SupplierTransaction
    {
        /// <summary>
        /// معرف المعاملة
        /// </summary>
        public int TransactionID { get; set; }

        /// <summary>
        /// معرف المورد
        /// </summary>
        public int SupplierID { get; set; }

        /// <summary>
        /// نوع المعاملة
        /// </summary>
        public string TransactionType { get; set; }

        /// <summary>
        /// تاريخ المعاملة
        /// </summary>
        public DateTime TransactionDate { get; set; }

        /// <summary>
        /// المبلغ
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// الوصف
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// رقم المرجع
        /// </summary>
        public string ReferenceNumber { get; set; }

        /// <summary>
        /// ملاحظات
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// منشئ المعاملة
        /// </summary>
        public int? CreatedBy { get; set; }
    }
}
