using System;
using System.Collections.Generic;

namespace PharmacyManagement.Models
{
    /// <summary>
    /// نموذج المبيعة
    /// </summary>
    public class Sale
    {
        public int SaleID { get; set; }
        public string InvoiceNumber { get; set; }
        public string SaleNumber { get; set; }
        public DateTime SaleDate { get; set; }
        public int? CustomerID { get; set; }  // جعلها nullable للعملاء النقديين
        public string CustomerName { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal SubTotal { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal NetAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal RemainingAmount { get; set; }
        public string PaymentMethod { get; set; }
        public string Status { get; set; }
        public string Notes { get; set; }  // إضافة الملاحظات
        public string SalesPersonName { get; set; }  // إضافة اسم البائع
        public bool IsActive { get; set; }
        public DateTime CreatedDate { get; set; }
        public int CreatedBy { get; set; }
    }

    /// <summary>
    /// نموذج إحصائيات المبيعات
    /// </summary>
    public class SalesStatistics
    {
        public SalesStatistics()
        {
            TopSellingDrugs = new List<TopSellingDrug>();
        }

        /// <summary>
        /// إجمالي عدد الفواتير
        /// </summary>
        public int TotalInvoices { get; set; }

        /// <summary>
        /// إجمالي المبيعات
        /// </summary>
        public decimal TotalSales { get; set; }

        /// <summary>
        /// متوسط قيمة الفاتورة
        /// </summary>
        public decimal AverageSale { get; set; }

        /// <summary>
        /// أفضل الأدوية مبيعاً
        /// </summary>
        public List<TopSellingDrug> TopSellingDrugs { get; set; }

        /// <summary>
        /// إجمالي الكمية المباعة
        /// </summary>
        public int TotalQuantitySold { get; set; }

        /// <summary>
        /// إجمالي الربح
        /// </summary>
        public decimal TotalProfit { get; set; }

        /// <summary>
        /// هامش الربح
        /// </summary>
        public decimal ProfitMargin
        {
            get
            {
                return TotalSales > 0 ? (TotalProfit / TotalSales) * 100 : 0;
            }
        }

        /// <summary>
        /// عدد العملاء الفريدين
        /// </summary>
        public int UniqueCustomers { get; set; }

        /// <summary>
        /// متوسط المبيعات لكل عميل
        /// </summary>
        public decimal AverageSalesPerCustomer
        {
            get
            {
                return UniqueCustomers > 0 ? TotalSales / UniqueCustomers : 0;
            }
        }
    }

    /// <summary>
    /// نموذج أفضل الأدوية مبيعاً
    /// </summary>
    public class TopSellingDrug
    {
        /// <summary>
        /// الترتيب
        /// </summary>
        public int Rank { get; set; }

        /// <summary>
        /// معرف الدواء
        /// </summary>
        public int DrugID { get; set; }

        /// <summary>
        /// كود الدواء
        /// </summary>
        public string DrugCode { get; set; }

        /// <summary>
        /// اسم الدواء
        /// </summary>
        public string DrugName { get; set; }

        /// <summary>
        /// اسم الفئة
        /// </summary>
        public string CategoryName { get; set; }

        /// <summary>
        /// الفئة (للتوافق مع الكود القديم)
        /// </summary>
        public string Category
        {
            get { return CategoryName; }
            set { CategoryName = value; }
        }

        /// <summary>
        /// اسم الشركة المصنعة
        /// </summary>
        public string ManufacturerName { get; set; }

        /// <summary>
        /// إجمالي الكمية المباعة
        /// </summary>
        public int TotalQuantitySold { get; set; }

        /// <summary>
        /// إجمالي الكمية (للتوافق مع الكود القديم)
        /// </summary>
        public int TotalQuantity
        {
            get { return TotalQuantitySold; }
            set { TotalQuantitySold = value; }
        }

        /// <summary>
        /// إجمالي قيمة المبيعات
        /// </summary>
        public decimal TotalSalesAmount { get; set; }

        /// <summary>
        /// إجمالي المبلغ (للتوافق مع الكود القديم)
        /// </summary>
        public decimal TotalAmount
        {
            get { return TotalSalesAmount; }
            set { TotalSalesAmount = value; }
        }

        /// <summary>
        /// عدد المبيعات
        /// </summary>
        public int NumberOfSales { get; set; }

        /// <summary>
        /// عدد الفواتير (للتوافق مع الكود القديم)
        /// </summary>
        public int InvoiceCount
        {
            get { return NumberOfSales; }
            set { NumberOfSales = value; }
        }

        /// <summary>
        /// متوسط السعر
        /// </summary>
        public decimal AveragePrice { get; set; }

        /// <summary>
        /// متوسط سعر البيع (للتوافق مع الكود القديم)
        /// </summary>
        public decimal AverageSellingPrice
        {
            get { return AveragePrice; }
            set { AveragePrice = value; }
        }

        /// <summary>
        /// نسبة المبيعات من الإجمالي
        /// </summary>
        public decimal SalesPercentage { get; set; }
    }

    /// <summary>
    /// نموذج المبيعات اليومية
    /// </summary>
    public class DailySales
    {
        /// <summary>
        /// التاريخ
        /// </summary>
        public DateTime SalesDate { get; set; }

        /// <summary>
        /// عدد الفواتير
        /// </summary>
        public int InvoiceCount { get; set; }

        /// <summary>
        /// إجمالي المبيعات
        /// </summary>
        public decimal TotalSales { get; set; }

        /// <summary>
        /// إجمالي الكمية
        /// </summary>
        public int TotalQuantity { get; set; }

        /// <summary>
        /// متوسط قيمة الفاتورة
        /// </summary>
        public decimal AverageInvoiceValue
        {
            get
            {
                return InvoiceCount > 0 ? TotalSales / InvoiceCount : 0;
            }
        }

        /// <summary>
        /// عدد العملاء الفريدين
        /// </summary>
        public int UniqueCustomers { get; set; }

        /// <summary>
        /// أفضل دواء مبيعاً في اليوم
        /// </summary>
        public string TopSellingDrug { get; set; }

        /// <summary>
        /// كمية أفضل دواء مبيعاً
        /// </summary>
        public int TopSellingDrugQuantity { get; set; }
    }

    /// <summary>
    /// نموذج المبيعات الشهرية
    /// </summary>
    public class MonthlySales
    {
        /// <summary>
        /// الشهر
        /// </summary>
        public int Month { get; set; }

        /// <summary>
        /// السنة
        /// </summary>
        public int Year { get; set; }

        /// <summary>
        /// اسم الشهر
        /// </summary>
        public string MonthName { get; set; }

        /// <summary>
        /// عدد الفواتير
        /// </summary>
        public int InvoiceCount { get; set; }

        /// <summary>
        /// إجمالي المبيعات
        /// </summary>
        public decimal TotalSales { get; set; }

        /// <summary>
        /// إجمالي الكمية
        /// </summary>
        public int TotalQuantity { get; set; }

        /// <summary>
        /// إجمالي الربح
        /// </summary>
        public decimal TotalProfit { get; set; }

        /// <summary>
        /// متوسط المبيعات اليومية
        /// </summary>
        public decimal DailyAverageSales { get; set; }

        /// <summary>
        /// نمو المبيعات مقارنة بالشهر السابق
        /// </summary>
        public decimal GrowthPercentage { get; set; }

        /// <summary>
        /// عدد العملاء الفريدين
        /// </summary>
        public int UniqueCustomers { get; set; }
    }

    /// <summary>
    /// نموذج أداء المبيعات حسب الفئة
    /// </summary>
    public class CategorySalesPerformance
    {
        /// <summary>
        /// اسم الفئة
        /// </summary>
        public string CategoryName { get; set; }

        /// <summary>
        /// عدد الأدوية في الفئة
        /// </summary>
        public int DrugCount { get; set; }

        /// <summary>
        /// إجمالي المبيعات
        /// </summary>
        public decimal TotalSales { get; set; }

        /// <summary>
        /// إجمالي الكمية
        /// </summary>
        public int TotalQuantity { get; set; }

        /// <summary>
        /// نسبة المبيعات من الإجمالي
        /// </summary>
        public decimal SalesPercentage { get; set; }

        /// <summary>
        /// متوسط سعر البيع
        /// </summary>
        public decimal AverageSellingPrice
        {
            get
            {
                return TotalQuantity > 0 ? TotalSales / TotalQuantity : 0;
            }
        }

        /// <summary>
        /// أفضل دواء في الفئة
        /// </summary>
        public string TopDrug { get; set; }

        /// <summary>
        /// مبيعات أفضل دواء
        /// </summary>
        public decimal TopDrugSales { get; set; }
    }

    #region Invoice Models - نماذج الفواتير

    /// <summary>
    /// نموذج فاتورة المبيعات
    /// </summary>
    public class SalesInvoice
    {
        public SalesInvoice()
        {
            Details = new List<SalesInvoiceDetail>();
        }

        public int InvoiceID { get; set; }
        public string InvoiceNumber { get; set; }
        public DateTime InvoiceDate { get; set; }
        public int CustomerID { get; set; }
        public string CustomerName { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal NetAmount { get; set; }
        public string PaymentMethod { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedDate { get; set; }
        public int CreatedBy { get; set; }
        public List<SalesInvoiceDetail> Details { get; set; }
    }

    /// <summary>
    /// نموذج تفاصيل فاتورة المبيعات
    /// </summary>
    public class SalesInvoiceDetail
    {
        public int DetailID { get; set; }
        public int InvoiceID { get; set; }
        public int DrugID { get; set; }
        public string DrugName { get; set; }
        public int Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalPrice { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal NetAmount { get; set; }
    }

    /// <summary>
    /// نموذج فاتورة المشتريات
    /// </summary>
    public class PurchaseInvoice
    {
        public int InvoiceID { get; set; }
        public string InvoiceNumber { get; set; }
        public DateTime InvoiceDate { get; set; }
        public int SupplierID { get; set; }
        public string SupplierName { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal NetAmount { get; set; }
        public string PaymentMethod { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedDate { get; set; }
        public int CreatedBy { get; set; }
    }

    #endregion
}
