using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Windows.Forms;
using PharmacyManagement.Models;

namespace PharmacyManagement.Classes
{
    /// <summary>
    /// مساعد التصدير - Export Helper
    /// يوفر وظائف التصدير إلى تنسيقات مختلفة
    /// </summary>
    public static class ExportHelper
    {
        /// <summary>
        /// تصدير تاريخ العميل إلى CSV
        /// </summary>
        /// <param name="customer">بيانات العميل</param>
        /// <param name="invoices">قائمة الفواتير</param>
        /// <param name="statistics">الإحصائيات</param>
        /// <param name="filePath">مسار الملف</param>
        public static void ExportCustomerHistoryToCSV(Customer customer, List<Invoice> invoices, 
                                                     Dictionary<string, string> statistics, string filePath)
        {
            try
            {
                StringBuilder csv = new StringBuilder();
                
                // إضافة BOM للدعم العربي
                csv.Append('\uFEFF');
                
                // معلومات العميل
                csv.AppendLine($"تقرير تاريخ العميل: {customer.CustomerName}");
                csv.AppendLine($"كود العميل: {customer.CustomerCode}");
                csv.AppendLine($"الهاتف: {customer.Phone ?? "غير محدد"}");
                csv.AppendLine($"الجوال: {customer.Mobile ?? "غير محدد"}");
                csv.AppendLine($"البريد: {customer.Email ?? "غير محدد"}");
                csv.AppendLine($"العنوان: {customer.Address ?? "غير محدد"}");
                csv.AppendLine();
                
                // الإحصائيات
                csv.AppendLine("الإحصائيات");
                foreach (var stat in statistics)
                {
                    csv.AppendLine($"{stat.Key}: {stat.Value}");
                }
                csv.AppendLine();
                
                // رأس الجدول
                csv.AppendLine("رقم الفاتورة,التاريخ,نوع الفاتورة,المبلغ الإجمالي,المبلغ المدفوع,المبلغ المتبقي,طريقة الدفع,الحالة,ملاحظات");
                
                // بيانات الفواتير
                if (invoices != null && invoices.Count > 0)
                {
                    foreach (var invoice in invoices)
                    {
                        csv.AppendLine($"{invoice.InvoiceNumber}," +
                                     $"{invoice.InvoiceDate.ToString("yyyy/MM/dd")}," +
                                     $"{invoice.InvoiceTypeArabic}," +
                                     $"{invoice.TotalAmount.ToString("F2")}," +
                                     $"{invoice.PaidAmount.ToString("F2")}," +
                                     $"{invoice.RemainingAmount.ToString("F2")}," +
                                     $"{invoice.PaymentMethod ?? "غير محدد"}," +
                                     $"{invoice.StatusArabic}," +
                                     $"{invoice.Notes ?? ""}");
                    }
                }
                else
                {
                    csv.AppendLine("لا توجد فواتير لهذا العميل");
                }
                
                // معلومات التذييل
                csv.AppendLine();
                csv.AppendLine($"تاريخ التقرير: {DateTime.Now.ToString("yyyy/MM/dd HH:mm")}");
                csv.AppendLine($"المستخدم: {UserManager.CurrentUser?.FullName ?? "غير محدد"}");
                
                // حفظ الملف
                File.WriteAllText(filePath, csv.ToString(), Encoding.UTF8);
                
                LogManager.LogInfo($"تم تصدير تاريخ العميل إلى CSV: {filePath}");
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تصدير CSV: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// تصدير تاريخ العميل إلى HTML
        /// </summary>
        /// <param name="customer">بيانات العميل</param>
        /// <param name="invoices">قائمة الفواتير</param>
        /// <param name="statistics">الإحصائيات</param>
        /// <param name="filePath">مسار الملف</param>
        public static void ExportCustomerHistoryToHTML(Customer customer, List<Invoice> invoices, 
                                                      Dictionary<string, string> statistics, string filePath)
        {
            try
            {
                StringBuilder html = new StringBuilder();
                
                // بداية ملف HTML
                html.AppendLine("<!DOCTYPE html>");
                html.AppendLine("<html dir='rtl' lang='ar'>");
                html.AppendLine("<head>");
                html.AppendLine("<meta charset='UTF-8'>");
                html.AppendLine("<meta name='viewport' content='width=device-width, initial-scale=1.0'>");
                html.AppendLine("<title>تقرير تاريخ العميل</title>");
                html.AppendLine("<style>");
                html.AppendLine("body { font-family: 'Segoe UI', Tahoma, Arial, sans-serif; margin: 20px; background-color: #f8f9fa; }");
                html.AppendLine(".container { max-width: 1200px; margin: 0 auto; background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }");
                html.AppendLine("h1 { color: #2c3e50; text-align: center; border-bottom: 3px solid #3498db; padding-bottom: 10px; }");
                html.AppendLine("h2 { color: #34495e; margin-top: 30px; }");
                html.AppendLine(".customer-info { background-color: #ecf0f1; padding: 20px; border-radius: 8px; margin: 20px 0; }");
                html.AppendLine(".customer-info p { margin: 8px 0; font-size: 14px; }");
                html.AppendLine(".stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin: 20px 0; }");
                html.AppendLine(".stat-card { background-color: #3498db; color: white; padding: 15px; border-radius: 8px; text-align: center; }");
                html.AppendLine(".stat-card h3 { margin: 0 0 10px 0; font-size: 16px; }");
                html.AppendLine(".stat-card p { margin: 0; font-size: 18px; font-weight: bold; }");
                html.AppendLine("table { width: 100%; border-collapse: collapse; margin-top: 20px; font-size: 14px; }");
                html.AppendLine("th, td { border: 1px solid #bdc3c7; padding: 12px 8px; text-align: right; }");
                html.AppendLine("th { background-color: #34495e; color: white; font-weight: bold; }");
                html.AppendLine("tr:nth-child(even) { background-color: #f8f9fa; }");
                html.AppendLine("tr:hover { background-color: #e8f4f8; }");
                html.AppendLine(".no-data { text-align: center; color: #7f8c8d; font-style: italic; padding: 20px; }");
                html.AppendLine(".footer { margin-top: 40px; padding-top: 20px; border-top: 1px solid #bdc3c7; color: #7f8c8d; font-size: 12px; text-align: center; }");
                html.AppendLine("@media print { body { background-color: white; } .container { box-shadow: none; } }");
                html.AppendLine("</style>");
                html.AppendLine("</head>");
                html.AppendLine("<body>");
                html.AppendLine("<div class='container'>");
                
                // عنوان التقرير
                html.AppendLine($"<h1>📋 تقرير تاريخ العميل</h1>");
                
                // معلومات العميل
                html.AppendLine("<div class='customer-info'>");
                html.AppendLine("<h2>👤 معلومات العميل</h2>");
                html.AppendLine($"<p><strong>اسم العميل:</strong> {customer.CustomerName}</p>");
                html.AppendLine($"<p><strong>كود العميل:</strong> {customer.CustomerCode}</p>");
                html.AppendLine($"<p><strong>الهاتف:</strong> {customer.Phone ?? "غير محدد"}</p>");
                html.AppendLine($"<p><strong>الجوال:</strong> {customer.Mobile ?? "غير محدد"}</p>");
                html.AppendLine($"<p><strong>البريد الإلكتروني:</strong> {customer.Email ?? "غير محدد"}</p>");
                html.AppendLine($"<p><strong>العنوان:</strong> {customer.Address ?? "غير محدد"}</p>");
                html.AppendLine("</div>");
                
                // الإحصائيات
                html.AppendLine("<h2>📊 الإحصائيات</h2>");
                html.AppendLine("<div class='stats'>");
                foreach (var stat in statistics)
                {
                    html.AppendLine("<div class='stat-card'>");
                    html.AppendLine($"<h3>{stat.Key}</h3>");
                    html.AppendLine($"<p>{stat.Value}</p>");
                    html.AppendLine("</div>");
                }
                html.AppendLine("</div>");
                
                // جدول الفواتير
                html.AppendLine("<h2>🧾 قائمة الفواتير</h2>");
                
                if (invoices != null && invoices.Count > 0)
                {
                    html.AppendLine("<table>");
                    html.AppendLine("<thead>");
                    html.AppendLine("<tr>");
                    html.AppendLine("<th>رقم الفاتورة</th>");
                    html.AppendLine("<th>التاريخ</th>");
                    html.AppendLine("<th>نوع الفاتورة</th>");
                    html.AppendLine("<th>المبلغ الإجمالي</th>");
                    html.AppendLine("<th>المبلغ المدفوع</th>");
                    html.AppendLine("<th>المبلغ المتبقي</th>");
                    html.AppendLine("<th>طريقة الدفع</th>");
                    html.AppendLine("<th>الحالة</th>");
                    html.AppendLine("</tr>");
                    html.AppendLine("</thead>");
                    html.AppendLine("<tbody>");
                    
                    foreach (var invoice in invoices)
                    {
                        html.AppendLine("<tr>");
                        html.AppendLine($"<td>{invoice.InvoiceNumber}</td>");
                        html.AppendLine($"<td>{invoice.InvoiceDate.ToString("yyyy/MM/dd")}</td>");
                        html.AppendLine($"<td>{invoice.InvoiceTypeArabic}</td>");
                        html.AppendLine($"<td>{invoice.TotalAmount.ToString("F2")} ر.ي</td>");
                        html.AppendLine($"<td>{invoice.PaidAmount.ToString("F2")} ر.ي</td>");
                        html.AppendLine($"<td>{invoice.RemainingAmount.ToString("F2")} ر.ي</td>");
                        html.AppendLine($"<td>{invoice.PaymentMethod ?? "غير محدد"}</td>");
                        html.AppendLine($"<td>{invoice.StatusArabic}</td>");
                        html.AppendLine("</tr>");
                    }
                    
                    html.AppendLine("</tbody>");
                    html.AppendLine("</table>");
                }
                else
                {
                    html.AppendLine("<div class='no-data'>لا توجد فواتير لهذا العميل</div>");
                }
                
                // التذييل
                html.AppendLine("<div class='footer'>");
                html.AppendLine($"<p>تاريخ التقرير: {DateTime.Now.ToString("yyyy/MM/dd HH:mm")}</p>");
                html.AppendLine($"<p>المستخدم: {UserManager.CurrentUser?.FullName ?? "غير محدد"}</p>");
                html.AppendLine("<p>نظام إدارة الصيدلية</p>");
                html.AppendLine("</div>");
                
                html.AppendLine("</div>");
                html.AppendLine("</body>");
                html.AppendLine("</html>");
                
                // حفظ الملف
                File.WriteAllText(filePath, html.ToString(), Encoding.UTF8);
                
                LogManager.LogInfo($"تم تصدير تاريخ العميل إلى HTML: {filePath}");
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تصدير HTML: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// فتح الملف المصدر
        /// </summary>
        /// <param name="filePath">مسار الملف</param>
        public static void OpenExportedFile(string filePath)
        {
            try
            {
                if (File.Exists(filePath))
                {
                    System.Diagnostics.Process.Start(filePath);
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في فتح الملف المصدر: {ex.Message}");
                MessageBox.Show($"تم حفظ الملف في: {filePath}\nلكن لا يمكن فتحه تلقائياً", "تنبيه", 
                              MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }
    }
}
