-- ===================================================================
-- إنشاء الجداول المفقودة للتقارير - Create Missing Tables for Reports
-- تاريخ الإنشاء: 2025-01-28
-- الغرض: إنشاء جميع الجداول المطلوبة لعمل التقارير
-- ===================================================================

USE PharmacyDB
GO

PRINT '🔧 بدء إنشاء الجداول المفقودة للتقارير...'
PRINT '📅 التاريخ: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT ''

-- ===================================================================
-- 1. إنشاء جدول المستخدمين إذا لم يكن موجوداً
-- ===================================================================

IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Users' AND xtype='U')
BEGIN
    CREATE TABLE Users (
        UserID INT IDENTITY(1,1) PRIMARY KEY,
        Username NVARCHAR(50) NOT NULL UNIQUE,
        FullName NVARCHAR(100) NOT NULL,
        Email NVARCHAR(100) NULL,
        Phone NVARCHAR(20) NULL,
        IsActive BIT NOT NULL DEFAULT 1,
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE()
    )
    
    -- إدراج مستخدم افتراضي
    INSERT INTO Users (Username, FullName, Email, IsActive)
    VALUES ('admin', 'مدير النظام', '<EMAIL>', 1)
    
    PRINT '✅ تم إنشاء جدول Users'
END
ELSE
    PRINT '✅ جدول Users موجود'

-- ===================================================================
-- 2. إنشاء جدول المبيعات
-- ===================================================================

IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Sales' AND xtype='U')
BEGIN
    CREATE TABLE Sales (
        SaleID INT IDENTITY(1,1) PRIMARY KEY,
        SaleDate DATETIME NOT NULL DEFAULT GETDATE(),
        CustomerID INT NULL,
        TotalAmount DECIMAL(10,2) NOT NULL DEFAULT 0,
        DiscountAmount DECIMAL(10,2) NOT NULL DEFAULT 0,
        TaxAmount DECIMAL(10,2) NOT NULL DEFAULT 0,
        NetAmount DECIMAL(10,2) NOT NULL DEFAULT 0,
        PaymentMethod NVARCHAR(50) NOT NULL DEFAULT 'نقدي',
        Notes NVARCHAR(200) NULL,
        UserID INT NOT NULL DEFAULT 1,
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        FOREIGN KEY (CustomerID) REFERENCES Customers(CustomerID),
        FOREIGN KEY (UserID) REFERENCES Users(UserID)
    )
    PRINT '✅ تم إنشاء جدول Sales'
END
ELSE
    PRINT '✅ جدول Sales موجود'

-- ===================================================================
-- 3. إنشاء جدول تفاصيل المبيعات
-- ===================================================================

IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SaleDetails' AND xtype='U')
BEGIN
    CREATE TABLE SaleDetails (
        SaleDetailID INT IDENTITY(1,1) PRIMARY KEY,
        SaleID INT NOT NULL,
        DrugID INT NOT NULL,
        Quantity INT NOT NULL,
        UnitPrice DECIMAL(10,2) NOT NULL,
        TotalPrice DECIMAL(10,2) NOT NULL,
        DiscountAmount DECIMAL(10,2) NOT NULL DEFAULT 0,
        FOREIGN KEY (SaleID) REFERENCES Sales(SaleID),
        FOREIGN KEY (DrugID) REFERENCES Drugs(DrugID)
    )
    PRINT '✅ تم إنشاء جدول SaleDetails'
END
ELSE
    PRINT '✅ جدول SaleDetails موجود'

-- ===================================================================
-- 4. إنشاء جدول المشتريات
-- ===================================================================

IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Purchases' AND xtype='U')
BEGIN
    CREATE TABLE Purchases (
        PurchaseID INT IDENTITY(1,1) PRIMARY KEY,
        PurchaseDate DATETIME NOT NULL DEFAULT GETDATE(),
        SupplierID INT NOT NULL,
        TotalAmount DECIMAL(10,2) NOT NULL DEFAULT 0,
        DiscountAmount DECIMAL(10,2) NOT NULL DEFAULT 0,
        TaxAmount DECIMAL(10,2) NOT NULL DEFAULT 0,
        NetAmount DECIMAL(10,2) NOT NULL DEFAULT 0,
        PaymentMethod NVARCHAR(50) NOT NULL DEFAULT 'آجل',
        Notes NVARCHAR(200) NULL,
        UserID INT NOT NULL DEFAULT 1,
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        FOREIGN KEY (SupplierID) REFERENCES Suppliers(SupplierID),
        FOREIGN KEY (UserID) REFERENCES Users(UserID)
    )
    PRINT '✅ تم إنشاء جدول Purchases'
END
ELSE
    PRINT '✅ جدول Purchases موجود'

-- ===================================================================
-- 5. إنشاء جدول تفاصيل المشتريات
-- ===================================================================

IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='PurchaseDetails' AND xtype='U')
BEGIN
    CREATE TABLE PurchaseDetails (
        PurchaseDetailID INT IDENTITY(1,1) PRIMARY KEY,
        PurchaseID INT NOT NULL,
        DrugID INT NOT NULL,
        Quantity INT NOT NULL,
        UnitPrice DECIMAL(10,2) NOT NULL,
        TotalPrice DECIMAL(10,2) NOT NULL,
        DiscountAmount DECIMAL(10,2) NOT NULL DEFAULT 0,
        FOREIGN KEY (PurchaseID) REFERENCES Purchases(PurchaseID),
        FOREIGN KEY (DrugID) REFERENCES Drugs(DrugID)
    )
    PRINT '✅ تم إنشاء جدول PurchaseDetails'
END
ELSE
    PRINT '✅ جدول PurchaseDetails موجود'

-- ===================================================================
-- 6. التأكد من وجود الجداول الأساسية الأخرى
-- ===================================================================

-- التحقق من جدول العملاء
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Customers' AND xtype='U')
BEGIN
    CREATE TABLE Customers (
        CustomerID INT IDENTITY(1,1) PRIMARY KEY,
        CustomerName NVARCHAR(100) NOT NULL,
        Phone NVARCHAR(20) NULL,
        Email NVARCHAR(100) NULL,
        Address NVARCHAR(200) NULL,
        Balance DECIMAL(10,2) NOT NULL DEFAULT 0,
        CreditLimit DECIMAL(10,2) NOT NULL DEFAULT 0,
        IsActive BIT NOT NULL DEFAULT 1,
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE()
    )
    PRINT '✅ تم إنشاء جدول Customers'
END
ELSE
    PRINT '✅ جدول Customers موجود'

-- التحقق من جدول الموردين
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Suppliers' AND xtype='U')
BEGIN
    CREATE TABLE Suppliers (
        SupplierID INT IDENTITY(1,1) PRIMARY KEY,
        SupplierName NVARCHAR(100) NOT NULL,
        ContactPerson NVARCHAR(100) NULL,
        Phone NVARCHAR(20) NULL,
        Email NVARCHAR(100) NULL,
        Address NVARCHAR(200) NULL,
        Balance DECIMAL(10,2) NOT NULL DEFAULT 0,
        CreditLimit DECIMAL(10,2) NOT NULL DEFAULT 0,
        IsActive BIT NOT NULL DEFAULT 1,
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE()
    )
    PRINT '✅ تم إنشاء جدول Suppliers'
END
ELSE
    PRINT '✅ جدول Suppliers موجود'

-- التحقق من جدول الشركات المصنعة
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Manufacturers' AND xtype='U')
BEGIN
    CREATE TABLE Manufacturers (
        ManufacturerID INT IDENTITY(1,1) PRIMARY KEY,
        ManufacturerCode NVARCHAR(20) NOT NULL UNIQUE,
        ManufacturerName NVARCHAR(100) NOT NULL,
        Country NVARCHAR(50) NULL,
        IsActive BIT NOT NULL DEFAULT 1,
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        CreatedBy INT NOT NULL DEFAULT 1
    )
    PRINT '✅ تم إنشاء جدول Manufacturers'
END
ELSE
    PRINT '✅ جدول Manufacturers موجود'

-- ===================================================================
-- 7. إنشاء الفهارس المطلوبة
-- ===================================================================

PRINT '🚀 إنشاء الفهارس...'

-- فهارس جدول المبيعات
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Sales_SaleDate')
    CREATE INDEX IX_Sales_SaleDate ON Sales(SaleDate)

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Sales_CustomerID')
    CREATE INDEX IX_Sales_CustomerID ON Sales(CustomerID)

-- فهارس جدول تفاصيل المبيعات
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_SaleDetails_SaleID')
    CREATE INDEX IX_SaleDetails_SaleID ON SaleDetails(SaleID)

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_SaleDetails_DrugID')
    CREATE INDEX IX_SaleDetails_DrugID ON SaleDetails(DrugID)

-- فهارس جدول المشتريات
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Purchases_PurchaseDate')
    CREATE INDEX IX_Purchases_PurchaseDate ON Purchases(PurchaseDate)

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Purchases_SupplierID')
    CREATE INDEX IX_Purchases_SupplierID ON Purchases(SupplierID)

PRINT '✅ تم إنشاء جميع الفهارس'

PRINT ''
PRINT '✅ تم إنشاء جميع الجداول المطلوبة بنجاح!'
PRINT '📅 تاريخ الانتهاء: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT ''
PRINT '📝 الجداول المنشأة:'
PRINT '   - Users (المستخدمين)'
PRINT '   - Sales (المبيعات)'
PRINT '   - SaleDetails (تفاصيل المبيعات)'
PRINT '   - Purchases (المشتريات)'
PRINT '   - PurchaseDetails (تفاصيل المشتريات)'
PRINT '   - Customers (العملاء)'
PRINT '   - Suppliers (الموردين)'
PRINT '   - Manufacturers (الشركات المصنعة)'
PRINT ''
PRINT '🎯 الآن يمكن تشغيل ملف البيانات التجريبية!'
