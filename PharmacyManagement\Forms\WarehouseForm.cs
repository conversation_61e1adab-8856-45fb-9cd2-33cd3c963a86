using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نموذج إدارة المخازن - Warehouse Management Form
    /// </summary>
    public partial class WarehouseForm : Form
    {
        #region Fields

        private BindingSource _bindingSource;
        private int? _selectedWarehouseId;
        private int? _filterBranchId;

        #endregion

        #region Constructors

        /// <summary>
        /// منشئ لعرض جميع المخازن
        /// </summary>
        public WarehouseForm()
        {
            InitializeComponent();
            SetupForm();
            LoadBranches();
            LoadData();
        }

        /// <summary>
        /// منشئ لعرض مخازن فرع معين
        /// </summary>
        /// <param name="branchId">معرف الفرع</param>
        public WarehouseForm(int branchId)
        {
            InitializeComponent();
            _filterBranchId = branchId;
            SetupForm();
            LoadBranches();
            LoadData();
            
            // تحديد الفرع في القائمة المنسدلة
            cmbBranchFilter.SelectedValue = branchId;
        }

        #endregion

        #region Setup Methods

        private void SetupForm()
        {
            this.Text = "إدارة المخازن";
            this.WindowState = FormWindowState.Maximized;
            
            // إعداد التصميم المسطح
            SetupFlatDesign();
            
            // إعداد الشبكة
            SetupDataGrid();
            
            // إعداد الأحداث
            SetupEvents();
        }

        private void SetupFlatDesign()
        {
            // إعدادات النموذج
            this.BackColor = Color.White;
            this.Font = new Font("Segoe UI", 9F);
            
            // إعداد الأزرار
            foreach (Control control in this.Controls)
            {
                if (control is Button btn)
                {
                    btn.FlatStyle = FlatStyle.Flat;
                    btn.FlatAppearance.BorderSize = 1;
                    btn.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
                }
                else if (control is TextBox txt)
                {
                    txt.BorderStyle = BorderStyle.FixedSingle;
                    txt.Font = new Font("Segoe UI", 10F);
                }
                else if (control is ComboBox cmb)
                {
                    cmb.FlatStyle = FlatStyle.Flat;
                    cmb.Font = new Font("Segoe UI", 10F);
                }
            }

            // ألوان الأزرار
            btnAdd.BackColor = Color.FromArgb(46, 204, 113);
            btnAdd.ForeColor = Color.White;
            btnEdit.BackColor = Color.FromArgb(52, 152, 219);
            btnEdit.ForeColor = Color.White;
            btnDelete.BackColor = Color.FromArgb(231, 76, 60);
            btnDelete.ForeColor = Color.White;
            btnSearch.BackColor = Color.FromArgb(155, 89, 182);
            btnSearch.ForeColor = Color.White;
            btnViewStock.BackColor = Color.FromArgb(26, 188, 156);
            btnViewStock.ForeColor = Color.White;
            btnTransfer.BackColor = Color.FromArgb(243, 156, 18);
            btnTransfer.ForeColor = Color.White;
        }

        private void SetupDataGrid()
        {
            // إعداد الشبكة
            dgvWarehouses.AutoGenerateColumns = false;
            dgvWarehouses.AllowUserToAddRows = false;
            dgvWarehouses.AllowUserToDeleteRows = false;
            dgvWarehouses.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvWarehouses.MultiSelect = false;
            dgvWarehouses.BackgroundColor = Color.White;
            dgvWarehouses.BorderStyle = BorderStyle.FixedSingle;
            dgvWarehouses.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 152, 219);
            dgvWarehouses.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvWarehouses.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            dgvWarehouses.DefaultCellStyle.Font = new Font("Segoe UI", 9F);
            dgvWarehouses.RowHeadersVisible = false;
            dgvWarehouses.EnableHeadersVisualStyles = false;

            // إضافة الأعمدة
            dgvWarehouses.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "WarehouseCode",
                HeaderText = "كود المخزن",
                DataPropertyName = "WarehouseCode",
                Width = 100
            });

            dgvWarehouses.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "WarehouseName",
                HeaderText = "اسم المخزن",
                DataPropertyName = "WarehouseName",
                Width = 200
            });

            dgvWarehouses.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "BranchName",
                HeaderText = "الفرع",
                DataPropertyName = "BranchName",
                Width = 150
            });

            dgvWarehouses.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Location",
                HeaderText = "الموقع",
                DataPropertyName = "Location",
                Width = 150
            });

            dgvWarehouses.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "WarehouseType",
                HeaderText = "النوع",
                DataPropertyName = "WarehouseType",
                Width = 100
            });

            dgvWarehouses.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Capacity",
                HeaderText = "السعة",
                DataPropertyName = "Capacity",
                Width = 100
            });

            dgvWarehouses.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Temperature",
                HeaderText = "درجة الحرارة",
                DataPropertyName = "Temperature",
                Width = 120
            });

            dgvWarehouses.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "KeeperName",
                HeaderText = "أمين المخزن",
                DataPropertyName = "KeeperName",
                Width = 150
            });

            dgvWarehouses.Columns.Add(new DataGridViewCheckBoxColumn
            {
                Name = "IsActive",
                HeaderText = "نشط",
                DataPropertyName = "IsActive",
                Width = 80
            });

            // إعداد مصدر البيانات
            _bindingSource = new BindingSource();
            dgvWarehouses.DataSource = _bindingSource;
        }

        private void SetupEvents()
        {
            // ربط الأحداث
            btnAdd.Click += BtnAdd_Click;
            btnEdit.Click += BtnEdit_Click;
            btnDelete.Click += BtnDelete_Click;
            btnSearch.Click += BtnSearch_Click;
            btnRefresh.Click += BtnRefresh_Click;
            btnViewStock.Click += BtnViewStock_Click;
            btnTransfer.Click += BtnTransfer_Click;
            
            dgvWarehouses.SelectionChanged += DgvWarehouses_SelectionChanged;
            dgvWarehouses.CellFormatting += DgvWarehouses_CellFormatting;
            dgvWarehouses.CellDoubleClick += DgvWarehouses_CellDoubleClick;
            
            txtSearch.KeyPress += TxtSearch_KeyPress;
            cmbBranchFilter.SelectedIndexChanged += CmbBranchFilter_SelectedIndexChanged;
        }

        #endregion

        #region Load Data Methods

        private void LoadBranches()
        {
            try
            {
                var branches = BranchManager.GetActiveBranches();
                
                // إضافة خيار "جميع الفروع"
                var allBranches = new List<Branch>
                {
                    new Branch { BranchID = 0, BranchName = "جميع الفروع" }
                };
                allBranches.AddRange(branches);

                cmbBranchFilter.DataSource = allBranches;
                cmbBranchFilter.DisplayMember = "BranchName";
                cmbBranchFilter.ValueMember = "BranchID";
                
                if (!_filterBranchId.HasValue)
                {
                    cmbBranchFilter.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الفروع: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadData()
        {
            try
            {
                var warehouses = _filterBranchId.HasValue && _filterBranchId.Value > 0
                    ? WarehouseManager.GetWarehousesByBranch(_filterBranchId.Value)
                    : WarehouseManager.GetAllWarehouses();
                
                _bindingSource.DataSource = warehouses;
                
                UpdateButtonStates();
                UpdateStatusLabel();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Event Handlers

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                using (var addForm = new WarehouseAddEditForm())
                {
                    if (addForm.ShowDialog() == DialogResult.OK)
                    {
                        LoadData();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة المخزن: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            try
            {
                if (_selectedWarehouseId.HasValue)
                {
                    using (var editForm = new WarehouseAddEditForm(_selectedWarehouseId.Value))
                    {
                        if (editForm.ShowDialog() == DialogResult.OK)
                        {
                            LoadData();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعديل المخزن: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            try
            {
                if (_selectedWarehouseId.HasValue)
                {
                    var result = MessageBox.Show("هل تريد حذف المخزن المحدد؟", "تأكيد الحذف",
                        MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    
                    if (result == DialogResult.Yes)
                    {
                        if (WarehouseManager.DeleteWarehouse(_selectedWarehouseId.Value))
                        {
                            MessageBox.Show("تم حذف المخزن بنجاح", "نجح",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                            LoadData();
                        }
                        else
                        {
                            MessageBox.Show("فشل في حذف المخزن", "خطأ",
                                MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف المخزن: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnSearch_Click(object sender, EventArgs e)
        {
            PerformSearch();
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            txtSearch.Clear();
            cmbBranchFilter.SelectedIndex = 0;
            _filterBranchId = null;
            LoadData();
        }

        private void BtnViewStock_Click(object sender, EventArgs e)
        {
            try
            {
                if (_selectedWarehouseId.HasValue)
                {
                    using (var stockForm = new WarehouseForm(_selectedWarehouseId.Value))
                    {
                        stockForm.ShowDialog();
                    }
                }
                else
                {
                    MessageBox.Show("يرجى اختيار مخزن أولاً", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض المخزون: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnTransfer_Click(object sender, EventArgs e)
        {
            try
            {
                if (_selectedWarehouseId.HasValue)
                {
                    using (var transferForm = new StockTransferForm(_selectedWarehouseId.Value))
                    {
                        transferForm.ShowDialog();
                    }
                }
                else
                {
                    MessageBox.Show("يرجى اختيار مخزن أولاً", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح تحويل المخزون: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DgvWarehouses_SelectionChanged(object sender, EventArgs e)
        {
            if (dgvWarehouses.SelectedRows.Count > 0)
            {
                var selectedRow = dgvWarehouses.SelectedRows[0];
                if (selectedRow.DataBoundItem is Warehouse warehouse)
                {
                    _selectedWarehouseId = warehouse.WarehouseID;
                    UpdateButtonStates();
                }
            }
            else
            {
                _selectedWarehouseId = null;
                UpdateButtonStates();
            }
        }

        private void DgvWarehouses_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (dgvWarehouses.Rows[e.RowIndex].DataBoundItem is Warehouse warehouse)
            {
                // تلوين الصف حسب الحالة
                if (!warehouse.IsActive)
                {
                    dgvWarehouses.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.LightGray;
                    dgvWarehouses.Rows[e.RowIndex].DefaultCellStyle.ForeColor = Color.DarkGray;
                }
                else if (warehouse.WarehouseType == "مبرد")
                {
                    dgvWarehouses.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.LightBlue;
                }
                else if (warehouse.WarehouseType == "خاص")
                {
                    dgvWarehouses.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.LightYellow;
                }
                else
                {
                    dgvWarehouses.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.White;
                }
            }
        }

        private void DgvWarehouses_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                BtnEdit_Click(sender, e);
            }
        }

        private void TxtSearch_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                PerformSearch();
                e.Handled = true;
            }
        }

        private void CmbBranchFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cmbBranchFilter.SelectedValue is int branchId)
            {
                _filterBranchId = branchId > 0 ? branchId : (int?)null;
                LoadData();
            }
        }

        #endregion

        #region Helper Methods

        private void UpdateButtonStates()
        {
            bool hasSelection = _selectedWarehouseId.HasValue;
            
            btnEdit.Enabled = hasSelection;
            btnDelete.Enabled = hasSelection;
            btnViewStock.Enabled = hasSelection;
            btnTransfer.Enabled = hasSelection;
        }

        private void UpdateStatusLabel()
        {
            var totalWarehouses = _bindingSource.Count;
            var activeWarehouses = 0;
            var refrigeratedWarehouses = 0;

            foreach (Warehouse warehouse in _bindingSource)
            {
                if (warehouse.IsActive)
                    activeWarehouses++;
                if (warehouse.WarehouseType == "مبرد")
                    refrigeratedWarehouses++;
            }

            lblStatus.Text = $"إجمالي المخازن: {totalWarehouses} | نشط: {activeWarehouses} | مبرد: {refrigeratedWarehouses}";
        }

        private void PerformSearch()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtSearch.Text))
                {
                    LoadData();
                    return;
                }

                var searchResults = WarehouseManager.SearchWarehouses(txtSearch.Text.Trim());
                
                // تطبيق فلتر الفرع إذا كان محدد
                if (_filterBranchId.HasValue && _filterBranchId.Value > 0)
                {
                    searchResults = searchResults.Where(w => w.BranchID == _filterBranchId.Value).ToList();
                }
                
                _bindingSource.DataSource = searchResults;
                UpdateStatusLabel();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion
    }
}
