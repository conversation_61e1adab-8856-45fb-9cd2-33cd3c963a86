﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>PharmacyManagement</RootNamespace>
    <AssemblyName>PharmacyManagement</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Windows.Forms.DataVisualization" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Data.SqlClient, Version=4.6.1.3, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Data.SqlClient.4.8.3\lib\net461\System.Data.SqlClient.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Classes\BranchManager.cs" />
    <Compile Include="Classes\ChartOfAccountsManager.cs" />
    <Compile Include="Classes\CostCenterManager.cs" />
    <Compile Include="Classes\CustomerManager.cs" />
    <Compile Include="Classes\ExportHelper.cs" />
    <Compile Include="Classes\FiscalYearManager.cs" />
    <Compile Include="Classes\IconManager.cs" />
    <Compile Include="Classes\ScreenshotManager.cs" />
    <Compile Include="Classes\SessionManager.cs" />
    <Compile Include="Classes\StockTransferManager.cs" />
    <Compile Include="Classes\UnifiedInventoryManager.cs" />
    <Compile Include="Classes\VoucherManager.cs" />
    <Compile Include="Classes\WarehouseManager.cs" />
    <Compile Include="Forms\AlertsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\AlertsForm.Designer.cs">
      <DependentUpon>AlertsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\BranchAddEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\BranchAddEditForm.Designer.cs">
      <DependentUpon>BranchAddEditForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\BranchForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\BranchForm.Designer.cs">
      <DependentUpon>BranchForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ChartOfAccountsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ChartOfAccountsForm.Designer.cs">
      <DependentUpon>ChartOfAccountsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\CostCenterForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\CostCenterForm.Designer.cs">
      <DependentUpon>CostCenterForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\CustomersReportForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\CustomersReportForm.Designer.cs">
      <DependentUpon>CustomersReportForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\EnhancedInventoryCountForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\EnhancedInventoryCountForm.Designer.cs">
      <DependentUpon>EnhancedInventoryCountForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ExpenseAddForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ExpenseAddForm.Designer.cs">
      <DependentUpon>ExpenseAddForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ExpensesManagementForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ExpensesManagementForm.Designer.cs">
      <DependentUpon>ExpensesManagementForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FiscalYearAddEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FiscalYearAddEditForm.Designer.cs">
      <DependentUpon>FiscalYearAddEditForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FiscalYearForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FiscalYearForm.Designer.cs">
      <DependentUpon>FiscalYearForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\InterfaceSelectionForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\InterfaceSelectionForm.Designer.cs">
      <DependentUpon>InterfaceSelectionForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\InventoryReportForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\InventoryReportForm.Designer.cs">
      <DependentUpon>InventoryReportForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ModernMainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ModernMainForm.Designer.cs">
      <DependentUpon>ModernMainForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\PasswordResetForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\PasswordResetForm.Designer.cs">
      <DependentUpon>PasswordResetForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\PaymentVoucherForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\PaymentVoucherForm.Designer.cs">
      <DependentUpon>PaymentVoucherForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\QuickSaleForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\QuickSaleForm.Designer.cs">
      <DependentUpon>QuickSaleForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\QuickSearchForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\QuickSearchForm.Designer.cs">
      <DependentUpon>QuickSearchForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ReceiptVoucherForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ReceiptVoucherForm.Designer.cs">
      <DependentUpon>ReceiptVoucherForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SalesReportForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SalesReportForm.Designer.cs">
      <DependentUpon>SalesReportForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\StockTransferForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\StockTransferForm.Designer.cs">
      <DependentUpon>StockTransferForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SupplierHistoryForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SupplierHistoryForm.Designer.cs">
      <DependentUpon>SupplierHistoryForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SuppliersReportForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SuppliersReportForm.Designer.cs">
      <DependentUpon>SuppliersReportForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\UserPermissionsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\UserPermissionsForm.Designer.cs">
      <DependentUpon>UserPermissionsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\VoucherAddEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\VoucherAddEditForm.Designer.cs">
      <DependentUpon>VoucherAddEditForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\VoucherForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\VoucherForm.Designer.cs">
      <DependentUpon>VoucherForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\WarehouseAddEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\WarehouseAddEditForm.Designer.cs">
      <DependentUpon>WarehouseAddEditForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\WarehouseForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\WarehouseForm.Designer.cs">
      <DependentUpon>WarehouseForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Models\AlertModels.cs" />
    <Compile Include="Models\Branch.cs" />
    <Compile Include="Models\CustomerSummary.cs" />
    <Compile Include="Models\InventoryAlert.cs" />
    <Compile Include="Models\InventoryItem.cs" />
    <Compile Include="Models\InventoryTransaction.cs" />
    <Compile Include="Models\StockMovement.cs" />
    <Compile Include="Models\StockTransfer.cs" />
    <Compile Include="Models\SupplierTransaction.cs" />
    <Compile Include="Models\FinancialModels.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <!-- Models -->
    <Compile Include="Models\User.cs" />
    <Compile Include="Models\Drug.cs" />
    <Compile Include="Models\Supplier.cs" />
    <Compile Include="Models\Customer.cs" />
    <Compile Include="Models\Currency.cs" />
    <Compile Include="Models\Invoice.cs" />
    <Compile Include="Models\InvoiceDetail.cs" />
    <Compile Include="Models\SalesModels.cs" />
    <Compile Include="Models\ReportModels.cs" />
    <!-- Classes -->
    <Compile Include="Classes\DatabaseHelper.cs" />
    <Compile Include="Classes\UserManager.cs" />
    <Compile Include="Classes\DrugManager.cs" />
    <Compile Include="Classes\SupplierManager.cs" />
    <Compile Include="Classes\InventoryManager.cs" />
    <Compile Include="Classes\SalesManager.cs" />
    <Compile Include="Classes\FinancialManager.cs" />
    <Compile Include="Classes\ReportsManager.cs" />
    <Compile Include="Classes\LogManager.cs" />
    <Compile Include="Classes\SettingsManager.cs" />
    <Compile Include="Classes\ExportManager.cs" />
    <Compile Include="Classes\BackupManager.cs" />
    <Compile Include="Classes\CurrencyManager.cs" />
    <Compile Include="Classes\InvoiceManager.cs" />
    <!-- Forms -->
    <Compile Include="Forms\LoginForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\LoginForm.Designer.cs">
      <DependentUpon>LoginForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\DrugsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\DrugsForm.Designer.cs">
      <DependentUpon>DrugsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\DrugAddEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\DrugAddEditForm.Designer.cs">
      <DependentUpon>DrugAddEditForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SupplierAddEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SupplierAddEditForm.Designer.cs">
      <DependentUpon>SupplierAddEditForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\CustomerAddEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\CustomerAddEditForm.Designer.cs">
      <DependentUpon>CustomerAddEditForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\CustomerHistoryForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\CustomerHistoryForm.Designer.cs">
      <DependentUpon>CustomerHistoryForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\UserAddEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\UserAddEditForm.Designer.cs">
      <DependentUpon>UserAddEditForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\CustomersForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\CustomersForm.Designer.cs">
      <DependentUpon>CustomersForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SalesForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SuppliersForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\InventoryForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FinancialForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ReportsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\UsersForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SalesForm.Designer.cs">
      <DependentUpon>SalesForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\StockUpdateForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\StockUpdateForm.Designer.cs">
      <DependentUpon>StockUpdateForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SuppliersForm.Designer.cs">
      <DependentUpon>SuppliersForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\InventoryForm.Designer.cs">
      <DependentUpon>InventoryForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FinancialForm.Designer.cs">
      <DependentUpon>FinancialForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ReportsForm.Designer.cs">
      <DependentUpon>ReportsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\UsersForm.Designer.cs">
      <DependentUpon>UsersForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SettingsForm.Designer.cs">
      <DependentUpon>SettingsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\CurrencyManagementForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\CurrencyManagementForm.Designer.cs">
      <DependentUpon>CurrencyManagementForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\CurrencyAddEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\CurrencyAddEditForm.Designer.cs">
      <DependentUpon>CurrencyAddEditForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ExchangeRateUpdateForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ExchangeRateUpdateForm.Designer.cs">
      <DependentUpon>ExchangeRateUpdateForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\BackupManagementForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\BackupManagementForm.Designer.cs">
      <DependentUpon>BackupManagementForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Classes\BackupManager.cs" />
    <Compile Include="Classes\CurrencyManager.cs" />
    <Compile Include="Classes\LogManager.cs" />
    <Compile Include="Classes\SettingsManager.cs" />
    <Compile Include="Classes\ExportManager.cs" />
    <EmbeddedResource Include="Forms\AlertsForm.resx">
      <DependentUpon>AlertsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ChartOfAccountsForm.resx">
      <DependentUpon>ChartOfAccountsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\CustomerHistoryForm.resx">
      <DependentUpon>CustomerHistoryForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\CustomersForm.resx">
      <DependentUpon>CustomersForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ExpenseAddForm.resx">
      <DependentUpon>ExpenseAddForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\InventoryForm.resx">
      <DependentUpon>InventoryForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ModernMainForm.resx">
      <DependentUpon>ModernMainForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\PaymentVoucherForm.resx">
      <DependentUpon>PaymentVoucherForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\QuickSaleForm.resx">
      <DependentUpon>QuickSaleForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\QuickSearchForm.resx">
      <DependentUpon>QuickSearchForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SalesForm.resx">
      <DependentUpon>SalesForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\StockUpdateForm.resx">
      <DependentUpon>StockUpdateForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SupplierHistoryForm.resx">
      <DependentUpon>SupplierHistoryForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\UsersForm.resx">
      <DependentUpon>UsersForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\WarehouseAddEditForm.resx">
      <DependentUpon>WarehouseAddEditForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\WarehouseForm.resx">
      <DependentUpon>WarehouseForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>