using System;
using System.Collections.Generic;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Forms;
using PharmacyManagement.Models;

namespace PharmacyManagement
{
    /// <summary>
    /// فئة اختبار لنافذة تاريخ العميل
    /// </summary>
    public static class TestCustomerHistoryForm
    {
        /// <summary>
        /// اختبار فتح نافذة تاريخ العميل
        /// </summary>
        public static void TestOpenCustomerHistoryForm()
        {
            try
            {
                Console.WriteLine("بدء اختبار فتح نافذة تاريخ العميل...");
                
                // إنشاء عميل تجريبي
                var customer = new Customer
                {
                    CustomerID = 1,
                    CustomerCode = "CUST001",
                    CustomerName = "أحمد محمد علي",
                    Phone = "01234567890",
                    Mobile = "01987654321",
                    Email = "<EMAIL>",
                    Address = "الرياض، المملكة العربية السعودية",
                    CreditLimit = 5000.00m,
                    CurrentBalance = 1250.00m,
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddMonths(-6)
                };
                
                Console.WriteLine($"تم إنشاء العميل التجريبي: {customer.CustomerName}");
                
                // فتح نافذة تاريخ العميل
                var historyForm = new CustomerHistoryForm(customer);
                
                Console.WriteLine("تم إنشاء نافذة تاريخ العميل بنجاح");
                
                // عرض النافذة
                historyForm.ShowDialog();
                
                Console.WriteLine("تم إغلاق نافذة تاريخ العميل");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في اختبار نافذة تاريخ العميل: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
                
                MessageBox.Show($"خطأ في اختبار النافذة: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        /// <summary>
        /// اختبار إنشاء عميل مع بيانات كاملة
        /// </summary>
        public static void TestCreateCustomerWithFullData()
        {
            try
            {
                Console.WriteLine("بدء اختبار إنشاء عميل مع بيانات كاملة...");
                
                var customer = new Customer
                {
                    CustomerID = 2,
                    CustomerCode = "CUST002",
                    CustomerName = "فاطمة أحمد محمد",
                    Phone = "01111111111",
                    Mobile = "01222222222",
                    Email = "<EMAIL>",
                    Address = "جدة، المملكة العربية السعودية",
                    DateOfBirth = new DateTime(1985, 5, 15),
                    Gender = "أنثى",
                    CustomerType = "VIP",
                    CreditLimit = 10000.00m,
                    CurrentBalance = 2500.00m,
                    IsActive = true,
                    CreatedDate = DateTime.Now.AddMonths(-12),
                    ModifiedDate = DateTime.Now.AddDays(-30),
                    TotalPurchases = 15000.00m,
                    LastPurchaseDate = DateTime.Now.AddDays(-5),
                    Notes = "عميل مميز - خصم 10%"
                };
                
                Console.WriteLine($"تم إنشاء العميل: {customer.CustomerName}");
                Console.WriteLine($"العمر: {customer.Age} سنة");
                Console.WriteLine($"الرصيد المتاح: {customer.AvailableCredit:F2} ر.ي");
                Console.WriteLine($"حالة الائتمان: {customer.CreditStatus}");
                
                // فتح نافذة تاريخ العميل
                var historyForm = new CustomerHistoryForm(customer);
                historyForm.ShowDialog();
                
                Console.WriteLine("تم اختبار العميل مع البيانات الكاملة بنجاح");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في اختبار العميل مع البيانات الكاملة: {ex.Message}");
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        /// <summary>
        /// اختبار شامل لنافذة تاريخ العميل
        /// </summary>
        public static void RunAllTests()
        {
            try
            {
                Console.WriteLine("=== بدء الاختبارات الشاملة لنافذة تاريخ العميل ===");
                
                // اختبار العميل الأساسي
                Console.WriteLine("\n1. اختبار العميل الأساسي:");
                TestOpenCustomerHistoryForm();
                
                Console.WriteLine("\n" + new string('-', 50));
                
                // اختبار العميل مع البيانات الكاملة
                Console.WriteLine("\n2. اختبار العميل مع البيانات الكاملة:");
                TestCreateCustomerWithFullData();
                
                Console.WriteLine("\n=== انتهت جميع الاختبارات ===");
                
                MessageBox.Show("تم تشغيل جميع اختبارات نافذة تاريخ العميل بنجاح!", 
                              "اختبارات النافذة", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تشغيل الاختبارات: {ex.Message}");
                MessageBox.Show($"خطأ في الاختبارات: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        /// <summary>
        /// اختبار سريع لفتح النافذة
        /// </summary>
        public static void QuickTest()
        {
            try
            {
                var customer = new Customer
                {
                    CustomerID = 999,
                    CustomerCode = "TEST001",
                    CustomerName = "عميل تجريبي",
                    Phone = "01000000000",
                    Email = "<EMAIL>"
                };
                
                var form = new CustomerHistoryForm(customer);
                form.Show();
                
                Console.WriteLine("تم فتح النافذة التجريبية بنجاح");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في الاختبار السريع: {ex.Message}");
                throw;
            }
        }
    }
}
