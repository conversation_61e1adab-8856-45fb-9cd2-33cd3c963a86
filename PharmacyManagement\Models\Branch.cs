using System;

namespace PharmacyManagement.Models
{
    /// <summary>
    /// نموذج الفرع - Branch Model
    /// </summary>
    public class Branch
    {
        public int BranchID { get; set; }
        public string BranchCode { get; set; }
        public string BranchName { get; set; }
        public string Address { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
        public string City { get; set; }
        public string Region { get; set; }
        public int? ManagerID { get; set; }
        public string ManagerName { get; set; }
        public bool IsMainBranch { get; set; }
        public bool IsActive { get; set; }
        public string OpeningHours { get; set; }
        public string LicenseNumber { get; set; }
        public string TaxNumber { get; set; }
        public DateTime CreatedDate { get; set; }
        public int? CreatedBy { get; set; }
        public string CreatedByName { get; set; }
    }

    /// <summary>
    /// نموذج المخزن - Warehouse Model
    /// </summary>
    public class Warehouse
    {
        public int WarehouseID { get; set; }
        public string WarehouseCode { get; set; }
        public string WarehouseName { get; set; }
        public int BranchID { get; set; }
        public string BranchName { get; set; }
        public string Location { get; set; }
        public decimal? Capacity { get; set; }
        public string Temperature { get; set; }
        public string Humidity { get; set; }
        public int? KeeperID { get; set; }
        public string KeeperName { get; set; }
        public bool IsActive { get; set; }
        public string WarehouseType { get; set; }
        public DateTime CreatedDate { get; set; }
        public int? CreatedBy { get; set; }
        public string CreatedByName { get; set; }
    }

    /// <summary>
    /// نموذج مخزون الدواء - Drug Stock Model
    /// </summary>
    public class DrugStock
    {
        public int StockID { get; set; }
        public int DrugID { get; set; }
        public string DrugName { get; set; }
        public string DrugCode { get; set; }
        public int WarehouseID { get; set; }
        public string WarehouseName { get; set; }
        public string BatchNumber { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public DateTime? ManufacturingDate { get; set; }
        public int Quantity { get; set; }
        public int ReservedQuantity { get; set; }
        public int AvailableQuantity { get; set; }
        public decimal? PurchasePrice { get; set; }
        public decimal? SalePrice { get; set; }
        public decimal? CostPrice { get; set; }
        public DateTime LastUpdated { get; set; }
        public int? UpdatedBy { get; set; }
        public string UpdatedByName { get; set; }
        
        // خصائص إضافية للعرض
        public int DaysToExpiry { get { return ExpiryDate.HasValue ? (ExpiryDate.Value - DateTime.Now).Days : 0; } }
        public bool IsExpired { get { return ExpiryDate.HasValue && ExpiryDate.Value < DateTime.Now; } }
        public bool IsNearExpiry { get { return DaysToExpiry <= 30 && DaysToExpiry > 0; } }
        public decimal TotalValue { get { return Quantity * (CostPrice ?? 0); } }
    }





    /// <summary>
    /// نموذج ملخص المخزون - Stock Summary Model
    /// </summary>
    public class StockSummary
    {
        public int DrugID { get; set; }
        public string DrugName { get; set; }
        public string DrugCode { get; set; }
        public int TotalQuantity { get; set; }
        public int AvailableQuantity { get; set; }
        public int ReservedQuantity { get; set; }
        public decimal TotalValue { get; set; }
        public int WarehouseCount { get; set; }
        public DateTime? NearestExpiryDate { get; set; }
        public int ExpiredQuantity { get; set; }
        public int NearExpiryQuantity { get; set; }
        public bool IsLowStock { get; set; }
        public int MinimumStock { get; set; }
        public int ReorderLevel { get; set; }
    }
}
