using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نافذة المبيعات والفواتير - Sales and Invoices Form
    /// </summary>
    public partial class SalesForm : Form
    {
        #region Fields - الحقول

        private Invoice _currentInvoice;
        private decimal _totalAmount = 0;

        #endregion

        #region Constructor - المنشئ

        public SalesForm()
        {
            InitializeComponent();
            SetupForm();
            StartNewInvoice();
        }

        #endregion

        #region Form Setup - إعداد النافذة

        /// <summary>
        /// إعداد النافذة
        /// </summary>
        private void SetupForm()
        {
            // إعداد DataGridViews
            SetupDataGridViews();
            
            // تحميل البيانات المرجعية
            LoadCustomers();
            
            // إعداد التصميم المسطح
            ApplyFlatDesign();
            
            // إعداد التحقق من صحة البيانات
            SetupValidation();
        }

        /// <summary>
        /// إعداد DataGridViews
        /// </summary>
        private void SetupDataGridViews()
        {
            // إعداد جدول أصناف الفاتورة
            dgvInvoiceItems.AutoGenerateColumns = false;
            dgvInvoiceItems.AllowUserToAddRows = false;
            dgvInvoiceItems.AllowUserToDeleteRows = false;
            dgvInvoiceItems.ReadOnly = true;
            dgvInvoiceItems.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            
            // إعداد جدول البحث عن الأدوية
            dgvDrugs.AutoGenerateColumns = false;
            dgvDrugs.AllowUserToAddRows = false;
            dgvDrugs.AllowUserToDeleteRows = false;
            dgvDrugs.ReadOnly = true;
            dgvDrugs.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            
            // تنسيق الألوان
            ApplyDataGridViewStyle(dgvInvoiceItems);
            ApplyDataGridViewStyle(dgvDrugs);
        }

        /// <summary>
        /// تطبيق تنسيق DataGridView
        /// </summary>
        private void ApplyDataGridViewStyle(DataGridView dgv)
        {
            dgv.BackgroundColor = Color.White;
            dgv.GridColor = Color.FromArgb(189, 195, 199);
            dgv.DefaultCellStyle.SelectionBackColor = Color.FromArgb(52, 152, 219);
            dgv.DefaultCellStyle.SelectionForeColor = Color.White;
            dgv.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 73, 94);
            dgv.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgv.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10, FontStyle.Bold);
        }

        /// <summary>
        /// تحميل العملاء
        /// </summary>
        private void LoadCustomers()
        {
            try
            {
                // التأكد من وجود العميل العام
                int defaultCustomerId = CustomerManager.EnsureDefaultCustomer();

                string query = "SELECT CustomerID, CustomerName FROM Customers WHERE IsActive = 1 ORDER BY CustomerName";
                var dataTable = DatabaseHelper.ExecuteQuery(query);

                cmbCustomer.DisplayMember = "CustomerName";
                cmbCustomer.ValueMember = "CustomerID";
                cmbCustomer.DataSource = dataTable;

                // تحديد العميل العام كافتراضي
                cmbCustomer.SelectedValue = defaultCustomerId;

                System.Diagnostics.Debug.WriteLine($"تم تحميل العملاء وتحديد العميل العام: {defaultCustomerId}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل العملاء: {ex.Message}");
                // في حالة الخطأ، اترك بدون تحديد
                if (cmbCustomer.Items.Count > 0)
                    cmbCustomer.SelectedIndex = 0;
            }
        }

        /// <summary>
        /// تطبيق التصميم المسطح
        /// </summary>
        private void ApplyFlatDesign()
        {
            // إزالة حدود الأزرار
            foreach (Control control in this.Controls)
            {
                ApplyFlatStyleToControl(control);
            }
        }

        /// <summary>
        /// تطبيق التصميم المسطح على عنصر
        /// </summary>
        private void ApplyFlatStyleToControl(Control control)
        {
            if (control is Button button)
            {
                button.FlatAppearance.BorderSize = 0;
                button.Cursor = Cursors.Hand;
            }
            
            // تطبيق على العناصر الفرعية
            foreach (Control child in control.Controls)
            {
                ApplyFlatStyleToControl(child);
            }
        }

        /// <summary>
        /// إعداد التحقق من صحة البيانات
        /// </summary>
        private void SetupValidation()
        {
            // التحقق من الأرقام فقط في حقول الكمية
            txtQuantity.KeyPress += IntegerTextBox_KeyPress;
            
            // التحقق من الأرقام العشرية في حقول المبالغ
            txtDiscount.KeyPress += NumericTextBox_KeyPress;
            txtPaidAmount.KeyPress += NumericTextBox_KeyPress;
        }

        #endregion

        #region Invoice Management - إدارة الفواتير

        /// <summary>
        /// بدء فاتورة جديدة
        /// </summary>
        private void StartNewInvoice()
        {
            _currentInvoice = new Invoice
            {
                InvoiceNumber = SalesManager.GenerateInvoiceNumber(),
                InvoiceDate = DateTime.Now,
                InvoiceType = "Sale",
                UserID = UserManager.CurrentUser?.UserID ?? 1,
                CreatedBy = UserManager.CurrentUser?.UserID ?? 1,
                Details = new List<InvoiceDetail>()
            };
            
            // تحديث الواجهة
            lblInvoiceNumber.Text = $"رقم الفاتورة: {_currentInvoice.InvoiceNumber}";
            lblInvoiceDate.Text = $"التاريخ: {_currentInvoice.InvoiceDate:yyyy/MM/dd}";
            
            // مسح البيانات
            ClearInvoiceData();
        }

        /// <summary>
        /// مسح بيانات الفاتورة
        /// </summary>
        private void ClearInvoiceData()
        {
            // إعادة تحديد العميل العام
            int defaultCustomerId = CustomerManager.GetDefaultCustomerId();
            cmbCustomer.SelectedValue = defaultCustomerId;
            dgvInvoiceItems.DataSource = null;
            txtDrugSearch.Clear();
            dgvDrugs.DataSource = null;
            txtQuantity.Clear();
            txtDiscount.Text = "0";
            txtPaidAmount.Clear();
            
            UpdateTotals();
        }



        /// <summary>
        /// تحديث المجاميع
        /// </summary>
        private void UpdateTotals()
        {
            _totalAmount = _currentInvoice.Details?.Sum(item => item.TotalPrice) ?? 0;
            decimal discount = decimal.TryParse(txtDiscount.Text, out decimal d) ? d : 0;
            decimal finalAmount = _totalAmount - discount;
            
            lblSubTotal.Text = $"المجموع الفرعي: {_totalAmount:F2} ر.ي";
            lblDiscount.Text = $"الخصم: {discount:F2} ر.ي";
            lblTotal.Text = $"المجموع النهائي: {finalAmount:F2} ر.ي";
            
            // حساب الباقي
            decimal paidAmount = decimal.TryParse(txtPaidAmount.Text, out decimal p) ? p : 0;
            decimal remaining = finalAmount - paidAmount;
            lblRemaining.Text = $"الباقي: {remaining:F2} ر.ي";
        }

        #endregion

        #region Event Handlers - معالجات الأحداث

        /// <summary>
        /// البحث عن الأدوية
        /// </summary>
        private void btnSearchDrug_Click(object sender, EventArgs e)
        {
            try
            {
                string searchTerm = txtDrugSearch.Text.Trim();
                if (string.IsNullOrEmpty(searchTerm))
                {
                    dgvDrugs.DataSource = null;
                    return;
                }
                
                // استخدام البحث مع المخزون الفعلي - عرض الأدوية المتوفرة فقط
                var drugs = DrugManager.SearchDrugsWithStock(searchTerm, onlyInStock: true);
                dgvDrugs.DataSource = drugs;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إضافة صنف للفاتورة
        /// </summary>
        private void btnAddItem_Click(object sender, EventArgs e)
        {
            if (dgvDrugs.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار دواء", "تنبيه", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (!int.TryParse(txtQuantity.Text, out int quantity) || quantity <= 0)
            {
                MessageBox.Show("يرجى إدخال كمية صحيحة", "تنبيه", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedDrug = dgvDrugs.SelectedRows[0].DataBoundItem as Drug;
            if (selectedDrug == null) return;

            if (quantity > selectedDrug.CurrentStock)
            {
                MessageBox.Show($"الكمية المطلوبة ({quantity}) أكبر من المتوفر ({selectedDrug.CurrentStock})", 
                              "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // إضافة الصنف للفاتورة
            var item = new InvoiceDetail
            {
                DrugID = selectedDrug.DrugID,
                DrugName = selectedDrug.DrugName,
                Quantity = quantity,
                UnitPrice = selectedDrug.SalePrice,
                TotalPrice = quantity * selectedDrug.SalePrice
            };

            _currentInvoice.Details.Add(item);

            // تحديث الجدول
            dgvInvoiceItems.DataSource = null;
            dgvInvoiceItems.DataSource = _currentInvoice.Details;
            
            // تحديث المجاميع
            UpdateTotals();
            
            // مسح الحقول
            txtQuantity.Clear();
        }

        /// <summary>
        /// حذف صنف من الفاتورة
        /// </summary>
        private void btnRemoveItem_Click(object sender, EventArgs e)
        {
            if (dgvInvoiceItems.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار صنف للحذف", "تنبيه", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedItem = dgvInvoiceItems.SelectedRows[0].DataBoundItem as InvoiceDetail;
            if (selectedItem != null)
            {
                _currentInvoice.Details.Remove(selectedItem);

                // تحديث الجدول
                dgvInvoiceItems.DataSource = null;
                dgvInvoiceItems.DataSource = _currentInvoice.Details;
                
                // تحديث المجاميع
                UpdateTotals();
            }
        }

        /// <summary>
        /// حفظ الفاتورة
        /// </summary>
        private void btnSave_Click(object sender, EventArgs e)
        {
            if (ValidateInvoice())
            {
                if (SaveInvoice())
                {
                    MessageBox.Show("تم حفظ الفاتورة بنجاح", "نجح", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                    StartNewInvoice();
                }
            }
        }

        /// <summary>
        /// فاتورة جديدة
        /// </summary>
        //private void btnNew_Click(object sender, EventArgs e)
        //{
        //    if (_currentInvoice?.Details?.Count > 0)
        //    {
        //        var result = MessageBox.Show("هل تريد بدء فاتورة جديدة؟ سيتم فقدان البيانات الحالية.",
        //                                   "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
        //        if (result == DialogResult.No) return;
        //    }

        //    StartNewInvoice();
        //}

        /// <summary>
        /// طباعة الفاتورة
        /// </summary>
        private void btnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                if (_currentInvoice?.Details?.Count == 0 || _currentInvoice?.Details == null)
                {
                    MessageBox.Show("لا يمكن طباعة فاتورة فارغة", "تنبيه",
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // منع الطباعة المزدوجة
                btnPrint.Enabled = false;

                // إنشاء تقرير الطباعة
                var printDocument = new System.Drawing.Printing.PrintDocument();

                printDocument.PrintPage += (s, ev) =>
                {
                    var graphics = ev.Graphics;
                    var font = new Font("Arial", 12);
                    var brush = Brushes.Black;
                    var y = 50;

                    // طباعة عنوان الفاتورة
                    graphics.DrawString("فاتورة مبيعات", new Font("Arial", 16, FontStyle.Bold), brush, 300, y);
                    y += 40;

                    // طباعة معلومات الفاتورة
                    graphics.DrawString($"رقم الفاتورة: {lblInvoiceNumber.Text}", font, brush, 50, y);
                    graphics.DrawString($"التاريخ: {DateTime.Now:yyyy/MM/dd}", font, brush, 400, y);
                    y += 30;

                    // طباعة معلومات العميل
                    if (cmbCustomer.SelectedItem != null)
                    {
                        graphics.DrawString($"العميل: {cmbCustomer.Text}", font, brush, 50, y);
                        y += 30;
                    }

                    y += 20;

                    // طباعة عناوين الأعمدة
                    graphics.DrawString("الصنف", new Font("Arial", 10, FontStyle.Bold), brush, 50, y);
                    graphics.DrawString("الكمية", new Font("Arial", 10, FontStyle.Bold), brush, 250, y);
                    graphics.DrawString("السعر", new Font("Arial", 10, FontStyle.Bold), brush, 350, y);
                    graphics.DrawString("الإجمالي", new Font("Arial", 10, FontStyle.Bold), brush, 450, y);
                    y += 25;

                    // طباعة خط فاصل
                    graphics.DrawLine(Pens.Black, 50, y, 550, y);
                    y += 10;

                    // طباعة الأصناف
                    foreach (DataGridViewRow row in dgvInvoiceItems.Rows)
                    {
                        if (row.Cells["ItemDrugName"].Value != null)
                        {
                            graphics.DrawString(row.Cells["ItemDrugName"].Value.ToString(), font, brush, 50, y);
                            graphics.DrawString(row.Cells["ItemQuantity"].Value.ToString(), font, brush, 250, y);
                            graphics.DrawString(row.Cells["ItemUnitPrice"].Value.ToString(), font, brush, 350, y);
                            graphics.DrawString(row.Cells["ItemTotalPrice"].Value.ToString(), font, brush, 450, y);
                            y += 20;
                        }
                    }

                    y += 20;
                    graphics.DrawLine(Pens.Black, 50, y, 550, y);
                    y += 20;

                    // طباعة المجاميع
                    graphics.DrawString(lblSubTotal.Text, new Font("Arial", 12, FontStyle.Bold), brush, 300, y);
                    y += 25;
                    graphics.DrawString(lblDiscount.Text, new Font("Arial", 12, FontStyle.Bold), brush, 300, y);
                    y += 25;
                    graphics.DrawString(lblTotal.Text, new Font("Arial", 14, FontStyle.Bold), brush, 300, y);
                };

                // خيار الطباعة المباشرة أو عرض مربع حوار
                var result = MessageBox.Show("هل تريد طباعة الفاتورة مباشرة؟\n\nYes = طباعة مباشرة\nNo = اختيار الطابعة",
                                           "طباعة الفاتورة", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // طباعة مباشرة
                    printDocument.Print();
                    MessageBox.Show("تم إرسال الفاتورة للطباعة", "نجح",
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else if (result == DialogResult.No)
                {
                    // عرض مربع حوار الطباعة
                    var printDialog = new PrintDialog();
                    if (printDialog.ShowDialog() == DialogResult.OK)
                    {
                        printDocument.PrinterSettings = printDialog.PrinterSettings;
                        printDocument.Print();
                        MessageBox.Show("تم إرسال الفاتورة للطباعة", "نجح",
                                      MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
                LogManager.LogError("SalesForm.btnPrint_Click", ex);
            }
            finally
            {
                // إعادة تفعيل زر الطباعة
                btnPrint.Enabled = true;
            }
        }

        /// <summary>
        /// إلغاء العملية والخروج
        /// </summary>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            try
            {
                if (_currentInvoice?.Details?.Count > 0)
                {
                    var result = MessageBox.Show("هل تريد إلغاء الفاتورة الحالية؟ سيتم فقدان جميع البيانات.",
                                               "تأكيد الإلغاء", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    if (result == DialogResult.No) return;
                }

                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إلغاء العملية: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
                LogManager.LogError("SalesForm.btnCancel_Click");
            }
        }

        /// <summary>
        /// البحث التلقائي عن الأدوية
        /// </summary>
        private void txtDrugSearch_TextChanged(object sender, EventArgs e)
        {
            drugSearchTimer.Stop();
            drugSearchTimer.Start();
        }

        /// <summary>
        /// البحث التلقائي
        /// </summary>
        private void drugSearchTimer_Tick(object sender, EventArgs e)
        {
            drugSearchTimer.Stop();
            btnSearchDrug_Click(sender, e);
        }

        /// <summary>
        /// تحديث المجاميع عند تغيير الخصم أو المبلغ المدفوع
        /// </summary>
        private void txtDiscount_TextChanged(object sender, EventArgs e)
        {
            UpdateTotals();
        }

        private void txtPaidAmount_TextChanged(object sender, EventArgs e)
        {
            UpdateTotals();
        }

        /// <summary>
        /// التحقق من الأرقام العشرية
        /// </summary>
        private void NumericTextBox_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (!char.IsControl(e.KeyChar) && !char.IsDigit(e.KeyChar) && e.KeyChar != '.')
            {
                e.Handled = true;
            }

            if (e.KeyChar == '.' && (sender as TextBox).Text.IndexOf('.') > -1)
            {
                e.Handled = true;
            }
        }

        /// <summary>
        /// التحقق من الأرقام الصحيحة
        /// </summary>
        private void IntegerTextBox_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (!char.IsControl(e.KeyChar) && !char.IsDigit(e.KeyChar))
            {
                e.Handled = true;
            }
        }

        #endregion

        #region Validation and Save - التحقق والحفظ

        /// <summary>
        /// التحقق من صحة الفاتورة
        /// </summary>
        private bool ValidateInvoice()
        {
            if (_currentInvoice.Details.Count == 0)
            {
                MessageBox.Show("يرجى إضافة أصناف للفاتورة", "تنبيه", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        /// <summary>
        /// حفظ الفاتورة
        /// </summary>
        private bool SaveInvoice()
        {
            try
            {
                // التحقق من وجود أصناف في الفاتورة
                if (_currentInvoice?.Details?.Count == 0)
                {
                    MessageBox.Show("لا يمكن حفظ فاتورة فارغة", "تنبيه",
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }

                // تحديث بيانات الفاتورة
                _currentInvoice.CustomerID = cmbCustomer.SelectedValue as int?;
                _currentInvoice.SubTotal = _totalAmount;
                _currentInvoice.DiscountAmount = decimal.TryParse(txtDiscount.Text, out decimal d) ? d : 0;
                _currentInvoice.TotalAmount = _totalAmount - _currentInvoice.DiscountAmount;
                _currentInvoice.PaidAmount = decimal.TryParse(txtPaidAmount.Text, out decimal p) ? p : 0;
                _currentInvoice.RemainingAmount = _currentInvoice.TotalAmount - _currentInvoice.PaidAmount;
                _currentInvoice.PaymentMethod = _currentInvoice.PaidAmount >= _currentInvoice.TotalAmount ? "مدفوع" : "آجل";
                _currentInvoice.PaymentStatus = _currentInvoice.PaidAmount >= _currentInvoice.TotalAmount ? "مدفوع" : "جزئي";
                _currentInvoice.CreatedBy = UserManager.CurrentUser?.UserID ?? 1;

                // تسجيل للتشخيص
                System.Diagnostics.Debug.WriteLine($"حفظ فاتورة: {_currentInvoice.InvoiceNumber}, أصناف: {_currentInvoice.Details.Count}, المجموع: {_currentInvoice.TotalAmount}");

                // حفظ في قاعدة البيانات
                int invoiceId = SalesManager.CreateSalesInvoice(_currentInvoice);
                
                if (invoiceId > 0)
                {
                    _currentInvoice.InvoiceID = invoiceId;
                    return true;
                }
                else
                {
                    MessageBox.Show("فشل في حفظ الفاتورة", "خطأ", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return false;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الفاتورة: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// إنشاء فاتورة جديدة
        /// </summary>
        private void btnNew_Click(object sender, EventArgs e)
        {
            try
            {
                if (_currentInvoice?.Details?.Count > 0)
                {
                    var result = MessageBox.Show("هل تريد حفظ الفاتورة الحالية قبل إنشاء فاتورة جديدة؟", "تأكيد",
                                                MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        if (SaveInvoice())
                        {
                            StartNewInvoice();
                        }
                    }
                    else if (result == DialogResult.No)
                    {
                        StartNewInvoice();
                    }
                    // إذا اختار Cancel، لا نفعل شيء
                }
                else
                {
                    StartNewInvoice();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء فاتورة جديدة: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
                LogManager.LogError($"SalesForm.btnNew_Click: {ex.Message}");
            }
        }

        #endregion
    }
}
