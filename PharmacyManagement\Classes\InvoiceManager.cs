using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using PharmacyManagement.Models;

namespace PharmacyManagement.Classes
{
    /// <summary>
    /// مدير الفواتير - Invoice Manager
    /// يدير جميع عمليات الفواتير في النظام
    /// </summary>
    public static class InvoiceManager
    {
        #region Add Invoice - إضافة فاتورة

        /// <summary>
        /// إضافة فاتورة جديدة
        /// </summary>
        /// <param name="invoice">بيانات الفاتورة</param>
        /// <returns>معرف الفاتورة الجديدة</returns>
        public static int AddInvoice(Invoice invoice)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // إدراج الفاتورة الرئيسية
                            string invoiceQuery = @"
                                INSERT INTO Invoices (InvoiceNumber, InvoiceDate, InvoiceType, CustomerID, 
                                                    SubTotal, DiscountAmount, DiscountPercent, TaxAmount, 
                                                    TotalAmount, PaidAmount, RemainingAmount, PaymentType, 
                                                    PaymentMethod, Status, Notes, UserID, CreatedDate)
                                VALUES (@InvoiceNumber, @InvoiceDate, @InvoiceType, @CustomerID, 
                                       @SubTotal, @DiscountAmount, @DiscountPercent, @TaxAmount, 
                                       @TotalAmount, @PaidAmount, @RemainingAmount, @PaymentType, 
                                       @PaymentMethod, @Status, @Notes, @UserID, @CreatedDate);
                                SELECT SCOPE_IDENTITY();";

                            int invoiceId;
                            using (var command = new SqlCommand(invoiceQuery, connection, transaction))
                            {
                                command.Parameters.AddWithValue("@InvoiceNumber", invoice.InvoiceNumber);
                                command.Parameters.AddWithValue("@InvoiceDate", invoice.InvoiceDate);
                                command.Parameters.AddWithValue("@InvoiceType", invoice.InvoiceType);
                                command.Parameters.AddWithValue("@CustomerID", invoice.CustomerID ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@SubTotal", invoice.SubTotal);
                                command.Parameters.AddWithValue("@DiscountAmount", invoice.DiscountAmount);
                                command.Parameters.AddWithValue("@DiscountPercent", invoice.DiscountPercent);
                                command.Parameters.AddWithValue("@TaxAmount", invoice.TaxAmount);
                                command.Parameters.AddWithValue("@TotalAmount", invoice.TotalAmount);
                                command.Parameters.AddWithValue("@PaidAmount", invoice.PaidAmount);
                                command.Parameters.AddWithValue("@RemainingAmount", invoice.RemainingAmount);
                                command.Parameters.AddWithValue("@PaymentType", invoice.PaymentType ?? "Cash");
                                command.Parameters.AddWithValue("@PaymentMethod", invoice.PaymentMethod ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Status", invoice.Status ?? "Completed");
                                command.Parameters.AddWithValue("@Notes", invoice.Notes ?? (object)DBNull.Value);
                                var userID = invoice.UserID;
                                if (userID == 0 && UserManager.CurrentUser != null)
                                {
                                    userID = UserManager.CurrentUser.UserID;
                                }
                                command.Parameters.AddWithValue("@UserID", userID > 0 ? (object)userID : DBNull.Value);
                                command.Parameters.AddWithValue("@CreatedDate", invoice.CreatedDate);

                                invoiceId = Convert.ToInt32(command.ExecuteScalar());
                            }

                            // إدراج تفاصيل الفاتورة
                            if (invoice.Details != null && invoice.Details.Count > 0)
                            {
                                string detailQuery = @"
                                    INSERT INTO InvoiceDetails (InvoiceID, DrugID, DrugName, DrugCode, 
                                                              Quantity, UnitPrice, TotalPrice, Notes)
                                    VALUES (@InvoiceID, @DrugID, @DrugName, @DrugCode, 
                                           @Quantity, @UnitPrice, @TotalPrice, @Notes)";

                                foreach (var detail in invoice.Details)
                                {
                                    using (var detailCommand = new SqlCommand(detailQuery, connection, transaction))
                                    {
                                        detailCommand.Parameters.AddWithValue("@InvoiceID", invoiceId);
                                        detailCommand.Parameters.AddWithValue("@DrugID", detail.DrugID);
                                        detailCommand.Parameters.AddWithValue("@DrugName", detail.DrugName);
                                        detailCommand.Parameters.AddWithValue("@DrugCode", detail.DrugCode ?? (object)DBNull.Value);
                                        detailCommand.Parameters.AddWithValue("@Quantity", detail.Quantity);
                                        detailCommand.Parameters.AddWithValue("@UnitPrice", detail.UnitPrice);
                                        detailCommand.Parameters.AddWithValue("@TotalPrice", detail.TotalPrice);
                                        detailCommand.Parameters.AddWithValue("@Notes", detail.Notes ?? (object)DBNull.Value);

                                        detailCommand.ExecuteNonQuery();
                                    }

                                    // تحديث المخزون
                                    UpdateDrugStock(detail.DrugID, -detail.Quantity, connection, transaction);
                                }
                            }

                            transaction.Commit();
                            LogManager.LogActivity("InvoiceManager.AddInvoice", $"تم إضافة فاتورة جديدة: {invoice.InvoiceNumber}");
                            return invoiceId;
                        }
                        catch
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("InvoiceManager.AddInvoice", ex);
                throw new Exception($"خطأ في إضافة الفاتورة: {ex.Message}");
            }
        }

        #endregion

        #region Get Invoices - جلب الفواتير

        /// <summary>
        /// جلب جميع الفواتير
        /// </summary>
        /// <returns>قائمة الفواتير</returns>
        public static List<Invoice> GetAllInvoices()
        {
            try
            {
                var invoices = new List<Invoice>();
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT i.InvoiceID, i.InvoiceNumber, i.InvoiceDate, i.SubTotal,
                               i.DiscountAmount, i.DiscountPercent, i.TaxAmount,
                               i.NetAmount as TotalAmount, i.PaidAmount,
                               (i.NetAmount - i.PaidAmount) as RemainingAmount,
                               i.PaymentMethod, i.PaymentStatus, i.InvoiceStatus as Status,
                               i.Notes, i.UserID, i.CreatedDate, i.CustomerID,
                               c.CustomerName, u.FullName as UserName,
                               'Sale' as InvoiceType, i.PaymentMethod as PaymentType
                        FROM SalesInvoices i
                        LEFT JOIN Customers c ON i.CustomerID = c.CustomerID
                        LEFT JOIN Users u ON i.UserID = u.UserID
                        ORDER BY i.InvoiceDate DESC, i.InvoiceID DESC";

                    using (var command = new SqlCommand(query, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var invoice = new Invoice
                            {
                                InvoiceID = Convert.ToInt32(reader["InvoiceID"]),
                                InvoiceNumber = reader["InvoiceNumber"].ToString(),
                                InvoiceDate = Convert.ToDateTime(reader["InvoiceDate"]),
                                InvoiceType = reader["InvoiceType"].ToString(),
                                CustomerID = reader["CustomerID"] != DBNull.Value ? (int?)Convert.ToInt32(reader["CustomerID"]) : null,
                                CustomerName = reader["CustomerName"]?.ToString(),
                                SubTotal = Convert.ToDecimal(reader["SubTotal"]),
                                DiscountAmount = Convert.ToDecimal(reader["DiscountAmount"]),
                                DiscountPercent = Convert.ToDecimal(reader["DiscountPercent"]),
                                TaxAmount = Convert.ToDecimal(reader["TaxAmount"]),
                                TotalAmount = Convert.ToDecimal(reader["TotalAmount"]),
                                PaidAmount = Convert.ToDecimal(reader["PaidAmount"]),
                                RemainingAmount = Convert.ToDecimal(reader["RemainingAmount"]),
                                PaymentType = reader["PaymentType"]?.ToString() ?? "نقدي",
                                PaymentMethod = reader["PaymentMethod"]?.ToString(),
                                Status = reader["Status"]?.ToString() ?? "مكتملة",
                                Notes = reader["Notes"]?.ToString(),
                                UserID = reader["UserID"] != DBNull.Value ? Convert.ToInt32(reader["UserID"]) : 0,
                                UserName = reader["UserName"]?.ToString(),
                                CreatedDate = Convert.ToDateTime(reader["CreatedDate"])
                            };

                            invoices.Add(invoice);
                        }
                    }
                }

                return invoices;
            }
            catch (Exception ex)
            {
                LogManager.LogError("InvoiceManager.GetAllInvoices", ex);
                throw new Exception($"خطأ في جلب الفواتير: {ex.Message}");
            }
        }

        /// <summary>
        /// جلب فواتير عميل معين
        /// </summary>
        /// <param name="customerId">معرف العميل</param>
        /// <returns>قائمة فواتير العميل</returns>
        public static List<Invoice> GetCustomerInvoices(int customerId)
        {
            try
            {
                var invoices = new List<Invoice>();
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT i.InvoiceID, i.InvoiceNumber, i.InvoiceDate, i.SubTotal,
                               i.DiscountAmount, i.DiscountPercent, i.TaxAmount,
                               i.NetAmount as TotalAmount, i.PaidAmount,
                               (i.NetAmount - i.PaidAmount) as RemainingAmount,
                               i.PaymentMethod, i.PaymentStatus, i.InvoiceStatus as Status,
                               i.Notes, i.UserID, i.CreatedDate, i.CustomerID,
                               c.CustomerName, u.FullName as UserName,
                               'Sale' as InvoiceType, i.PaymentMethod as PaymentType
                        FROM SalesInvoices i
                        LEFT JOIN Customers c ON i.CustomerID = c.CustomerID
                        LEFT JOIN Users u ON i.UserID = u.UserID
                        WHERE i.CustomerID = @CustomerID
                        ORDER BY i.InvoiceDate DESC, i.InvoiceID DESC";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@CustomerID", customerId);
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var invoice = new Invoice
                                {
                                    InvoiceID = Convert.ToInt32(reader["InvoiceID"]),
                                    InvoiceNumber = reader["InvoiceNumber"].ToString(),
                                    InvoiceDate = Convert.ToDateTime(reader["InvoiceDate"]),
                                    InvoiceType = reader["InvoiceType"].ToString(),
                                    CustomerID = reader["CustomerID"] != DBNull.Value ? (int?)Convert.ToInt32(reader["CustomerID"]) : null,
                                    CustomerName = reader["CustomerName"]?.ToString(),
                                    SubTotal = Convert.ToDecimal(reader["SubTotal"]),
                                    DiscountAmount = Convert.ToDecimal(reader["DiscountAmount"]),
                                    DiscountPercent = Convert.ToDecimal(reader["DiscountPercent"]),
                                    TaxAmount = Convert.ToDecimal(reader["TaxAmount"]),
                                    TotalAmount = Convert.ToDecimal(reader["TotalAmount"]),
                                    PaidAmount = Convert.ToDecimal(reader["PaidAmount"]),
                                    RemainingAmount = Convert.ToDecimal(reader["RemainingAmount"]),
                                    PaymentType = reader["PaymentType"]?.ToString() ?? "نقدي",
                                    PaymentMethod = reader["PaymentMethod"]?.ToString(),
                                    Status = reader["Status"]?.ToString() ?? "مكتملة",
                                    Notes = reader["Notes"]?.ToString(),
                                    UserID = reader["UserID"] != DBNull.Value ? Convert.ToInt32(reader["UserID"]) : 0,
                                    UserName = reader["UserName"]?.ToString(),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"])
                                };

                                invoices.Add(invoice);
                            }
                        }
                    }
                }

                LogManager.LogInfo($"تم جلب {invoices.Count} فاتورة للعميل رقم {customerId}");
                return invoices;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في جلب فواتير العميل رقم {customerId}: {ex.Message}");
                throw new Exception($"خطأ في جلب فواتير العميل: {ex.Message}");
            }
        }

        /// <summary>
        /// جلب فاتورة بالمعرف
        /// </summary>
        /// <param name="invoiceId">معرف الفاتورة</param>
        /// <returns>بيانات الفاتورة</returns>
        public static Invoice GetInvoiceById(int invoiceId)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    
                    // جلب بيانات الفاتورة الرئيسية
                    string invoiceQuery = @"
                        SELECT i.*, c.CustomerName, u.FullName as UserName
                        FROM SalesInvoices i
                        LEFT JOIN Customers c ON i.CustomerID = c.CustomerID
                        LEFT JOIN Users u ON i.UserID = u.UserID
                        WHERE i.InvoiceID = @InvoiceID";

                    Invoice invoice = null;
                    using (var command = new SqlCommand(invoiceQuery, connection))
                    {
                        command.Parameters.AddWithValue("@InvoiceID", invoiceId);
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                invoice = new Invoice
                                {
                                    InvoiceID = Convert.ToInt32(reader["InvoiceID"]),
                                    InvoiceNumber = reader["InvoiceNumber"].ToString(),
                                    InvoiceDate = Convert.ToDateTime(reader["InvoiceDate"]),
                                    InvoiceType = reader["InvoiceType"].ToString(),
                                    CustomerID = reader["CustomerID"] != DBNull.Value ? (int?)Convert.ToInt32(reader["CustomerID"]) : null,
                                    CustomerName = reader["CustomerName"]?.ToString(),
                                    SubTotal = Convert.ToDecimal(reader["SubTotal"]),
                                    DiscountAmount = Convert.ToDecimal(reader["DiscountAmount"]),
                                    DiscountPercent = Convert.ToDecimal(reader["DiscountPercent"]),
                                    TaxAmount = Convert.ToDecimal(reader["TaxAmount"]),
                                    TotalAmount = Convert.ToDecimal(reader["TotalAmount"]),
                                    PaidAmount = Convert.ToDecimal(reader["PaidAmount"]),
                                    RemainingAmount = Convert.ToDecimal(reader["RemainingAmount"]),
                                    PaymentType = reader["PaymentType"].ToString(),
                                    PaymentMethod = reader["PaymentMethod"]?.ToString(),
                                    Status = reader["Status"].ToString(),
                                    Notes = reader["Notes"]?.ToString(),
                                    UserID = reader["UserID"] != DBNull.Value ? Convert.ToInt32(reader["UserID"]) : 0,
                                    UserName = reader["UserName"]?.ToString(),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"])
                                };
                            }
                        }
                    }

                    if (invoice != null)
                    {
                        // جلب تفاصيل الفاتورة
                        invoice.Details = GetInvoiceDetails(invoiceId, connection);
                    }

                    return invoice;
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("InvoiceManager.GetInvoiceById", ex);
                throw new Exception($"خطأ في جلب الفاتورة: {ex.Message}");
            }
        }

        #endregion

        #region Helper Methods - الطرق المساعدة

        /// <summary>
        /// جلب تفاصيل الفاتورة
        /// </summary>
        /// <param name="invoiceId">معرف الفاتورة</param>
        /// <param name="connection">اتصال قاعدة البيانات</param>
        /// <returns>قائمة تفاصيل الفاتورة</returns>
        private static List<InvoiceDetail> GetInvoiceDetails(int invoiceId, SqlConnection connection)
        {
            var details = new List<InvoiceDetail>();
            string query = @"
                SELECT * FROM InvoiceDetails 
                WHERE InvoiceID = @InvoiceID 
                ORDER BY DetailID";

            using (var command = new SqlCommand(query, connection))
            {
                command.Parameters.AddWithValue("@InvoiceID", invoiceId);
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        details.Add(new InvoiceDetail
                        {
                            DetailID = Convert.ToInt32(reader["DetailID"]),
                            InvoiceID = Convert.ToInt32(reader["InvoiceID"]),
                            DrugID = Convert.ToInt32(reader["DrugID"]),
                            DrugName = reader["DrugName"].ToString(),
                            DrugCode = reader["DrugCode"]?.ToString(),
                            Quantity = Convert.ToInt32(reader["Quantity"]),
                            UnitPrice = Convert.ToDecimal(reader["UnitPrice"]),
                            TotalPrice = Convert.ToDecimal(reader["TotalPrice"]),
                            Notes = reader["Notes"]?.ToString()
                        });
                    }
                }
            }

            return details;
        }

        /// <summary>
        /// تحديث مخزون الدواء
        /// </summary>
        /// <param name="drugId">معرف الدواء</param>
        /// <param name="quantityChange">التغيير في الكمية</param>
        /// <param name="connection">اتصال قاعدة البيانات</param>
        /// <param name="transaction">المعاملة</param>
        private static void UpdateDrugStock(int drugId, int quantityChange, SqlConnection connection, SqlTransaction transaction)
        {
            string query = @"
                UPDATE Drugs 
                SET CurrentStock = CurrentStock + @QuantityChange 
                WHERE DrugID = @DrugID";

            using (var command = new SqlCommand(query, connection, transaction))
            {
                command.Parameters.AddWithValue("@DrugID", drugId);
                command.Parameters.AddWithValue("@QuantityChange", quantityChange);
                command.ExecuteNonQuery();
            }
        }

        /// <summary>
        /// توليد رقم فاتورة جديد
        /// </summary>
        /// <returns>رقم الفاتورة</returns>
        public static string GenerateInvoiceNumber()
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT COUNT(*) + 1 
                        FROM Invoices 
                        WHERE YEAR(InvoiceDate) = YEAR(GETDATE())";

                    using (var command = new SqlCommand(query, connection))
                    {
                        int nextNumber = Convert.ToInt32(command.ExecuteScalar());
                        return "INV-" + DateTime.Now.Year.ToString() + "-" + nextNumber.ToString("D6");
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("InvoiceManager.GenerateInvoiceNumber", ex);
                return "INV-" + DateTime.Now.Year.ToString() + "-" + DateTime.Now.Ticks.ToString().Substring(10);
            }
        }

        /// <summary>
        /// الحصول على مبيعات اليوم
        /// </summary>
        /// <returns>مبلغ المبيعات</returns>
        public static decimal GetTodaySales()
        {
            try
            {
                string query = @"
                    SELECT ISNULL(SUM(NetAmount), 0)
                    FROM Invoices
                    WHERE CAST(InvoiceDate AS DATE) = CAST(GETDATE() AS DATE)
                    AND InvoiceType = 'بيع'";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        return Convert.ToDecimal(command.ExecuteScalar());
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على مبيعات اليوم: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// البحث في الفواتير
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <returns>قائمة الفواتير المطابقة</returns>
        public static List<Invoice> SearchInvoices(string searchTerm)
        {
            try
            {
                var invoices = new List<Invoice>();
                string query = @"
                    SELECT i.*, c.CustomerName, u.FullName as CreatedByName
                    FROM SalesInvoices i
                    LEFT JOIN Customers c ON i.CustomerID = c.CustomerID
                    LEFT JOIN Users u ON i.CreatedBy = u.UserID
                    WHERE (i.InvoiceNumber LIKE @SearchTerm OR
                           c.CustomerName LIKE @SearchTerm OR
                           u.FullName LIKE @SearchTerm)
                    ORDER BY i.InvoiceDate DESC";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@SearchTerm", $"%{searchTerm}%");
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                invoices.Add(new Invoice
                                {
                                    InvoiceID = Convert.ToInt32(reader["InvoiceID"]),
                                    InvoiceNumber = reader["InvoiceNumber"].ToString(),
                                    InvoiceDate = Convert.ToDateTime(reader["InvoiceDate"]),
                                    CustomerID = reader["CustomerID"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["CustomerID"]),
                                    CustomerName = reader["CustomerName"]?.ToString(),
                                    TotalAmount = Convert.ToDecimal(reader["TotalAmount"]),
                                    DiscountAmount = reader["DiscountAmount"] == DBNull.Value ? 0 : Convert.ToDecimal(reader["DiscountAmount"]),
                                    NetAmount = Convert.ToDecimal(reader["NetAmount"]),
                                    PaymentMethod = reader["PaymentMethod"].ToString(),
                                    InvoiceType = reader["InvoiceType"].ToString(),
                                    Notes = reader["Notes"] != DBNull.Value ? reader["Notes"].ToString() : null,
                                    CreatedBy = reader["CreatedBy"] != DBNull.Value ? Convert.ToInt32(reader["CreatedBy"]) : 0,
                                    CreatedByName = reader["CreatedByName"] != DBNull.Value ? reader["CreatedByName"].ToString() : null,
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                                    BranchID = reader["BranchID"] != DBNull.Value ? Convert.ToInt32(reader["BranchID"]) : 0
                                });
                            }
                        }
                    }
                }
                return invoices;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في البحث في الفواتير: {ex.Message}");
                return new List<Invoice>();
            }
        }



        #endregion
    }
}
