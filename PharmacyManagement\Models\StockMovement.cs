using System;

namespace PharmacyManagement.Models
{
    /// <summary>
    /// نموذج حركة المخزون - Stock Movement Model
    /// </summary>
    public class StockMovement
    {
        #region Properties - الخصائص

        /// <summary>
        /// معرف حركة المخزون
        /// </summary>
        public int MovementID { get; set; }

        /// <summary>
        /// معرف الدواء
        /// </summary>
        public int DrugID { get; set; }

        /// <summary>
        /// اسم الدواء
        /// </summary>
        public string DrugName { get; set; }

        /// <summary>
        /// كود الدواء
        /// </summary>
        public string DrugCode { get; set; }

        /// <summary>
        /// نوع الحركة (إضافة، خصم، تعديل، جرد)
        /// </summary>
        public string MovementType { get; set; }

        /// <summary>
        /// الكمية
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// المخزون السابق
        /// </summary>
        public decimal PreviousStock { get; set; }

        /// <summary>
        /// المخزون الجديد
        /// </summary>
        public decimal NewStock { get; set; }

        /// <summary>
        /// تاريخ الحركة
        /// </summary>
        public DateTime MovementDate { get; set; }

        /// <summary>
        /// ملاحظات
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// معرف المستخدم
        /// </summary>
        public int? UserID { get; set; }

        /// <summary>
        /// اسم المستخدم
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedDate { get; set; }

        #endregion

        #region Constructor - المنشئ

        /// <summary>
        /// منشئ افتراضي
        /// </summary>
        public StockMovement()
        {
            MovementDate = DateTime.Now;
            CreatedDate = DateTime.Now;
        }

        #endregion
    }
}
