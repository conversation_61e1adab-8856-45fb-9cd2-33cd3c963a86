using System;
using System.Linq;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Forms;

namespace PharmacyManagement
{
    /// <summary>
    /// اختبار النافذة المبسطة لتحديث المخزون
    /// </summary>
    public static class TestSimpleStockUpdate
    {
        /// <summary>
        /// اختبار النافذة المبسطة
        /// </summary>
        public static void TestSimpleStockUpdateForm()
        {
            try
            {
                Console.WriteLine("=== اختبار النافذة المبسطة لتحديث المخزون ===");
                
                // 1. جلب دواء للاختبار
                var drugs = DrugManager.GetAllDrugs();
                if (drugs.Count == 0)
                {
                    Console.WriteLine("❌ لا توجد أدوية للاختبار");
                    MessageBox.Show("لا توجد أدوية في قاعدة البيانات للاختبار", "تنبيه", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                
                var testDrug = drugs.First();
                Console.WriteLine($"الدواء المختار للاختبار: {testDrug.DrugName} (كود: {testDrug.DrugCode})");
                
                // 2. جلب المخزون الحالي
                decimal currentStock = GetCurrentStockFromInventory(testDrug.DrugID);
                Console.WriteLine($"المخزون الحالي: {currentStock}");
                
                // 3. فتح النافذة المبسطة
                Console.WriteLine("فتح النافذة المبسطة لتحديث المخزون...");
                
                var updateForm = new SimpleStockUpdateForm(testDrug);
                
                // عرض معلومات النافذة
                Console.WriteLine("تم إنشاء النافذة بنجاح");
                Console.WriteLine("يمكنك الآن:");
                Console.WriteLine("1. اختيار نوع العملية (إضافة أو خصم)");
                Console.WriteLine("2. إدخال الكمية");
                Console.WriteLine("3. مشاهدة المعاينة المباشرة للمخزون الجديد");
                Console.WriteLine("4. إضافة ملاحظات");
                Console.WriteLine("5. حفظ التحديث");
                
                // فتح النافذة
                if (updateForm.ShowDialog() == DialogResult.OK)
                {
                    Console.WriteLine("✅ تم تحديث المخزون بنجاح");
                    
                    // التحقق من المخزون الجديد
                    decimal newStock = GetCurrentStockFromInventory(testDrug.DrugID);
                    Console.WriteLine($"المخزون الجديد: {newStock}");
                    Console.WriteLine($"التغيير: {newStock - currentStock}");
                }
                else
                {
                    Console.WriteLine("❌ تم إلغاء العملية");
                }
                
                Console.WriteLine("=== انتهى اختبار النافذة المبسطة ===");
                
                MessageBox.Show("تم اختبار النافذة المبسطة بنجاح!", "اختبار النافذة المبسطة", 
                              MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار النافذة المبسطة: {ex.Message}");
                MessageBox.Show($"خطأ في الاختبار: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        /// <summary>
        /// جلب المخزون الحالي من جدول Inventory
        /// </summary>
        private static decimal GetCurrentStockFromInventory(int drugId)
        {
            try
            {
                string query = @"
                    SELECT ISNULL(SUM(Quantity), 0) as TotalStock
                    FROM Inventory 
                    WHERE DrugID = @DrugID AND Quantity > 0";
                
                var parameters = new[] { DatabaseHelper.CreateParameter("@DrugID", drugId) };
                
                using (var reader = DatabaseHelper.ExecuteReader(query, parameters))
                {
                    if (reader.Read())
                    {
                        return Convert.ToDecimal(reader["TotalStock"]);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في جلب المخزون: {ex.Message}");
            }
            
            return 0;
        }
        
        /// <summary>
        /// اختبار سيناريوهات مختلفة
        /// </summary>
        public static void TestDifferentScenarios()
        {
            try
            {
                Console.WriteLine("=== اختبار سيناريوهات مختلفة للنافذة المبسطة ===");
                
                var drugs = DrugManager.GetAllDrugs();
                if (drugs.Count == 0)
                {
                    Console.WriteLine("❌ لا توجد أدوية للاختبار");
                    return;
                }
                
                var testDrug = drugs.First();
                decimal initialStock = GetCurrentStockFromInventory(testDrug.DrugID);
                
                Console.WriteLine($"الدواء: {testDrug.DrugName}");
                Console.WriteLine($"المخزون الأولي: {initialStock}");
                
                // سيناريو 1: إضافة 50 وحدة
                Console.WriteLine("\n--- سيناريو 1: إضافة 50 وحدة ---");
                Console.WriteLine($"المخزون الحالي: {initialStock}");
                Console.WriteLine("العملية: إضافة 50");
                Console.WriteLine($"النتيجة المتوقعة: {initialStock + 50}");
                
                // سيناريو 2: خصم 25 وحدة
                Console.WriteLine("\n--- سيناريو 2: خصم 25 وحدة ---");
                decimal stockAfterAddition = initialStock + 50;
                Console.WriteLine($"المخزون الحالي: {stockAfterAddition}");
                Console.WriteLine("العملية: خصم 25");
                Console.WriteLine($"النتيجة المتوقعة: {stockAfterAddition - 25}");
                
                // سيناريو 3: محاولة خصم أكثر من المتاح
                Console.WriteLine("\n--- سيناريو 3: محاولة خصم أكثر من المتاح ---");
                Console.WriteLine($"المخزون الحالي: {initialStock}");
                Console.WriteLine($"محاولة خصم: {initialStock + 100}");
                Console.WriteLine("النتيجة المتوقعة: رسالة خطأ ومنع العملية");
                
                Console.WriteLine("\n=== انتهت اختبارات السيناريوهات ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار السيناريوهات: {ex.Message}");
            }
        }
        
        /// <summary>
        /// اختبار التحقق من صحة البيانات
        /// </summary>
        public static void TestValidation()
        {
            try
            {
                Console.WriteLine("=== اختبار التحقق من صحة البيانات ===");
                
                Console.WriteLine("اختبارات التحقق المطلوبة:");
                Console.WriteLine("1. ✅ منع إدخال كمية صفر أو سالبة");
                Console.WriteLine("2. ✅ منع خصم كمية أكبر من المخزون الحالي");
                Console.WriteLine("3. ✅ عرض المعاينة المباشرة للمخزون الجديد");
                Console.WriteLine("4. ✅ تلوين المعاينة (أخضر للإضافة، أحمر للخصم السالب)");
                Console.WriteLine("5. ✅ تحديث المعاينة عند تغيير القيم");
                
                Console.WriteLine("\nهذه الاختبارات يتم تنفيذها تلقائياً في النافذة");
                Console.WriteLine("=== انتهى اختبار التحقق من صحة البيانات ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار التحقق: {ex.Message}");
            }
        }
        
        /// <summary>
        /// اختبار قاعدة البيانات
        /// </summary>
        public static void TestDatabaseOperations()
        {
            try
            {
                Console.WriteLine("=== اختبار عمليات قاعدة البيانات ===");
                
                // اختبار الاتصال
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    Console.WriteLine("✅ تم الاتصال بقاعدة البيانات بنجاح");
                    
                    // اختبار وجود الجداول المطلوبة
                    var tables = new[] { "Drugs", "Inventory", "Warehouses" };
                    
                    foreach (var table in tables)
                    {
                        var cmd = connection.CreateCommand();
                        cmd.CommandText = $"SELECT COUNT(*) FROM {table}";
                        try
                        {
                            var count = cmd.ExecuteScalar();
                            Console.WriteLine($"✅ جدول {table}: {count} سجل");
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"❌ خطأ في جدول {table}: {ex.Message}");
                        }
                    }
                }
                
                Console.WriteLine("=== انتهى اختبار قاعدة البيانات ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار قاعدة البيانات: {ex.Message}");
            }
        }
        
        /// <summary>
        /// تشغيل جميع اختبارات النافذة المبسطة
        /// </summary>
        public static void RunAllSimpleStockUpdateTests()
        {
            Console.WriteLine("=== بدء جميع اختبارات النافذة المبسطة ===");
            
            TestDatabaseOperations();
            Console.WriteLine();
            
            TestValidation();
            Console.WriteLine();
            
            TestDifferentScenarios();
            Console.WriteLine();
            
            TestSimpleStockUpdateForm();
            
            Console.WriteLine("\n=== انتهت جميع اختبارات النافذة المبسطة ===");
            
            MessageBox.Show("تم تشغيل جميع اختبارات النافذة المبسطة بنجاح!\nتحقق من وحدة التحكم للتفاصيل.", 
                          "اختبارات النافذة المبسطة", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
}
