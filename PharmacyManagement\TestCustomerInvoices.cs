using System;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement
{
    /// <summary>
    /// فئة اختبار لجلب فواتير العملاء
    /// </summary>
    public static class TestCustomerInvoices
    {
        /// <summary>
        /// اختبار جلب فواتير عميل معين
        /// </summary>
        /// <param name="customerId">معرف العميل</param>
        public static void TestGetCustomerInvoices(int customerId)
        {
            try
            {
                Console.WriteLine($"بدء اختبار جلب فواتير العميل رقم: {customerId}");
                
                // جلب بيانات العميل أولاً
                var customer = CustomerManager.GetCustomer(customerId);
                if (customer == null)
                {
                    Console.WriteLine($"لم يتم العثور على العميل رقم: {customerId}");
                    return;
                }
                
                Console.WriteLine($"تم العثور على العميل: {customer.CustomerName} - {customer.CustomerCode}");
                
                // جلب فواتير العميل
                var invoices = InvoiceManager.GetCustomerInvoices(customerId);
                
                Console.WriteLine($"تم جلب {invoices.Count} فاتورة للعميل");
                
                if (invoices.Count > 0)
                {
                    Console.WriteLine("تفاصيل الفواتير:");
                    foreach (var invoice in invoices)
                    {
                        Console.WriteLine($"- فاتورة رقم: {invoice.InvoiceNumber}");
                        Console.WriteLine($"  التاريخ: {invoice.InvoiceDate:yyyy/MM/dd}");
                        Console.WriteLine($"  المبلغ: {invoice.TotalAmount:F2} ر.ي");
                        Console.WriteLine($"  الحالة: {invoice.Status}");
                        Console.WriteLine($"  طريقة الدفع: {invoice.PaymentMethod}");
                        Console.WriteLine("  ---");
                    }
                }
                else
                {
                    Console.WriteLine("لا توجد فواتير لهذا العميل");
                }
                
                Console.WriteLine("انتهى الاختبار بنجاح");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في الاختبار: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
            }
        }
        
        /// <summary>
        /// اختبار جلب جميع العملاء
        /// </summary>
        public static void TestGetAllCustomers()
        {
            try
            {
                Console.WriteLine("بدء اختبار جلب جميع العملاء");
                
                var customers = CustomerManager.GetAllCustomers();
                
                Console.WriteLine($"تم جلب {customers.Count} عميل");
                
                if (customers.Count > 0)
                {
                    Console.WriteLine("أول 5 عملاء:");
                    for (int i = 0; i < Math.Min(5, customers.Count); i++)
                    {
                        var customer = customers[i];
                        Console.WriteLine($"- {customer.CustomerCode}: {customer.CustomerName}");
                        Console.WriteLine($"  الهاتف: {customer.Phone ?? "غير محدد"}");
                        Console.WriteLine($"  الجوال: {customer.Mobile ?? "غير محدد"}");
                        Console.WriteLine($"  الرصيد: {customer.CurrentBalance:F2} ر.ي");
                        Console.WriteLine("  ---");
                    }
                }
                
                Console.WriteLine("انتهى الاختبار بنجاح");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في الاختبار: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
            }
        }
        
        /// <summary>
        /// اختبار شامل لوظائف العملاء والفواتير
        /// </summary>
        public static void RunAllTests()
        {
            try
            {
                Console.WriteLine("=== بدء الاختبارات الشاملة ===");
                
                // اختبار جلب جميع العملاء
                TestGetAllCustomers();
                
                Console.WriteLine("\n" + new string('=', 50) + "\n");
                
                // اختبار جلب فواتير أول عميل
                var customers = CustomerManager.GetAllCustomers();
                if (customers.Count > 0)
                {
                    TestGetCustomerInvoices(customers[0].CustomerID);
                }
                else
                {
                    Console.WriteLine("لا توجد عملاء في النظام لاختبار الفواتير");
                }
                
                Console.WriteLine("\n=== انتهت جميع الاختبارات ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تشغيل الاختبارات: {ex.Message}");
            }
        }
    }
}
