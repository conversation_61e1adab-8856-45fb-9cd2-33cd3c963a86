using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using InventoryItem = PharmacyManagement.Models.InventoryItem;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نافذة تقرير المخزون - Inventory Report Form
    /// </summary>
    public partial class InventoryReportForm : Form
    {
        #region Constructor - المنشئ

        /// <summary>
        /// منشئ نافذة تقرير المخزون
        /// </summary>
        public InventoryReportForm()
        {
            InitializeComponent();
            SetupForm();
            LoadReport();
        }

        #endregion

        #region Form Setup - إعداد النافذة

        /// <summary>
        /// إعداد النافذة
        /// </summary>
        private void SetupForm()
        {
            this.Text = "تقرير المخزون";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MinimumSize = new Size(800, 600);

            SetupDataGridView();
            ApplyFlatDesign();
        }

        /// <summary>
        /// إعداد DataGridView
        /// </summary>
        private void SetupDataGridView()
        {
            dgvReport.AutoGenerateColumns = false;
            dgvReport.AllowUserToAddRows = false;
            dgvReport.AllowUserToDeleteRows = false;
            dgvReport.ReadOnly = true;
            dgvReport.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvReport.MultiSelect = false;
        }

        /// <summary>
        /// تطبيق التصميم المسطح
        /// </summary>
        private void ApplyFlatDesign()
        {
            this.BackColor = Color.White;
            
            foreach (Control control in this.Controls)
            {
                if (control is Button button)
                {
                    button.FlatStyle = FlatStyle.Flat;
                    button.FlatAppearance.BorderSize = 0;
                }
            }
        }

        #endregion

        #region Data Loading - تحميل البيانات

        /// <summary>
        /// تحميل التقرير
        /// </summary>
        private void LoadReport()
        {
            try
            {
                var inventory = InventoryManager.GetAllInventoryItems();
                dgvReport.DataSource = inventory;

                // تحديث الإحصائيات
                UpdateStatistics(inventory);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل التقرير: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحديث الإحصائيات
        /// </summary>
        /// <param name="inventory">قائمة المخزون</param>
        private void UpdateStatistics(System.Collections.Generic.List<InventoryItem> inventory)
        {
            if (inventory != null && inventory.Count > 0)
            {
                var totalItems = inventory.Count;
                var totalValue = inventory.Sum(i => i.StockValue);
                var lowStockItems = inventory.Count(i => i.CurrentStock <= i.MinimumStock);
                var outOfStockItems = inventory.Count(i => i.CurrentStock <= 0);
                var expiredItems = inventory.Count(i => i.ExpiryDate.HasValue && i.ExpiryDate.Value < DateTime.Now);

                lblTotalItems.Text = $"إجمالي الأصناف: {totalItems}";
                lblTotalValue.Text = $"إجمالي قيمة المخزون: {totalValue:F2} ر.ي";
                lblLowStock.Text = $"أصناف منخفضة المخزون: {lowStockItems}";
                lblOutOfStock.Text = $"أصناف نفد مخزونها: {outOfStockItems}";
                lblExpiredItems.Text = $"أصناف منتهية الصلاحية: {expiredItems}";
            }
            else
            {
                lblTotalItems.Text = "إجمالي الأصناف: 0";
                lblTotalValue.Text = "إجمالي قيمة المخزون: 0.00 ر.ي";
                lblLowStock.Text = "أصناف منخفضة المخزون: 0";
                lblOutOfStock.Text = "أصناف نفد مخزونها: 0";
                lblExpiredItems.Text = "أصناف منتهية الصلاحية: 0";
            }
        }

        #endregion

        #region Event Handlers - معالجات الأحداث

        /// <summary>
        /// تصدير التقرير
        /// </summary>
        private void btnExport_Click(object sender, EventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "Excel Files|*.xlsx|PDF Files|*.pdf|CSV Files|*.csv",
                    Title = "تصدير تقرير المخزون",
                    FileName = $"Inventory_Report_{DateTime.Now:yyyyMMdd_HHmmss}"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    var inventory = dgvReport.DataSource as System.Collections.Generic.List<InventoryItem>;
                    if (inventory != null)
                    {
                        ExportManager.ExportInventory(inventory, saveDialog.FileName);
                        MessageBox.Show("تم تصدير التقرير بنجاح", "نجح", 
                                      MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير التقرير: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// طباعة التقرير
        /// </summary>
        private void btnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                // يمكن إضافة وظيفة الطباعة هنا
                MessageBox.Show("وظيفة الطباعة ستكون متاحة قريباً", "معلومات", 
                              MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحديث التقرير
        /// </summary>
        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadReport();
        }

        /// <summary>
        /// إغلاق النافذة
        /// </summary>
        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// فلترة البيانات
        /// </summary>
        private void cmbFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                var allInventory = InventoryManager.GetAllInventoryItems();
                System.Collections.Generic.List<InventoryItem> filteredInventory;

                switch (cmbFilter.SelectedIndex)
                {
                    case 1: // مخزون منخفض
                        filteredInventory = allInventory.Where(i => i.CurrentStock <= i.MinimumStock).ToList();
                        break;
                    case 2: // نفد المخزون
                        filteredInventory = allInventory.Where(i => i.CurrentStock <= 0).ToList();
                        break;
                    case 3: // منتهي الصلاحية
                        filteredInventory = allInventory.Where(i => i.ExpiryDate.HasValue && i.ExpiryDate.Value < DateTime.Now).ToList();
                        break;
                    case 4: // قريب الانتهاء
                        filteredInventory = allInventory.Where(i => i.ExpiryDate.HasValue && 
                                                                   i.ExpiryDate.Value >= DateTime.Now && 
                                                                   i.ExpiryDate.Value <= DateTime.Now.AddDays(30)).ToList();
                        break;
                    default: // الكل
                        filteredInventory = allInventory;
                        break;
                }

                dgvReport.DataSource = filteredInventory;
                UpdateStatistics(filteredInventory);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فلترة البيانات: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion
    }
}
