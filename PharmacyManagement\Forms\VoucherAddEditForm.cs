using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نموذج إضافة وتعديل السندات المحاسبية
    /// </summary>
    public partial class VoucherAddEditForm : Form
    {
        #region Fields

        private Voucher _voucher;
        private bool _isEditMode;
        private List<VoucherDetail> _voucherDetails;

        #endregion

        #region Properties

        public Voucher Voucher
        {
            get { return _voucher; }
            set
            {
                _voucher = value;
                _isEditMode = value != null && value.VoucherID > 0;
                LoadVoucherData();
            }
        }

        #endregion

        #region Constructor

        public VoucherAddEditForm()
        {
            InitializeComponent();
            //InitializeForm();
        }

        public VoucherAddEditForm(Voucher voucher) : this()
        {
            Voucher = voucher;
        }

        #endregion

        //#region Initialization

    
        //private void InitializeForm()
        //{
        //    _voucherDetails = new List<VoucherDetail>();
            
        //    if (!_isEditMode)
        //    {
        //        _voucher = new Voucher
        //        {
        //            VoucherDate = DateTime.Now,
        //            VoucherType = "General",
        //            Status = "Draft",
        //            CreatedBy = UserManager.CurrentUser?.UserID,
        //            CreatedDate = DateTime.Now
        //        };
        //    }
        //}

        //#endregion

        #region Data Loading

        private void LoadVoucherData()
        {
            if (_voucher == null) return;

            try
            {
                if (_isEditMode)
                {
                    // تحميل تفاصيل السند
                    _voucherDetails = VoucherManager.GetVoucherDetails(_voucher.VoucherID);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات السند: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Event Handlers

        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (ValidateData())
                {
                    if (_isEditMode)
                    {
                        if (VoucherManager.UpdateVoucher(_voucher))
                        {
                            MessageBox.Show("تم تحديث السند بنجاح", "نجح",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                            this.DialogResult = DialogResult.OK;
                            this.Close();
                        }
                    }
                    else
                    {
                        int voucherId = VoucherManager.AddVoucher(_voucher);
                        if (voucherId > 0)
                        {
                            _voucher.VoucherID = voucherId;
                            MessageBox.Show("تم إضافة السند بنجاح", "نجح",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                            this.DialogResult = DialogResult.OK;
                            this.Close();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ السند: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        #endregion

        #region Validation

        private bool ValidateData()
        {
            if (_voucher == null)
            {
                MessageBox.Show("بيانات السند غير صحيحة", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (string.IsNullOrWhiteSpace(_voucher.VoucherNumber))
            {
                MessageBox.Show("يجب إدخال رقم السند", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (string.IsNullOrWhiteSpace(_voucher.Description))
            {
                MessageBox.Show("يجب إدخال وصف السند", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        #endregion

        #region Helper Methods

        private void ClearForm()
        {
            _voucher = new Voucher
            {
                VoucherDate = DateTime.Now,
                VoucherType = "General",
                Status = "Draft",
                CreatedBy = UserManager.CurrentUser?.UserID ?? 0,
                CreatedDate = DateTime.Now
            };
            
            _voucherDetails.Clear();
        }

        #endregion
    }
}







