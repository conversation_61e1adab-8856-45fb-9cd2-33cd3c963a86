using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نموذج إدارة الجرد المحسن - Enhanced Inventory Count Form
    /// تصميم مسطح حديث مع ميزات متقدمة لإدارة الجرد
    /// </summary>
    public partial class EnhancedInventoryCountForm : Form
    {
        #region Fields

        private BindingSource _detailsBindingSource;
        private List<InventoryCountDetail> _countDetails;
        private bool _isNewCount;
        private bool _hasChanges;
        private int _nextDetailId = 1;

        #endregion

        #region Constructor

        public EnhancedInventoryCountForm()
        {
            InitializeComponent();
            SetupForm();
            SetupDataGrid();
            SetupEvents();
            LoadSampleData();
        }

        #endregion

        #region Setup Methods

        private void SetupForm()
        {
            // إعداد النموذج الأساسي
            this.Text = "📋 إدارة الجرد المحسن";
            this.WindowState = FormWindowState.Maximized;
            this.BackColor = Color.White;
            this.Font = new Font("Segoe UI", 9F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // إعداد التصميم المسطح
            SetupFlatDesign();
            
            // إعداد أنواع الجرد
            SetupCountTypes();
            
            // إعداد البيانات الأولية
            InitializeNewCount();
        }

        private void SetupFlatDesign()
        {
            // تطبيق التصميم المسطح على جميع العناصر
            ApplyFlatDesignToControls(this);
            
            // إعداد ألوان خاصة للأزرار
            SetupButtonColors();
            
            // إعداد ألوان الشبكة
            SetupDataGridColors();
        }

        private void ApplyFlatDesignToControls(Control parent)
        {
            foreach (Control control in parent.Controls)
            {
                if (control is Button btn)
                {
                    btn.FlatStyle = FlatStyle.Flat;
                    btn.FlatAppearance.BorderSize = 0;
                    btn.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
                    btn.Cursor = Cursors.Hand;
                }
                else if (control is TextBox txt)
                {
                    txt.BorderStyle = BorderStyle.FixedSingle;
                    txt.Font = new Font("Segoe UI", 10F);
                }
                else if (control is ComboBox cmb)
                {
                    cmb.FlatStyle = FlatStyle.Flat;
                    cmb.Font = new Font("Segoe UI", 10F);
                }
                else if (control is DateTimePicker dtp)
                {
                    dtp.Font = new Font("Segoe UI", 10F);
                }
                
                // تطبيق التصميم على العناصر الفرعية
                if (control.HasChildren)
                {
                    ApplyFlatDesignToControls(control);
                }
            }
        }

        private void SetupButtonColors()
        {
            // ألوان الأزرار حسب الوظيفة
            btnAddDrug.BackColor = Color.FromArgb(46, 204, 113);
            btnAddDrug.ForeColor = Color.White;
            
            btnRemoveDrug.BackColor = Color.FromArgb(231, 76, 60);
            btnRemoveDrug.ForeColor = Color.White;
            
            btnLoadAllDrugs.BackColor = Color.FromArgb(52, 152, 219);
            btnLoadAllDrugs.ForeColor = Color.White;
            
            btnScanBarcode.BackColor = Color.FromArgb(155, 89, 182);
            btnScanBarcode.ForeColor = Color.White;
            
            btnSave.BackColor = Color.FromArgb(46, 204, 113);
            btnSave.ForeColor = Color.White;
            
            btnComplete.BackColor = Color.FromArgb(52, 152, 219);
            btnComplete.ForeColor = Color.White;
            
            btnPrint.BackColor = Color.FromArgb(155, 89, 182);
            btnPrint.ForeColor = Color.White;
            
            btnCancel.BackColor = Color.FromArgb(149, 165, 166);
            btnCancel.ForeColor = Color.White;
        }

        private void SetupDataGridColors()
        {
            dgvDetails.BackgroundColor = Color.White;
            dgvDetails.BorderStyle = BorderStyle.FixedSingle;
            dgvDetails.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 73, 94);
            dgvDetails.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvDetails.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            dgvDetails.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgvDetails.DefaultCellStyle.Font = new Font("Segoe UI", 10F);
            dgvDetails.DefaultCellStyle.SelectionBackColor = Color.FromArgb(52, 152, 219);
            dgvDetails.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvDetails.RowHeadersVisible = false;
            dgvDetails.EnableHeadersVisualStyles = false;
            dgvDetails.GridColor = Color.FromArgb(189, 195, 199);
            dgvDetails.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);
            dgvDetails.ColumnHeadersHeight = 40;
            dgvDetails.RowTemplate.Height = 35;
        }

        private void SetupCountTypes()
        {
            // إعداد أنواع الجرد
            cmbCountType.Items.Clear();
            cmbCountType.Items.AddRange(new[] { 
                "FullInventory", 
                "CycleCount", 
                "SpotCheck", 
                "AdjustmentCount" 
            });
            cmbCountType.SelectedIndex = 0;

            // إعداد حالات الجرد
            cmbStatus.Items.Clear();
            cmbStatus.Items.AddRange(new[] { 
                "InProgress", 
                "Completed", 
                "Cancelled" 
            });
            cmbStatus.SelectedIndex = 0;
        }

        private void SetupDataGrid()
        {
            // إعداد شبكة التفاصيل
            dgvDetails.AutoGenerateColumns = false;
            dgvDetails.AllowUserToAddRows = false;
            dgvDetails.AllowUserToDeleteRows = false;
            dgvDetails.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvDetails.MultiSelect = true;
            dgvDetails.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;

            // إضافة الأعمدة المحسنة
            AddDataGridColumns();

            // إعداد مصدر البيانات
            _detailsBindingSource = new BindingSource();
            dgvDetails.DataSource = _detailsBindingSource;
        }

        private void AddDataGridColumns()
        {
            // عمود كود الدواء
            var drugCodeColumn = new DataGridViewTextBoxColumn
            {
                Name = "DrugCode",
                HeaderText = "كود الدواء",
                DataPropertyName = "DrugCode",
                FillWeight = 12,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Font = new Font("Segoe UI", 10F, FontStyle.Bold)
                }
            };
            dgvDetails.Columns.Add(drugCodeColumn);

            // عمود اسم الدواء
            var drugNameColumn = new DataGridViewTextBoxColumn
            {
                Name = "DrugName",
                HeaderText = "اسم الدواء",
                DataPropertyName = "DrugName",
                FillWeight = 25,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Alignment = DataGridViewContentAlignment.MiddleRight,
                    Font = new Font("Segoe UI", 10F, FontStyle.Bold)
                }
            };
            dgvDetails.Columns.Add(drugNameColumn);

            // عمود رقم الدفعة
            var batchColumn = new DataGridViewTextBoxColumn
            {
                Name = "BatchNumber",
                HeaderText = "رقم الدفعة",
                DataPropertyName = "BatchNumber",
                FillWeight = 12,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Alignment = DataGridViewContentAlignment.MiddleCenter
                }
            };
            dgvDetails.Columns.Add(batchColumn);

            // عمود الكمية في النظام
            var systemQtyColumn = new DataGridViewTextBoxColumn
            {
                Name = "SystemQuantity",
                HeaderText = "الكمية في النظام",
                DataPropertyName = "SystemQuantity",
                FillWeight = 15,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    BackColor = Color.FromArgb(240, 248, 255)
                }
            };
            dgvDetails.Columns.Add(systemQtyColumn);

            // عمود الكمية الفعلية
            var physicalQtyColumn = new DataGridViewTextBoxColumn
            {
                Name = "PhysicalQuantity",
                HeaderText = "الكمية الفعلية",
                DataPropertyName = "PhysicalQuantity",
                FillWeight = 15,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    BackColor = Color.FromArgb(255, 248, 220)
                }
            };
            dgvDetails.Columns.Add(physicalQtyColumn);

            // عمود الفرق
            var varianceColumn = new DataGridViewTextBoxColumn
            {
                Name = "VarianceQuantity",
                HeaderText = "الفرق",
                DataPropertyName = "VarianceQuantity",
                FillWeight = 10,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Font = new Font("Segoe UI", 10F, FontStyle.Bold)
                }
            };
            dgvDetails.Columns.Add(varianceColumn);

            // عمود سعر الوحدة
            var unitCostColumn = new DataGridViewTextBoxColumn
            {
                Name = "UnitCost",
                HeaderText = "سعر الوحدة",
                DataPropertyName = "UnitCost",
                FillWeight = 12,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Alignment = DataGridViewContentAlignment.MiddleRight,
                    Format = "F2"
                }
            };
            dgvDetails.Columns.Add(unitCostColumn);

            // عمود قيمة الفرق
            var varianceValueColumn = new DataGridViewTextBoxColumn
            {
                Name = "VarianceValue",
                HeaderText = "قيمة الفرق",
                DataPropertyName = "VarianceValue",
                FillWeight = 12,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Alignment = DataGridViewContentAlignment.MiddleRight,
                    Format = "F2",
                    Font = new Font("Segoe UI", 10F, FontStyle.Bold)
                }
            };
            dgvDetails.Columns.Add(varianceValueColumn);

            // عمود الملاحظات
            var notesColumn = new DataGridViewTextBoxColumn
            {
                Name = "Notes",
                HeaderText = "ملاحظات",
                DataPropertyName = "Notes",
                FillWeight = 20,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Alignment = DataGridViewContentAlignment.MiddleRight
                }
            };
            dgvDetails.Columns.Add(notesColumn);
        }

        private void SetupEvents()
        {
            // أحداث الأزرار
            btnAddDrug.Click += BtnAddDrug_Click;
            btnRemoveDrug.Click += BtnRemoveDrug_Click;
            btnLoadAllDrugs.Click += BtnLoadAllDrugs_Click;
            btnScanBarcode.Click += BtnScanBarcode_Click;
            btnSave.Click += BtnSave_Click;
            btnComplete.Click += BtnComplete_Click;
            btnPrint.Click += BtnPrint_Click;
            btnCancel.Click += BtnCancel_Click;
            
            // أحداث الشبكة
            dgvDetails.CellValueChanged += DgvDetails_CellValueChanged;
            dgvDetails.CellFormatting += DgvDetails_CellFormatting;
            dgvDetails.SelectionChanged += DgvDetails_SelectionChanged;
            
            // أحداث النموذج
            this.Load += EnhancedInventoryCountForm_Load;
            this.FormClosing += EnhancedInventoryCountForm_FormClosing;
        }

        private void InitializeNewCount()
        {
            _isNewCount = true;
            _hasChanges = false;
            _countDetails = new List<InventoryCountDetail>();
            
            // إعداد البيانات الأولية
            txtCountNumber.Text = GenerateCountNumber();
            dtpCountDate.Value = DateTime.Now;
            cmbCountType.SelectedIndex = 0;
            cmbStatus.SelectedIndex = 0;
            txtDescription.Text = "";
            
            UpdateSummary();
        }

        #endregion

        #region Data Methods

        private void LoadSampleData()
        {
            try
            {
                // إنشاء بيانات تجريبية للجرد
                _countDetails = new List<InventoryCountDetail>
                {
                    new InventoryCountDetail
                    {
                        CountDetailID = _nextDetailId++,
                        DrugCode = "MED001",
                        DrugName = "باراسيتامول 500 مجم",
                        BatchNumber = "B2024001",
                        SystemQuantity = 100,
                        PhysicalQuantity = 95,
                        UnitCost = 2.50m,
                        Notes = "نقص 5 علب"
                    },
                    new InventoryCountDetail
                    {
                        CountDetailID = _nextDetailId++,
                        DrugCode = "MED002",
                        DrugName = "أموكسيسيلين 250 مجم",
                        BatchNumber = "B2024002",
                        SystemQuantity = 50,
                        PhysicalQuantity = 52,
                        UnitCost = 8.75m,
                        Notes = "زيادة علبتين"
                    },
                    new InventoryCountDetail
                    {
                        CountDetailID = _nextDetailId++,
                        DrugCode = "MED003",
                        DrugName = "إيبوبروفين 400 مجم",
                        BatchNumber = "B2024003",
                        SystemQuantity = 75,
                        PhysicalQuantity = 75,
                        UnitCost = 3.25m,
                        Notes = "مطابق للنظام"
                    },
                    new InventoryCountDetail
                    {
                        CountDetailID = _nextDetailId++,
                        DrugCode = "MED004",
                        DrugName = "أسبرين 100 مجم",
                        BatchNumber = "B2024004",
                        SystemQuantity = 200,
                        PhysicalQuantity = 190,
                        UnitCost = 1.50m,
                        Notes = "نقص 10 علب"
                    },
                    new InventoryCountDetail
                    {
                        CountDetailID = _nextDetailId++,
                        DrugCode = "MED005",
                        DrugName = "فيتامين د 1000 وحدة",
                        BatchNumber = "B2024005",
                        SystemQuantity = 30,
                        PhysicalQuantity = 35,
                        UnitCost = 12.00m,
                        Notes = "زيادة 5 علب"
                    },
                    new InventoryCountDetail
                    {
                        CountDetailID = _nextDetailId++,
                        DrugCode = "MED006",
                        DrugName = "أوميجا 3 كبسولات",
                        BatchNumber = "B2024006",
                        SystemQuantity = 40,
                        PhysicalQuantity = 38,
                        UnitCost = 15.50m,
                        Notes = "نقص علبتين"
                    },
                    new InventoryCountDetail
                    {
                        CountDetailID = _nextDetailId++,
                        DrugCode = "MED007",
                        DrugName = "كالسيوم + مغنيسيوم",
                        BatchNumber = "B2024007",
                        SystemQuantity = 60,
                        PhysicalQuantity = 60,
                        UnitCost = 9.25m,
                        Notes = "مطابق للنظام"
                    },
                    new InventoryCountDetail
                    {
                        CountDetailID = _nextDetailId++,
                        DrugCode = "MED008",
                        DrugName = "لوراتادين 10 مجم",
                        BatchNumber = "B2024008",
                        SystemQuantity = 25,
                        PhysicalQuantity = 20,
                        UnitCost = 4.75m,
                        Notes = "نقص 5 علب"
                    }
                };

                // حساب الفروقات
                foreach (var detail in _countDetails)
                {
                    detail.VarianceQuantity = detail.PhysicalQuantity - detail.SystemQuantity;
                    detail.VarianceValue = detail.VarianceQuantity * detail.UnitCost;
                }

                // ربط البيانات
                _detailsBindingSource.DataSource = _countDetails;

                // تحديث الملخص
                UpdateSummary();

                Console.WriteLine($"تم تحميل {_countDetails.Count} عنصر جرد تجريبي");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات التجريبية: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private string GenerateCountNumber()
        {
            return $"CNT{DateTime.Now:yyyyMM}{DateTime.Now.Millisecond:D3}";
        }

        private void UpdateSummary()
        {
            try
            {
                if (_countDetails == null || _countDetails.Count == 0)
                {
                    lblTotalItems.Text = "إجمالي الأصناف: 0";
                    lblItemsWithVariance.Text = "أصناف بها فروقات: 0";
                    lblTotalVarianceValue.Text = "إجمالي قيمة الفروقات: 0.00";
                    return;
                }

                var totalItems = _countDetails.Count;
                var itemsWithVariance = _countDetails.Count(d => d.VarianceQuantity != 0);
                var totalVarianceValue = _countDetails.Sum(d => Math.Abs(d.VarianceValue));

                lblTotalItems.Text = $"إجمالي الأصناف: {totalItems}";
                lblItemsWithVariance.Text = $"أصناف بها فروقات: {itemsWithVariance}";
                lblTotalVarianceValue.Text = $"إجمالي قيمة الفروقات: {totalVarianceValue:F2}";

                // تحديث لون قيمة الفروقات
                if (totalVarianceValue > 0)
                {
                    lblTotalVarianceValue.ForeColor = Color.FromArgb(231, 76, 60);
                }
                else
                {
                    lblTotalVarianceValue.ForeColor = Color.FromArgb(46, 204, 113);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تحديث الملخص: {ex.Message}");
            }
        }

        #endregion

        #region Event Handlers

        private void EnhancedInventoryCountForm_Load(object sender, EventArgs e)
        {
            // تحديث إضافي عند تحميل النموذج
            UpdateSummary();
        }

        private void EnhancedInventoryCountForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (_hasChanges)
            {
                var result = MessageBox.Show("هناك تغييرات غير محفوظة. هل تريد حفظها قبل الإغلاق؟",
                    "تأكيد الإغلاق", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    SaveCount();
                }
                else if (result == DialogResult.Cancel)
                {
                    e.Cancel = true;
                }
            }
        }

        private void BtnAddDrug_Click(object sender, EventArgs e)
        {
            try
            {
                // محاكاة إضافة دواء جديد
                var newDetail = new InventoryCountDetail
                {
                    CountDetailID = _nextDetailId++,
                    DrugCode = $"MED{_nextDetailId:D3}",
                    DrugName = "دواء جديد",
                    BatchNumber = $"B2024{_nextDetailId:D3}",
                    SystemQuantity = 0,
                    PhysicalQuantity = 0,
                    UnitCost = 5.00m,
                    Notes = "تم إضافته يدوياً"
                };

                _countDetails.Add(newDetail);
                _detailsBindingSource.ResetBindings(false);
                _hasChanges = true;

                UpdateSummary();

                MessageBox.Show("تم إضافة دواء جديد بنجاح", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة الدواء: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnRemoveDrug_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvDetails.SelectedRows.Count > 0)
                {
                    var selectedDetails = dgvDetails.SelectedRows.Cast<DataGridViewRow>()
                        .Where(row => row.DataBoundItem is InventoryCountDetail)
                        .Select(row => row.DataBoundItem as InventoryCountDetail)
                        .ToList();

                    if (selectedDetails.Count() == 0)
                    {
                        MessageBox.Show("يرجى تحديد عنصر واحد على الأقل للحذف", "تنبيه",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    var result = MessageBox.Show($"هل تريد حذف {selectedDetails.Count()} عنصر محدد؟",
                        "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        foreach (var detail in selectedDetails)
                        {
                            _countDetails.Remove(detail);
                        }

                        _detailsBindingSource.ResetBindings(false);
                        _hasChanges = true;

                        UpdateSummary();

                        MessageBox.Show($"تم حذف {selectedDetails.Count()} عنصر بنجاح", "نجح",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
                else
                {
                    MessageBox.Show("يرجى تحديد عنصر واحد على الأقل للحذف", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف العنصر: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnLoadAllDrugs_Click(object sender, EventArgs e)
        {
            try
            {
                var result = MessageBox.Show("هل تريد تحميل جميع الأدوية من النظام؟\nسيتم استبدال البيانات الحالية.",
                    "تأكيد التحميل", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    LoadSampleData();
                    MessageBox.Show("تم تحميل جميع الأدوية بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الأدوية: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnScanBarcode_Click(object sender, EventArgs e)
        {
            try
            {
                // محاكاة مسح الباركود
                var barcodeResult = ShowInputDialog("أدخل الباركود أو كود الدواء:", "مسح الباركود", "MED009");

                if (!string.IsNullOrWhiteSpace(barcodeResult))
                {
                    // البحث عن الدواء في القائمة
                    var existingDetail = _countDetails.FirstOrDefault(d => d.DrugCode == barcodeResult);

                    if (existingDetail != null)
                    {
                        // زيادة الكمية الفعلية
                        existingDetail.PhysicalQuantity++;
                        existingDetail.VarianceQuantity = existingDetail.PhysicalQuantity - existingDetail.SystemQuantity;
                        existingDetail.VarianceValue = existingDetail.VarianceQuantity * existingDetail.UnitCost;

                        _detailsBindingSource.ResetBindings(false);
                        _hasChanges = true;
                        UpdateSummary();

                        MessageBox.Show($"تم تحديث الكمية للدواء: {existingDetail.DrugName}", "نجح",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        MessageBox.Show($"لم يتم العثور على دواء بالكود: {barcodeResult}", "تنبيه",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في مسح الباركود: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            SaveCount();
        }

        private void BtnComplete_Click(object sender, EventArgs e)
        {
            try
            {
                if (_countDetails.Count == 0)
                {
                    MessageBox.Show("لا يمكن إنهاء جرد فارغ", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var result = MessageBox.Show("هل تريد إنهاء عملية الجرد؟\nلن يمكن تعديلها بعد الإنهاء.",
                    "تأكيد الإنهاء", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    cmbStatus.SelectedItem = "Completed";
                    _hasChanges = true;
                    SaveCount();

                    MessageBox.Show("تم إنهاء عملية الجرد بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // تعطيل التحرير
                    SetFormReadOnly(true);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنهاء الجرد: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("ميزة الطباعة ستكون متاحة قريباً", "قيد التطوير",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        #endregion

        #region Grid Event Handlers

        private void DgvDetails_CellValueChanged(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                if (e.RowIndex >= 0 && e.ColumnIndex >= 0)
                {
                    var detail = dgvDetails.Rows[e.RowIndex].DataBoundItem as InventoryCountDetail;
                    if (detail != null)
                    {
                        // إعادة حساب الفروقات عند تغيير الكمية الفعلية
                        if (dgvDetails.Columns[e.ColumnIndex].Name == "PhysicalQuantity")
                        {
                            detail.VarianceQuantity = detail.PhysicalQuantity - detail.SystemQuantity;
                            detail.VarianceValue = detail.VarianceQuantity * detail.UnitCost;

                            _detailsBindingSource.ResetBindings(false);
                            _hasChanges = true;
                            UpdateSummary();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تحديث الخلية: {ex.Message}");
            }
        }

        private void DgvDetails_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            try
            {
                if (e.RowIndex >= 0 && dgvDetails.Rows[e.RowIndex].DataBoundItem is InventoryCountDetail detail)
                {
                    var row = dgvDetails.Rows[e.RowIndex];

                    // تلوين الصفوف حسب الفروقات
                    if (detail.VarianceQuantity > 0)
                    {
                        // زيادة - لون أخضر فاتح
                        row.DefaultCellStyle.BackColor = Color.FromArgb(235, 255, 235);
                    }
                    else if (detail.VarianceQuantity < 0)
                    {
                        // نقص - لون أحمر فاتح
                        row.DefaultCellStyle.BackColor = Color.FromArgb(255, 235, 235);
                    }
                    else
                    {
                        // مطابق - لون أبيض
                        row.DefaultCellStyle.BackColor = Color.White;
                    }

                    // تنسيق خاص للأعمدة
                    if (e.ColumnIndex == dgvDetails.Columns["VarianceQuantity"]?.Index)
                    {
                        if (detail.VarianceQuantity > 0)
                        {
                            e.CellStyle.ForeColor = Color.Green;
                            e.CellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
                            e.Value = $"+{detail.VarianceQuantity}";
                        }
                        else if (detail.VarianceQuantity < 0)
                        {
                            e.CellStyle.ForeColor = Color.Red;
                            e.CellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
                            e.Value = detail.VarianceQuantity.ToString();
                        }
                        else
                        {
                            e.CellStyle.ForeColor = Color.Gray;
                            e.Value = "0";
                        }
                    }
                    else if (e.ColumnIndex == dgvDetails.Columns["VarianceValue"]?.Index)
                    {
                        if (detail.VarianceValue > 0)
                        {
                            e.CellStyle.ForeColor = Color.Green;
                            e.CellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
                        }
                        else if (detail.VarianceValue < 0)
                        {
                            e.CellStyle.ForeColor = Color.Red;
                            e.CellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
                        }
                        else
                        {
                            e.CellStyle.ForeColor = Color.Gray;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تنسيق الخلية: {ex.Message}");
            }
        }

        private void DgvDetails_SelectionChanged(object sender, EventArgs e)
        {
            // تحديث حالة الأزرار
            btnRemoveDrug.Enabled = dgvDetails.SelectedRows.Count > 0;
        }

        #endregion

        #region Helper Methods

        private void SaveCount()
        {
            try
            {
                // محاكاة حفظ البيانات
                _hasChanges = false;

                MessageBox.Show("تم حفظ بيانات الجرد بنجاح", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SetFormReadOnly(bool readOnly)
        {
            // تعطيل/تفعيل عناصر التحكم
            txtCountNumber.ReadOnly = readOnly;
            dtpCountDate.Enabled = !readOnly;
            cmbCountType.Enabled = !readOnly;
            cmbStatus.Enabled = !readOnly;
            txtDescription.ReadOnly = readOnly;

            btnAddDrug.Enabled = !readOnly;
            btnRemoveDrug.Enabled = !readOnly;
            btnLoadAllDrugs.Enabled = !readOnly;
            btnScanBarcode.Enabled = !readOnly;
            btnSave.Enabled = !readOnly;
            btnComplete.Enabled = !readOnly;

            dgvDetails.ReadOnly = readOnly;
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// عرض مربع حوار لإدخال النص
        /// </summary>
        private string ShowInputDialog(string text, string caption, string defaultValue = "")
        {
            Form prompt = new Form()
            {
                Width = 400,
                Height = 150,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                Text = caption,
                StartPosition = FormStartPosition.CenterScreen,
                MaximizeBox = false,
                MinimizeBox = false,
                RightToLeft = RightToLeft.Yes,
                RightToLeftLayout = true
            };

            Label textLabel = new Label() { Left = 20, Top = 20, Width = 350, Text = text };
            TextBox textBox = new TextBox() { Left = 20, Top = 50, Width = 250, Text = defaultValue };
            Button confirmation = new Button() { Text = "موافق", Left = 280, Width = 80, Top = 48, DialogResult = DialogResult.OK };
            Button cancel = new Button() { Text = "إلغاء", Left = 190, Width = 80, Top = 48, DialogResult = DialogResult.Cancel };

            confirmation.Click += (sender, e) => { prompt.Close(); };
            cancel.Click += (sender, e) => { prompt.Close(); };

            prompt.Controls.Add(textBox);
            prompt.Controls.Add(confirmation);
            prompt.Controls.Add(cancel);
            prompt.Controls.Add(textLabel);
            prompt.AcceptButton = confirmation;
            prompt.CancelButton = cancel;

            return prompt.ShowDialog() == DialogResult.OK ? textBox.Text : "";
        }

        #endregion
    }

    #region Helper Models

    /// <summary>
    /// تفصيل الجرد
    /// </summary>
    public class InventoryCountDetail
    {
        public int CountDetailID { get; set; }
        public string DrugCode { get; set; }
        public string DrugName { get; set; }
        public string BatchNumber { get; set; }
        public int SystemQuantity { get; set; }
        public int PhysicalQuantity { get; set; }
        public int VarianceQuantity { get; set; }
        public decimal UnitCost { get; set; }
        public decimal VarianceValue { get; set; }
        public string Notes { get; set; }
    }

        #endregion

    
    
}
