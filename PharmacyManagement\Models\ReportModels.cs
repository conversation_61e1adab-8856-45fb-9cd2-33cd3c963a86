using System;
using System.Collections.Generic;

namespace PharmacyManagement.Models
{




    /// <summary>
    /// نموذج تقرير الأرباح والخسائر
    /// </summary>
    public class ProfitLossReport
    {
        public ProfitLossReport()
        {
            Revenue = new RevenueSection();
            CostOfGoodsSold = new CostOfGoodsSoldSection();
            OperatingExpenses = new OperatingExpensesSection();
        }

        /// <summary>
        /// تاريخ بداية التقرير
        /// </summary>
        public DateTime FromDate { get; set; }

        /// <summary>
        /// تاريخ نهاية التقرير
        /// </summary>
        public DateTime ToDate { get; set; }

        /// <summary>
        /// الإيرادات
        /// </summary>
        public RevenueSection Revenue { get; set; }

        /// <summary>
        /// تكلفة البضاعة المباعة
        /// </summary>
        public CostOfGoodsSoldSection CostOfGoodsSold { get; set; }

        /// <summary>
        /// المصروفات التشغيلية
        /// </summary>
        public OperatingExpensesSection OperatingExpenses { get; set; }

        /// <summary>
        /// الأرباح الإجمالية
        /// </summary>
        public decimal GrossProfit
        {
            get { return Revenue.TotalRevenue - CostOfGoodsSold.TotalCost; }
        }

        /// <summary>
        /// الأرباح الصافية
        /// </summary>
        public decimal NetProfit
        {
            get { return GrossProfit - OperatingExpenses.TotalExpenses; }
        }

        /// <summary>
        /// هامش الربح الإجمالي
        /// </summary>
        public decimal GrossProfitMargin
        {
            get
            {
                return Revenue.TotalRevenue > 0 ? (GrossProfit / Revenue.TotalRevenue) * 100 : 0;
            }
        }

        /// <summary>
        /// هامش الربح الصافي
        /// </summary>
        public decimal NetProfitMargin
        {
            get
            {
                return Revenue.TotalRevenue > 0 ? (NetProfit / Revenue.TotalRevenue) * 100 : 0;
            }
        }

        /// <summary>
        /// تاريخ إنشاء التقرير
        /// </summary>
        public DateTime GeneratedDate { get; set; }

        /// <summary>
        /// منشئ التقرير
        /// </summary>
        public string GeneratedBy { get; set; }
    }

    /// <summary>
    /// قسم الإيرادات
    /// </summary>
    public class RevenueSection
    {
        /// <summary>
        /// مبيعات الأدوية
        /// </summary>
        public decimal DrugSales { get; set; }

        /// <summary>
        /// إيرادات أخرى
        /// </summary>
        public decimal OtherRevenue { get; set; }

        /// <summary>
        /// إجمالي الإيرادات
        /// </summary>
        public decimal TotalRevenue
        {
            get { return DrugSales + OtherRevenue; }
        }
    }

    /// <summary>
    /// قسم تكلفة البضاعة المباعة
    /// </summary>
    public class CostOfGoodsSoldSection
    {
        /// <summary>
        /// تكلفة الأدوية المباعة
        /// </summary>
        public decimal DrugCosts { get; set; }

        /// <summary>
        /// تكاليف أخرى
        /// </summary>
        public decimal OtherCosts { get; set; }

        /// <summary>
        /// إجمالي التكلفة
        /// </summary>
        public decimal TotalCost
        {
            get { return DrugCosts + OtherCosts; }
        }
    }

    /// <summary>
    /// قسم المصروفات التشغيلية
    /// </summary>
    public class OperatingExpensesSection
    {
        /// <summary>
        /// رواتب الموظفين
        /// </summary>
        public decimal Salaries { get; set; }

        /// <summary>
        /// إيجار المحل
        /// </summary>
        public decimal Rent { get; set; }

        /// <summary>
        /// فواتير الكهرباء والماء
        /// </summary>
        public decimal Utilities { get; set; }

        /// <summary>
        /// مصروفات التسويق
        /// </summary>
        public decimal Marketing { get; set; }

        /// <summary>
        /// مصروفات إدارية
        /// </summary>
        public decimal Administrative { get; set; }

        /// <summary>
        /// مصروفات أخرى
        /// </summary>
        public decimal OtherExpenses { get; set; }

        /// <summary>
        /// إجمالي المصروفات
        /// </summary>
        public decimal TotalExpenses
        {
            get
            {
                return Salaries + Rent + Utilities + Marketing + Administrative + OtherExpenses;
            }
        }
    }

    /// <summary>
    /// نموذج تقرير العملاء
    /// </summary>
    public class CustomerReport
    {
        public CustomerReport()
        {
            Summary = new CustomerSummary();
            TopCustomers = new List<TopCustomer>();
            NewCustomers = new List<Customer>();
            InactiveCustomers = new List<Customer>();
        }

        /// <summary>
        /// تاريخ بداية التقرير
        /// </summary>
        public DateTime FromDate { get; set; }

        /// <summary>
        /// تاريخ نهاية التقرير
        /// </summary>
        public DateTime ToDate { get; set; }

        /// <summary>
        /// الملخص العام
        /// </summary>
        public CustomerSummary Summary { get; set; }

        /// <summary>
        /// أفضل العملاء
        /// </summary>
        public List<TopCustomer> TopCustomers { get; set; }

        /// <summary>
        /// العملاء الجدد
        /// </summary>
        public List<Customer> NewCustomers { get; set; }

        /// <summary>
        /// العملاء غير النشطين
        /// </summary>
        public List<Customer> InactiveCustomers { get; set; }

        /// <summary>
        /// تاريخ إنشاء التقرير
        /// </summary>
        public DateTime GeneratedDate { get; set; }

        /// <summary>
        /// منشئ التقرير
        /// </summary>
        public string GeneratedBy { get; set; }
    }



    /// <summary>
    /// أفضل عميل
    /// </summary>
    public class TopCustomer
    {
        /// <summary>
        /// معرف العميل
        /// </summary>
        public int CustomerID { get; set; }

        /// <summary>
        /// كود العميل
        /// </summary>
        public string CustomerCode { get; set; }

        /// <summary>
        /// اسم العميل
        /// </summary>
        public string CustomerName { get; set; }

        /// <summary>
        /// إجمالي المشتريات
        /// </summary>
        public decimal TotalPurchases { get; set; }

        /// <summary>
        /// عدد الفواتير
        /// </summary>
        public int InvoiceCount { get; set; }
      
        
        /// <summary>
        /// متوسط قيمة الفاتورة
        /// </summary>
        public decimal AverageInvoiceValue
        {
            get
            {
                return InvoiceCount > 0 ? TotalPurchases / InvoiceCount : 0;
            }
        }

        /// <summary>
        /// آخر تاريخ شراء
        /// </summary>
        public DateTime? LastPurchaseDate { get; set; }

        /// <summary>
        /// الرصيد المستحق
        /// </summary>
        public decimal OutstandingBalance { get; set; }
    }

    #region Inventory Models - نماذج المخزون

    /// <summary>
    /// نموذج إحصائيات المخزون
    /// </summary>
    public class InventoryStatistics
    {
        public int TotalDrugs { get; set; }
        public int LowStockDrugs { get; set; }
        public int ExpiredDrugs { get; set; }
        public decimal TotalInventoryValue { get; set; }
    }





    /// <summary>
    /// نموذج تفاصيل بيانات المخزون
    /// </summary>
    public class InventoryDetailData
    {
        public string DrugCode { get; set; }
        public string DrugName { get; set; }
        public string Category { get; set; }
        public string Manufacturer { get; set; }
        public int CurrentStock { get; set; }
        public int MinimumStock { get; set; }
        public int MaximumStock { get; set; }
        public decimal UnitPrice { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public string StockStatus { get; set; }
        public string ExpiryStatus { get; set; }
    }

    #endregion

    /// <summary>
    /// نموذج تقرير المبيعات
    /// </summary>
    public class SalesReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int TotalInvoices { get; set; }
        public decimal TotalSales { get; set; }
        public decimal TotalDiscounts { get; set; }
        public decimal AverageSale { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal TotalDiscount { get; set; }
        public decimal TotalTax { get; set; }
        public decimal NetSales
        {
            get
            {
                return TotalAmount - TotalDiscount + TotalTax;
            }
        }
        public int InvoiceCount { get; set; }
        public decimal AverageInvoiceValue
        {
            get
            {
                return InvoiceCount > 0 ? TotalAmount / InvoiceCount : 0;
            }
        }
        public DateTime GeneratedDate { get; set; }
    }

    /// <summary>
    /// نموذج تقرير المخزون
    /// </summary>
    public class InventoryReport
    {
        public InventoryReport()
        {
            Summary = new InventoryStatistics();
            AllItems = new List<InventoryItem>();
            InventoryDetails = new List<InventoryDetailData>();
            LowStockItems = new List<InventoryItem>();
            OutOfStockItems = new List<InventoryItem>();
            ExpiredItems = new List<InventoryItem>();
            NearExpiryItems = new List<InventoryItem>();
        }

        /// <summary>
        /// تاريخ التقرير
        /// </summary>
        public DateTime ReportDate { get; set; }

        /// <summary>
        /// إجمالي عدد الأدوية
        /// </summary>
        public int TotalDrugs { get; set; }

        /// <summary>
        /// عدد الأدوية منخفضة المخزون
        /// </summary>
        public int LowStockCount { get; set; }

        /// <summary>
        /// عدد الأدوية منتهية الصلاحية
        /// </summary>
        public int ExpiredCount { get; set; }

        /// <summary>
        /// إجمالي قيمة المخزون
        /// </summary>
        public decimal TotalInventoryValue { get; set; }

        /// <summary>
        /// الملخص العام
        /// </summary>
        public InventoryStatistics Summary { get; set; }

        /// <summary>
        /// تفاصيل جميع الأدوية
        /// </summary>
        public List<InventoryItem> AllItems { get; set; }

        /// <summary>
        /// تفاصيل المخزون
        /// </summary>
        public List<InventoryDetailData> InventoryDetails { get; set; }

        /// <summary>
        /// الأدوية منخفضة المخزون
        /// </summary>
        public List<InventoryItem> LowStockItems { get; set; }

        /// <summary>
        /// الأدوية نافدة المخزون
        /// </summary>
        public List<InventoryItem> OutOfStockItems { get; set; }

        /// <summary>
        /// الأدوية منتهية الصلاحية
        /// </summary>
        public List<InventoryItem> ExpiredItems { get; set; }

        /// <summary>
        /// الأدوية قريبة الانتهاء
        /// </summary>
        public List<InventoryItem> NearExpiryItems { get; set; }

        /// <summary>
        /// تاريخ إنشاء التقرير
        /// </summary>
        public DateTime GeneratedDate { get; set; }

        /// <summary>
        /// منشئ التقرير
        /// </summary>
        public string GeneratedBy { get; set; }
    }

    /// <summary>
    /// نموذج عملية البيع للتقارير - استخدام الفئة الموجودة في SalesModels.cs
    /// </summary>
    // تم حذف هذه الفئة لتجنب التعارض - استخدم Sale من SalesModels.cs

    /// <summary>
    /// نموذج المورد للتقارير - استخدام الفئة الموجودة في Supplier.cs
    /// </summary>
    // تم حذف هذه الفئة لتجنب التعارض - استخدم Supplier من Supplier.cs

    /// <summary>
    /// نموذج الدواء الأكثر مبيعاً - استخدام الفئة الموجودة في SalesModels.cs
    /// </summary>
    // تم حذف هذه الفئة لتجنب التعارض - استخدم TopSellingDrug من SalesModels.cs

    /// <summary>
    /// نموذج الدواء منتهي الصلاحية
    /// </summary>
    public class ExpiredDrug
    {
        public int DrugID { get; set; }
        public string DrugCode { get; set; }
        public string DrugName { get; set; }
        public string CategoryName { get; set; }
        public string ManufacturerName { get; set; }
        public string BatchNumber { get; set; }
        public int Quantity { get; set; }
        public DateTime ExpiryDate { get; set; }
        public decimal PurchasePrice { get; set; }
        public decimal TotalLoss { get; set; }
        public int DaysExpired { get; set; }
    }

    /// <summary>
    /// نموذج التقرير المالي
    /// </summary>
    public class FinancialReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public DateTime GeneratedDate { get; set; }
        public decimal TotalSales { get; set; }
        public decimal TotalPurchases { get; set; }
        public decimal TotalDiscounts { get; set; }
        public decimal TotalTax { get; set; }
        public decimal GrossProfit { get; set; }
        public decimal NetProfit { get; set; }
        public int TotalTransactions { get; set; }
        public int TotalPurchaseOrders { get; set; }
        public decimal ProfitMargin => TotalSales > 0 ? (NetProfit / TotalSales) * 100 : 0;
    }

    /// <summary>
    /// نموذج تقرير الأرباح المبسط
    /// </summary>
    public class ProfitReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public DateTime GeneratedDate { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal TotalCost { get; set; }
        public decimal GrossProfit { get; set; }
        public decimal OperatingExpenses { get; set; }
        public decimal NetProfit { get; set; }
        public decimal ProfitMargin { get; set; }
        public List<MonthlyProfit> MonthlyProfits { get; set; } = new List<MonthlyProfit>();
    }

    /// <summary>
    /// نموذج الربح الشهري
    /// </summary>
    public class MonthlyProfit
    {
        public int Year { get; set; }
        public int Month { get; set; }
        public string MonthName { get; set; }
        public decimal Revenue { get; set; }
        public decimal Cost { get; set; }
        public decimal Profit { get; set; }
        public decimal ProfitMargin { get; set; }
    }

    /// <summary>
    /// نموذج التقرير الشامل
    /// </summary>
    public class ComprehensiveReport
    {
        public DateTime GeneratedDate { get; set; } = DateTime.Now;
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }

        // إحصائيات المبيعات
        public SalesReport SalesReport { get; set; }

        // إحصائيات المخزون
        public InventoryReport InventoryReport { get; set; }

        // إحصائيات العملاء
        public CustomerReport CustomerReport { get; set; }

        // التقرير المالي
        public FinancialReport FinancialReport { get; set; }

        // الأدوية الأكثر مبيعاً
        public List<TopSellingDrug> TopSellingDrugs { get; set; } = new List<TopSellingDrug>();

        // الأدوية منتهية الصلاحية
        public List<ExpiredDrug> ExpiredDrugs { get; set; } = new List<ExpiredDrug>();
    }
}
