using System;

namespace PharmacyManagement.Models
{
    /// <summary>
    /// نموذج تفاصيل الفاتورة - Invoice Detail Model
    /// يمثل بيانات أصناف الفاتورة
    /// </summary>
    public class InvoiceDetail
    {
        #region Properties - الخصائص

        /// <summary>
        /// معرف التفصيل الفريد
        /// </summary>
        public int DetailID { get; set; }

        /// <summary>
        /// معرف الفاتورة
        /// </summary>
        public int InvoiceID { get; set; }

        /// <summary>
        /// معرف الدواء
        /// </summary>
        public int DrugID { get; set; }

        /// <summary>
        /// اسم الدواء
        /// </summary>
        public string DrugName { get; set; }

        /// <summary>
        /// كود الدواء
        /// </summary>
        public string DrugCode { get; set; }

        /// <summary>
        /// معرف الدفعة
        /// </summary>
        public int? BatchID { get; set; }

        /// <summary>
        /// رقم الدفعة
        /// </summary>
        public string BatchNumber { get; set; }

        /// <summary>
        /// الكمية
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// سعر الوحدة
        /// </summary>
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// مبلغ الخصم على الصنف
        /// </summary>
        public decimal DiscountAmount { get; set; }

        /// <summary>
        /// السعر الإجمالي للصنف
        /// </summary>
        public decimal TotalPrice { get; set; }

        /// <summary>
        /// تاريخ انتهاء الصلاحية
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// الوحدة
        /// </summary>
        public string Unit { get; set; }

        /// <summary>
        /// ملاحظات
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// معرف تفصيل الفاتورة
        /// </summary>
        public int InvoiceDetailID { get; set; }

        /// <summary>
        /// المبلغ الإجمالي
        /// </summary>
        public decimal TotalAmount { get; set; }

        #endregion

        #region Calculated Properties - الخصائص المحسوبة

        /// <summary>
        /// السعر قبل الخصم
        /// </summary>
        public decimal SubTotal
        {
            get { return Quantity * UnitPrice; }
        }

        /// <summary>
        /// نسبة الخصم
        /// </summary>
        public decimal DiscountPercent
        {
            get
            {
                if (SubTotal == 0) return 0;
                return (DiscountAmount / SubTotal) * 100;
            }
        }

        /// <summary>
        /// السعر النهائي بعد الخصم
        /// </summary>
        public decimal FinalPrice
        {
            get { return SubTotal - DiscountAmount; }
        }

        /// <summary>
        /// حالة انتهاء الصلاحية
        /// </summary>
        public string ExpiryStatus
        {
            get
            {
                if (!ExpiryDate.HasValue) return "غير محدد";
                
                var daysToExpiry = (ExpiryDate.Value - DateTime.Today).Days;
                
                if (daysToExpiry < 0) return "منتهي الصلاحية";
                if (daysToExpiry <= 30) return "قريب الانتهاء";
                if (daysToExpiry <= 90) return "تنبيه";
                return "صالح";
            }
        }

        /// <summary>
        /// معلومات الدواء الكاملة
        /// </summary>
        public string DrugInfo
        {
            get
            {
                var info = DrugName;
                if (!string.IsNullOrEmpty(DrugCode))
                    info += " (" + DrugCode + ")";
                if (!string.IsNullOrEmpty(BatchNumber))
                    info += " - دفعة: " + BatchNumber;
                return info;
            }
        }

        /// <summary>
        /// معلومات الكمية والوحدة
        /// </summary>
        public string QuantityInfo
        {
            get
            {
                return Quantity.ToString() + " " + (Unit ?? "قطعة");
            }
        }

        #endregion

        #region Constructors - المنشئات

        /// <summary>
        /// منشئ افتراضي
        /// </summary>
        public InvoiceDetail()
        {
            Quantity = 1;
            UnitPrice = 0;
            DiscountAmount = 0;
            TotalPrice = 0;
            Unit = "قطعة";
        }

        /// <summary>
        /// منشئ مع المعاملات الأساسية
        /// </summary>
        /// <param name="drugID">معرف الدواء</param>
        /// <param name="quantity">الكمية</param>
        /// <param name="unitPrice">سعر الوحدة</param>
        public InvoiceDetail(int drugID, int quantity, decimal unitPrice)
        {
            DrugID = drugID;
            Quantity = quantity;
            UnitPrice = unitPrice;
            DiscountAmount = 0;
            Unit = "قطعة";
            CalculateTotal();
        }

        /// <summary>
        /// منشئ مع جميع المعاملات
        /// </summary>
        /// <param name="drugID">معرف الدواء</param>
        /// <param name="drugName">اسم الدواء</param>
        /// <param name="quantity">الكمية</param>
        /// <param name="unitPrice">سعر الوحدة</param>
        /// <param name="discountAmount">مبلغ الخصم</param>
        public InvoiceDetail(int drugID, string drugName, int quantity, decimal unitPrice, decimal discountAmount = 0)
        {
            DrugID = drugID;
            DrugName = drugName;
            Quantity = quantity;
            UnitPrice = unitPrice;
            DiscountAmount = discountAmount;
            Unit = "قطعة";
            CalculateTotal();
        }

        #endregion

        #region Methods - الطرق

        /// <summary>
        /// التحقق من صحة بيانات التفصيل
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            return DrugID > 0 &&
                   Quantity > 0 &&
                   UnitPrice >= 0 &&
                   DiscountAmount >= 0 &&
                   DiscountAmount <= SubTotal;
        }

        /// <summary>
        /// حساب السعر الإجمالي
        /// </summary>
        public void CalculateTotal()
        {
            TotalPrice = (Quantity * UnitPrice) - DiscountAmount;
        }

        /// <summary>
        /// تطبيق خصم بالنسبة المئوية
        /// </summary>
        /// <param name="discountPercent">نسبة الخصم</param>
        public void ApplyDiscountPercent(decimal discountPercent)
        {
            if (discountPercent >= 0 && discountPercent <= 100)
            {
                DiscountAmount = SubTotal * (discountPercent / 100);
                CalculateTotal();
            }
        }

        /// <summary>
        /// تطبيق خصم بالمبلغ
        /// </summary>
        /// <param name="discountAmount">مبلغ الخصم</param>
        public void ApplyDiscountAmount(decimal discountAmount)
        {
            if (discountAmount >= 0 && discountAmount <= SubTotal)
            {
                DiscountAmount = discountAmount;
                CalculateTotal();
            }
        }

        /// <summary>
        /// التحقق من صلاحية الدواء
        /// </summary>
        /// <returns>true إذا كان الدواء صالحاً</returns>
        public bool IsNotExpired()
        {
            return !ExpiryDate.HasValue || ExpiryDate.Value > DateTime.Today;
        }

        /// <summary>
        /// التحقق من قرب انتهاء الصلاحية
        /// </summary>
        /// <param name="warningDays">عدد أيام التنبيه</param>
        /// <returns>true إذا كان قريب الانتهاء</returns>
        public bool IsNearExpiry(int warningDays = 30)
        {
            if (!ExpiryDate.HasValue) return false;
            return (ExpiryDate.Value - DateTime.Today).Days <= warningDays;
        }

        /// <summary>
        /// إرجاع تمثيل نصي للتفصيل
        /// </summary>
        /// <returns>معلومات الصنف</returns>
        public override string ToString()
        {
            return DrugName + " - " + QuantityInfo + " - " + TotalPrice.ToString("C");
        }

        #endregion
    }
}
