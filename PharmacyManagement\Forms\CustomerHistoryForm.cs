using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نافذة تاريخ العميل - Customer History Form
    /// </summary>
    public partial class CustomerHistoryForm : Form
    {
        #region Fields - الحقول

        private Customer _customer;

        #endregion

        #region Constructor - المنشئ

        /// <summary>
        /// منشئ نافذة تاريخ العميل
        /// </summary>
        /// <param name="customer">العميل</param>
        public CustomerHistoryForm(Customer customer)
        {
            InitializeComponent();
            _customer = customer;
            SetupForm();
            LoadCustomerHistory();
        }

        #endregion

        #region Form Setup - إعداد النافذة

        /// <summary>
        /// إعداد النافذة
        /// </summary>
        private void SetupForm()
        {
            this.Text = $"تاريخ العميل - {_customer.CustomerName}";
            this.Size = new Size(900, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MinimumSize = new Size(800, 500);

            SetupDataGridView();
            ApplyFlatDesign();
            LoadCustomerInfo();
        }

        /// <summary>
        /// إعداد DataGridView
        /// </summary>
        private void SetupDataGridView()
        {
            dgvHistory.AutoGenerateColumns = false;
            dgvHistory.AllowUserToAddRows = false;
            dgvHistory.AllowUserToDeleteRows = false;
            dgvHistory.ReadOnly = true;
            dgvHistory.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvHistory.MultiSelect = false;
        }

        /// <summary>
        /// تطبيق التصميم المسطح
        /// </summary>
        private void ApplyFlatDesign()
        {
            this.BackColor = Color.White;
            
            foreach (Control control in this.Controls)
            {
                if (control is Button button)
                {
                    button.FlatStyle = FlatStyle.Flat;
                    button.FlatAppearance.BorderSize = 0;
                }
            }
        }

        /// <summary>
        /// تحميل معلومات العميل
        /// </summary>
        private void LoadCustomerInfo()
        {
            lblCustomerName.Text = $"العميل: {_customer.CustomerName}";
            lblCustomerCode.Text = $"الكود: {_customer.CustomerCode}";
            lblCustomerPhone.Text = $"الهاتف: {_customer.Phone}";
            lblCustomerEmail.Text = $"البريد: {_customer.Email ?? "غير محدد"}";
        }

        #endregion

        #region Data Loading - تحميل البيانات

        /// <summary>
        /// تحميل تاريخ العميل
        /// </summary>
        private void LoadCustomerHistory()
        {
            try
            {
                // تحميل فواتير العميل
                var invoices = InvoiceManager.GetCustomerInvoices(_customer.CustomerID);
                dgvHistory.DataSource = invoices;

                // تحديث الإحصائيات
                UpdateStatistics(invoices);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تاريخ العميل: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحديث الإحصائيات
        /// </summary>
        /// <param name="invoices">قائمة الفواتير</param>
        private void UpdateStatistics(System.Collections.Generic.List<Invoice> invoices)
        {
            if (invoices != null && invoices.Count > 0)
            {
                lblTotalInvoices.Text = $"إجمالي الفواتير: {invoices.Count}";
                lblTotalAmount.Text = $"إجمالي المبلغ: {invoices.Sum(i => i.TotalAmount):F2} ر.ي";
                lblLastInvoice.Text = $"آخر فاتورة: {invoices.Max(i => i.InvoiceDate):yyyy/MM/dd}";
                lblFirstInvoice.Text = $"أول فاتورة: {invoices.Min(i => i.InvoiceDate):yyyy/MM/dd}";
            }
            else
            {
                lblTotalInvoices.Text = "إجمالي الفواتير: 0";
                lblTotalAmount.Text = "إجمالي المبلغ: 0.00 ر.ي";
                lblLastInvoice.Text = "آخر فاتورة: لا توجد";
                lblFirstInvoice.Text = "أول فاتورة: لا توجد";
            }
        }

        #endregion

        #region Event Handlers - معالجات الأحداث

        /// <summary>
        /// عرض تفاصيل الفاتورة
        /// </summary>
        private void btnViewInvoice_Click(object sender, EventArgs e)
        {
            if (dgvHistory.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار فاتورة لعرض التفاصيل", "تنبيه", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedInvoice = dgvHistory.SelectedRows[0].DataBoundItem as Invoice;
            if (selectedInvoice != null)
            {
                // فتح نافذة تفاصيل الفاتورة
                MessageBox.Show($"عرض تفاصيل الفاتورة رقم: {selectedInvoice.InvoiceNumber}", "تفاصيل الفاتورة", 
                              MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        /// <summary>
        /// تحديث البيانات
        /// </summary>
        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadCustomerHistory();
        }

        /// <summary>
        /// إغلاق النافذة
        /// </summary>
        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// النقر المزدوج لعرض تفاصيل الفاتورة
        /// </summary>
        private void dgvHistory_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                btnViewInvoice_Click(sender, e);
            }
        }

        #endregion
    }
}
