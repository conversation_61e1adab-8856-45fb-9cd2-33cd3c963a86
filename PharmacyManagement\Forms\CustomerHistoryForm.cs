using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Printing;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نافذة تاريخ العميل - Customer History Form
    /// </summary>
    public partial class CustomerHistoryForm : Form
    {
        #region Fields - الحقول

        private Customer _customer;

        #endregion

        #region Constructor - المنشئ

        /// <summary>
        /// منشئ نافذة تاريخ العميل
        /// </summary>
        /// <param name="customer">العميل</param>
        public CustomerHistoryForm(Customer customer)
        {
            InitializeComponent();
            _customer = customer;
            SetupForm();
            LoadCustomerHistory();
        }

        #endregion

        #region Form Setup - إعداد النافذة

        /// <summary>
        /// إعداد النافذة
        /// </summary>
        private void SetupForm()
        {
            this.Text = $"تاريخ العميل - {_customer.CustomerName}";
            this.Size = new Size(900, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MinimumSize = new Size(800, 500);

            SetupDataGridView();
            ApplyFlatDesign();
            LoadCustomerInfo();
        }

        /// <summary>
        /// إعداد DataGridView
        /// </summary>
        private void SetupDataGridView()
        {
            dgvHistory.AutoGenerateColumns = false;
            dgvHistory.AllowUserToAddRows = false;
            dgvHistory.AllowUserToDeleteRows = false;
            dgvHistory.ReadOnly = true;
            dgvHistory.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvHistory.MultiSelect = false;
        }

        /// <summary>
        /// تطبيق التصميم المسطح
        /// </summary>
        private void ApplyFlatDesign()
        {
            this.BackColor = Color.White;
            
            foreach (Control control in this.Controls)
            {
                if (control is Button button)
                {
                    button.FlatStyle = FlatStyle.Flat;
                    button.FlatAppearance.BorderSize = 0;
                }
            }
        }

        /// <summary>
        /// تحميل معلومات العميل
        /// </summary>
        private void LoadCustomerInfo()
        {
            lblCustomerName.Text = $"العميل: {_customer.CustomerName}";
            lblCustomerCode.Text = $"الكود: {_customer.CustomerCode}";
            lblCustomerPhone.Text = $"الهاتف: {_customer.Phone}";
            lblCustomerEmail.Text = $"البريد: {_customer.Email ?? "غير محدد"}";
        }

        #endregion

        #region Data Loading - تحميل البيانات

        /// <summary>
        /// تحميل تاريخ العميل
        /// </summary>
        private void LoadCustomerHistory()
        {
            try
            {
                LogManager.LogInfo($"بدء تحميل تاريخ العميل: {_customer.CustomerName} (ID: {_customer.CustomerID})");

                // التحقق من صحة معرف العميل
                if (_customer.CustomerID <= 0)
                {
                    MessageBox.Show("معرف العميل غير صحيح", "خطأ",
                                  MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // تحميل فواتير العميل
                var invoices = InvoiceManager.GetCustomerInvoices(_customer.CustomerID);

                if (invoices == null)
                {
                    invoices = new List<Invoice>();
                    LogManager.LogInfo($"لم يتم العثور على فواتير للعميل: {_customer.CustomerName}");
                }

                dgvHistory.DataSource = invoices;

                // تحديث الإحصائيات
                UpdateStatistics(invoices);

                LogManager.LogInfo($"تم تحميل {invoices.Count} فاتورة للعميل: {_customer.CustomerName}");
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تحميل تاريخ العميل {_customer.CustomerName}: {ex.Message}");
                MessageBox.Show($"خطأ في تحميل تاريخ العميل: {ex.Message}\n\nتفاصيل الخطأ: {ex.InnerException?.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);

                // تحميل قائمة فارغة في حالة الخطأ
                dgvHistory.DataSource = new List<Invoice>();
            }
        }

        /// <summary>
        /// تحديث الإحصائيات
        /// </summary>
        /// <param name="invoices">قائمة الفواتير</param>
        private void UpdateStatistics(List<Invoice> invoices)
        {
            if (invoices != null && invoices.Count > 0)
            {
                lblTotalInvoices.Text = $"إجمالي الفواتير: {invoices.Count}";
                lblTotalAmount.Text = $"إجمالي المبلغ: {invoices.Sum(i => i.TotalAmount):F2} ر.ي";
                lblLastInvoice.Text = $"آخر فاتورة: {invoices.Max(i => i.InvoiceDate):yyyy/MM/dd}";
                lblFirstInvoice.Text = $"أول فاتورة: {invoices.Min(i => i.InvoiceDate):yyyy/MM/dd}";
            }
            else
            {
                lblTotalInvoices.Text = "إجمالي الفواتير: 0";
                lblTotalAmount.Text = "إجمالي المبلغ: 0.00 ر.ي";
                lblLastInvoice.Text = "آخر فاتورة: لا توجد";
                lblFirstInvoice.Text = "أول فاتورة: لا توجد";
            }
        }

        #endregion

        #region Event Handlers - معالجات الأحداث

        /// <summary>
        /// عرض تفاصيل الفاتورة
        /// </summary>
        private void btnViewInvoice_Click(object sender, EventArgs e)
        {
            if (dgvHistory.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار فاتورة لعرض التفاصيل", "تنبيه", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedInvoice = dgvHistory.SelectedRows[0].DataBoundItem as Invoice;
            if (selectedInvoice != null)
            {
                // فتح نافذة تفاصيل الفاتورة
                MessageBox.Show($"عرض تفاصيل الفاتورة رقم: {selectedInvoice.InvoiceNumber}", "تفاصيل الفاتورة", 
                              MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        /// <summary>
        /// تحديث البيانات
        /// </summary>
        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadCustomerHistory();
        }

        /// <summary>
        /// طباعة تاريخ العميل
        /// </summary>
        private void btnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                LogManager.LogInfo($"بدء طباعة تاريخ العميل: {_customer.CustomerName}");

                PrintDocument printDoc = new PrintDocument();
                printDoc.PrintPage += new PrintPageEventHandler(PrintPage);

                PrintPreviewDialog printPreviewDialog = new PrintPreviewDialog();
                printPreviewDialog.Document = printDoc;
                printPreviewDialog.ShowDialog();

                LogManager.LogInfo($"تمت طباعة تاريخ العميل: {_customer.CustomerName}");
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في طباعة تاريخ العميل: {ex.Message}");
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// طباعة الصفحة
        /// </summary>
        private void PrintPage(object sender, PrintPageEventArgs e)
        {
            try
            {
                Graphics g = e.Graphics;
                Font titleFont = new Font("Arial", 18, FontStyle.Bold);
                Font headerFont = new Font("Arial", 14, FontStyle.Bold);
                Font normalFont = new Font("Arial", 12);
                Font smallFont = new Font("Arial", 10);

                float yPos = 50;
                int leftMargin = 50;
                int rightMargin = e.PageBounds.Width - 100;
                int width = rightMargin - leftMargin;

                // عنوان التقرير
                string title = $"تقرير تاريخ العميل: {_customer.CustomerName}";
                g.DrawString(title, titleFont, Brushes.Black, new RectangleF(leftMargin, yPos, width, 30));
                yPos += 40;

                // معلومات العميل
                g.DrawString($"كود العميل: {_customer.CustomerCode}", headerFont, Brushes.Black, new RectangleF(leftMargin, yPos, width, 25));
                yPos += 25;
                g.DrawString($"الهاتف: {_customer.Phone}", normalFont, Brushes.Black, new RectangleF(leftMargin, yPos, width, 20));
                yPos += 20;
                g.DrawString($"البريد: {_customer.Email ?? "غير محدد"}", normalFont, Brushes.Black, new RectangleF(leftMargin, yPos, width, 20));
                yPos += 30;

                // إحصائيات
                g.DrawString("إحصائيات", headerFont, Brushes.Black, new RectangleF(leftMargin, yPos, width, 25));
                yPos += 25;
                g.DrawString(lblTotalInvoices.Text, normalFont, Brushes.Black, new RectangleF(leftMargin, yPos, width, 20));
                yPos += 20;
                g.DrawString(lblTotalAmount.Text, normalFont, Brushes.Black, new RectangleF(leftMargin, yPos, width, 20));
                yPos += 20;
                g.DrawString(lblFirstInvoice.Text, normalFont, Brushes.Black, new RectangleF(leftMargin, yPos, width, 20));
                yPos += 20;
                g.DrawString(lblLastInvoice.Text, normalFont, Brushes.Black, new RectangleF(leftMargin, yPos, width, 20));
                yPos += 40;

                // جدول الفواتير
                g.DrawString("قائمة الفواتير", headerFont, Brushes.Black, new RectangleF(leftMargin, yPos, width, 25));
                yPos += 30;

                // رسم رأس الجدول
                float colWidth = width / 4;
                g.DrawLine(Pens.Black, leftMargin, yPos, rightMargin, yPos);
                g.DrawString("رقم الفاتورة", normalFont, Brushes.Black, new RectangleF(leftMargin, yPos, colWidth, 20));
                g.DrawString("التاريخ", normalFont, Brushes.Black, new RectangleF(leftMargin + colWidth, yPos, colWidth, 20));
                g.DrawString("المبلغ", normalFont, Brushes.Black, new RectangleF(leftMargin + colWidth * 2, yPos, colWidth, 20));
                g.DrawString("الحالة", normalFont, Brushes.Black, new RectangleF(leftMargin + colWidth * 3, yPos, colWidth, 20));
                yPos += 20;
                g.DrawLine(Pens.Black, leftMargin, yPos, rightMargin, yPos);
                yPos += 10;

                // رسم بيانات الجدول
                var invoices = dgvHistory.DataSource as List<Invoice>;
                if (invoices != null && invoices.Count > 0)
                {
                    foreach (var invoice in invoices)
                    {
                        g.DrawString(invoice.InvoiceNumber, smallFont, Brushes.Black, new RectangleF(leftMargin, yPos, colWidth, 20));
                        g.DrawString(invoice.InvoiceDate.ToString("yyyy/MM/dd"), smallFont, Brushes.Black, new RectangleF(leftMargin + colWidth, yPos, colWidth, 20));
                        g.DrawString(invoice.TotalAmount.ToString("F2"), smallFont, Brushes.Black, new RectangleF(leftMargin + colWidth * 2, yPos, colWidth, 20));
                        g.DrawString(invoice.Status, smallFont, Brushes.Black, new RectangleF(leftMargin + colWidth * 3, yPos, colWidth, 20));
                        yPos += 20;

                        // التحقق من وصول نهاية الصفحة
                        if (yPos > e.PageBounds.Height - 100)
                        {
                            e.HasMorePages = true;
                            return;
                        }
                    }
                }
                else
                {
                    g.DrawString("لا توجد فواتير لهذا العميل", normalFont, Brushes.Black, new RectangleF(leftMargin, yPos, width, 20));
                }

                // رسم خط أسفل الجدول
                yPos += 10;
                g.DrawLine(Pens.Black, leftMargin, yPos, rightMargin, yPos);

                // معلومات التذييل
                yPos = e.PageBounds.Height - 50;
                g.DrawString($"تاريخ الطباعة: {DateTime.Now.ToString("yyyy/MM/dd HH:mm")}", smallFont, Brushes.Gray, new RectangleF(leftMargin, yPos, width, 20));

                e.HasMorePages = false;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في طباعة الصفحة: {ex.Message}");
            }
        }

        /// <summary>
        /// تصدير إلى Excel
        /// </summary>
        private void btnExportExcel_Click(object sender, EventArgs e)
        {
            try
            {
                LogManager.LogInfo($"بدء تصدير تاريخ العميل إلى Excel: {_customer.CustomerName}");

                var invoices = dgvHistory.DataSource as List<Invoice>;
                if (invoices == null || invoices.Count == 0)
                {
                    MessageBox.Show("لا توجد بيانات للتصدير", "تنبيه",
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                SaveFileDialog saveDialog = new SaveFileDialog();
                saveDialog.Filter = "Excel Files (*.csv)|*.csv";
                saveDialog.Title = "تصدير إلى Excel";
                saveDialog.FileName = $"تاريخ_العميل_{_customer.CustomerCode}_{DateTime.Now.ToString("yyyyMMdd")}";

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    // إعداد الإحصائيات
                    var statistics = new Dictionary<string, string>
                    {
                        { "إجمالي الفواتير", lblTotalInvoices.Text.Replace("إجمالي الفواتير: ", "") },
                        { "إجمالي المبلغ", lblTotalAmount.Text.Replace("إجمالي المبلغ: ", "") },
                        { "أول فاتورة", lblFirstInvoice.Text.Replace("أول فاتورة: ", "") },
                        { "آخر فاتورة", lblLastInvoice.Text.Replace("آخر فاتورة: ", "") }
                    };

                    // استخدام فئة المساعد للتصدير
                    ExportHelper.ExportCustomerHistoryToCSV(_customer, invoices, statistics, saveDialog.FileName);

                    MessageBox.Show("تم تصدير البيانات بنجاح", "تم",
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // سؤال المستخدم إذا كان يريد فتح الملف
                    if (MessageBox.Show("هل تريد فتح الملف المصدر؟", "فتح الملف",
                                      MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                    {
                        ExportHelper.OpenExportedFile(saveDialog.FileName);
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تصدير البيانات إلى Excel: {ex.Message}");
                MessageBox.Show($"خطأ في تصدير البيانات: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تصدير إلى Word
        /// </summary>
        private void btnExportWord_Click(object sender, EventArgs e)
        {
            try
            {
                LogManager.LogInfo($"بدء تصدير تاريخ العميل إلى Word: {_customer.CustomerName}");

                var invoices = dgvHistory.DataSource as List<Invoice>;
                if (invoices == null || invoices.Count == 0)
                {
                    MessageBox.Show("لا توجد بيانات للتصدير", "تنبيه",
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                SaveFileDialog saveDialog = new SaveFileDialog();
                saveDialog.Filter = "Word Files (*.html)|*.html";
                saveDialog.Title = "تصدير إلى Word";
                saveDialog.FileName = $"تاريخ_العميل_{_customer.CustomerCode}_{DateTime.Now.ToString("yyyyMMdd")}";

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    // إعداد الإحصائيات
                    var statistics = new Dictionary<string, string>
                    {
                        { "إجمالي الفواتير", lblTotalInvoices.Text.Replace("إجمالي الفواتير: ", "") },
                        { "إجمالي المبلغ", lblTotalAmount.Text.Replace("إجمالي المبلغ: ", "") },
                        { "أول فاتورة", lblFirstInvoice.Text.Replace("أول فاتورة: ", "") },
                        { "آخر فاتورة", lblLastInvoice.Text.Replace("آخر فاتورة: ", "") }
                    };

                    // استخدام فئة المساعد للتصدير
                    ExportHelper.ExportCustomerHistoryToHTML(_customer, invoices, statistics, saveDialog.FileName);

                    MessageBox.Show("تم تصدير البيانات بنجاح", "تم",
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // سؤال المستخدم إذا كان يريد فتح الملف
                    if (MessageBox.Show("هل تريد فتح الملف المصدر؟", "فتح الملف",
                                      MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                    {
                        ExportHelper.OpenExportedFile(saveDialog.FileName);
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تصدير البيانات إلى Word: {ex.Message}");
                MessageBox.Show($"خطأ في تصدير البيانات: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إغلاق النافذة
        /// </summary>
        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// النقر المزدوج لعرض تفاصيل الفاتورة
        /// </summary>
        private void dgvHistory_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                btnViewInvoice_Click(sender, e);
            }
        }

        /// <summary>
        /// إغلاق النافذة
        /// </summary>
        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// النقر المزدوج على الجدول لعرض تفاصيل الفاتورة
        /// </summary>
        private void dgvHistory_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            btnViewInvoice_Click(sender, e);
        }

        #endregion
    }
}
