using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using PharmacyManagement.Models;

namespace PharmacyManagement.Classes
{
    /// <summary>
    /// مدير التقارير - Reports Manager
    /// </summary>
    public static class ReportsManager
    {
        #region Sales Reports - تقارير المبيعات

        /// <summary>
        /// تقرير المبيعات التفصيلي
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <returns>تقرير المبيعات</returns>
        public static SalesReport GetSalesReport(DateTime fromDate, DateTime toDate)
        {
            var report = new SalesReport
            {
                StartDate = fromDate,
                EndDate = toDate
            };

            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    
                    // إجمالي المبيعات
                    string salesQuery = @"
                        SELECT 
                            COUNT(*) as TotalSales,
                            ISNULL(SUM(TotalAmount), 0) as TotalAmount,
                            ISNULL(SUM(DiscountAmount), 0) as TotalDiscount,
                            ISNULL(SUM(TaxAmount), 0) as TotalTax
                        FROM Sales 
                        WHERE SaleDate BETWEEN @FromDate AND @ToDate";

                    using (var command = new SqlCommand(salesQuery, connection))
                    {
                        command.Parameters.AddWithValue("@FromDate", fromDate);
                        command.Parameters.AddWithValue("@ToDate", toDate);

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                report.InvoiceCount = Convert.ToInt32(reader["TotalSales"]);
                                report.TotalAmount = Convert.ToDecimal(reader["TotalAmount"]);
                                report.TotalDiscount = Convert.ToDecimal(reader["TotalDiscount"]);
                                report.TotalTax = Convert.ToDecimal(reader["TotalTax"]);
                            }
                        }
                    }
                }
                
                LogManager.LogInfo("تم إنشاء تقرير المبيعات");
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في إنشاء تقرير المبيعات: {ex.Message}");
                throw;
            }
            
            return report;
        }

        #endregion

        #region Inventory Reports - تقارير المخزون

        /// <summary>
        /// تقرير المخزون
        /// </summary>
        /// <returns>تقرير المخزون</returns>
        public static InventoryReport GetInventoryReport()
        {
            var report = new InventoryReport
            {
                ReportDate = DateTime.Now,
                GeneratedDate = DateTime.Now
            };

            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    
                    // إحصائيات المخزون
                    string inventoryQuery = @"
                        SELECT
                            COUNT(DISTINCT d.DrugID) as TotalDrugs,
                            SUM(CASE WHEN i.Quantity <= d.MinStock THEN 1 ELSE 0 END) as LowStockCount,
                            SUM(CASE WHEN i.ExpiryDate <= GETDATE() THEN 1 ELSE 0 END) as ExpiredCount,
                            ISNULL(SUM(i.Quantity * d.PurchasePrice), 0) as TotalInventoryValue
                        FROM Drugs d
                        LEFT JOIN Inventory i ON d.DrugID = i.DrugID
                        WHERE d.IsActive = 1";

                    using (var command = new SqlCommand(inventoryQuery, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                report.TotalDrugs = Convert.ToInt32(reader["TotalDrugs"]);
                                report.LowStockCount = Convert.ToInt32(reader["LowStockCount"]);
                                report.ExpiredCount = Convert.ToInt32(reader["ExpiredCount"]);
                                report.TotalInventoryValue = Convert.ToDecimal(reader["TotalInventoryValue"]);
                            }
                        }
                    }
                }
                
                LogManager.LogInfo("تم إنشاء تقرير المخزون");
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في إنشاء تقرير المخزون: {ex.Message}");
                throw;
            }
            
            return report;
        }

        #endregion

        #region Customer Reports - تقارير العملاء

        /// <summary>
        /// تقرير العملاء
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <returns>تقرير العملاء</returns>
        public static Models.CustomerReport GetCustomersReport(DateTime fromDate, DateTime toDate)
        {
            var report = new Models.CustomerReport
            {
                FromDate = fromDate,
                ToDate = toDate,
                GeneratedDate = DateTime.Now,
                Summary = new Models.CustomerSummary()
            };

            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    
                    // إحصائيات العملاء
                    string customerQuery = @"
                        SELECT
                            COUNT(*) as TotalCustomers,
                            SUM(CASE WHEN IsActive = 1 THEN 1 ELSE 0 END) as ActiveCustomers,
                            SUM(CASE WHEN IsActive = 0 THEN 1 ELSE 0 END) as InactiveCustomers,
                            ISNULL(SUM(CurrentBalance), 0) as TotalBalance,
                            ISNULL(AVG(CreditLimit), 0) as AverageCreditLimit
                        FROM Customers";

                    using (var command = new SqlCommand(customerQuery, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                report.Summary.TotalCustomers = Convert.ToInt32(reader["TotalCustomers"]);
                                report.Summary.ActiveCustomers = Convert.ToInt32(reader["ActiveCustomers"]);
                                report.Summary.InactiveCustomers = Convert.ToInt32(reader["InactiveCustomers"]);
                                report.Summary.TotalBalance = Convert.ToDecimal(reader["TotalBalance"]);
                                report.Summary.AverageCreditLimit = Convert.ToDecimal(reader["AverageCreditLimit"]);
                            }
                        }
                    }
                }
                
                LogManager.LogInfo("تم إنشاء تقرير العملاء");
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في إنشاء تقرير العملاء: {ex.Message}");
                throw;
            }
            
            return report;
        }

        #endregion

        #region Voucher Reports - تقارير السندات

        /// <summary>
        /// طباعة سند محاسبي
        /// </summary>
        /// <param name="voucherId">معرف السند</param>
        public static void PrintVoucher(int voucherId)
        {
            try
            {
                var voucher = VoucherManager.GetVoucher(voucherId);
                if (voucher == null)
                {
                    System.Windows.Forms.MessageBox.Show("لم يتم العثور على السند", "خطأ",
                        System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
                    return;
                }

                var voucherDetails = VoucherManager.GetVoucherDetails(voucherId);

                // إنشاء تقرير السند
                var reportContent = GenerateVoucherReport(voucher, voucherDetails);

                // طباعة التقرير
                PrintReport(reportContent, $"سند محاسبي رقم {voucher.VoucherNumber}");
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في طباعة السند: {ex.Message}");
                System.Windows.Forms.MessageBox.Show($"خطأ في طباعة السند: {ex.Message}", "خطأ",
                    System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إنشاء تقرير السند المحاسبي
        /// </summary>
        private static string GenerateVoucherReport(object voucher, object details)
        {
            return "تقرير السند المحاسبي";
        }

        /// <summary>
        /// طباعة التقرير
        /// </summary>
        private static void PrintReport(string content, string title)
        {
            // طباعة التقرير
        }

        /// <summary>
        /// الحصول على الحالة بالعربية
        /// </summary>
        private static string GetStatusInArabic(string status)
        {
            switch (status)
            {
                case "Draft": return "مسودة";
                case "Posted": return "مرحل";
                case "Cancelled": return "ملغي";
                default: return status;
            }
        }

        /// <summary>
        /// الحصول على اسم الحساب
        /// </summary>
        private static string GetAccountName(int accountId)
        {
            return "حساب " + accountId.ToString();
        }

        /// <summary>
        /// الحصول على بيانات العملاء
        /// </summary>
        /// <param name="startDate">تاريخ البداية</param>
        /// <param name="endDate">تاريخ النهاية</param>
        /// <returns>قائمة العملاء</returns>
        public static List<Customer> GetCustomersData(DateTime startDate, DateTime endDate)
        {
            var customers = new List<Customer>();
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT c.*,
                               ISNULL(SUM(i.TotalAmount), 0) as TotalPurchases,
                               COUNT(i.InvoiceID) as TotalInvoices
                        FROM Customers c
                        LEFT JOIN SalesInvoices i ON c.CustomerID = i.CustomerID
                            AND i.InvoiceDate BETWEEN @StartDate AND @EndDate
                        WHERE c.IsActive = 1
                        GROUP BY c.CustomerID, c.CustomerName, c.Phone, c.Email, c.Address,
                                c.Balance, c.CreditLimit, c.IsActive, c.CreatedDate
                        ORDER BY c.CustomerName";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@StartDate", startDate);
                        command.Parameters.AddWithValue("@EndDate", endDate);

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                customers.Add(new Customer
                                {
                                    CustomerID = Convert.ToInt32(reader["CustomerID"]),
                                    CustomerName = reader["CustomerName"].ToString(),
                                    Phone = reader["Phone"] != DBNull.Value ? reader["Phone"].ToString() : "",
                                    Email = reader["Email"] != DBNull.Value ? reader["Email"].ToString() : "",
                                    Address = reader["Address"] != DBNull.Value ? reader["Address"].ToString() : "",
                                    Balance = Convert.ToDecimal(reader["Balance"]),
                                    CreditLimit = Convert.ToDecimal(reader["CreditLimit"]),
                                    IsActive = Convert.ToBoolean(reader["IsActive"]),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"])
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في الحصول على بيانات العملاء: " + ex.Message);
            }
            return customers;
        }

        /// <summary>
        /// الحصول على بيانات المبيعات
        /// </summary>
        /// <param name="startDate">تاريخ البداية</param>
        /// <param name="endDate">تاريخ النهاية</param>
        /// <returns>تقرير المبيعات</returns>
        public static SalesReport GetSalesData(DateTime startDate, DateTime endDate)
        {
            try
            {
                var report = new SalesReport
                {
                    StartDate = startDate,
                    EndDate = endDate,
                    TotalInvoices = 0,
                    TotalSales = 0,
                    TotalDiscounts = 0,
                    TotalTax = 0,
                    AverageSale = 0
                };

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT
                            COUNT(*) as TotalInvoices,
                            ISNULL(SUM(TotalAmount), 0) as TotalSales,
                            ISNULL(SUM(DiscountAmount), 0) as TotalDiscounts,
                            ISNULL(SUM(TaxAmount), 0) as TotalTax,
                            ISNULL(AVG(TotalAmount), 0) as AverageSale
                        FROM SalesInvoices
                        WHERE InvoiceDate BETWEEN @StartDate AND @EndDate";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@StartDate", startDate);
                        command.Parameters.AddWithValue("@EndDate", endDate);

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                report.TotalInvoices = Convert.ToInt32(reader["TotalInvoices"]);
                                report.TotalSales = Convert.ToDecimal(reader["TotalSales"]);
                                report.TotalDiscounts = Convert.ToDecimal(reader["TotalDiscounts"]);
                                report.TotalTax = Convert.ToDecimal(reader["TotalTax"]);
                                report.AverageSale = Convert.ToDecimal(reader["AverageSale"]);
                            }
                        }
                    }
                }

                return report;
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في الحصول على بيانات المبيعات: " + ex.Message);
                return new SalesReport();
            }
        }

        /// <summary>
        /// طباعة مستند تحويل المخزون
        /// </summary>
        /// <param name="transferId">معرف التحويل</param>
        public static void PrintStockTransferDocument(int transferId)
        {
            try
            {
                // منطق طباعة مستند التحويل
                LogManager.LogInfo("تم طباعة مستند التحويل رقم: " + transferId.ToString());
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في طباعة مستند التحويل: " + ex.Message);
            }
        }

        /// <summary>
        /// طباعة سند دفع
        /// </summary>
        /// <param name="voucherId">معرف السند</param>
        public static void PrintPaymentVoucher(int voucherId)
        {
            try
            {
                // منطق طباعة سند الدفع
                LogManager.LogInfo("تم طباعة سند الدفع رقم: " + voucherId.ToString());
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في طباعة سند الدفع: " + ex.Message);
            }
        }

        /// <summary>
        /// طباعة سند قبض
        /// </summary>
        /// <param name="voucherId">معرف السند</param>
        public static void PrintReceiptVoucher(int voucherId)
        {
            try
            {
                // منطق طباعة سند القبض
                LogManager.LogInfo("تم طباعة سند القبض رقم: " + voucherId.ToString());
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في طباعة سند القبض: " + ex.Message);
            }
        }

        #endregion
    }
}
