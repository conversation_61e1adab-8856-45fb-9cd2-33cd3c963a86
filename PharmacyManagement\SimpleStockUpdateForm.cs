using System;
using System.Data.SqlClient;
using System.Drawing;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نافذة تحديث المخزون المبسطة - Simple Stock Update Form
    /// </summary>
    public partial class SimpleStockUpdateForm : Form
    {
        #region Fields - الحقول

        private Drug _drug;
        private decimal _currentStock;

        #endregion

        #region Constructor - المنشئ

        /// <summary>
        /// منشئ نافذة تحديث المخزون المبسطة
        /// </summary>
        /// <param name="drug">الدواء</param>
        public SimpleStockUpdateForm(Drug drug)
        {
            InitializeComponent();
            _drug = drug;
            SetupForm();
            LoadDrugInfo();
        }

        #endregion

        #region Form Setup - إعداد النافذة

        /// <summary>
        /// إعداد النافذة
        /// </summary>
        private void SetupForm()
        {
            this.Text = $"تحديث مخزون - {_drug.DrugName}";
            this.Size = new Size(450, 350);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.White;
        }

        /// <summary>
        /// تحميل معلومات الدواء
        /// </summary>
        private void LoadDrugInfo()
        {
            try
            {
                // جلب المخزون الحالي من جدول Inventory
                _currentStock = GetCurrentStockFromInventory(_drug.DrugID);
                
                lblDrugName.Text = $"الدواء: {_drug.DrugName}";
                lblDrugCode.Text = $"الكود: {_drug.DrugCode}";
                lblCurrentStock.Text = $"المخزون الحالي: {_currentStock}";
                lblUnit.Text = $"الوحدة: {_drug.Unit}";
                
                // تعيين القيم الافتراضية
                numQuantity.Value = 0;
                rbAdd.Checked = true;
                txtNotes.Clear();
                
                UpdatePreview();
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تحميل معلومات الدواء: {ex.Message}");
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// جلب المخزون الحالي من جدول Inventory
        /// </summary>
        private decimal GetCurrentStockFromInventory(int drugId)
        {
            try
            {
                string query = @"
                    SELECT ISNULL(SUM(Quantity), 0) as TotalStock
                    FROM Inventory 
                    WHERE DrugID = @DrugID AND Quantity > 0";
                
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@DrugID", drugId);
                        var result = command.ExecuteScalar();
                        return Convert.ToDecimal(result);
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في جلب المخزون الحالي: {ex.Message}");
                return 0;
            }
        }

        #endregion

        #region Event Handlers - معالجات الأحداث

        /// <summary>
        /// حفظ تحديث المخزون
        /// </summary>
        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (ValidateInput())
                {
                    UpdateStock();
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في حفظ تحديث المخزون: {ex.Message}");
                MessageBox.Show($"خطأ في تحديث المخزون: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إلغاء العملية
        /// </summary>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// تحديث المعاينة عند تغيير القيم
        /// </summary>
        private void UpdatePreview()
        {
            try
            {
                decimal quantity = numQuantity.Value;
                bool isAddition = rbAdd.Checked;
                
                decimal newStock;
                if (isAddition)
                {
                    newStock = _currentStock + quantity;
                    lblPreview.Text = $"المخزون الجديد: {newStock}";
                    lblPreview.ForeColor = Color.Green;
                }
                else
                {
                    newStock = _currentStock - quantity;
                    lblPreview.Text = $"المخزون الجديد: {newStock}";
                    lblPreview.ForeColor = newStock < 0 ? Color.Red : Color.Orange;
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تحديث المعاينة: {ex.Message}");
            }
        }

        private void numQuantity_ValueChanged(object sender, EventArgs e)
        {
            UpdatePreview();
        }

        private void rbAdd_CheckedChanged(object sender, EventArgs e)
        {
            UpdatePreview();
        }

        private void rbSubtract_CheckedChanged(object sender, EventArgs e)
        {
            UpdatePreview();
        }

        #endregion

        #region Private Methods - الطرق الخاصة

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        private bool ValidateInput()
        {
            if (numQuantity.Value <= 0)
            {
                MessageBox.Show("يرجى إدخال كمية صحيحة", "تنبيه", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                numQuantity.Focus();
                return false;
            }

            if (rbSubtract.Checked && numQuantity.Value > _currentStock)
            {
                MessageBox.Show($"لا يمكن خصم {numQuantity.Value} وحدة\nالمخزون الحالي: {_currentStock}", "تنبيه", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                numQuantity.Focus();
                return false;
            }

            return true;
        }

        /// <summary>
        /// تحديث المخزون
        /// </summary>
        private void UpdateStock()
        {
            decimal quantity = numQuantity.Value;
            bool isAddition = rbAdd.Checked;
            string notes = txtNotes.Text.Trim();

            LogManager.LogInfo($"بدء تحديث المخزون - الدواء: {_drug.DrugName}, الكمية: {quantity}, العملية: {(isAddition ? "إضافة" : "خصم")}");

            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            if (isAddition)
                            {
                                // إضافة دفعة جديدة
                                string insertQuery = @"
                                    INSERT INTO Inventory (DrugID, WarehouseID, BatchNumber, Quantity, 
                                                         ExpiryDate, PurchasePrice, SalePrice, CreatedDate)
                                    VALUES (@DrugID, 1, @BatchNumber, @Quantity, 
                                           DATEADD(YEAR, 2, GETDATE()), @PurchasePrice, @SalePrice, GETDATE())";

                                using (var cmd = new SqlCommand(insertQuery, connection, transaction))
                                {
                                    cmd.Parameters.AddWithValue("@DrugID", _drug.DrugID);
                                    cmd.Parameters.AddWithValue("@BatchNumber", $"BATCH_{DateTime.Now:yyyyMMddHHmmss}");
                                    cmd.Parameters.AddWithValue("@Quantity", quantity);
                                    cmd.Parameters.AddWithValue("@PurchasePrice", _drug.PurchasePrice);
                                    cmd.Parameters.AddWithValue("@SalePrice", _drug.SalePrice);
                                    cmd.ExecuteNonQuery();
                                }
                            }
                            else
                            {
                                // خصم من أول دفعة متاحة
                                string updateQuery = @"
                                    UPDATE TOP(1) Inventory 
                                    SET Quantity = Quantity - @Quantity, UpdatedDate = GETDATE()
                                    WHERE DrugID = @DrugID AND Quantity >= @Quantity";

                                using (var cmd = new SqlCommand(updateQuery, connection, transaction))
                                {
                                    cmd.Parameters.AddWithValue("@DrugID", _drug.DrugID);
                                    cmd.Parameters.AddWithValue("@Quantity", quantity);
                                    int rowsAffected = cmd.ExecuteNonQuery();
                                    
                                    if (rowsAffected == 0)
                                    {
                                        throw new Exception("لا توجد دفعة كافية للخصم");
                                    }
                                }
                            }

                            transaction.Commit();

                            decimal newStock = _currentStock + (isAddition ? quantity : -quantity);
                            string operation = isAddition ? "إضافة" : "خصم";
                            
                            MessageBox.Show($"تم {operation} {quantity} {_drug.Unit} بنجاح\n" +
                                          $"المخزون السابق: {_currentStock}\n" +
                                          $"المخزون الجديد: {newStock}", 
                                          "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);

                            LogManager.LogInfo($"تم تحديث مخزون الدواء {_drug.DrugName}: {operation} {quantity} - من {_currentStock} إلى {newStock}");
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            LogManager.LogError($"خطأ في المعاملة: {ex.Message}");
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تحديث المخزون: {ex.Message}");
                throw;
            }
        }

        #endregion
    }
}
