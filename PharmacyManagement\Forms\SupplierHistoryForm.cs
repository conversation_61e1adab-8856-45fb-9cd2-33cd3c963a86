using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نافذة تاريخ المورد - Supplier History Form
    /// </summary>
    public partial class SupplierHistoryForm : Form
    {
        #region Fields - الحقول

        private int _supplierID;
        private Supplier _supplier;

        #endregion

        #region Constructor - المنشئ

        /// <summary>
        /// منشئ نافذة تاريخ المورد
        /// </summary>
        /// <param name="supplierID">معرف المورد</param>
        public SupplierHistoryForm(int supplierID)
        {
            _supplierID = supplierID;
            InitializeComponent();
            LoadData();
        }

       

        #endregion

        #region Form Setup - إعداد النافذة

        /// <summary>
        /// إعداد النافذة
        /// </summary>
        private void SetupForm()
        {
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MinimumSize = new Size(800, 600);

            SetupDataGridView();
            ApplyFlatDesign();
        }

        /// <summary>
        /// إعداد DataGridView
        /// </summary>
        private void SetupDataGridView()
        {
            // TODO: إعداد DataGridView
        }

        /// <summary>
        /// تطبيق التصميم المسطح
        /// </summary>
        private void ApplyFlatDesign()
        {
            this.BackColor = Color.White;
            
            foreach (Control control in this.Controls)
            {
                if (control is Button button)
                {
                    button.FlatStyle = FlatStyle.Flat;
                    button.FlatAppearance.BorderSize = 0;
                }
            }
        }

        #endregion

        #region Data Loading - تحميل البيانات

        /// <summary>
        /// تحميل البيانات
        /// </summary>
        private void LoadData()
        {
            try
            {
                // تحميل بيانات المورد
                _supplier = SupplierManager.GetSupplierById(_supplierID);
                if (_supplier != null)
                {
                    this.Text = "تاريخ المورد - " + _supplier.SupplierName;
                }

                // تحميل تاريخ المعاملات
                LoadTransactionHistory();

                // تحميل الإحصائيات
                LoadStatistics();
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تحميل البيانات: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحميل تاريخ المعاملات
        /// </summary>
        private void LoadTransactionHistory()
        {
            try
            {
                var history = SupplierManager.GetSupplierTransactionHistory(_supplierID);
                // TODO: عرض البيانات
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تحميل تاريخ المعاملات: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحميل الإحصائيات
        /// </summary>
        private void LoadStatistics()
        {
            try
            {
                var stats = SupplierManager.GetSupplierStatistics(_supplierID);
                // TODO: عرض الإحصائيات
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تحميل الإحصائيات: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Event Handlers - معالجات الأحداث

        /// <summary>
        /// تصدير التاريخ
        /// </summary>
        private void btnExport_Click(object sender, EventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "Excel Files|*.xlsx|PDF Files|*.pdf|CSV Files|*.csv",
                    Title = "تصدير تاريخ المورد",
                    FileName = "Supplier_History_" + (_supplier != null ? _supplier.SupplierCode : "Unknown") + "_" + DateTime.Now.ToString("yyyyMMdd")
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    var history = SupplierManager.GetSupplierTransactionHistory(_supplierID);
                    if (history != null)
                    {
                        // يمكن إضافة تصدير مخصص للموردين هنا
                        MessageBox.Show("تم تصدير التاريخ بنجاح", "نجح", 
                                      MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تصدير التاريخ: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// طباعة التاريخ
        /// </summary>
        private void btnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                // يمكن إضافة وظيفة الطباعة هنا
                MessageBox.Show("وظيفة الطباعة ستكون متاحة قريباً", "معلومات", 
                              MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في طباعة التاريخ: " + ex.Message, "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحديث البيانات
        /// </summary>
        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadData();
        }

        /// <summary>
        /// إغلاق النافذة
        /// </summary>
        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// عرض تفاصيل المعاملة
        /// </summary>
        private void dgvHistory_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                // TODO: الحصول على المعاملة المحددة وعرض التفاصيل
            }
        }

        #endregion

        private void SupplierHistoryForm_Load(object sender, EventArgs e)
        {

        }
    }


}
