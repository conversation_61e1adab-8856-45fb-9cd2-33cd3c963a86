[2025-07-26 05:38:41] [INFO] تم إنشاء المجلد: C:\Users\<USER>\Desktop\ph\PharmacyManagement\bin\Debug\Data
[2025-07-26 05:38:41] [INFO] تم إنشاء المجلد: C:\Users\<USER>\Desktop\ph\PharmacyManagement\bin\Debug\Backup
[2025-07-26 05:38:41] [INFO] تم إنشاء المجلد: C:\Users\<USER>\Desktop\ph\PharmacyManagement\bin\Debug\Reports
[2025-07-26 05:38:41] [INFO] تم إنشاء المجلد: C:\Users\<USER>\Desktop\ph\PharmacyManagement\bin\Debug\Database
[2025-07-26 05:38:41] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-26 05:38:41] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-26 05:38:41] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-26 05:38:41] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-26 05:38:41] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-26 05:38:41] [INFO] فحص قاعدة البيانات...
[2025-07-26 05:38:43] [INFO] محاولة إنشاء قاعدة البيانات...
[2025-07-26 05:38:43] [ERROR] ملف قاعدة البيانات الشاملة غير موجود: C:\Users\<USER>\Desktop\ph\PharmacyManagement\bin\Debug\Database\PharmacyDB_Complete.sql
[2025-07-26 05:38:43] [ERROR] سيتم إنشاء قاعدة بيانات أساسية بدلاً من ذلك
[2025-07-26 05:38:43] [INFO] إنشاء قاعدة بيانات أساسية...
[2025-07-26 05:38:43] [INFO] بدء تنفيذ سكريبت قاعدة البيانات...
[2025-07-26 05:38:43] [ERROR] خطأ في تنفيذ سكريبت قاعدة البيانات: An attempt to attach an auto-named database for file C:\Users\<USER>\Desktop\ph\PharmacyManagement\bin\Debug\PharmacyDB.mdf failed. A database with the same name exists, or specified file cannot be opened, or it is located on UNC share.
[2025-07-26 05:38:43] [ERROR] تفاصيل الخطأ:    عند System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   عند System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   عند System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   عند System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   عند System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   عند System.Data.SqlClient.SqlConnection.TryOpenInner(TaskCompletionSource`1 retry)
   عند System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   عند System.Data.SqlClient.SqlConnection.Open()
   عند PharmacyManagement.Classes.DatabaseHelper.ExecuteDatabaseScript(String script) في C:\Users\<USER>\Desktop\ph\PharmacyManagement\Classes\DatabaseHelper.cs:السطر 230
[2025-07-26 05:38:47] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-26 05:38:47] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-26 05:38:51] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-26 05:38:51] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-26 05:38:51] [INFO] تشغيل الواجهة الحديثة...
[2025-07-26 05:38:54] [INFO] تم فتح نافذة: DrugsForm
[2025-07-26 05:38:56] [ERROR] خطأ في الحصول على جميع الموردين: An attempt to attach an auto-named database for file C:\Users\<USER>\Desktop\ph\PharmacyManagement\bin\Debug\PharmacyDB.mdf failed. A database with the same name exists, or specified file cannot be opened, or it is located on UNC share.
[2025-07-26 05:38:56] [INFO] تم فتح نافذة: SuppliersForm
[2025-07-26 05:38:57] [ERROR] خطأ في الحصول على عناصر المخزون: An attempt to attach an auto-named database for file C:\Users\<USER>\Desktop\ph\PharmacyManagement\bin\Debug\PharmacyDB.mdf failed. A database with the same name exists, or specified file cannot be opened, or it is located on UNC share.
[2025-07-26 05:38:59] [INFO] تم فتح نافذة: InventoryForm
[2025-07-26 05:39:01] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 04:48:06] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 04:48:06] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 04:48:06] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 04:48:06] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 04:48:06] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 04:48:06] [INFO] فحص قاعدة البيانات...
[2025-07-27 04:48:08] [INFO] محاولة إنشاء قاعدة البيانات...
[2025-07-27 04:48:08] [ERROR] ملف قاعدة البيانات الشاملة غير موجود: C:\Users\<USER>\Desktop\ph\PharmacyManagement\bin\Debug\Database\PharmacyDB_Complete.sql
[2025-07-27 04:48:08] [ERROR] سيتم إنشاء قاعدة بيانات أساسية بدلاً من ذلك
[2025-07-27 04:48:08] [INFO] إنشاء قاعدة بيانات أساسية...
[2025-07-27 04:48:08] [INFO] بدء تنفيذ سكريبت قاعدة البيانات...
[2025-07-27 04:48:08] [ERROR] خطأ في تنفيذ سكريبت قاعدة البيانات: An attempt to attach an auto-named database for file C:\Users\<USER>\Desktop\ph\PharmacyManagement\bin\Debug\PharmacyDB.mdf failed. A database with the same name exists, or specified file cannot be opened, or it is located on UNC share.
[2025-07-27 04:48:08] [ERROR] تفاصيل الخطأ:    عند System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   عند System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   عند System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   عند System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   عند System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   عند System.Data.SqlClient.SqlConnection.TryOpenInner(TaskCompletionSource`1 retry)
   عند System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   عند System.Data.SqlClient.SqlConnection.Open()
   عند PharmacyManagement.Classes.DatabaseHelper.ExecuteDatabaseScript(String script) في C:\Users\<USER>\Desktop\ph\PharmacyManagement\Classes\DatabaseHelper.cs:السطر 230
[2025-07-27 04:48:14] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 04:48:14] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 04:48:20] [INFO] تم حفظ الإعداد UseModernInterface = False
[2025-07-27 04:48:20] [INFO] تم اختيار الواجهة: التقليدية
[2025-07-27 04:48:20] [INFO] تشغيل الواجهة التقليدية...
[2025-07-27 04:48:22] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 04:48:25] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 04:48:25] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 04:48:25] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 04:48:25] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 04:48:25] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 04:48:25] [INFO] فحص قاعدة البيانات...
[2025-07-27 04:48:25] [INFO] محاولة إنشاء قاعدة البيانات...
[2025-07-27 04:48:25] [ERROR] ملف قاعدة البيانات الشاملة غير موجود: C:\Users\<USER>\Desktop\ph\PharmacyManagement\bin\Debug\Database\PharmacyDB_Complete.sql
[2025-07-27 04:48:25] [ERROR] سيتم إنشاء قاعدة بيانات أساسية بدلاً من ذلك
[2025-07-27 04:48:25] [INFO] إنشاء قاعدة بيانات أساسية...
[2025-07-27 04:48:25] [INFO] بدء تنفيذ سكريبت قاعدة البيانات...
[2025-07-27 04:48:25] [ERROR] خطأ في تنفيذ سكريبت قاعدة البيانات: An attempt to attach an auto-named database for file C:\Users\<USER>\Desktop\ph\PharmacyManagement\bin\Debug\PharmacyDB.mdf failed. A database with the same name exists, or specified file cannot be opened, or it is located on UNC share.
[2025-07-27 04:48:25] [ERROR] تفاصيل الخطأ:    عند System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   عند System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   عند System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   عند System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   عند System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   عند System.Data.SqlClient.SqlConnection.TryOpenInner(TaskCompletionSource`1 retry)
   عند System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   عند System.Data.SqlClient.SqlConnection.Open()
   عند PharmacyManagement.Classes.DatabaseHelper.ExecuteDatabaseScript(String script) في C:\Users\<USER>\Desktop\ph\PharmacyManagement\Classes\DatabaseHelper.cs:السطر 230
[2025-07-27 04:48:26] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 04:48:26] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 04:48:27] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 04:48:27] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 04:48:27] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 04:48:30] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 04:51:48] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 04:51:48] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 04:51:48] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 04:51:48] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 04:51:48] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 04:51:48] [INFO] فحص قاعدة البيانات...
[2025-07-27 04:51:48] [INFO] محاولة إنشاء قاعدة البيانات...
[2025-07-27 04:51:48] [ERROR] ملف قاعدة البيانات الشاملة غير موجود: C:\Users\<USER>\Desktop\ph\PharmacyManagement\bin\Debug\Database\PharmacyDB_Complete.sql
[2025-07-27 04:51:48] [ERROR] سيتم إنشاء قاعدة بيانات أساسية بدلاً من ذلك
[2025-07-27 04:51:48] [INFO] إنشاء قاعدة بيانات أساسية...
[2025-07-27 04:51:48] [INFO] بدء تنفيذ سكريبت قاعدة البيانات...
[2025-07-27 04:51:48] [ERROR] خطأ في تنفيذ سكريبت قاعدة البيانات: An attempt to attach an auto-named database for file C:\Users\<USER>\Desktop\ph\PharmacyManagement\bin\Debug\PharmacyDB.mdf failed. A database with the same name exists, or specified file cannot be opened, or it is located on UNC share.
[2025-07-27 04:51:48] [ERROR] تفاصيل الخطأ:    عند System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   عند System.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   عند System.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   عند System.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   عند System.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   عند System.Data.SqlClient.SqlConnection.TryOpenInner(TaskCompletionSource`1 retry)
   عند System.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry)
   عند System.Data.SqlClient.SqlConnection.Open()
   عند PharmacyManagement.Classes.DatabaseHelper.ExecuteDatabaseScript(String script) في C:\Users\<USER>\Desktop\ph\PharmacyManagement\Classes\DatabaseHelper.cs:السطر 230
[2025-07-27 04:51:50] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 04:51:50] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 04:51:52] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 04:51:52] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 04:51:52] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 04:52:02] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 04:52:23] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 04:52:23] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 04:52:23] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 04:52:23] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 04:52:23] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 04:52:23] [INFO] فحص قاعدة البيانات...
[2025-07-27 04:52:23] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 04:52:23] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 04:52:23] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 04:52:25] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 04:52:25] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 04:52:25] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 04:55:20] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 05:00:01] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 05:00:01] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 05:00:01] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 05:00:01] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 05:00:01] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 05:00:01] [INFO] فحص قاعدة البيانات...
[2025-07-27 05:00:02] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 05:00:02] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 05:00:02] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 05:00:03] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 05:00:03] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 05:00:03] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 05:00:06] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 05:00:26] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 05:00:26] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 05:00:26] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 05:00:26] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 05:00:26] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 05:00:26] [INFO] فحص قاعدة البيانات...
[2025-07-27 05:00:26] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 05:00:26] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 05:00:26] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 05:00:28] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 05:00:28] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 05:00:28] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 05:00:49] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 05:02:04] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 05:02:04] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 05:02:04] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 05:02:04] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 05:02:04] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 05:02:04] [INFO] فحص قاعدة البيانات...
[2025-07-27 05:02:04] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 05:02:04] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 05:02:04] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 05:02:06] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 05:02:06] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 05:02:06] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 05:03:04] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 05:03:04] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 05:03:04] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 05:03:04] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 05:03:04] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 05:03:04] [INFO] فحص قاعدة البيانات...
[2025-07-27 05:03:05] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 05:03:05] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 05:03:05] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 05:03:06] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 05:03:06] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 05:03:06] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 05:03:20] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 05:04:55] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 05:04:55] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 05:04:55] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 05:04:55] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 05:04:55] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 05:04:55] [INFO] فحص قاعدة البيانات...
[2025-07-27 05:04:55] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 05:04:56] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 05:04:56] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 05:04:57] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 05:04:57] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 05:04:57] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 05:05:06] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 05:05:25] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 05:05:25] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 05:05:25] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 05:05:25] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 05:05:25] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 05:05:25] [INFO] فحص قاعدة البيانات...
[2025-07-27 05:05:26] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 05:05:26] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 05:05:26] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 05:05:27] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 05:05:27] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 05:05:27] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 05:05:37] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 05:06:06] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 05:06:06] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 05:06:06] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 05:06:06] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 05:06:06] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 05:06:06] [INFO] فحص قاعدة البيانات...
[2025-07-27 05:06:06] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 05:06:06] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 05:06:06] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 05:06:07] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 05:06:07] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 05:06:07] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 05:06:17] [INFO] تم فتح نافذة: SettingsForm
[2025-07-27 05:06:18] [INFO] تم فتح نافذة: UsersForm
[2025-07-27 05:06:27] [ERROR] خطأ في البحث عن المستخدمين: CanAddUsers
[2025-07-27 05:06:33] [ERROR] خطأ في البحث عن المستخدمين: CanAddUsers
[2025-07-27 05:06:37] [ERROR] خطأ في البحث عن المستخدمين: CanAddUsers
[2025-07-27 05:06:49] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 05:10:45] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 05:10:45] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 05:10:45] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 05:10:45] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 05:10:45] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 05:10:45] [INFO] فحص قاعدة البيانات...
[2025-07-27 05:10:45] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 05:10:45] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 05:10:45] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 05:10:47] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 05:10:47] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 05:10:47] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 05:10:58] [INFO] تم فتح نافذة: UsersForm
[2025-07-27 06:32:44] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 06:32:44] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 06:32:44] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 06:32:44] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 06:32:44] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 06:32:44] [INFO] فحص قاعدة البيانات...
[2025-07-27 06:32:44] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 06:32:44] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 06:32:44] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 06:32:46] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 06:32:46] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 06:32:46] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 06:33:01] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 06:38:24] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 06:38:24] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 06:38:24] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 06:38:25] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 06:38:25] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 06:38:25] [INFO] فحص قاعدة البيانات...
[2025-07-27 06:38:25] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 06:38:25] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 06:38:25] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 06:38:26] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 06:38:26] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 06:38:26] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 06:40:49] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 06:40:49] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 06:40:49] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 06:40:49] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 06:40:49] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 06:40:49] [INFO] فحص قاعدة البيانات...
[2025-07-27 06:40:49] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 06:40:49] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 06:40:49] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 06:40:51] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 06:40:51] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 06:40:51] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 06:41:01] [INFO] تم فتح نافذة: DrugsForm
[2025-07-27 06:41:02] [INFO] تم فتح نافذة: UsersForm
[2025-07-27 06:41:05] [INFO] تم البحث عن المستخدمين بالمصطلح: a، النتائج: 5
[2025-07-27 06:41:17] [INFO] تم البحث عن المستخدمين بالمصطلح: ad، النتائج: 1
[2025-07-27 06:41:18] [INFO] تم البحث عن المستخدمين بالمصطلح: adm، النتائج: 1
[2025-07-27 06:41:28] [ERROR] خطأ في الحصول على صلاحيات المستخدم: Invalid column name 'CanView'.
Invalid column name 'CanAdd'.
Invalid column name 'CanEdit'.
Invalid column name 'CanDelete'.
[2025-07-27 06:41:37] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 06:43:16] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 06:43:16] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 06:43:16] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 06:43:16] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 06:43:16] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 06:43:16] [INFO] فحص قاعدة البيانات...
[2025-07-27 06:43:17] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 06:43:17] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 06:43:17] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 06:43:18] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 06:43:18] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 06:43:18] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 06:43:30] [INFO] تم فتح نافذة: UsersForm
[2025-07-27 06:43:33] [INFO] تم البحث عن المستخدمين بالمصطلح: ad، النتائج: 1
[2025-07-27 06:43:35] [INFO] تم البحث عن المستخدمين بالمصطلح: admin، النتائج: 1
[2025-07-27 06:43:37] [INFO] تم فتح نافذة تعديل مستخدم
[2025-07-27 06:44:04] [INFO] تم تعديل المستخدم: admin
[2025-07-27 06:44:08] [INFO] تم البحث عن المستخدمين بالمصطلح: ad، النتائج: 1
[2025-07-27 06:44:10] [ERROR] خطأ في الحصول على صلاحيات المستخدم: Invalid column name 'CanView'.
Invalid column name 'CanAdd'.
Invalid column name 'CanEdit'.
Invalid column name 'CanDelete'.
[2025-07-27 06:44:20] [ERROR] خطأ في الحصول على صلاحيات المستخدم: Invalid column name 'CanView'.
Invalid column name 'CanAdd'.
Invalid column name 'CanEdit'.
Invalid column name 'CanDelete'.
[2025-07-27 06:44:25] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 06:45:52] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 06:45:52] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 06:45:52] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 06:45:52] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 06:45:52] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 06:45:52] [INFO] فحص قاعدة البيانات...
[2025-07-27 06:45:52] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 06:45:52] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 06:45:52] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 06:45:53] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 06:45:53] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 06:45:53] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 06:46:01] [INFO] تم فتح نافذة: UsersForm
[2025-07-27 06:46:04] [INFO] تم البحث عن المستخدمين بالمصطلح: ad، النتائج: 1
[2025-07-27 06:46:07] [ERROR] خطأ في الحصول على صلاحيات المستخدم: Invalid column name 'CanView'.
Invalid column name 'CanAdd'.
Invalid column name 'CanEdit'.
Invalid column name 'CanDelete'.
[2025-07-27 06:46:15] [ERROR] خطأ في الحصول على صلاحيات المستخدم: Invalid column name 'CanView'.
Invalid column name 'CanAdd'.
Invalid column name 'CanEdit'.
Invalid column name 'CanDelete'.
[2025-07-27 06:46:20] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               [2025-07-27 16:23:22] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 16:23:22] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 16:23:22] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 16:23:23] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 16:23:23] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 16:23:23] [INFO] فحص قاعدة البيانات...
[2025-07-27 16:23:23] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 16:23:23] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 16:23:23] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 16:23:26] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 16:23:26] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 16:23:26] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 16:23:37] [INFO] تم فتح نافذة: UsersForm
[2025-07-27 16:23:41] [INFO] تم البحث عن المستخدمين بالمصطلح: a، النتائج: 5
[2025-07-27 16:23:42] [INFO] تم البحث عن المستخدمين بالمصطلح: ad، النتائج: 1
[2025-07-27 16:23:43] [INFO] تم البحث عن المستخدمين بالمصطلح: adm، النتائج: 1
[2025-07-27 16:23:46] [ERROR] خطأ في الحصول على صلاحيات المستخدم: Invalid column name 'CanView'.
Invalid column name 'CanAdd'.
Invalid column name 'CanEdit'.
Invalid column name 'CanDelete'.
[2025-07-27 16:23:57] [INFO] تم فتح نافذة إضافة مستخدم
[2025-07-27 16:24:05] [INFO] تم فتح نافذة تعديل مستخدم
[2025-07-27 16:24:14] [INFO] تم فتح نافذة: SettingsForm
[2025-07-27 16:24:22] [ERROR] خطأ في الحصول على جميع الموردين: Notes
[2025-07-27 16:24:22] [INFO] تم فتح نافذة: SuppliersForm
[2025-07-27 16:24:23] [ERROR] InvoiceManager.GenerateInvoiceNumber - Invalid object name 'Invoices'.
[2025-07-27 16:24:23] [INFO] تم فتح نافذة: SalesForm
[2025-07-27 16:24:24] [ERROR] خطأ في الحصول على جميع الموردين: Notes
[2025-07-27 16:24:24] [INFO] تم فتح نافذة: SuppliersForm
[2025-07-27 16:24:30] [ERROR] خطأ في البحث في الموردين: Notes
[2025-07-27 16:24:32] [ERROR] خطأ في البحث في الموردين: Notes
[2025-07-27 16:24:34] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 16:28:05] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 16:28:05] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 16:28:05] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 16:28:05] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 16:28:05] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 16:28:05] [INFO] فحص قاعدة البيانات...
[2025-07-27 16:28:05] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 16:28:06] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 16:28:06] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 16:28:07] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 16:28:07] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 16:28:07] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 16:28:14] [INFO] تم فتح نافذة: UsersForm
[2025-07-27 16:28:15] [ERROR] خطأ في الحصول على صلاحيات المستخدم: Invalid column name 'CanView'.
Invalid column name 'CanAdd'.
Invalid column name 'CanEdit'.
Invalid column name 'CanDelete'.
[2025-07-27 16:29:24] [ERROR] خطأ في الحصول على صلاحيات المستخدم: Invalid column name 'CanView'.
Invalid column name 'CanAdd'.
Invalid column name 'CanEdit'.
Invalid column name 'CanDelete'.
[2025-07-27 16:29:31] [ERROR] خطأ في الحصول على صلاحيات المستخدم: Invalid column name 'CanView'.
Invalid column name 'CanAdd'.
Invalid column name 'CanEdit'.
Invalid column name 'CanDelete'.
[2025-07-27 16:31:19] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 16:31:19] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 16:31:19] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 16:31:19] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 16:31:19] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 16:31:19] [INFO] فحص قاعدة البيانات...
[2025-07-27 16:31:19] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 16:31:20] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 16:31:20] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 16:31:21] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 16:31:21] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 16:31:21] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 16:31:35] [INFO] تم فتح نافذة: UsersForm
[2025-07-27 16:32:16] [ERROR] خطأ في الحصول على صلاحيات المستخدم: Invalid column name 'CanView'.
Invalid column name 'CanAdd'.
Invalid column name 'CanEdit'.
Invalid column name 'CanDelete'.
[2025-07-27 16:32:20] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 16:32:29] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 16:32:29] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 16:32:29] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 16:32:29] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 16:32:29] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 16:32:29] [INFO] فحص قاعدة البيانات...
[2025-07-27 16:32:29] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 16:32:29] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 16:32:29] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 16:32:31] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 16:32:31] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 16:32:31] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 16:32:41] [INFO] تم فتح نافذة: UsersForm
[2025-07-27 16:32:51] [ERROR] خطأ في الحصول على صلاحيات المستخدم: Invalid column name 'CanView'.
Invalid column name 'CanAdd'.
Invalid column name 'CanEdit'.
Invalid column name 'CanDelete'.
[2025-07-27 16:36:37] [ERROR] خطأ في الحصول على صلاحيات المستخدم: Invalid column name 'CanView'.
Invalid column name 'CanAdd'.
Invalid column name 'CanEdit'.
Invalid column name 'CanDelete'.
[2025-07-27 16:37:12] [ERROR] خطأ في الحصول على صلاحيات المستخدم: Invalid column name 'CanView'.
Invalid column name 'CanAdd'.
Invalid column name 'CanEdit'.
Invalid column name 'CanDelete'.
[2025-07-27 16:37:37] [ERROR] خطأ في الحصول على صلاحيات المستخدم: Invalid column name 'CanView'.
Invalid column name 'CanAdd'.
Invalid column name 'CanEdit'.
Invalid column name 'CanDelete'.
[2025-07-27 16:38:24] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 16:38:36] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 16:38:36] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 16:38:36] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 16:38:36] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 16:38:37] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 16:38:37] [INFO] فحص قاعدة البيانات...
[2025-07-27 16:38:37] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 16:38:37] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 16:38:37] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 16:38:38] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 16:38:38] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 16:38:38] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 16:38:46] [INFO] تم فتح نافذة: UsersForm
[2025-07-27 16:38:51] [INFO] تم الحصول على 0 صلاحية للمستخدم 4
[2025-07-27 16:39:10] [INFO] تم الحصول على 0 صلاحية للمستخدم 4
[2025-07-27 16:39:45] [INFO] تم فتح نافذة: DrugsForm
[2025-07-27 16:39:52] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 17:50:03] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 17:50:03] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 17:50:04] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 17:50:04] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 17:50:04] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 17:50:04] [INFO] فحص قاعدة البيانات...
[2025-07-27 17:50:04] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 17:50:04] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 17:50:04] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 17:50:06] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 17:50:06] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 17:50:06] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 17:50:21] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 17:55:38] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 17:55:38] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 17:55:38] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 17:55:38] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 17:55:38] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 17:55:38] [INFO] فحص قاعدة البيانات...
[2025-07-27 17:55:38] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 17:55:38] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 17:55:38] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 17:55:39] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 17:55:39] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 17:55:39] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 17:56:02] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 17:57:12] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 17:57:12] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 17:57:12] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 17:57:12] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 17:57:12] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 17:57:12] [INFO] فحص قاعدة البيانات...
[2025-07-27 17:57:13] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 17:57:13] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 17:57:13] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 17:57:14] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 17:57:14] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 17:57:14] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 17:57:23] [INFO] تم فتح نافذة: UsersForm
[2025-07-27 17:57:25] [INFO] تم البحث عن المستخدمين بالمصطلح: a، النتائج: 5
[2025-07-27 17:57:29] [INFO] تم البحث عن المستخدمين بالمصطلح: admi، النتائج: 1
[2025-07-27 17:57:30] [INFO] تم البحث عن المستخدمين بالمصطلح: admin، النتائج: 1
[2025-07-27 17:57:31] [INFO] تم الحصول على 0 صلاحية للمستخدم 1
[2025-07-27 17:57:36] [INFO] تم فتح نافذة إضافة مستخدم
[2025-07-27 17:58:11] [INFO] تم إضافة المستخدم: mahmood
[2025-07-27 17:58:16] [INFO] تم فتح نافذة: UsersForm
[2025-07-27 17:58:20] [INFO] تم فتح نافذة: UsersForm
[2025-07-27 17:58:21] [INFO] تم استرجاع 3 فرع نشط
[2025-07-27 17:58:21] [ERROR] خطأ في الحصول على المخازن: Invalid column name 'KeeperID'.
[2025-07-27 17:58:21] [INFO] تم فتح نافذة: WarehouseForm
[2025-07-27 17:58:22] [INFO] تم فتح نافذة: UsersForm
[2025-07-27 17:58:27] [INFO] تم البحث عن المستخدمين بالمصطلح: m، النتائج: 3
[2025-07-27 17:58:30] [INFO] تم البحث عن المستخدمين بالمصطلح: mah، النتائج: 0
[2025-07-27 17:59:22] [INFO] تم فتح نافذة: DrugsForm
[2025-07-27 17:59:23] [ERROR] خطأ في تنفيذ استعلام العملاء: Invalid column name 'TotalAmount'.
[2025-07-27 17:59:23] [INFO] تم استرجاع 0 عميل
[2025-07-27 17:59:23] [INFO] تم فتح نافذة: CustomersForm
[2025-07-27 17:59:24] [ERROR] خطأ في الحصول على جميع الموردين: Notes
[2025-07-27 17:59:24] [INFO] تم فتح نافذة: SuppliersForm
[2025-07-27 17:59:26] [ERROR] InvoiceManager.GenerateInvoiceNumber - Invalid object name 'Invoices'.
[2025-07-27 17:59:26] [INFO] تم فتح نافذة: SalesForm
[2025-07-27 17:59:27] [ERROR] خطأ في الحصول على عناصر المخزون: Invalid object name 'Categories'.
[2025-07-27 17:59:29] [INFO] تم فتح نافذة: InventoryForm
[2025-07-27 17:59:30] [INFO] تم فتح نافذة: ReportsForm
[2025-07-27 17:59:31] [ERROR] خطأ في الحصول على الملخص المالي: Invalid object name 'Invoices'.
[2025-07-27 17:59:31] [ERROR] خطأ في الحصول على المعاملات المالية: Invalid object name 'Invoices'.
[2025-07-27 17:59:31] [ERROR] خطأ في الحصول على الملخص المالي: Invalid object name 'Invoices'.
[2025-07-27 17:59:31] [ERROR] خطأ في الحصول على المعاملات المالية: Invalid object name 'Invoices'.
[2025-07-27 17:59:31] [ERROR] خطأ في الحصول على الملخص المالي: Invalid object name 'Invoices'.
[2025-07-27 17:59:31] [ERROR] خطأ في الحصول على المعاملات المالية: Invalid object name 'Invoices'.
[2025-07-27 17:59:31] [INFO] تم فتح نافذة: FinancialForm
[2025-07-27 17:59:32] [INFO] تم فتح نافذة: BranchForm
[2025-07-27 17:59:33] [ERROR] خطأ في الحصول على الملخص المالي: Invalid object name 'Invoices'.
[2025-07-27 17:59:33] [ERROR] خطأ في الحصول على المعاملات المالية: Invalid object name 'Invoices'.
[2025-07-27 17:59:33] [ERROR] خطأ في الحصول على الملخص المالي: Invalid object name 'Invoices'.
[2025-07-27 17:59:33] [ERROR] خطأ في الحصول على المعاملات المالية: Invalid object name 'Invoices'.
[2025-07-27 17:59:33] [ERROR] خطأ في الحصول على الملخص المالي: Invalid object name 'Invoices'.
[2025-07-27 17:59:33] [ERROR] خطأ في الحصول على المعاملات المالية: Invalid object name 'Invoices'.
[2025-07-27 17:59:33] [INFO] تم فتح نافذة: FinancialForm
[2025-07-27 17:59:35] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 18:05:41] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 18:05:41] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 18:05:41] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 18:05:41] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 18:05:42] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 18:05:42] [INFO] فحص قاعدة البيانات...
[2025-07-27 18:05:42] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 18:05:42] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 18:05:42] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 18:05:43] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 18:05:43] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 18:05:43] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 18:05:49] [INFO] تم فتح نافذة: UsersForm
[2025-07-27 18:05:51] [INFO] تم فتح نافذة إضافة مستخدم
[2025-07-27 18:06:17] [INFO] تم إضافة المستخدم: mahmood
[2025-07-27 18:06:23] [INFO] تم فتح نافذة تعديل مستخدم
[2025-07-27 18:06:30] [INFO] تم الحصول على 0 صلاحية للمستخدم 6
[2025-07-27 18:07:22] [ERROR] خطأ في تحديث صلاحيات المستخدم: Must declare the scalar variable "@CanView".
[2025-07-27 18:07:30] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 18:08:09] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 18:08:09] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 18:08:09] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 18:08:09] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 18:08:09] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 18:08:09] [INFO] فحص قاعدة البيانات...
[2025-07-27 18:08:09] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 18:08:09] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 18:08:09] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 18:08:10] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 18:08:10] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 18:08:10] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 18:08:21] [INFO] تم فتح نافذة: UsersForm
[2025-07-27 18:08:25] [INFO] تم فتح نافذة تعديل مستخدم
[2025-07-27 18:08:44] [INFO] تم تعديل المستخدم: admin
[2025-07-27 18:09:07] [INFO] تم فتح نافذة: DrugsForm
[2025-07-27 18:10:04] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 18:16:17] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 18:16:17] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 18:16:17] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 18:16:17] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 18:16:17] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 18:16:17] [INFO] فحص قاعدة البيانات...
[2025-07-27 18:16:17] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 18:16:17] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 18:16:17] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 18:16:18] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 18:16:18] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 18:16:18] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 18:16:26] [INFO] تم فتح نافذة: DrugsForm
[2025-07-27 18:17:32] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 18:17:32] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 18:17:32] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 18:17:32] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 18:17:32] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 18:17:32] [INFO] فحص قاعدة البيانات...
[2025-07-27 18:17:32] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 18:17:32] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 18:17:32] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 18:17:34] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 18:17:34] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 18:17:34] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 18:18:57] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 18:18:57] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 18:18:57] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 18:18:57] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 18:18:58] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 18:18:58] [INFO] فحص قاعدة البيانات...
[2025-07-27 18:18:58] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 18:18:58] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 18:18:58] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 18:18:59] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 18:18:59] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 18:18:59] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 18:19:09] [INFO] تم فتح نافذة: DrugsForm
[2025-07-27 18:35:00] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 18:35:00] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 18:35:00] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 18:35:00] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 18:35:00] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 18:35:00] [INFO] فحص قاعدة البيانات...
[2025-07-27 18:35:00] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 18:35:00] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 18:35:00] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 18:35:02] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 18:35:02] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 18:35:02] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 18:35:13] [INFO] تم فتح نافذة: DrugsForm
[2025-07-27 18:36:39] [ERROR] خطأ في تنفيذ استعلام العملاء: Invalid column name 'TotalAmount'.
[2025-07-27 18:36:39] [INFO] تم استرجاع 0 عميل
[2025-07-27 18:36:39] [INFO] تم فتح نافذة: CustomersForm
[2025-07-27 18:36:58] [ERROR] خطأ في تنفيذ استعلام العملاء: Invalid column name 'NationalID'.
Invalid column name 'TotalAmount'.
[2025-07-27 18:36:58] [INFO] تم العثور على 0 عميل مطابق للبحث: CUST001
[2025-07-27 18:37:01] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 18:39:39] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 18:39:39] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 18:39:39] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 18:39:39] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 18:39:39] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 18:39:39] [INFO] فحص قاعدة البيانات...
[2025-07-27 18:39:39] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 18:39:39] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 18:39:39] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 18:39:41] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 18:39:41] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 18:39:41] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 18:39:50] [INFO] تم فتح نافذة: DrugsForm
[2025-07-27 18:39:53] [ERROR] خطأ في الحصول على جميع الموردين: Notes
[2025-07-27 18:39:53] [INFO] تم فتح نافذة: SuppliersForm
[2025-07-27 18:44:15] [ERROR] خطأ في البحث في الموردين: Notes
[2025-07-27 18:44:43] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 18:49:39] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 18:49:39] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 18:49:39] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 18:49:39] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 18:49:39] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 18:49:39] [INFO] فحص قاعدة البيانات...
[2025-07-27 18:49:39] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 18:49:39] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 18:49:39] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 18:49:41] [INFO] تشغيل الواجهة التقليدية...
[2025-07-27 18:49:43] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 18:51:08] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 18:51:08] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 18:51:08] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 18:51:08] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 18:51:08] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 18:51:08] [INFO] فحص قاعدة البيانات...
[2025-07-27 18:51:08] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 18:51:08] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 18:51:08] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 18:51:09] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 18:51:09] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 18:51:09] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 18:51:18] [ERROR] خطأ في تنفيذ استعلام العملاء: Invalid column name 'TotalAmount'.
[2025-07-27 18:51:18] [INFO] تم استرجاع 0 عميل
[2025-07-27 18:51:18] [INFO] تم فتح نافذة: CustomersForm
[2025-07-27 18:51:31] [ERROR] خطأ في تنفيذ استعلام العملاء: Invalid column name 'TotalAmount'.
[2025-07-27 18:51:32] [INFO] تم العثور على 0 عميل مطابق للبحث: CUST001
[2025-07-27 18:52:50] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 18:52:50] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 18:52:50] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 18:52:50] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 18:52:50] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 18:52:50] [INFO] فحص قاعدة البيانات...
[2025-07-27 18:52:50] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 18:52:50] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 18:52:50] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 18:52:51] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 18:52:51] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 18:52:51] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 18:52:58] [ERROR] خطأ في تنفيذ استعلام العملاء: Invalid column name 'TotalAmount'.
[2025-07-27 18:52:58] [INFO] تم استرجاع 0 عميل
[2025-07-27 18:52:58] [INFO] تم فتح نافذة: CustomersForm
[2025-07-27 18:53:27] [ERROR] خطأ في تنفيذ استعلام العملاء: Invalid column name 'TotalAmount'.
[2025-07-27 18:53:30] [INFO] تم العثور على 0 عميل مطابق للبحث: أحمد محمد علي
[2025-07-27 18:54:22] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 19:03:04] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 19:03:04] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 19:03:04] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 19:03:04] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 19:03:04] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 19:03:04] [INFO] فحص قاعدة البيانات...
[2025-07-27 19:03:04] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 19:03:04] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 19:03:04] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 19:03:05] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 19:03:05] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 19:03:05] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 19:03:27] [ERROR] خطأ في تنفيذ استعلام العملاء: Invalid column name 'TotalAmount'.
[2025-07-27 19:03:27] [INFO] تم استرجاع 0 عميل
[2025-07-27 19:03:27] [INFO] تم فتح نافذة: CustomersForm
[2025-07-27 19:03:35] [ERROR] خطأ في تنفيذ استعلام العملاء: Invalid column name 'TotalAmount'.
[2025-07-27 19:03:35] [INFO] تم العثور على 0 عميل مطابق للبحث: أحمد محمد علي
[2025-07-27 19:03:37] [INFO] تم فتح نافذة إضافة عميل
[2025-07-27 19:03:44] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 19:06:23] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 19:06:23] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 19:06:23] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 19:06:23] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 19:06:23] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 19:06:23] [INFO] فحص قاعدة البيانات...
[2025-07-27 19:06:23] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 19:06:23] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 19:06:23] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 19:06:24] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 19:06:24] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 19:06:24] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 19:06:33] [ERROR] خطأ في تنفيذ استعلام العملاء: Invalid column name 'TotalAmount'.
[2025-07-27 19:06:33] [INFO] تم استرجاع 0 عميل
[2025-07-27 19:06:33] [INFO] تم فتح نافذة: CustomersForm
[2025-07-27 19:06:40] [ERROR] خطأ في تنفيذ استعلام العملاء: Invalid column name 'TotalAmount'.
[2025-07-27 19:06:40] [INFO] تم العثور على 0 عميل مطابق للبحث: أحمد محمد علي
[2025-07-27 19:06:45] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 19:15:47] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 19:15:47] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 19:15:48] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 19:15:48] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 19:15:48] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 19:15:48] [INFO] فحص قاعدة البيانات...
[2025-07-27 19:15:48] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 19:15:48] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 19:15:48] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 19:15:49] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 19:15:49] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 19:15:49] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 19:16:28] [INFO] تم فتح نافذة: DrugsForm
[2025-07-27 19:16:38] [ERROR] خطأ في تنفيذ استعلام العملاء: Invalid column name 'TotalAmount'.
[2025-07-27 19:16:38] [ERROR] Stack Trace:    عند System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   عند System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   عند System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   عند System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   عند System.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   عند System.Data.SqlClient.SqlDataReader.get_MetaData()
   عند System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   عند System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   عند System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry)
   عند System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   عند System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   عند System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   عند PharmacyManagement.Classes.DatabaseHelper.ExecuteReader(String query, SqlParameter[] parameters) في C:\Users\<USER>\Desktop\ph\PharmacyManagement\Classes\DatabaseHelper.cs:السطر 508
   عند PharmacyManagement.Classes.CustomerManager.ExecuteCustomerQuery(String query, SqlParameter[] parameters) في C:\Users\<USER>\Desktop\ph\PharmacyManagement\Classes\CustomerManager.cs:السطر 553
[2025-07-27 19:16:38] [ERROR] Query: 
                    SELECT c.*, 
                           ISNULL(c.CreditLimit, 0) as CreditLimit,
                           ISNULL(c.CurrentBalance, 0) as CurrentBalance,
                           ISNULL((SELECT SUM(TotalAmount) FROM SalesInvoices WHERE CustomerID = c.CustomerID), 0) as TotalPurchases
                    FROM Customers c
                    ORDER BY c.CustomerCode
[2025-07-27 19:16:38] [INFO] تم استرجاع 0 عميل
[2025-07-27 19:16:38] [INFO] تم فتح نافذة: CustomersForm
[2025-07-27 19:17:18] [ERROR] خطأ في تنفيذ استعلام العملاء: Invalid column name 'TotalAmount'.
[2025-07-27 19:17:18] [ERROR] Stack Trace:    عند System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   عند System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   عند System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   عند System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   عند System.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   عند System.Data.SqlClient.SqlDataReader.get_MetaData()
   عند System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   عند System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   عند System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry)
   عند System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   عند System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   عند System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   عند PharmacyManagement.Classes.DatabaseHelper.ExecuteReader(String query, SqlParameter[] parameters) في C:\Users\<USER>\Desktop\ph\PharmacyManagement\Classes\DatabaseHelper.cs:السطر 508
   عند PharmacyManagement.Classes.CustomerManager.ExecuteCustomerQuery(String query, SqlParameter[] parameters) في C:\Users\<USER>\Desktop\ph\PharmacyManagement\Classes\CustomerManager.cs:السطر 553
[2025-07-27 19:17:18] [ERROR] Query: 
                    SELECT c.*, 
                           ISNULL(c.CreditLimit, 0) as CreditLimit,
                           ISNULL(c.CurrentBalance, 0) as CurrentBalance,
                           ISNULL((SELECT SUM(TotalAmount) FROM SalesInvoices WHERE CustomerID = c.CustomerID), 0) as TotalPurchases
                    FROM Customers c
                    WHERE (c.CustomerCode LIKE @SearchTerm OR
                           c.CustomerName LIKE @SearchTerm OR
                           c.Phone LIKE @SearchTerm OR
                           c.Mobile LIKE @SearchTerm OR
                           c.Email LIKE @SearchTerm OR
                           c.Address LIKE @SearchTerm)
                    AND c.IsActive = 1
                    ORDER BY c.CustomerCode
[2025-07-27 19:17:18] [INFO] تم العثور على 0 عميل مطابق للبحث: أحمد محمد علي
[2025-07-27 19:17:20] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 19:21:25] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 19:21:25] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 19:21:25] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 19:21:25] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 19:21:25] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 19:21:25] [INFO] فحص قاعدة البيانات...
[2025-07-27 19:21:25] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 19:21:25] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 19:21:25] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 19:21:26] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 19:21:26] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 19:21:26] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 19:21:35] [INFO] تم استرجاع 8 عميل
[2025-07-27 19:21:35] [INFO] تم فتح نافذة: CustomersForm
[2025-07-27 19:21:41] [INFO] تم فتح نافذة إضافة عميل
[2025-07-27 19:21:49] [INFO] تم فتح نافذة إضافة عميل
[2025-07-27 19:21:55] [INFO] تم فتح نافذة تعديل عميل
[2025-07-27 19:22:30] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 19:27:17] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 19:27:17] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 19:27:17] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 19:27:17] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 19:27:17] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 19:27:17] [INFO] فحص قاعدة البيانات...
[2025-07-27 19:27:18] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 19:27:18] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 19:27:18] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 19:27:19] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 19:27:19] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 19:27:19] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 19:27:28] [INFO] تم استرجاع 8 عميل
[2025-07-27 19:27:28] [INFO] تم فتح نافذة: CustomersForm
[2025-07-27 19:27:31] [ERROR] خطأ في إنشاء كود العميل: Conversion failed when converting the nvarchar value 'UST001' to data type int.
[2025-07-27 19:27:31] [INFO] تم فتح نافذة إضافة عميل
[2025-07-27 19:27:39] [INFO] تم فتح نافذة تعديل عميل
[2025-07-27 19:27:46] [ERROR] InvoiceManager.GenerateInvoiceNumber - Invalid object name 'Invoices'.
[2025-07-27 19:27:46] [INFO] تم فتح نافذة: SalesForm
[2025-07-27 19:29:51] [ERROR] InvoiceManager.GenerateInvoiceNumber - Invalid object name 'Invoices'.
[2025-07-27 19:30:00] [ERROR] InvoiceManager.GenerateInvoiceNumber - Invalid object name 'Invoices'.
[2025-07-27 19:30:00] [INFO] تم فتح نافذة: SalesForm
[2025-07-27 19:30:01] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 19:33:27] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 19:33:27] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 19:33:27] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 19:33:27] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 19:33:27] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 19:33:27] [INFO] فحص قاعدة البيانات...
[2025-07-27 19:33:27] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 19:33:27] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 19:33:27] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 19:33:28] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 19:33:28] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 19:33:28] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 19:33:37] [ERROR] خطأ في الحصول على جميع الموردين: Notes
[2025-07-27 19:33:37] [INFO] تم فتح نافذة: SuppliersForm
[2025-07-27 19:33:38] [ERROR] InvoiceManager.GenerateInvoiceNumber - Invalid object name 'Invoices'.
[2025-07-27 19:33:38] [INFO] تم فتح نافذة: SalesForm
[2025-07-27 19:38:08] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 19:38:08] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 19:38:08] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 19:38:08] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 19:38:08] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 19:38:08] [INFO] فحص قاعدة البيانات...
[2025-07-27 19:38:08] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 19:38:08] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 19:38:08] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 19:38:09] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 19:38:09] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 19:38:09] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 19:38:18] [ERROR] InvoiceManager.GenerateInvoiceNumber - Invalid object name 'Invoices'.
[2025-07-27 19:38:18] [INFO] تم فتح نافذة: SalesForm
[2025-07-27 19:39:02] [ERROR] خطأ في إنشاء فاتورة المبيعات: The parameterized query '(@InvoiceNumber nvarchar(17),@InvoiceDate datetime,@InvoiceType ' expects the parameter '@PaymentMethod', which was not supplied.
[2025-07-27 19:42:30] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 19:46:05] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 19:46:05] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 19:46:05] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 19:46:05] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 19:46:05] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 19:46:05] [INFO] فحص قاعدة البيانات...
[2025-07-27 19:46:05] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 19:46:05] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 19:46:05] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 19:46:06] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 19:46:06] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 19:46:06] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 19:46:15] [ERROR] InvoiceManager.GenerateInvoiceNumber - Invalid object name 'Invoices'.
[2025-07-27 19:46:15] [INFO] تم فتح نافذة: SalesForm
[2025-07-27 19:46:24] [INFO] تم فتح نافذة: DrugsForm
[2025-07-27 19:47:02] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 19:47:33] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 19:47:33] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 19:47:34] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 19:47:34] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 19:47:34] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 19:47:34] [INFO] فحص قاعدة البيانات...
[2025-07-27 19:47:34] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 19:47:34] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 19:47:34] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 19:47:49] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 19:47:49] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 19:47:49] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 19:47:58] [INFO] تم فتح نافذة: DrugsForm
[2025-07-27 19:48:00] [ERROR] InvoiceManager.GenerateInvoiceNumber - Invalid object name 'Invoices'.
[2025-07-27 19:48:00] [INFO] تم فتح نافذة: SalesForm
[2025-07-27 19:48:45] [INFO] تم فتح نافذة: DrugsForm
[2025-07-27 19:48:48] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 19:53:51] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 19:53:51] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 19:53:51] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 19:53:51] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 19:53:51] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 19:53:51] [INFO] فحص قاعدة البيانات...
[2025-07-27 19:53:51] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 19:53:51] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 19:53:51] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 19:53:53] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 19:53:53] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 19:53:53] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 19:54:01] [ERROR] InvoiceManager.GenerateInvoiceNumber - Invalid object name 'Invoices'.
[2025-07-27 19:54:01] [INFO] تم فتح نافذة: SalesForm
[2025-07-27 19:54:09] [INFO] تم فتح نافذة: DrugsForm
[2025-07-27 19:54:20] [ERROR] خطأ في الحصول على جميع الموردين: Notes
[2025-07-27 19:54:20] [INFO] تم فتح نافذة: SuppliersForm
[2025-07-27 19:54:21] [ERROR] InvoiceManager.GenerateInvoiceNumber - Invalid object name 'Invoices'.
[2025-07-27 19:54:21] [INFO] تم فتح نافذة: SalesForm
[2025-07-27 19:58:46] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 19:58:46] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 19:58:46] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 19:58:46] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 19:58:46] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 19:58:46] [INFO] فحص قاعدة البيانات...
[2025-07-27 19:58:46] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 19:58:46] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 19:58:46] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 19:58:48] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 19:58:48] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 19:58:48] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 19:58:58] [ERROR] خطأ في توليد رقم الفاتورة: Invalid object name 'Invoices'.
[2025-07-27 19:58:58] [INFO] تم فتح نافذة: SalesForm
[2025-07-27 19:59:10] [ERROR] خطأ في إنشاء فاتورة المبيعات: Invalid object name 'Invoices'.
[2025-07-27 20:00:12] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 20:04:38] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 20:04:38] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 20:04:38] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 20:04:38] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 20:04:38] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 20:04:38] [INFO] فحص قاعدة البيانات...
[2025-07-27 20:04:38] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 20:04:38] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 20:04:38] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 20:04:39] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 20:04:39] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 20:04:39] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 20:04:49] [INFO] تم فتح نافذة: SalesForm
[2025-07-27 20:05:05] [ERROR] خطأ في إنشاء فاتورة المبيعات: Invalid column name 'TotalAmount'.
[2025-07-27 20:10:35] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 20:10:35] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 20:10:36] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 20:10:36] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 20:10:36] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 20:10:36] [INFO] فحص قاعدة البيانات...
[2025-07-27 20:10:36] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 20:10:36] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 20:10:36] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 20:10:37] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 20:10:37] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 20:10:37] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 20:10:45] [INFO] تم فتح نافذة: SalesForm
[2025-07-27 20:11:02] [ERROR] خطأ في إنشاء فاتورة المبيعات: Invalid column name 'TotalAmount'.
[2025-07-27 20:11:46] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 20:15:32] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 20:15:32] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 20:15:32] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 20:15:32] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 20:15:32] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 20:15:32] [INFO] فحص قاعدة البيانات...
[2025-07-27 20:15:32] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 20:15:33] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 20:15:33] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 20:15:34] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 20:15:34] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 20:15:34] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 20:15:43] [INFO] تم فتح نافذة: SalesForm
[2025-07-27 20:15:56] [ERROR] خطأ في إنشاء فاتورة المبيعات: Invalid column name 'TotalAmount'.
[2025-07-27 20:18:34] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 20:18:34] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 20:18:34] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 20:18:34] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 20:18:34] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 20:18:34] [INFO] فحص قاعدة البيانات...
[2025-07-27 20:18:35] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 20:18:35] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 20:18:35] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 20:18:36] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 20:18:36] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 20:18:36] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 20:18:43] [INFO] تم فتح نافذة: SalesForm
[2025-07-27 20:18:50] [INFO] تم إنشاء فاتورة مبيعات: INV-2025-000001
[2025-07-27 20:19:34] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 20:24:45] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 20:24:45] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 20:24:45] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 20:24:45] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 20:24:45] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 20:24:45] [INFO] فحص قاعدة البيانات...
[2025-07-27 20:24:46] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 20:24:46] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 20:24:46] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 20:24:47] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 20:24:47] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 20:24:47] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 20:24:59] [INFO] تم فتح نافذة: SalesForm
[2025-07-27 20:25:18] [ERROR] خطأ في حفظ فاتورة المبيعات: Invalid column name 'ModifiedDate'.
[2025-07-27 20:25:18] [ERROR] خطأ في إنشاء فاتورة المبيعات: Invalid column name 'ModifiedDate'.
[2025-07-27 20:28:15] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 20:28:15] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 20:28:15] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 20:28:15] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 20:28:15] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 20:28:15] [INFO] فحص قاعدة البيانات...
[2025-07-27 20:28:15] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 20:28:16] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 20:28:16] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 20:28:17] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 20:28:17] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 20:28:17] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 20:28:26] [INFO] تم فتح نافذة: SalesForm
[2025-07-27 20:28:34] [INFO] تم إنشاء فاتورة مبيعات: INV-2025-000002
[2025-07-27 20:30:11] [INFO] تم إنشاء فاتورة مبيعات: INV-2025-000003
[2025-07-27 20:31:07] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 20:34:00] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 20:34:00] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 20:34:00] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 20:34:00] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 20:34:00] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 20:34:00] [INFO] فحص قاعدة البيانات...
[2025-07-27 20:34:00] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 20:34:00] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 20:34:00] [INFO] تم إضافة عميل جديد: GENERAL - عميل عام
[2025-07-27 20:34:00] [INFO] تم إنشاء العميل العام برقم: 9
[2025-07-27 20:34:00] [INFO] تم التأكد من وجود العميل العام برقم: 9
[2025-07-27 20:34:00] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 20:34:01] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 20:34:01] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 20:34:01] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 20:34:10] [INFO] تم فتح نافذة: SalesForm
[2025-07-27 20:34:14] [INFO] تم استرجاع 9 عميل
[2025-07-27 20:34:14] [INFO] تم فتح نافذة: CustomersForm
[2025-07-27 20:34:32] [INFO] تم فتح نافذة: DrugsForm
[2025-07-27 20:34:35] [ERROR] خطأ في الحصول على جميع الموردين: Notes
[2025-07-27 20:34:35] [INFO] تم فتح نافذة: SuppliersForm
[2025-07-27 20:34:58] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-27 20:44:20] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-27 20:44:20] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-27 20:44:20] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-27 20:44:20] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-27 20:44:20] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-27 20:44:20] [INFO] فحص قاعدة البيانات...
[2025-07-27 20:44:20] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-27 20:44:20] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-27 20:44:20] [INFO] تم التأكد من وجود العميل العام برقم: 9
[2025-07-27 20:44:20] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-27 20:44:21] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-27 20:44:21] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-27 20:44:21] [INFO] تشغيل الواجهة الحديثة...
[2025-07-27 20:44:28] [INFO] تم فتح نافذة: SuppliersForm
[2025-07-27 20:44:33] [INFO] تم فتح نافذة إضافة مورد
[2025-07-27 20:44:51] [INFO] تم إضافة مورد جديد: mahmood
[2025-07-27 20:44:52] [INFO] تم إضافة المورد: mahmood بالمعرف: 6
[2025-07-27 20:44:56] [ERROR] خطأ في الحصول على تاريخ معاملات المورد: Invalid object name 'PurchaseOrders'.
[2025-07-27 20:44:56] [ERROR] خطأ في الحصول على إحصائيات المورد: Invalid object name 'PurchaseOrders'.
[2025-07-27 20:45:04] [INFO] تم فتح نافذة: DrugsForm
[2025-07-27 20:45:06] [INFO] تم استرجاع 9 عميل
[2025-07-27 20:45:06] [INFO] تم فتح نافذة: CustomersForm
[2025-07-27 20:45:22] [ERROR] خطأ في الحصول على عناصر المخزون: Invalid object name 'Categories'.
[2025-07-27 20:45:23] [INFO] تم فتح نافذة: InventoryForm
[2025-07-27 20:45:32] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-28 05:25:56] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-28 05:25:56] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-28 05:25:57] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-28 05:25:57] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-28 05:25:57] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-28 05:25:57] [INFO] فحص قاعدة البيانات...
[2025-07-28 05:25:57] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-28 05:25:57] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-28 05:25:57] [INFO] تم التأكد من وجود العميل العام برقم: 9
[2025-07-28 05:25:57] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-28 05:25:59] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-28 05:25:59] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-28 05:26:00] [INFO] تشغيل الواجهة الحديثة...
[2025-07-28 05:26:11] [INFO] تم فتح نافذة: SalesForm
[2025-07-28 05:26:52] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-28 05:26:52] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-28 05:26:52] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-28 05:26:52] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-28 05:26:52] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-28 05:26:52] [INFO] فحص قاعدة البيانات...
[2025-07-28 05:26:52] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-28 05:26:52] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-28 05:26:52] [INFO] تم التأكد من وجود العميل العام برقم: 9
[2025-07-28 05:26:52] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-28 05:26:54] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-28 05:26:54] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-28 05:26:54] [INFO] تشغيل الواجهة الحديثة...
[2025-07-28 05:27:05] [INFO] تم فتح نافذة: SalesForm
[2025-07-28 05:27:30] [INFO] تم إنشاء فاتورة مبيعات: INV-2025-000004
[2025-07-28 05:27:36] [INFO] تم استرجاع 9 عميل
[2025-07-28 05:27:37] [INFO] تم فتح نافذة: CustomersForm
[2025-07-28 05:27:42] [ERROR] InvoiceManager.GetCustomerInvoices - InvoiceType
[2025-07-28 05:27:56] [INFO] تم فتح نافذة تعديل عميل
[2025-07-28 05:28:03] [ERROR] InvoiceManager.GetCustomerInvoices - InvoiceType
[2025-07-28 05:28:26] [INFO] تم العثور على 9 عميل مطابق للبحث: ع
[2025-07-28 05:28:28] [INFO] تم العثور على 1 عميل مطابق للبحث: عم
[2025-07-28 05:28:30] [INFO] تم العثور على 1 عميل مطابق للبحث: عميل
[2025-07-28 05:28:31] [INFO] تم فتح نافذة تعديل عميل
[2025-07-28 05:28:35] [ERROR] InvoiceManager.GetCustomerInvoices - InvoiceType
[2025-07-28 05:49:11] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-28 05:49:11] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-28 05:49:11] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-28 05:49:11] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-28 05:49:12] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-28 05:49:12] [INFO] فحص قاعدة البيانات...
[2025-07-28 05:49:12] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-28 05:49:12] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-28 05:49:12] [INFO] تم التأكد من وجود العميل العام برقم: 9
[2025-07-28 05:49:12] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-28 05:49:14] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-28 05:49:14] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-28 05:49:14] [INFO] تشغيل الواجهة الحديثة...
[2025-07-28 05:49:25] [INFO] تم فتح نافذة: SalesForm
[2025-07-28 05:49:29] [INFO] تم استرجاع 9 عميل
[2025-07-28 05:49:29] [INFO] تم فتح نافذة: CustomersForm
[2025-07-28 05:49:33] [INFO] تم استرجاع 9 عميل نشط
[2025-07-28 05:49:35] [INFO] تم فتح نافذة تعديل عميل
[2025-07-28 05:49:39] [INFO] بدء تحميل تاريخ العميل: عميل عام (ID: 9)
[2025-07-28 05:49:39] [INFO] تم جلب 1 فاتورة للعميل رقم 9
[2025-07-28 05:49:39] [INFO] تم تحميل 1 فاتورة للعميل: عميل عام
[2025-07-28 05:49:55] [INFO] بدء تحميل تاريخ العميل: عميل عام (ID: 9)
[2025-07-28 05:49:55] [INFO] تم جلب 1 فاتورة للعميل رقم 9
[2025-07-28 05:49:55] [INFO] تم تحميل 1 فاتورة للعميل: عميل عام
[2025-07-28 05:49:55] [INFO] بدء تحميل تاريخ العميل: عميل عام (ID: 9)
[2025-07-28 05:49:55] [INFO] تم جلب 1 فاتورة للعميل رقم 9
[2025-07-28 05:49:55] [INFO] تم تحميل 1 فاتورة للعميل: عميل عام
[2025-07-28 05:50:00] [INFO] تم فتح نافذة تعديل عميل
[2025-07-28 05:50:03] [INFO] بدء تحميل تاريخ العميل: عميل عام (ID: 9)
[2025-07-28 05:50:03] [INFO] تم جلب 1 فاتورة للعميل رقم 9
[2025-07-28 05:50:03] [INFO] تم تحميل 1 فاتورة للعميل: عميل عام
[2025-07-28 06:14:07] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-28 06:14:07] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-28 06:14:07] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-28 06:14:07] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-28 06:14:07] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-28 06:14:07] [INFO] فحص قاعدة البيانات...
[2025-07-28 06:14:07] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-28 06:14:07] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-28 06:14:07] [INFO] تم التأكد من وجود العميل العام برقم: 9
[2025-07-28 06:14:07] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-28 06:14:09] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-28 06:14:09] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-28 06:14:09] [INFO] تشغيل الواجهة الحديثة...
[2025-07-28 06:14:18] [INFO] تم استرجاع 9 عميل
[2025-07-28 06:14:18] [INFO] تم فتح نافذة: CustomersForm
[2025-07-28 06:16:59] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-28 06:16:59] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-28 06:16:59] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-28 06:16:59] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-28 06:16:59] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-28 06:16:59] [INFO] فحص قاعدة البيانات...
[2025-07-28 06:16:59] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-28 06:16:59] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-28 06:16:59] [INFO] تم التأكد من وجود العميل العام برقم: 9
[2025-07-28 06:16:59] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-28 06:17:00] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-28 06:17:00] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-28 06:17:00] [INFO] تشغيل الواجهة الحديثة...
[2025-07-28 06:17:09] [INFO] تم استرجاع 9 عميل
[2025-07-28 06:17:09] [INFO] تم فتح نافذة: CustomersForm
[2025-07-28 06:17:11] [INFO] بدء تحميل تاريخ العميل: عميل عام (ID: 9)
[2025-07-28 06:17:11] [INFO] تم جلب 1 فاتورة للعميل رقم 9
[2025-07-28 06:17:11] [INFO] تم تحميل 1 فاتورة للعميل: عميل عام
[2025-07-28 06:17:13] [INFO] بدء تصدير تاريخ العميل إلى Word: عميل عام
[2025-07-28 06:17:18] [INFO] تم تصدير تاريخ العميل إلى HTML: C:\Users\<USER>\Documents\تاريخ_العميل_GENERAL_20250728.html
[2025-07-28 06:17:38] [INFO] بدء تصدير تاريخ العميل إلى Excel: عميل عام
[2025-07-28 06:17:40] [INFO] تم تصدير تاريخ العميل إلى CSV: C:\Users\<USER>\Documents\تاريخ_العميل_GENERAL_20250728.csv
[2025-07-28 06:17:46] [INFO] بدء طباعة تاريخ العميل: عميل عام
[2025-07-28 06:17:55] [INFO] تمت طباعة تاريخ العميل: عميل عام
[2025-07-28 06:18:03] [ERROR] خطأ في الحصول على عناصر المخزون: Invalid object name 'Categories'.
[2025-07-28 06:18:05] [INFO] تم فتح نافذة: InventoryForm
[2025-07-28 06:18:07] [ERROR] خطأ في الحصول على الملخص المالي: Invalid object name 'Invoices'.
[2025-07-28 06:18:07] [ERROR] خطأ في الحصول على المعاملات المالية: Invalid object name 'Invoices'.
[2025-07-28 06:18:07] [ERROR] خطأ في الحصول على الملخص المالي: Invalid object name 'Invoices'.
[2025-07-28 06:18:07] [ERROR] خطأ في الحصول على المعاملات المالية: Invalid object name 'Invoices'.
[2025-07-28 06:18:07] [ERROR] خطأ في الحصول على الملخص المالي: Invalid object name 'Invoices'.
[2025-07-28 06:18:07] [ERROR] خطأ في الحصول على المعاملات المالية: Invalid object name 'Invoices'.
[2025-07-28 06:18:07] [INFO] تم فتح نافذة: FinancialForm
[2025-07-28 06:18:16] [ERROR] خطأ في الحصول على الملخص المالي: Invalid object name 'Invoices'.
[2025-07-28 06:18:16] [ERROR] خطأ في الحصول على المعاملات المالية: Invalid object name 'Invoices'.
[2025-07-28 06:18:20] [ERROR] خطأ في الحصول على الملخص المالي: Invalid object name 'Invoices'.
[2025-07-28 06:18:20] [ERROR] خطأ في الحصول على المعاملات المالية: Invalid object name 'Invoices'.
[2025-07-28 06:18:20] [ERROR] خطأ في الحصول على الملخص المالي: Invalid object name 'Invoices'.
[2025-07-28 06:18:20] [ERROR] خطأ في الحصول على المعاملات المالية: Invalid object name 'Invoices'.
[2025-07-28 06:18:20] [ERROR] خطأ في الحصول على الملخص المالي: Invalid object name 'Invoices'.
[2025-07-28 06:18:20] [ERROR] خطأ في الحصول على المعاملات المالية: Invalid object name 'Invoices'.
[2025-07-28 06:18:21] [ERROR] خطأ في الحصول على الملخص المالي: Invalid object name 'Invoices'.
[2025-07-28 06:18:21] [ERROR] خطأ في الحصول على المعاملات المالية: Invalid object name 'Invoices'.
[2025-07-28 06:18:21] [ERROR] خطأ في الحصول على الملخص المالي: Invalid object name 'Invoices'.
[2025-07-28 06:18:22] [ERROR] خطأ في الحصول على المعاملات المالية: Invalid object name 'Invoices'.
[2025-07-28 06:18:22] [ERROR] خطأ في الحصول على الملخص المالي: Invalid object name 'Invoices'.
[2025-07-28 06:18:22] [ERROR] خطأ في الحصول على المعاملات المالية: Invalid object name 'Invoices'.
[2025-07-28 06:18:22] [ERROR] خطأ في الحصول على الملخص المالي: Invalid object name 'Invoices'.
[2025-07-28 06:18:22] [ERROR] خطأ في الحصول على المعاملات المالية: Invalid object name 'Invoices'.
[2025-07-28 06:18:37] [ERROR] خطأ في الحصول على الملخص المالي: Invalid object name 'Invoices'.
[2025-07-28 06:18:37] [ERROR] خطأ في الحصول على المعاملات المالية: Invalid object name 'Invoices'.
[2025-07-28 06:18:37] [ERROR] خطأ في الحصول على الملخص المالي: Invalid object name 'Invoices'.
[2025-07-28 06:18:37] [ERROR] خطأ في الحصول على المعاملات المالية: Invalid object name 'Invoices'.
[2025-07-28 06:18:40] [INFO] تم استرجاع 9 عميل
[2025-07-28 06:18:40] [INFO] تم فتح نافذة: CustomersForm
[2025-07-28 06:18:43] [INFO] بدء تحميل تاريخ العميل: أحمد محمد علي (ID: 1)
[2025-07-28 06:18:43] [INFO] تم جلب 0 فاتورة للعميل رقم 1
[2025-07-28 06:18:43] [INFO] تم تحميل 0 فاتورة للعميل: أحمد محمد علي
[2025-07-28 06:18:47] [ERROR] خطأ في الحصول على الملخص المالي: Invalid object name 'Invoices'.
[2025-07-28 06:18:47] [ERROR] خطأ في الحصول على المعاملات المالية: Invalid object name 'Invoices'.
[2025-07-28 06:18:47] [ERROR] خطأ في الحصول على الملخص المالي: Invalid object name 'Invoices'.
[2025-07-28 06:18:47] [ERROR] خطأ في الحصول على المعاملات المالية: Invalid object name 'Invoices'.
[2025-07-28 06:18:47] [ERROR] خطأ في الحصول على الملخص المالي: Invalid object name 'Invoices'.
[2025-07-28 06:18:47] [ERROR] خطأ في الحصول على المعاملات المالية: Invalid object name 'Invoices'.
[2025-07-28 06:18:47] [INFO] تم فتح نافذة: FinancialForm
[2025-07-28 06:18:48] [INFO] تم فتح نافذة: BranchForm
[2025-07-28 06:19:02] [INFO] تم العثور على 1 فرع مطابق للبحث: تعز
[2025-07-28 06:19:06] [INFO] تم استرجاع 3 فرع نشط
[2025-07-28 06:19:06] [ERROR] خطأ في الحصول على مخازن الفرع: Invalid column name 'KeeperID'.
[2025-07-28 06:19:06] [ERROR] خطأ في الحصول على مخازن الفرع: Invalid column name 'KeeperID'.
[2025-07-28 06:19:20] [INFO] تم توليد كود فرع جديد: BR004
[2025-07-28 06:19:39] [INFO] تم إضافة فرع جديد: BR004 - ه
[2025-07-28 06:19:51] [INFO] تم تحديث الفرع: BR004 - ه
[2025-07-28 06:19:55] [INFO] تم حذف الفرع رقم: 4
[2025-07-28 06:20:00] [INFO] تم استرجاع 3 فرع نشط
[2025-07-28 06:20:00] [ERROR] خطأ في الحصول على مخازن الفرع: Invalid column name 'KeeperID'.
[2025-07-28 06:20:00] [ERROR] خطأ في الحصول على مخازن الفرع: Invalid column name 'KeeperID'.
[2025-07-28 06:20:07] [INFO] تم استرجاع 3 فرع نشط
[2025-07-28 06:20:07] [ERROR] خطأ في الحصول على المخازن: Invalid column name 'KeeperID'.
[2025-07-28 06:20:07] [INFO] تم فتح نافذة: WarehouseForm
[2025-07-28 06:20:08] [INFO] تم فتح نافذة: BranchForm
[2025-07-28 06:20:10] [INFO] تم فتح نافذة: UsersForm
[2025-07-28 06:20:11] [INFO] تم فتح نافذة: SettingsForm
[2025-07-28 06:20:28] [ERROR] خطأ في تحسين قاعدة البيانات: Incorrect syntax near 'STATISTICS'.
[2025-07-28 06:20:33] [ERROR] خطأ في الحصول على عناصر المخزون: Invalid object name 'Categories'.
[2025-07-28 06:20:35] [INFO] تم فتح نافذة: InventoryForm
[2025-07-28 06:20:45] [ERROR] خطأ في الحصول على عناصر المخزون: Invalid object name 'Categories'.
[2025-07-28 06:21:37] [INFO] تم فتح نافذة: InventoryForm
[2025-07-28 06:21:42] [ERROR] خطأ في الحصول على عناصر المخزون: Invalid object name 'Categories'.
[2025-07-28 06:25:52] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-28 06:25:52] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-28 06:25:52] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-28 06:25:52] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-28 06:25:52] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-28 06:25:52] [INFO] فحص قاعدة البيانات...
[2025-07-28 06:25:52] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-28 06:25:52] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-28 06:25:52] [INFO] تم التأكد من وجود العميل العام برقم: 9
[2025-07-28 06:25:52] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-28 06:25:53] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-28 06:25:53] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-28 06:25:53] [INFO] تشغيل الواجهة الحديثة...
[2025-07-28 06:26:00] [ERROR] خطأ في الحصول على عناصر المخزون: Invalid column name 'TradeName'.
Invalid column name 'StockQuantity'.
Invalid column name 'MinStockLevel'.
Invalid column name 'MaxStockLevel'.
Invalid column name 'ExpiryDate'.
Invalid column name 'TradeName'.
[2025-07-28 06:26:03] [INFO] تم فتح نافذة: InventoryForm
[2025-07-28 06:26:57] [ERROR] خطأ في الحصول على عناصر المخزون: Invalid column name 'TradeName'.
Invalid column name 'StockQuantity'.
Invalid column name 'MinStockLevel'.
Invalid column name 'MaxStockLevel'.
Invalid column name 'ExpiryDate'.
Invalid column name 'TradeName'.
[2025-07-28 06:26:58] [INFO] تم فتح نافذة: InventoryForm
[2025-07-28 06:27:00] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-28 06:27:06] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-28 06:27:06] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-28 06:27:06] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-28 06:27:06] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-28 06:27:06] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-28 06:27:06] [INFO] فحص قاعدة البيانات...
[2025-07-28 06:27:07] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-28 06:27:07] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-28 06:27:07] [INFO] تم التأكد من وجود العميل العام برقم: 9
[2025-07-28 06:27:07] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-28 06:27:08] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-28 06:27:08] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-28 06:27:08] [INFO] تشغيل الواجهة الحديثة...
[2025-07-28 06:27:13] [ERROR] خطأ في الحصول على عناصر المخزون: Invalid column name 'TradeName'.
Invalid column name 'StockQuantity'.
Invalid column name 'MinStockLevel'.
Invalid column name 'MaxStockLevel'.
Invalid column name 'ExpiryDate'.
Invalid column name 'TradeName'.
[2025-07-28 06:28:25] [INFO] تم فتح نافذة: InventoryForm
[2025-07-28 06:28:28] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-28 06:28:32] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-28 06:28:32] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-28 06:28:32] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-28 06:28:32] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-28 06:28:32] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-28 06:28:32] [INFO] فحص قاعدة البيانات...
[2025-07-28 06:28:32] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-28 06:28:32] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-28 06:28:32] [INFO] تم التأكد من وجود العميل العام برقم: 9
[2025-07-28 06:28:32] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-28 06:28:33] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-28 06:28:33] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-28 06:28:33] [INFO] تشغيل الواجهة الحديثة...
[2025-07-28 06:28:41] [ERROR] خطأ في الحصول على عناصر المخزون: Invalid column name 'StockQuantity'.
Invalid column name 'MinStockLevel'.
Invalid column name 'MaxStockLevel'.
Invalid column name 'ExpiryDate'.
Invalid column name 'TradeName'.
[2025-07-28 06:34:42] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-28 06:34:42] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-28 06:34:42] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-28 06:34:42] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-28 06:34:42] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-28 06:34:42] [INFO] فحص قاعدة البيانات...
[2025-07-28 06:34:42] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-28 06:34:42] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-28 06:34:42] [INFO] تم التأكد من وجود العميل العام برقم: 9
[2025-07-28 06:34:42] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-28 06:34:43] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-28 06:34:43] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-28 06:34:43] [INFO] تشغيل الواجهة الحديثة...
[2025-07-28 06:34:49] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 06:34:49] [INFO] تم فتح نافذة: InventoryForm
[2025-07-28 06:35:32] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 06:35:32] [INFO] تم فتح نافذة: InventoryForm
[2025-07-28 06:36:50] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-28 06:44:13] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-28 06:44:13] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-28 06:44:13] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-28 06:44:13] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-28 06:44:13] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-28 06:44:13] [INFO] فحص قاعدة البيانات...
[2025-07-28 06:44:13] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-28 06:44:13] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-28 06:44:13] [INFO] تم التأكد من وجود العميل العام برقم: 9
[2025-07-28 06:44:13] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-28 06:44:14] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-28 06:44:14] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-28 06:44:14] [INFO] تشغيل الواجهة الحديثة...
[2025-07-28 06:44:24] [INFO] بدء تحميل بيانات المخزون
[2025-07-28 06:44:24] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 06:44:24] [INFO] تم تنسيق الجدول بنجاح
[2025-07-28 06:44:24] [INFO] تم تحميل 51 عنصر من المخزون
[2025-07-28 06:44:24] [INFO] تم فتح نافذة: InventoryForm
[2025-07-28 06:44:43] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-28 06:46:49] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-28 06:46:49] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-28 06:46:49] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-28 06:46:49] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-28 06:46:49] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-28 06:46:49] [INFO] فحص قاعدة البيانات...
[2025-07-28 06:46:49] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-28 06:46:49] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-28 06:46:49] [INFO] تم التأكد من وجود العميل العام برقم: 9
[2025-07-28 06:46:49] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-28 06:46:51] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-28 06:46:51] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-28 06:46:51] [INFO] تشغيل الواجهة الحديثة...
[2025-07-28 06:47:01] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 06:47:01] [INFO] تم البحث في المخزون - النتائج: 0 عنصر
[2025-07-28 06:47:01] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 06:47:01] [INFO] تم البحث في المخزون - النتائج: 51 عنصر
[2025-07-28 06:47:01] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 06:47:01] [INFO] تم البحث في المخزون - النتائج: 51 عنصر
[2025-07-28 06:47:01] [INFO] بدء تحميل بيانات المخزون
[2025-07-28 06:47:01] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 06:47:01] [INFO] تم تنسيق الجدول بنجاح
[2025-07-28 06:47:01] [INFO] تم تحميل 51 عنصر من المخزون
[2025-07-28 06:47:01] [INFO] تم فتح نافذة: InventoryForm
[2025-07-28 06:47:06] [INFO] بدء تصدير بيانات المخزون - 51 عنصر
[2025-07-28 06:47:10] [INFO] تم تصدير بيانات المخزون بنجاح: C:\Users\<USER>\Documents\تقرير_المخزون_20250728_064704.csv
[2025-07-28 06:47:16] [INFO] بدء إنشاء تقرير المخزون
[2025-07-28 06:47:22] [INFO] تم إنشاء تقرير المخزون: C:\Users\<USER>\Documents\تقرير_المخزون_التفصيلي_20250728_064716.html
[2025-07-28 06:47:36] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-28 06:54:04] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-28 06:54:04] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-28 06:54:05] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-28 06:54:05] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-28 06:54:05] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-28 06:54:05] [INFO] فحص قاعدة البيانات...
[2025-07-28 06:54:05] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-28 06:54:05] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-28 06:54:05] [INFO] تم التأكد من وجود العميل العام برقم: 9
[2025-07-28 06:54:05] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-28 06:54:06] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-28 06:54:06] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-28 06:54:06] [INFO] تشغيل الواجهة الحديثة...
[2025-07-28 06:54:15] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 06:54:15] [INFO] تم البحث في المخزون - النتائج: 5 عنصر
[2025-07-28 06:54:15] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 06:54:15] [INFO] تم البحث في المخزون - النتائج: 51 عنصر
[2025-07-28 06:54:15] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 06:54:15] [INFO] تم البحث في المخزون - النتائج: 51 عنصر
[2025-07-28 06:54:15] [INFO] بدء تحميل بيانات المخزون
[2025-07-28 06:54:15] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 06:54:15] [INFO] عينة من البيانات - الدواء: أتينولول 50 مجم, الفئة: أدوية القلب والأوعية, المصنع: Pfizer
[2025-07-28 06:54:15] [INFO] تم تنسيق الجدول بنجاح
[2025-07-28 06:54:15] [INFO] تم تحميل 51 عنصر من المخزون
[2025-07-28 06:54:15] [INFO] تم فتح نافذة: InventoryForm
[2025-07-28 06:54:19] [INFO] بدء تحديث مخزون الدواء: DRG017 - أتينولول 50 مجم
[2025-07-28 06:54:46] [INFO] تم تحديث مخزون الدواء رقم: 17 إلى 200
[2025-07-28 06:54:46] [INFO] تم إضافة حركة مخزون: الدواء 17, النوع دخول, الكمية 200
[2025-07-28 06:54:48] [INFO] تم تحديث مخزون الدواء أتينولول 50 مجم: إضافة 200
[2025-07-28 06:54:48] [INFO] بدء تحميل بيانات المخزون
[2025-07-28 06:54:48] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 06:54:48] [INFO] عينة من البيانات - الدواء: أتينولول 50 مجم, الفئة: أدوية القلب والأوعية, المصنع: Pfizer
[2025-07-28 06:54:48] [INFO] تم تنسيق الجدول بنجاح
[2025-07-28 06:54:48] [INFO] تم تحميل 51 عنصر من المخزون
[2025-07-28 06:54:48] [INFO] تم تحديث مخزون الدواء بنجاح: DRG017
[2025-07-28 06:54:56] [INFO] بدء تحديث مخزون الدواء: DRG017 - أتينولول 50 مجم
[2025-07-28 06:55:07] [INFO] تم تحديث مخزون الدواء رقم: 17 إلى 300
[2025-07-28 06:55:07] [INFO] تم إضافة حركة مخزون: الدواء 17, النوع دخول, الكمية 300
[2025-07-28 06:55:12] [INFO] تم تحديث مخزون الدواء أتينولول 50 مجم: إضافة 300
[2025-07-28 06:55:12] [INFO] بدء تحميل بيانات المخزون
[2025-07-28 06:55:12] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 06:55:12] [INFO] عينة من البيانات - الدواء: أتينولول 50 مجم, الفئة: أدوية القلب والأوعية, المصنع: Pfizer
[2025-07-28 06:55:12] [INFO] تم تنسيق الجدول بنجاح
[2025-07-28 06:55:12] [INFO] تم تحميل 51 عنصر من المخزون
[2025-07-28 06:55:12] [INFO] تم تحديث مخزون الدواء بنجاح: DRG017
[2025-07-28 06:55:21] [INFO] بدء تحديث مخزون الدواء: DRG017 - أتينولول 50 مجم
[2025-07-28 07:02:09] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-28 07:02:09] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-28 07:02:09] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-28 07:02:09] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-28 07:02:09] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-28 07:02:09] [INFO] فحص قاعدة البيانات...
[2025-07-28 07:02:10] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-28 07:02:10] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-28 07:02:10] [INFO] تم التأكد من وجود العميل العام برقم: 9
[2025-07-28 07:02:10] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-28 07:02:11] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-28 07:02:11] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-28 07:02:11] [INFO] تشغيل الواجهة الحديثة...
[2025-07-28 07:02:20] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 07:02:20] [INFO] تم البحث في المخزون - النتائج: 5 عنصر
[2025-07-28 07:02:20] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 07:02:20] [INFO] تم البحث في المخزون - النتائج: 51 عنصر
[2025-07-28 07:02:20] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 07:02:20] [INFO] تم البحث في المخزون - النتائج: 51 عنصر
[2025-07-28 07:02:20] [INFO] بدء تحميل بيانات المخزون
[2025-07-28 07:02:20] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 07:02:20] [INFO] عينة من البيانات - الدواء: أتينولول 50 مجم, الفئة: أدوية القلب والأوعية, المصنع: Pfizer
[2025-07-28 07:02:20] [INFO] تم تنسيق الجدول بنجاح
[2025-07-28 07:02:20] [INFO] تم تحميل 51 عنصر من المخزون
[2025-07-28 07:02:20] [INFO] تم فتح نافذة: InventoryForm
[2025-07-28 07:02:22] [INFO] بدء تحديث مخزون الدواء: DRG017 - أتينولول 50 مجم
[2025-07-28 07:02:39] [ERROR] خطأ في تحديث المخزون في قاعدة البيانات: Cannot insert the value NULL into column 'WarehouseID', table 'PharmacyDB.dbo.StockMovements'; column does not allow nulls. INSERT fails.
The statement has been terminated.
[2025-07-28 07:02:39] [ERROR] خطأ في تحديث المخزون: فشل في تحديث المخزون
[2025-07-28 07:02:43] [ERROR] خطأ في تحديث المخزون في قاعدة البيانات: Cannot insert the value NULL into column 'WarehouseID', table 'PharmacyDB.dbo.StockMovements'; column does not allow nulls. INSERT fails.
The statement has been terminated.
[2025-07-28 07:02:43] [ERROR] خطأ في تحديث المخزون: فشل في تحديث المخزون
[2025-07-28 07:02:56] [ERROR] خطأ في تحديث المخزون في قاعدة البيانات: Cannot insert the value NULL into column 'WarehouseID', table 'PharmacyDB.dbo.StockMovements'; column does not allow nulls. INSERT fails.
The statement has been terminated.
[2025-07-28 07:02:56] [ERROR] خطأ في تحديث المخزون: فشل في تحديث المخزون
[2025-07-28 07:03:01] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-28 07:03:42] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-28 07:03:42] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-28 07:03:42] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-28 07:03:43] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-28 07:03:43] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-28 07:03:43] [INFO] فحص قاعدة البيانات...
[2025-07-28 07:03:43] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-28 07:03:43] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-28 07:03:43] [INFO] تم التأكد من وجود العميل العام برقم: 9
[2025-07-28 07:03:43] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-28 07:03:44] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-28 07:03:44] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-28 07:03:44] [INFO] تشغيل الواجهة الحديثة...
[2025-07-28 07:03:52] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 07:03:52] [INFO] تم البحث في المخزون - النتائج: 5 عنصر
[2025-07-28 07:03:52] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 07:03:52] [INFO] تم البحث في المخزون - النتائج: 51 عنصر
[2025-07-28 07:03:52] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 07:03:52] [INFO] تم البحث في المخزون - النتائج: 51 عنصر
[2025-07-28 07:03:52] [INFO] بدء تحميل بيانات المخزون
[2025-07-28 07:03:52] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 07:03:52] [INFO] عينة من البيانات - الدواء: أتينولول 50 مجم, الفئة: أدوية القلب والأوعية, المصنع: Pfizer
[2025-07-28 07:03:52] [INFO] تم تنسيق الجدول بنجاح
[2025-07-28 07:03:52] [INFO] تم تحميل 51 عنصر من المخزون
[2025-07-28 07:03:52] [INFO] تم فتح نافذة: InventoryForm
[2025-07-28 07:03:54] [INFO] بدء تحديث مخزون الدواء: DRG017 - أتينولول 50 مجم
[2025-07-28 07:04:00] [ERROR] خطأ في تحديث المخزون في قاعدة البيانات: Cannot insert the value NULL into column 'WarehouseID', table 'PharmacyDB.dbo.StockMovements'; column does not allow nulls. INSERT fails.
The statement has been terminated.
[2025-07-28 07:04:00] [ERROR] خطأ في تحديث المخزون: فشل في تحديث المخزون
[2025-07-28 07:04:05] [ERROR] خطأ في تحديث المخزون في قاعدة البيانات: Cannot insert the value NULL into column 'WarehouseID', table 'PharmacyDB.dbo.StockMovements'; column does not allow nulls. INSERT fails.
The statement has been terminated.
[2025-07-28 07:04:05] [ERROR] خطأ في تحديث المخزون: فشل في تحديث المخزون
[2025-07-28 07:04:43] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-28 07:13:44] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-28 07:13:44] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-28 07:13:44] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-28 07:13:44] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-28 07:13:44] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-28 07:13:44] [INFO] فحص قاعدة البيانات...
[2025-07-28 07:13:45] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-28 07:13:45] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-28 07:13:45] [INFO] تم التأكد من وجود العميل العام برقم: 9
[2025-07-28 07:13:45] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-28 07:13:51] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-28 07:13:51] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-28 07:13:51] [INFO] تشغيل الواجهة الحديثة...
[2025-07-28 07:14:01] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 07:14:01] [INFO] تم البحث في المخزون - النتائج: 5 عنصر
[2025-07-28 07:14:01] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 07:14:01] [INFO] تم البحث في المخزون - النتائج: 51 عنصر
[2025-07-28 07:14:01] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 07:14:01] [INFO] تم البحث في المخزون - النتائج: 51 عنصر
[2025-07-28 07:14:01] [INFO] بدء تحميل بيانات المخزون
[2025-07-28 07:14:01] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 07:14:01] [INFO] عينة من البيانات - الدواء: أتينولول 50 مجم, الفئة: أدوية القلب والأوعية, المصنع: Pfizer
[2025-07-28 07:14:01] [INFO] تم تنسيق الجدول بنجاح
[2025-07-28 07:14:01] [INFO] تم تحميل 51 عنصر من المخزون
[2025-07-28 07:14:01] [INFO] تم فتح نافذة: InventoryForm
[2025-07-28 07:14:02] [INFO] بدء تحديث مخزون الدواء: DRG017 - أتينولول 50 مجم
[2025-07-28 07:14:08] [INFO] بدء تحديث المخزون - الدواء: 17, الكمية: 10, العملية: إضافة
[2025-07-28 07:14:08] [INFO] تم الاتصال بقاعدة البيانات بنجاح
[2025-07-28 07:14:08] [INFO] إضافة 10 وحدة للدواء 17
[2025-07-28 07:14:08] [INFO] تم إدراج 1 سجل في جدول Inventory
[2025-07-28 07:14:08] [INFO] تم تأكيد المعاملة بنجاح
[2025-07-28 07:14:10] [INFO] تم تحديث مخزون الدواء أتينولول 50 مجم: إضافة 10 - من 300 إلى 310
[2025-07-28 07:14:11] [INFO] بدء تحميل بيانات المخزون
[2025-07-28 07:14:11] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 07:14:11] [INFO] عينة من البيانات - الدواء: أتينولول 50 مجم, الفئة: أدوية القلب والأوعية, المصنع: Pfizer
[2025-07-28 07:14:11] [INFO] تم تنسيق الجدول بنجاح
[2025-07-28 07:14:11] [INFO] تم تحميل 51 عنصر من المخزون
[2025-07-28 07:14:11] [INFO] تم تحديث مخزون الدواء بنجاح: DRG017
[2025-07-28 07:14:15] [INFO] بدء تحديث مخزون الدواء: DRG017 - أتينولول 50 مجم
[2025-07-28 07:14:23] [INFO] بدء تحديث المخزون - الدواء: 17, الكمية: 20, العملية: خصم
[2025-07-28 07:14:23] [INFO] تم الاتصال بقاعدة البيانات بنجاح
[2025-07-28 07:14:23] [INFO] خصم 20 وحدة من الدواء 17
[2025-07-28 07:14:23] [INFO] تم تأكيد المعاملة بنجاح
[2025-07-28 07:14:24] [INFO] تم تحديث مخزون الدواء أتينولول 50 مجم: خصم 20 - من 310 إلى 290
[2025-07-28 07:14:24] [INFO] بدء تحميل بيانات المخزون
[2025-07-28 07:14:24] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 07:14:24] [INFO] عينة من البيانات - الدواء: أتينولول 50 مجم, الفئة: أدوية القلب والأوعية, المصنع: Pfizer
[2025-07-28 07:14:24] [INFO] تم تنسيق الجدول بنجاح
[2025-07-28 07:14:24] [INFO] تم تحميل 51 عنصر من المخزون
[2025-07-28 07:14:24] [INFO] تم تحديث مخزون الدواء بنجاح: DRG017
[2025-07-28 07:14:41] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 07:14:41] [INFO] تم البحث في المخزون - النتائج: 0 عنصر
[2025-07-28 07:14:50] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 07:14:50] [INFO] تم البحث في المخزون - النتائج: 1 عنصر
[2025-07-28 07:14:59] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 07:14:59] [INFO] تم البحث في المخزون - النتائج: 51 عنصر
[2025-07-28 07:15:06] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 07:15:07] [INFO] تم البحث في المخزون - النتائج: 5 عنصر
[2025-07-28 07:15:09] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 07:15:09] [INFO] تم البحث في المخزون - النتائج: 0 عنصر
[2025-07-28 07:15:11] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 07:15:11] [INFO] تم البحث في المخزون - النتائج: 51 عنصر
[2025-07-28 07:15:11] [INFO] بدء تحميل بيانات المخزون
[2025-07-28 07:15:11] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 07:15:11] [INFO] عينة من البيانات - الدواء: أتينولول 50 مجم, الفئة: أدوية القلب والأوعية, المصنع: Pfizer
[2025-07-28 07:15:11] [INFO] تم تنسيق الجدول بنجاح
[2025-07-28 07:15:11] [INFO] تم تحميل 51 عنصر من المخزون
[2025-07-28 07:15:15] [INFO] تم فتح نافذة: ReportsForm
[2025-07-28 07:15:17] [ERROR] خطأ في إنشاء تقرير المبيعات: Invalid object name 'Sales'.
[2025-07-28 07:15:24] [INFO] تم إنشاء تقرير العملاء
[2025-07-28 07:15:24] [ERROR] خطأ في الحصول على بيانات العملاء: Invalid column name 'Balance'.
Invalid column name 'TotalAmount'.
[2025-07-28 07:15:50] [ERROR] خطأ في الحصول على الملخص المالي: Invalid object name 'Invoices'.
[2025-07-28 07:15:50] [ERROR] خطأ في الحصول على المعاملات المالية: Invalid object name 'Invoices'.
[2025-07-28 07:15:50] [ERROR] خطأ في الحصول على الملخص المالي: Invalid object name 'Invoices'.
[2025-07-28 07:15:50] [ERROR] خطأ في الحصول على المعاملات المالية: Invalid object name 'Invoices'.
[2025-07-28 07:15:50] [ERROR] خطأ في الحصول على الملخص المالي: Invalid object name 'Invoices'.
[2025-07-28 07:15:50] [ERROR] خطأ في الحصول على المعاملات المالية: Invalid object name 'Invoices'.
[2025-07-28 07:15:50] [INFO] تم فتح نافذة: FinancialForm
[2025-07-28 07:15:51] [INFO] تم فتح نافذة: ReportsForm
[2025-07-28 07:16:18] [ERROR] خطأ في الحصول على الملخص المالي: Invalid object name 'Invoices'.
[2025-07-28 07:16:18] [ERROR] خطأ في الحصول على المعاملات المالية: Invalid object name 'Invoices'.
[2025-07-28 07:16:18] [ERROR] خطأ في الحصول على الملخص المالي: Invalid object name 'Invoices'.
[2025-07-28 07:16:18] [ERROR] خطأ في الحصول على المعاملات المالية: Invalid object name 'Invoices'.
[2025-07-28 07:16:18] [ERROR] خطأ في الحصول على الملخص المالي: Invalid object name 'Invoices'.
[2025-07-28 07:16:18] [ERROR] خطأ في الحصول على المعاملات المالية: Invalid object name 'Invoices'.
[2025-07-28 07:16:18] [INFO] تم فتح نافذة: FinancialForm
[2025-07-28 07:16:25] [INFO] تم فتح نافذة: BranchForm
[2025-07-28 07:16:31] [INFO] تم توليد كود فرع جديد: BR004
[2025-07-28 07:16:36] [INFO] تم استرجاع 3 فرع نشط
[2025-07-28 07:16:36] [ERROR] خطأ في الحصول على مخازن الفرع: Invalid column name 'KeeperID'.
[2025-07-28 07:16:36] [ERROR] خطأ في الحصول على مخازن الفرع: Invalid column name 'KeeperID'.
[2025-07-28 07:16:41] [INFO] تم استرجاع 3 فرع نشط
[2025-07-28 07:16:41] [ERROR] خطأ في الحصول على المخازن: Invalid column name 'KeeperID'.
[2025-07-28 07:16:41] [INFO] تم فتح نافذة: WarehouseForm
[2025-07-28 07:16:43] [INFO] تم فتح نافذة: BranchForm
[2025-07-28 07:16:44] [INFO] تم فتح نافذة: SettingsForm
[2025-07-28 07:16:45] [INFO] تم استرجاع 3 فرع نشط
[2025-07-28 07:16:45] [ERROR] خطأ في الحصول على المخازن: Invalid column name 'KeeperID'.
[2025-07-28 07:16:46] [INFO] تم فتح نافذة: WarehouseForm
[2025-07-28 07:16:47] [INFO] تم فتح نافذة: BranchForm
[2025-07-28 07:16:48] [ERROR] خطأ في الحصول على الملخص المالي: Invalid object name 'Invoices'.
[2025-07-28 07:16:48] [ERROR] خطأ في الحصول على المعاملات المالية: Invalid object name 'Invoices'.
[2025-07-28 07:16:48] [ERROR] خطأ في الحصول على الملخص المالي: Invalid object name 'Invoices'.
[2025-07-28 07:16:48] [ERROR] خطأ في الحصول على المعاملات المالية: Invalid object name 'Invoices'.
[2025-07-28 07:16:48] [ERROR] خطأ في الحصول على الملخص المالي: Invalid object name 'Invoices'.
[2025-07-28 07:16:48] [ERROR] خطأ في الحصول على المعاملات المالية: Invalid object name 'Invoices'.
[2025-07-28 07:16:48] [INFO] تم فتح نافذة: FinancialForm
[2025-07-28 07:16:49] [INFO] تم فتح نافذة: ReportsForm
[2025-07-28 07:16:56] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-28 16:31:16] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-28 16:31:16] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-28 16:31:16] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-28 16:31:16] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-28 16:31:16] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-28 16:31:16] [INFO] فحص قاعدة البيانات...
[2025-07-28 16:31:17] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-28 16:31:17] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-28 16:31:17] [INFO] تم التأكد من وجود العميل العام برقم: 9
[2025-07-28 16:31:17] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-28 16:31:19] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-28 16:31:19] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-28 16:31:19] [INFO] تشغيل الواجهة الحديثة...
[2025-07-28 16:31:29] [INFO] تم فتح نافذة: SalesForm
[2025-07-28 16:31:30] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 16:31:30] [INFO] تم البحث في المخزون - النتائج: 5 عنصر
[2025-07-28 16:31:30] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 16:31:30] [INFO] تم البحث في المخزون - النتائج: 51 عنصر
[2025-07-28 16:31:30] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 16:31:30] [INFO] تم البحث في المخزون - النتائج: 51 عنصر
[2025-07-28 16:31:30] [INFO] بدء تحميل بيانات المخزون
[2025-07-28 16:31:30] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 16:31:30] [INFO] عينة من البيانات - الدواء: أتينولول 50 مجم, الفئة: أدوية القلب والأوعية, المصنع: Pfizer
[2025-07-28 16:31:30] [INFO] تم تنسيق الجدول بنجاح
[2025-07-28 16:31:30] [INFO] تم تحميل 51 عنصر من المخزون
[2025-07-28 16:31:30] [INFO] تم فتح نافذة: InventoryForm
[2025-07-28 16:31:35] [INFO] تم فتح نافذة: ReportsForm
[2025-07-28 16:35:54] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-28 16:46:06] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-28 16:46:06] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-28 16:46:06] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-28 16:46:06] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-28 16:46:06] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-28 16:46:06] [INFO] فحص قاعدة البيانات...
[2025-07-28 16:46:07] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-28 16:46:07] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-28 16:46:07] [INFO] تم التأكد من وجود العميل العام برقم: 9
[2025-07-28 16:46:07] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-28 16:46:08] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-28 16:46:08] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-28 16:46:08] [INFO] تشغيل الواجهة الحديثة...
[2025-07-28 16:46:17] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 16:46:17] [INFO] تم البحث في المخزون - النتائج: 5 عنصر
[2025-07-28 16:46:17] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 16:46:17] [INFO] تم البحث في المخزون - النتائج: 51 عنصر
[2025-07-28 16:46:17] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 16:46:17] [INFO] تم البحث في المخزون - النتائج: 51 عنصر
[2025-07-28 16:46:17] [INFO] بدء تحميل بيانات المخزون
[2025-07-28 16:46:17] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 16:46:17] [INFO] عينة من البيانات - الدواء: أتينولول 50 مجم, الفئة: أدوية القلب والأوعية, المصنع: Pfizer
[2025-07-28 16:46:17] [INFO] تم تنسيق الجدول بنجاح
[2025-07-28 16:46:17] [INFO] تم تحميل 51 عنصر من المخزون
[2025-07-28 16:46:17] [INFO] تم فتح نافذة: InventoryForm
[2025-07-28 16:46:22] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-28 17:42:23] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-28 17:42:23] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-28 17:42:23] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-28 17:42:23] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-28 17:42:23] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-28 17:42:23] [INFO] فحص قاعدة البيانات...
[2025-07-28 17:42:23] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-28 17:42:23] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-28 17:42:24] [INFO] تم التأكد من وجود العميل العام برقم: 9
[2025-07-28 17:42:24] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-28 17:42:25] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-28 17:42:25] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-28 17:42:25] [INFO] تشغيل الواجهة الحديثة...
[2025-07-28 17:43:05] [INFO] تم فتح نافذة: ReportsForm
[2025-07-28 17:43:06] [ERROR] خطأ في إنشاء تقرير المبيعات: Invalid object name 'Sales'.
[2025-07-28 17:43:06] [INFO] تم تحميل تقرير sales بنجاح
[2025-07-28 17:43:17] [INFO] تم طباعة تقرير sales
[2025-07-28 17:43:22] [INFO] تم إنشاء تقرير المخزون
[2025-07-28 17:43:22] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 17:43:22] [INFO] تم تحميل تقرير inventory بنجاح
[2025-07-28 17:43:33] [INFO] تم تصدير تقرير inventory إلى C:\Users\<USER>\Documents\تقرير المخزون_20250728_174330.html
[2025-07-28 17:43:42] [INFO] تم إنشاء تقرير العملاء
[2025-07-28 17:43:42] [ERROR] خطأ في الحصول على بيانات العملاء: Invalid column name 'Balance'.
Invalid column name 'TotalAmount'.
[2025-07-28 17:43:42] [INFO] تم تحميل تقرير customers بنجاح
[2025-07-28 17:43:47] [ERROR] خطأ في الحصول على بيانات الموردين: Invalid object name 'Purchases'.
[2025-07-28 17:43:47] [INFO] تم تحميل تقرير suppliers بنجاح
[2025-07-28 17:43:51] [ERROR] خطأ في إنشاء التقرير المالي: Invalid object name 'Sales'.
[2025-07-28 17:43:51] [INFO] تم تحميل تقرير financial بنجاح
[2025-07-28 17:43:56] [INFO] تم جلب 12 دواء منتهي الصلاحية
[2025-07-28 17:43:56] [INFO] تم تحميل تقرير expired بنجاح
[2025-07-28 17:44:01] [ERROR] خطأ في الحصول على الأدوية الأكثر مبيعاً: Invalid object name 'SaleDetails'.
[2025-07-28 17:44:01] [INFO] تم تحميل تقرير topselling بنجاح
[2025-07-28 17:44:05] [ERROR] خطأ في إنشاء التقرير المالي: Invalid object name 'Sales'.
[2025-07-28 17:44:05] [INFO] تم تحميل تقرير profit بنجاح
[2025-07-28 17:44:09] [ERROR] خطأ في إنشاء تقرير المبيعات: Invalid object name 'Sales'.
[2025-07-28 17:44:09] [INFO] تم إنشاء تقرير المخزون
[2025-07-28 17:44:09] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 17:44:09] [ERROR] خطأ في إنشاء التقرير المالي: Invalid object name 'Sales'.
[2025-07-28 17:44:09] [INFO] تم تحميل تقرير comprehensive بنجاح
[2025-07-28 17:44:41] [ERROR] خطأ في إنشاء تقرير المبيعات: Invalid object name 'Sales'.
[2025-07-28 17:44:41] [INFO] تم تحميل تقرير sales بنجاح
[2025-07-28 17:45:07] [INFO] تم إنشاء تقرير المخزون
[2025-07-28 17:45:07] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 17:45:07] [INFO] تم تحميل تقرير inventory بنجاح
[2025-07-28 17:45:12] [INFO] تم إنشاء تقرير العملاء
[2025-07-28 17:45:12] [ERROR] خطأ في الحصول على بيانات العملاء: Invalid column name 'Balance'.
Invalid column name 'TotalAmount'.
[2025-07-28 17:45:12] [INFO] تم تحميل تقرير customers بنجاح
[2025-07-28 17:45:29] [ERROR] خطأ في الحصول على بيانات الموردين: Invalid object name 'Purchases'.
[2025-07-28 17:45:29] [INFO] تم تحميل تقرير suppliers بنجاح
[2025-07-28 17:45:46] [ERROR] خطأ في إنشاء التقرير المالي: Invalid object name 'Sales'.
[2025-07-28 17:45:46] [INFO] تم تحميل تقرير financial بنجاح
[2025-07-28 17:46:02] [INFO] تم جلب 12 دواء منتهي الصلاحية
[2025-07-28 17:46:02] [INFO] تم تحميل تقرير expired بنجاح
[2025-07-28 17:46:08] [ERROR] خطأ في الحصول على الأدوية الأكثر مبيعاً: Invalid object name 'SaleDetails'.
[2025-07-28 17:46:08] [INFO] تم تحميل تقرير topselling بنجاح
[2025-07-28 17:46:23] [ERROR] خطأ في إنشاء التقرير المالي: Invalid object name 'Sales'.
[2025-07-28 17:46:23] [INFO] تم تحميل تقرير profit بنجاح
[2025-07-28 17:46:50] [ERROR] خطأ في إنشاء تقرير المبيعات: Invalid object name 'Sales'.
[2025-07-28 17:46:50] [INFO] تم إنشاء تقرير المخزون
[2025-07-28 17:46:50] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 17:46:50] [ERROR] خطأ في إنشاء التقرير المالي: Invalid object name 'Sales'.
[2025-07-28 17:46:50] [INFO] تم تحميل تقرير comprehensive بنجاح
[2025-07-28 17:47:14] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-28 17:53:02] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-28 17:53:02] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-28 17:53:02] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-28 17:53:02] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-28 17:53:02] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-28 17:53:02] [INFO] فحص قاعدة البيانات...
[2025-07-28 17:53:02] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-28 17:53:02] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-28 17:53:02] [INFO] تم التأكد من وجود العميل العام برقم: 9
[2025-07-28 17:53:02] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-28 17:53:04] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-28 17:53:04] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-28 17:53:04] [INFO] تشغيل الواجهة الحديثة...
[2025-07-28 17:53:12] [INFO] تم فتح نافذة: ReportsForm
[2025-07-28 17:53:14] [INFO] تم إنشاء تقرير المبيعات
[2025-07-28 17:53:14] [INFO] تم جلب 0 عملية بيع
[2025-07-28 17:53:15] [INFO] تم تحميل تقرير sales بنجاح
[2025-07-28 17:53:21] [INFO] إنهاء تشغيل نظام إدارة الصيدلية
[2025-07-28 18:12:47] [INFO] تم تهيئة نظام السجلات بنجاح
[2025-07-28 18:12:48] [INFO] === بدء تشغيل نظام إدارة الصيدلية ===
[2025-07-28 18:12:48] [INFO] تم إنشاء الأيقونات بنجاح
[2025-07-28 18:12:48] [INFO] تم إنشاء صورة الواجهة التقليدية بنجاح
[2025-07-28 18:12:48] [INFO] تم إنشاء صورة الواجهة الحديثة بنجاح
[2025-07-28 18:12:48] [INFO] فحص قاعدة البيانات...
[2025-07-28 18:12:49] [INFO] قاعدة البيانات موجودة ومتصلة
[2025-07-28 18:12:49] [INFO] تم إنشاء المستخدم الافتراضي للاختبار
[2025-07-28 18:12:49] [INFO] تم التأكد من وجود العميل العام برقم: 9
[2025-07-28 18:12:49] [INFO] تشغيل الواجهة الرئيسية...
[2025-07-28 18:12:50] [INFO] تم حفظ الإعداد UseModernInterface = True
[2025-07-28 18:12:50] [INFO] تم اختيار الواجهة: الحديثة
[2025-07-28 18:12:50] [INFO] تشغيل الواجهة الحديثة...
[2025-07-28 18:13:07] [INFO] تم فتح نافذة: ReportsForm
[2025-07-28 18:13:09] [ERROR] خطأ في إنشاء تقرير المبيعات: Invalid column name 'TotalAmount'.
[2025-07-28 18:13:09] [INFO] تم تحميل تقرير sales بنجاح
[2025-07-28 18:13:12] [INFO] تم إنشاء تقرير المخزون
[2025-07-28 18:13:12] [INFO] تم الحصول على 51 عنصر مخزون
[2025-07-28 18:13:12] [INFO] تم تحميل تقرير inventory بنجاح
[2025-07-28 18:13:19] [INFO] تم إنشاء تقرير العملاء
[2025-07-28 18:13:19] [ERROR] خطأ في الحصول على بيانات العملاء: Invalid column name 'Balance'.
Invalid column name 'TotalAmount'.
[2025-07-28 18:13:19] [INFO] تم تحميل تقرير customers بنجاح
[2025-07-28 18:13:24] [ERROR] خطأ في الحصول على بيانات الموردين: Invalid column name 'Balance'.
Invalid column name 'Balance'.
[2025-07-28 18:13:24] [INFO] تم تحميل تقرير suppliers بنجاح
