using System;
using System.Collections.Generic;

namespace PharmacyManagement.Models
{
    /// <summary>
    /// نموذج تحويل المخزون
    /// </summary>
    public class StockTransfer
    {
        /// <summary>
        /// معرف التحويل
        /// </summary>
        public int TransferID { get; set; }

        /// <summary>
        /// رقم التحويل
        /// </summary>
        public string TransferNumber { get; set; }

        /// <summary>
        /// تاريخ التحويل
        /// </summary>
        public DateTime TransferDate { get; set; }

        /// <summary>
        /// الفرع المرسل
        /// </summary>
        public int FromBranchID { get; set; }
        public string FromBranchName { get; set; }

        /// <summary>
        /// الفرع المستقبل
        /// </summary>
        public int ToBranchID { get; set; }
        public string ToBranchName { get; set; }

        /// <summary>
        /// المخزن المرسل
        /// </summary>
        public int FromWarehouseID { get; set; }
        public string FromWarehouseName { get; set; }

        /// <summary>
        /// المخزن المستقبل
        /// </summary>
        public int ToWarehouseID { get; set; }
        public string ToWarehouseName { get; set; }

        /// <summary>
        /// الحالة
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// ملاحظات
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// منشئ التحويل
        /// </summary>
        public int? CreatedBy { get; set; }
        public string CreatedByName { get; set; }

        /// <summary>
        /// تاريخ الطلب
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// منشئ التحويل
        /// </summary>
        public int? RequestedBy { get; set; }
        public string RequestedByName { get; set; }

        /// <summary>
        /// تاريخ الطلب
        /// </summary>
        public DateTime RequestDate { get; set; }

        /// <summary>
        /// الموافقة عليه
        /// </summary>
        public int? ApprovedBy { get; set; }
        public string ApprovedByName { get; set; }

        /// <summary>
        /// تاريخ الموافقة
        /// </summary>
        public DateTime? ApprovedDate { get; set; }
        public DateTime? ApprovalDate { get; set; }

        /// <summary>
        /// من قام بشحنه
        /// </summary>
        public int? ShippedBy { get; set; }
        public string ShippedByName { get; set; }

        /// <summary>
        /// تاريخ الشحن
        /// </summary>
        public DateTime? ShippedDate { get; set; }
        public DateTime? ShipmentDate { get; set; }

        /// <summary>
        /// من قام باستلامه
        /// </summary>
        public int? ReceivedBy { get; set; }
        public string ReceivedByName { get; set; }

        /// <summary>
        /// تاريخ الاستلام
        /// </summary>
        public DateTime? ReceivedDate { get; set; }
        public DateTime? ReceiptDate { get; set; }

        /// <summary>
        /// الكمية الكلية
        /// </summary>
        public int TotalQuantity { get; set; }

        /// <summary>
        /// القيمة الكلية
        /// </summary>
        public decimal TotalValue { get; set; }

        /// <summary>
        /// أولوية التحويل
        /// </summary>
        public string Priority { get; set; }

        /// <summary>
        /// نوع التحويل
        /// </summary>
        public string TransferType { get; set; }

        /// <summary>
        /// رقم المرجع
        /// </summary>
        public string ReferenceNumber { get; set; }

        /// <summary>
        /// تفاصيل التحويل
        /// 
        /// </summary>
        public List<StockTransferDetail> Details { get; set; }

        /// <summary>
        /// Constructor
        /// </summary>
        public StockTransfer()
        {
            Details = new List<StockTransferDetail>();
            TransferDate = DateTime.Now;
            CreatedDate = DateTime.Now;
            RequestDate = DateTime.Now;
            Status = "Draft";
            Priority = "Normal";
            TransferType = "Internal";
        }
    }

    /// <summary>
    /// نموذج تفاصيل تحويل المخزون
    /// </summary>
    public class StockTransferDetail
    {
        /// <summary>
        /// معرف التفصيل
        /// </summary>
        public int DetailID { get; set; }

        /// <summary>
        /// معرف التحويل
        /// </summary>
        public int TransferDetailID { get; set; }
        public int TransferID { get; set; }

        /// <summary>
        /// معرف الدواء
        /// </summary>
        public int DrugID { get; set; }

        public string DrugCode { get; set; }
        /// <summary>
        /// اسم الدواء
        /// </summary>
        public string DrugName { get; set; }

        /// <summary>
        /// الكمية المطلوبة
        /// </summary>
        public int RequestedQuantity { get; set; }

        /// <summary>
        /// الكمية المشحونة
        /// </summary>
        public int? ApprovedQuantity { get; set; }
        public int? ShippedQuantity { get; set; }

        /// <summary>
        /// الكمية المستلمة
        /// </summary>
        public int? ReceivedQuantity { get; set; }

        /// <summary>
        /// سعر الوحدة
        /// </summary>
        public decimal? UnitPrice { get; set; }

        /// <summary>
        /// إجمالي القيمة
        /// </summary>
        public decimal TotalValue 
        { 
            get 
            { 
                return RequestedQuantity * (UnitPrice ?? 0); 
            } 
        }

        /// <summary>
        /// ملاحظات
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// رقم الدفعة
        /// </summary>
        public string BatchNumber { get; set; }

        /// <summary>
        /// تاريخ انتهاء الصلاحية
        /// </summary>
        public DateTime? ExpiryDate { get; set; }
    }
}

















