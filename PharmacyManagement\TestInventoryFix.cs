using System;
using System.Collections.Generic;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement
{
    /// <summary>
    /// فئة اختبار لإصلاح مشكلة المخزون
    /// </summary>
    public static class TestInventoryFix
    {
        /// <summary>
        /// اختبار جلب عناصر المخزون
        /// </summary>
        public static void TestGetInventoryItems()
        {
            try
            {
                Console.WriteLine("بدء اختبار جلب عناصر المخزون...");
                
                var inventoryItems = InventoryManager.GetAllInventoryItems();
                
                Console.WriteLine($"تم جلب {inventoryItems.Count} عنصر من المخزون");
                
                if (inventoryItems.Count > 0)
                {
                    Console.WriteLine("أول 5 عناصر:");
                    for (int i = 0; i < Math.Min(5, inventoryItems.Count); i++)
                    {
                        var item = inventoryItems[i];
                        Console.WriteLine($"- {item.DrugCode}: {item.TradeName}");
                        Console.WriteLine($"  الفئة: {item.CategoryName ?? "غير محدد"}");
                        Console.WriteLine($"  المصنع: {item.ManufacturerName ?? "غير محدد"}");
                        Console.WriteLine($"  المخزون: {item.CurrentStock}");
                        Console.WriteLine("  ---");
                    }
                }
                else
                {
                    Console.WriteLine("لا توجد عناصر في المخزون");
                }
                
                Console.WriteLine("تم اختبار جلب المخزون بنجاح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في اختبار المخزون: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
                
                MessageBox.Show($"خطأ في اختبار المخزون: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        /// <summary>
        /// اختبار جلب الأدوية منخفضة المخزون
        /// </summary>
        public static void TestGetLowStockDrugs()
        {
            try
            {
                Console.WriteLine("بدء اختبار جلب الأدوية منخفضة المخزون...");
                
                var lowStockDrugs = DrugManager.GetLowStockDrugs();
                
                Console.WriteLine($"تم جلب {lowStockDrugs.Count} دواء منخفض المخزون");
                
                if (lowStockDrugs.Count > 0)
                {
                    Console.WriteLine("الأدوية منخفضة المخزون:");
                    foreach (var drug in lowStockDrugs)
                    {
                        Console.WriteLine($"- {drug.DrugCode}: {drug.DrugName}");
                        Console.WriteLine($"  الفئة: {drug.CategoryName ?? "غير محدد"}");
                        Console.WriteLine($"  المصنع: {drug.ManufacturerName ?? "غير محدد"}");
                        Console.WriteLine($"  الحد الأدنى: {drug.MinStock}");
                        Console.WriteLine("  ---");
                    }
                }
                else
                {
                    Console.WriteLine("لا توجد أدوية منخفضة المخزون");
                }
                
                Console.WriteLine("تم اختبار الأدوية منخفضة المخزون بنجاح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في اختبار الأدوية منخفضة المخزون: {ex.Message}");
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        /// <summary>
        /// اختبار جلب الأدوية منتهية الصلاحية
        /// </summary>
        public static void TestGetExpiredDrugs()
        {
            try
            {
                Console.WriteLine("بدء اختبار جلب الأدوية منتهية الصلاحية...");
                
                var expiredDrugs = DrugManager.GetExpiredDrugs();
                
                Console.WriteLine($"تم جلب {expiredDrugs.Count} دواء منتهي الصلاحية");
                
                if (expiredDrugs.Count > 0)
                {
                    Console.WriteLine("الأدوية منتهية الصلاحية:");
                    foreach (var drug in expiredDrugs)
                    {
                        Console.WriteLine($"- {drug.DrugCode}: {drug.DrugName}");
                        Console.WriteLine($"  الفئة: {drug.CategoryName ?? "غير محدد"}");
                        Console.WriteLine($"  المصنع: {drug.ManufacturerName ?? "غير محدد"}");
                        Console.WriteLine($"  تاريخ الانتهاء: {drug.ExpiryDate?.ToString("yyyy/MM/dd") ?? "غير محدد"}");
                        Console.WriteLine("  ---");
                    }
                }
                else
                {
                    Console.WriteLine("لا توجد أدوية منتهية الصلاحية");
                }
                
                Console.WriteLine("تم اختبار الأدوية منتهية الصلاحية بنجاح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في اختبار الأدوية منتهية الصلاحية: {ex.Message}");
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        /// <summary>
        /// اختبار جلب فئات الأدوية
        /// </summary>
        public static void TestGetDrugCategories()
        {
            try
            {
                Console.WriteLine("بدء اختبار جلب فئات الأدوية...");
                
                string query = "SELECT CategoryID, CategoryName FROM DrugCategories ORDER BY CategoryName";
                var dataTable = DatabaseHelper.ExecuteQuery(query);
                
                Console.WriteLine($"تم جلب {dataTable.Rows.Count} فئة");
                
                if (dataTable.Rows.Count > 0)
                {
                    Console.WriteLine("فئات الأدوية:");
                    foreach (System.Data.DataRow row in dataTable.Rows)
                    {
                        Console.WriteLine($"- {row["CategoryID"]}: {row["CategoryName"]}");
                    }
                }
                else
                {
                    Console.WriteLine("لا توجد فئات أدوية");
                }
                
                Console.WriteLine("تم اختبار فئات الأدوية بنجاح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في اختبار فئات الأدوية: {ex.Message}");
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        /// <summary>
        /// تشغيل جميع اختبارات المخزون
        /// </summary>
        public static void RunAllInventoryTests()
        {
            try
            {
                Console.WriteLine("=== بدء اختبارات المخزون الشاملة ===");
                
                // اختبار فئات الأدوية أولاً
                TestGetDrugCategories();
                
                Console.WriteLine("\n" + new string('-', 50) + "\n");
                
                // اختبار عناصر المخزون
                TestGetInventoryItems();
                
                Console.WriteLine("\n" + new string('-', 50) + "\n");
                
                // اختبار الأدوية منخفضة المخزون
                TestGetLowStockDrugs();
                
                Console.WriteLine("\n" + new string('-', 50) + "\n");
                
                // اختبار الأدوية منتهية الصلاحية
                TestGetExpiredDrugs();
                
                Console.WriteLine("\n=== انتهت جميع اختبارات المخزون ===");
                
                MessageBox.Show("تم تشغيل جميع اختبارات المخزون بنجاح!\nتحقق من وحدة التحكم للتفاصيل.", 
                              "اختبارات المخزون", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تشغيل اختبارات المخزون: {ex.Message}");
                MessageBox.Show($"خطأ في اختبارات المخزون: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        /// <summary>
        /// اختبار سريع للمخزون
        /// </summary>
        public static void QuickInventoryTest()
        {
            try
            {
                Console.WriteLine("اختبار سريع للمخزون...");

                // اختبار الاتصال بقاعدة البيانات
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    Console.WriteLine("✅ تم الاتصال بقاعدة البيانات بنجاح");

                    // اختبار وجود جدول DrugCategories
                    var cmd = connection.CreateCommand();
                    cmd.CommandText = "SELECT COUNT(*) FROM DrugCategories";
                    var count = cmd.ExecuteScalar();
                    Console.WriteLine($"✅ جدول فئات الأدوية يحتوي على {count} سجل");

                    // اختبار وجود جدول Drugs
                    cmd.CommandText = "SELECT COUNT(*) FROM Drugs";
                    count = cmd.ExecuteScalar();
                    Console.WriteLine($"✅ جدول الأدوية يحتوي على {count} سجل");

                    // اختبار وجود جدول Inventory
                    cmd.CommandText = "SELECT COUNT(*) FROM Inventory";
                    count = cmd.ExecuteScalar();
                    Console.WriteLine($"✅ جدول المخزون يحتوي على {count} سجل");

                    // اختبار الاستعلام المحدث
                    cmd.CommandText = @"
                        SELECT TOP 1
                            d.DrugID,
                            d.DrugCode,
                            d.DrugName as TradeName,
                            c.CategoryName,
                            m.ManufacturerName,
                            d.PurchasePrice,
                            d.SalePrice,
                            d.MinStock as MinStockLevel,
                            d.MaxStock as MaxStockLevel,
                            d.Unit,
                            d.IsActive
                        FROM Drugs d
                        LEFT JOIN DrugCategories c ON d.CategoryID = c.CategoryID
                        LEFT JOIN Manufacturers m ON d.ManufacturerID = m.ManufacturerID
                        WHERE d.IsActive = 1";

                    using (var reader = cmd.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            Console.WriteLine($"✅ تم اختبار الاستعلام بنجاح:");
                            Console.WriteLine($"   - كود الدواء: {reader["DrugCode"]}");
                            Console.WriteLine($"   - اسم الدواء: {reader["TradeName"]}");
                            Console.WriteLine($"   - الفئة: {reader["CategoryName"] ?? "غير محدد"}");
                            Console.WriteLine($"   - المصنع: {reader["ManufacturerName"] ?? "غير محدد"}");
                        }
                        else
                        {
                            Console.WriteLine("⚠️ لا توجد أدوية في قاعدة البيانات");
                        }
                    }
                }

                Console.WriteLine("✅ الاختبار السريع مكتمل بنجاح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ فشل الاختبار السريع: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
                throw;
            }
        }
    }
}
