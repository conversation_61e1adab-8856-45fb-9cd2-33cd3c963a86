using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using PharmacyManagement.Models;

namespace PharmacyManagement.Classes
{
    /// <summary>
    /// مدير الشؤون المالية - Financial Manager
    /// </summary>
    public static class FinancialManager
    {
        #region Financial Summary - الملخص المالي

        /// <summary>
        /// الحصول على الملخص المالي
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <returns>الملخص المالي</returns>
        public static FinancialSummary GetFinancialSummary(DateTime fromDate, DateTime toDate)
        {
            var summary = new FinancialSummary();
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    
                    // إجمالي المبيعات
                    string salesQuery = @"
                        SELECT ISNULL(SUM(TotalAmount), 0) 
                        FROM Invoices 
                        WHERE InvoiceDate BETWEEN @FromDate AND @ToDate 
                        AND InvoiceType = 'Sale'";
                    
                    using (var command = new SqlCommand(salesQuery, connection))
                    {
                        command.Parameters.AddWithValue("@FromDate", fromDate);
                        command.Parameters.AddWithValue("@ToDate", toDate);
                        summary.TotalSales = Convert.ToDecimal(command.ExecuteScalar());
                    }
                    
                    // إجمالي المشتريات
                    string purchasesQuery = @"
                        SELECT ISNULL(SUM(TotalAmount), 0) 
                        FROM Invoices 
                        WHERE InvoiceDate BETWEEN @FromDate AND @ToDate 
                        AND InvoiceType = 'Purchase'";
                    
                    using (var command = new SqlCommand(purchasesQuery, connection))
                    {
                        command.Parameters.AddWithValue("@FromDate", fromDate);
                        command.Parameters.AddWithValue("@ToDate", toDate);
                        summary.TotalPurchases = Convert.ToDecimal(command.ExecuteScalar());
                    }
                    
                    // إجمالي المصروفات
                    string expensesQuery = @"
                        SELECT ISNULL(SUM(Amount), 0) 
                        FROM Expenses 
                        WHERE ExpenseDate BETWEEN @FromDate AND @ToDate";
                    
                    using (var command = new SqlCommand(expensesQuery, connection))
                    {
                        command.Parameters.AddWithValue("@FromDate", fromDate);
                        command.Parameters.AddWithValue("@ToDate", toDate);
                        summary.TotalExpenses = Convert.ToDecimal(command.ExecuteScalar());
                    }
                    
                    // حساب الأرباح وصافي الربح
                    summary.TotalProfit = summary.TotalSales - summary.TotalPurchases;
                    summary.NetProfit = summary.TotalProfit - summary.TotalExpenses;
                }
                
                LogManager.LogInfo($"تم الحصول على الملخص المالي من {fromDate:yyyy-MM-dd} إلى {toDate:yyyy-MM-dd}");
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على الملخص المالي: {ex.Message}");
                throw;
            }
            return summary;
        }

        #endregion

        #region Transactions - المعاملات المالية

        /// <summary>
        /// الحصول على المعاملات المالية
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <returns>قائمة المعاملات</returns>
        public static List<FinancialTransaction> GetTransactions(DateTime fromDate, DateTime toDate)
        {
            var transactions = new List<FinancialTransaction>();
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT 
                            'فاتورة مبيعات' as TransactionType,
                            InvoiceNumber as Reference,
                            InvoiceDate as TransactionDate,
                            TotalAmount as Amount,
                            'دخل' as Category,
                            CustomerName as Description
                        FROM Invoices i
                        LEFT JOIN Customers c ON i.CustomerID = c.CustomerID
                        WHERE i.InvoiceDate BETWEEN @FromDate AND @ToDate 
                        AND i.InvoiceType = 'Sale'
                        
                        UNION ALL
                        
                        SELECT 
                            'فاتورة مشتريات' as TransactionType,
                            InvoiceNumber as Reference,
                            InvoiceDate as TransactionDate,
                            -TotalAmount as Amount,
                            'مشتريات' as Category,
                            SupplierName as Description
                        FROM Invoices i
                        LEFT JOIN Suppliers s ON i.SupplierID = s.SupplierID
                        WHERE i.InvoiceDate BETWEEN @FromDate AND @ToDate 
                        AND i.InvoiceType = 'Purchase'
                        
                        UNION ALL
                        
                        SELECT 
                            'مصروف' as TransactionType,
                            ExpenseNumber as Reference,
                            ExpenseDate as TransactionDate,
                            -Amount as Amount,
                            Category as Category,
                            Description as Description
                        FROM Expenses
                        WHERE ExpenseDate BETWEEN @FromDate AND @ToDate
                        
                        ORDER BY TransactionDate DESC";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@FromDate", fromDate);
                        command.Parameters.AddWithValue("@ToDate", toDate);
                        
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                transactions.Add(new FinancialTransaction
                                {
                                    TransactionType = reader["TransactionType"].ToString(),
                                    Reference = reader["Reference"].ToString(),
                                    TransactionDate = Convert.ToDateTime(reader["TransactionDate"]),
                                    Amount = Convert.ToDecimal(reader["Amount"]),
                                    Category = reader["Category"].ToString(),
                                    Description = reader["Description"].ToString()
                                });
                            }
                        }
                    }
                }
                
                LogManager.LogInfo($"تم الحصول على {transactions.Count} معاملة مالية");
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على المعاملات المالية: {ex.Message}");
                throw;
            }
            return transactions;
        }

        #endregion

        #region Cash Flow - التدفق النقدي

        /// <summary>
        /// الحصول على التدفق النقدي الشهري
        /// </summary>
        /// <param name="year">السنة</param>
        /// <returns>قائمة التدفق النقدي الشهري</returns>
        public static List<MonthlyCashFlow> GetMonthlyCashFlow(int year)
        {
            var cashFlow = new List<MonthlyCashFlow>();
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        WITH Months AS (
                            SELECT 1 as Month UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 
                            UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 
                            UNION SELECT 9 UNION SELECT 10 UNION SELECT 11 UNION SELECT 12
                        ),
                        Sales AS (
                            SELECT 
                                MONTH(InvoiceDate) as Month,
                                SUM(TotalAmount) as TotalSales
                            FROM Invoices 
                            WHERE YEAR(InvoiceDate) = @Year AND InvoiceType = 'Sale'
                            GROUP BY MONTH(InvoiceDate)
                        ),
                        Purchases AS (
                            SELECT 
                                MONTH(InvoiceDate) as Month,
                                SUM(TotalAmount) as TotalPurchases
                            FROM Invoices 
                            WHERE YEAR(InvoiceDate) = @Year AND InvoiceType = 'Purchase'
                            GROUP BY MONTH(InvoiceDate)
                        ),
                        Expenses AS (
                            SELECT 
                                MONTH(ExpenseDate) as Month,
                                SUM(Amount) as TotalExpenses
                            FROM Expenses 
                            WHERE YEAR(ExpenseDate) = @Year
                            GROUP BY MONTH(ExpenseDate)
                        )
                        SELECT 
                            m.Month,
                            ISNULL(s.TotalSales, 0) as TotalSales,
                            ISNULL(p.TotalPurchases, 0) as TotalPurchases,
                            ISNULL(e.TotalExpenses, 0) as TotalExpenses
                        FROM Months m
                        LEFT JOIN Sales s ON m.Month = s.Month
                        LEFT JOIN Purchases p ON m.Month = p.Month
                        LEFT JOIN Expenses e ON m.Month = e.Month
                        ORDER BY m.Month";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@Year", year);
                        
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var month = Convert.ToInt32(reader["Month"]);
                                var sales = Convert.ToDecimal(reader["TotalSales"]);
                                var purchases = Convert.ToDecimal(reader["TotalPurchases"]);
                                var expenses = Convert.ToDecimal(reader["TotalExpenses"]);
                                
                                cashFlow.Add(new MonthlyCashFlow
                                {
                                    Month = month,
                                    MonthName = GetMonthName(month),
                                    TotalSales = sales,
                                    TotalPurchases = purchases,
                                    TotalExpenses = expenses,
                                    NetCashFlow = sales - purchases - expenses
                                });
                            }
                        }
                    }
                }
                
                LogManager.LogInfo($"تم الحصول على التدفق النقدي لسنة {year}");
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على التدفق النقدي: {ex.Message}");
                throw;
            }
            return cashFlow;
        }

        #endregion

        #region Expenses Management - إدارة المصروفات

        /// <summary>
        /// إضافة مصروف جديد
        /// </summary>
        /// <param name="expense">بيانات المصروف</param>
        /// <returns>معرف المصروف</returns>
        public static int AddExpense(Expense expense)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        INSERT INTO Expenses (ExpenseNumber, Description, Amount, Category, 
                                            ExpenseDate, PaymentMethod, Notes, CreatedDate)
                        VALUES (@ExpenseNumber, @Description, @Amount, @Category, 
                               @ExpenseDate, @PaymentMethod, @Notes, @CreatedDate);
                        SELECT SCOPE_IDENTITY();";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@ExpenseNumber", expense.ExpenseNumber);
                        command.Parameters.AddWithValue("@Description", expense.Description);
                        command.Parameters.AddWithValue("@Amount", expense.Amount);
                        command.Parameters.AddWithValue("@Category", expense.Category);
                        command.Parameters.AddWithValue("@ExpenseDate", expense.ExpenseDate);
                        command.Parameters.AddWithValue("@PaymentMethod", expense.PaymentMethod);
                        command.Parameters.AddWithValue("@Notes", expense.Notes ?? "");
                        command.Parameters.AddWithValue("@CreatedDate", DateTime.Now);

                        int expenseId = Convert.ToInt32(command.ExecuteScalar());
                        LogManager.LogInfo($"تم إضافة مصروف جديد: {expense.Description} - {expense.Amount:N2}");
                        return expenseId;
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في إضافة المصروف: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على المصروفات في فترة محددة
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <returns>قائمة المصروفات</returns>
        public static List<Expense> GetExpenses(DateTime fromDate, DateTime toDate)
        {
            var expenses = new List<Expense>();

            try
            {
                string query = @"
                    SELECT ExpenseID, ExpenseNumber, ExpenseDate, Category, Description,
                           Amount, PaymentMethod, Notes, CreatedBy, CreatedDate
                    FROM Expenses
                    WHERE ExpenseDate BETWEEN @FromDate AND @ToDate
                    ORDER BY ExpenseDate DESC";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@FromDate", fromDate);
                        command.Parameters.AddWithValue("@ToDate", toDate);

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                expenses.Add(new Expense
                                {
                                    ExpenseID = Convert.ToInt32(reader["ExpenseID"]),
                                    ExpenseNumber = reader["ExpenseNumber"].ToString(),
                                    ExpenseDate = Convert.ToDateTime(reader["ExpenseDate"]),
                                    Category = reader["Category"].ToString(),
                                    Description = reader["Description"].ToString(),
                                    Amount = Convert.ToDecimal(reader["Amount"]),
                                    PaymentMethod = reader["PaymentMethod"].ToString(),
                                    Notes = reader["Notes"].ToString(),
                                    CreatedBy = Convert.ToInt32(reader["CreatedBy"]),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"])
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في جلب المصروفات: {ex.Message}");
            }

            return expenses;
        }

        /// <summary>
        /// تحديث مصروف
        /// </summary>
        /// <param name="expense">المصروف</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public static bool UpdateExpense(Expense expense)
        {
            try
            {
                string query = @"
                    UPDATE Expenses
                    SET ExpenseDate = @ExpenseDate, Category = @Category, Description = @Description,
                        Amount = @Amount, PaymentMethod = @PaymentMethod, Notes = @Notes,
                        ModifiedDate = @ModifiedDate
                    WHERE ExpenseID = @ExpenseID";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@ExpenseID", expense.ExpenseID);
                        command.Parameters.AddWithValue("@ExpenseDate", expense.ExpenseDate);
                        command.Parameters.AddWithValue("@Category", expense.Category);
                        command.Parameters.AddWithValue("@Description", expense.Description);
                        command.Parameters.AddWithValue("@Amount", expense.Amount);
                        command.Parameters.AddWithValue("@PaymentMethod", expense.PaymentMethod);
                        command.Parameters.AddWithValue("@Notes", expense.Notes ?? "");
                        command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);

                        int rowsAffected = command.ExecuteNonQuery();
                        return rowsAffected > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تحديث المصروف: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// حذف مصروف
        /// </summary>
        /// <param name="expenseID">معرف المصروف</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public static bool DeleteExpense(int expenseID)
        {
            try
            {
                string query = "DELETE FROM Expenses WHERE ExpenseID = @ExpenseID";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@ExpenseID", expenseID);

                        int rowsAffected = command.ExecuteNonQuery();
                        return rowsAffected > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في حذف المصروف: {ex.Message}");
                return false;
            }
        }



        /// <summary>
        /// توليد رقم مصروف جديد
        /// </summary>
        /// <returns>رقم المصروف</returns>
        public static string GenerateExpenseNumber()
        {
            try
            {
                string query = "SELECT COUNT(*) FROM Expenses WHERE YEAR(ExpenseDate) = YEAR(GETDATE())";

                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        int count = Convert.ToInt32(command.ExecuteScalar()) + 1;
                        return "EXP-" + DateTime.Now.Year.ToString() + "-" + count.ToString("D4");
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في توليد رقم المصروف: " + ex.Message);
                return "EXP-" + DateTime.Now.Year.ToString() + "-" + DateTime.Now.Ticks.ToString().Substring(10);
            }
        }

        #endregion

        #region Helper Methods - الطرق المساعدة

        /// <summary>
        /// الحصول على اسم الشهر بالعربية
        /// </summary>
        /// <param name="month">رقم الشهر</param>
        /// <returns>اسم الشهر</returns>
        private static string GetMonthName(int month)
        {
            string[] monthNames = {
                "", "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
            };
            return monthNames[month];
        }

        /// <summary>
        /// الحصول على معرف السنة المالية الحالية
        /// </summary>
        /// <returns>معرف السنة المالية</returns>
        public static int GetCurrentFiscalYearId()
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT TOP 1 FiscalYearID
                        FROM FiscalYears
                        WHERE IsActive = 1
                        AND GETDATE() BETWEEN StartDate AND EndDate
                        ORDER BY StartDate DESC";

                    using (var command = new SqlCommand(query, connection))
                    {
                        var result = command.ExecuteScalar();
                        return result != null ? Convert.ToInt32(result) : 1;
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في الحصول على السنة المالية الحالية: " + ex.Message);
                return 1; // افتراضي
            }
        }

        /// <summary>
        /// توليد رقم إيصال جديد
        /// </summary>
        /// <returns>رقم الإيصال</returns>
        public static string GenerateReceiptNumber()
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT COUNT(*) + 1
                        FROM ReceiptVouchers
                        WHERE YEAR(VoucherDate) = YEAR(GETDATE())";

                    using (var command = new SqlCommand(query, connection))
                    {
                        int nextNumber = Convert.ToInt32(command.ExecuteScalar());
                        return "REC-" + DateTime.Now.Year.ToString() + "-" + nextNumber.ToString("D6");
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في توليد رقم الإيصال: " + ex.Message);
                return "REC-" + DateTime.Now.Year.ToString() + "-" + DateTime.Now.Ticks.ToString().Substring(10);
            }
        }

        /// <summary>
        /// توليد رقم دفع جديد
        /// </summary>
        /// <returns>رقم الدفع</returns>
        public static string GeneratePaymentNumber()
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT COUNT(*) + 1
                        FROM PaymentVouchers
                        WHERE YEAR(VoucherDate) = YEAR(GETDATE())";

                    using (var command = new SqlCommand(query, connection))
                    {
                        int nextNumber = Convert.ToInt32(command.ExecuteScalar());
                        return "PAY-" + DateTime.Now.Year.ToString() + "-" + nextNumber.ToString("D6");
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في توليد رقم الدفع: " + ex.Message);
                return "PAY-" + DateTime.Now.Year.ToString() + "-" + DateTime.Now.Ticks.ToString().Substring(10);
            }
        }

        /// <summary>
        /// الحصول على سند دفع
        /// </summary>
        /// <param name="voucherId">معرف السند</param>
        /// <returns>سند الدفع</returns>
        public static object GetPaymentVoucher(int voucherId)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = "SELECT * FROM PaymentVouchers WHERE VoucherID = @VoucherID";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@VoucherID", voucherId);
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return new
                                {
                                    VoucherID = Convert.ToInt32(reader["VoucherID"]),
                                    VoucherNumber = reader["VoucherNumber"].ToString(),
                                    VoucherDate = Convert.ToDateTime(reader["VoucherDate"]),
                                    Amount = Convert.ToDecimal(reader["Amount"]),
                                    Description = reader["Description"].ToString()
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في الحصول على سند الدفع: " + ex.Message);
            }
            return null;
        }

        /// <summary>
        /// تحديث سند دفع
        /// </summary>
        /// <param name="voucher">سند الدفع</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public static bool UpdatePaymentVoucher(object voucher)
        {
            try
            {
                // منطق تحديث سند الدفع
                LogManager.LogInfo("تم تحديث سند الدفع");
                return true;
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في تحديث سند الدفع: " + ex.Message);
                return false;
            }
        }

        /// <summary>
        /// إضافة سند دفع
        /// </summary>
        /// <param name="voucher">سند الدفع</param>
        /// <returns>true إذا تم الإضافة بنجاح</returns>
        public static bool AddPaymentVoucher(object voucher)
        {
            try
            {
                // منطق إضافة سند الدفع
                LogManager.LogInfo("تم إضافة سند الدفع");
                return true;
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في إضافة سند الدفع: " + ex.Message);
                return false;
            }
        }

        /// <summary>
        /// الحصول على سند قبض
        /// </summary>
        /// <param name="voucherId">معرف السند</param>
        /// <returns>سند القبض</returns>
        public static object GetReceiptVoucher(int voucherId)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = "SELECT * FROM ReceiptVouchers WHERE VoucherID = @VoucherID";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@VoucherID", voucherId);
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return new
                                {
                                    VoucherID = Convert.ToInt32(reader["VoucherID"]),
                                    ReceiptNumber = reader["VoucherNumber"].ToString(),
                                    ReceiptDate = Convert.ToDateTime(reader["VoucherDate"]),
                                    CustomerID = reader["CustomerID"] != DBNull.Value ? (int?)Convert.ToInt32(reader["CustomerID"]) : null,
                                    Amount = Convert.ToDecimal(reader["Amount"]),
                                    PaymentMethod = reader["PaymentMethod"] != DBNull.Value ? reader["PaymentMethod"].ToString() : "",
                                    BankName = reader["BankName"] != DBNull.Value ? reader["BankName"].ToString() : null,
                                    CheckNumber = reader["CheckNumber"] != DBNull.Value ? reader["CheckNumber"].ToString() : null,
                                    CheckDate = reader["CheckDate"] != DBNull.Value ? (DateTime?)Convert.ToDateTime(reader["CheckDate"]) : null,
                                    Description = reader["Description"] != DBNull.Value ? reader["Description"].ToString() : ""
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في الحصول على سند القبض: " + ex.Message);
            }
            return null;
        }

        /// <summary>
        /// تحديث سند قبض
        /// </summary>
        /// <param name="voucher">سند القبض</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public static bool UpdateReceiptVoucher(object voucher)
        {
            try
            {
                // منطق تحديث سند القبض
                LogManager.LogInfo("تم تحديث سند القبض");
                return true;
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في تحديث سند القبض: " + ex.Message);
                return false;
            }
        }

        /// <summary>
        /// إضافة سند قبض
        /// </summary>
        /// <param name="voucher">سند القبض</param>
        /// <returns>true إذا تم الإضافة بنجاح</returns>
        public static bool AddReceiptVoucher(object voucher)
        {
            try
            {
                // منطق إضافة سند القبض
                LogManager.LogInfo("تم إضافة سند القبض");
                return true;
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في إضافة سند القبض: " + ex.Message);
                return false;
            }
        }

        #endregion
    }

    #region Financial Models - نماذج البيانات المالية

    /// <summary>
    /// نموذج الملخص المالي
    /// </summary>
    public class FinancialSummary
    {
        public decimal TotalSales { get; set; }
        public decimal TotalPurchases { get; set; }
        public decimal TotalExpenses { get; set; }
        public decimal TotalProfit { get; set; }
        public decimal NetProfit { get; set; }
    }

    /// <summary>
    /// نموذج المعاملة المالية
    /// </summary>
    public class FinancialTransaction
    {
        public string TransactionType { get; set; }
        public string Reference { get; set; }
        public DateTime TransactionDate { get; set; }
        public decimal Amount { get; set; }
        public string Category { get; set; }
        public string Description { get; set; }

        public string ReferenceNumber { get; set; }

        public decimal Balance { get; set; }
    }

    /// <summary>
    /// نموذج التدفق النقدي الشهري
    /// </summary>
    public class MonthlyCashFlow
    {
        public int Month { get; set; }

        public int Year { get; set; }
        public string MonthName { get; set; }
        public decimal TotalSales { get; set; }
        public decimal TotalPurchases { get; set; }
        public decimal TotalExpenses { get; set; }
        public decimal NetCashFlow { get; set; }
        public decimal Sales { get; set; }

        public decimal Purchases { get; set; }

        public decimal Expenses { get; set; }



    }



    #endregion
}
