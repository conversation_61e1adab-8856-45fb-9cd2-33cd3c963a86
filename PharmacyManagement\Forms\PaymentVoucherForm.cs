using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نموذج سند الصرف المحسن - Enhanced Payment Voucher Form
    /// تصميم مسطح حديث مع بيانات تجريبية شاملة
    /// </summary>
    public partial class PaymentVoucherForm : Form
    {
        #region Fields

        private int? _paymentId;
        private bool _isEditMode;
        private List<PaymentVoucherSupplier> _sampleSuppliers;
        private List<PaymentVoucherData> _samplePayments;

        #endregion

        #region Constructor

        public PaymentVoucherForm(int? paymentId = null)
        {
            InitializeComponent();
            _paymentId = paymentId;
            _isEditMode = paymentId.HasValue;
            
            SetupForm();
            LoadData();
            
            if (_isEditMode)
                LoadPaymentData();
        }

        #endregion

        #region Setup Methods

        private void SetupForm()
        {
            // إعداد النموذج الأساسي
            this.Text = _isEditMode ? "💰 تعديل سند صرف" : "💰 سند صرف جديد";
            this.WindowState = FormWindowState.Normal;
            this.StartPosition = FormStartPosition.CenterParent;
            this.BackColor = Color.White;
            this.Font = new Font("Segoe UI", 9F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // إعداد التصميم المسطح
            SetupFlatDesign();

            // إعداد التحقق من البيانات
            SetupValidation();

            // إعداد الأحداث
            SetupEvents();

            // تحميل البيانات التجريبية
            LoadSampleData();
        }

        private void SetupFlatDesign()
        {
            // تنسيق الأزرار المحسن
            btnSave.BackColor = Color.FromArgb(52, 152, 219);
            btnSave.ForeColor = Color.White;
            btnSave.FlatStyle = FlatStyle.Flat;
            btnSave.FlatAppearance.BorderSize = 0;
            btnSave.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            btnSave.Cursor = Cursors.Hand;

            btnPrint.BackColor = Color.FromArgb(155, 89, 182);
            btnPrint.ForeColor = Color.White;
            btnPrint.FlatStyle = FlatStyle.Flat;
            btnPrint.FlatAppearance.BorderSize = 0;
            btnPrint.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            btnPrint.Cursor = Cursors.Hand;

            btnCancel.BackColor = Color.FromArgb(149, 165, 166);
            btnCancel.ForeColor = Color.White;
            btnCancel.FlatStyle = FlatStyle.Flat;
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            btnCancel.Cursor = Cursors.Hand;

            // تنسيق حقول النص
            txtAmount.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            txtAmount.TextAlign = HorizontalAlignment.Right;

            // تنسيق القوائم المنسدلة
            cmbSupplier.FlatStyle = FlatStyle.Flat;
            cmbPaymentMethod.FlatStyle = FlatStyle.Flat;
        }

        private void SetupValidation()
        {
            // إعداد التحقق من البيانات
            txtAmount.KeyPress += (s, e) => {
                if (!char.IsControl(e.KeyChar) && !char.IsDigit(e.KeyChar) && e.KeyChar != '.')
                    e.Handled = true;
            };
        }

        private void SetupEvents()
        {
            // ربط الأحداث
            btnSave.Click += BtnSave_Click;
            btnCancel.Click += BtnCancel_Click;
            btnPrint.Click += BtnPrint_Click;
            cmbSupplier.SelectedIndexChanged += CmbSupplier_SelectedIndexChanged;
            cmbPaymentMethod.SelectedIndexChanged += CmbPaymentMethod_SelectedIndexChanged;
        }

        #endregion

        #region Load Data Methods

        private void LoadData()
        {
            try
            {
                // تحميل الموردين
                LoadSuppliers();
                
                // تحميل طرق الدفع
                LoadPaymentMethods();
                
                // إعداد القيم الافتراضية
                SetDefaultValues();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadSuppliers()
        {
            try
            {
                // استخدام البيانات التجريبية إذا لم تكن متوفرة من قاعدة البيانات
                if (_sampleSuppliers?.Any() == true)
                {
                    cmbSupplier.DataSource = _sampleSuppliers;
                    cmbSupplier.DisplayMember = "SupplierName";
                    cmbSupplier.ValueMember = "SupplierID";
                    cmbSupplier.SelectedIndex = -1;
                }
                else
                {
                    // محاولة تحميل من قاعدة البيانات
                    try
                    {
                        var suppliers = SupplierManager.GetAllSuppliers()
                            .Where(s => s.IsActive)
                            .OrderBy(s => s.SupplierName)
                            .ToList();

                        cmbSupplier.DataSource = suppliers;
                        cmbSupplier.DisplayMember = "SupplierName";
                        cmbSupplier.ValueMember = "SupplierID";
                        cmbSupplier.SelectedIndex = -1;
                    }
                    catch
                    {
                        // في حالة فشل قاعدة البيانات، استخدم البيانات التجريبية
                        LoadSampleSuppliers();
                        cmbSupplier.DataSource = _sampleSuppliers;
                        cmbSupplier.DisplayMember = "SupplierName";
                        cmbSupplier.ValueMember = "SupplierID";
                        cmbSupplier.SelectedIndex = -1;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الموردين: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadPaymentMethods()
        {
            var paymentMethods = new[]
            {
                new { Value = "Cash", Text = "نقدي" },
                new { Value = "Bank", Text = "بنكي" },
                new { Value = "Card", Text = "بطاقة ائتمان" }
            };

            cmbPaymentMethod.DataSource = paymentMethods;
            cmbPaymentMethod.DisplayMember = "Text";
            cmbPaymentMethod.ValueMember = "Value";
            cmbPaymentMethod.SelectedIndex = 0;
        }

        private void SetDefaultValues()
        {
            if (!_isEditMode)
            {
                txtPaymentNumber.Text = GeneratePaymentNumber();
                dtpPaymentDate.Value = DateTime.Now;
                txtAmount.Text = "0.00";
            }
        }

        private void LoadSampleData()
        {
            try
            {
                LoadSampleSuppliers();
                LoadSamplePayments();
                Console.WriteLine("تم تحميل البيانات التجريبية لسند الصرف بنجاح");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات التجريبية: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadSampleSuppliers()
        {
            _sampleSuppliers = new List<PaymentVoucherSupplier>
            {
                new PaymentVoucherSupplier { SupplierID = 1, SupplierName = "شركة الدواء الأولى", CurrentBalance = 15000.50m, Phone = "01234567", IsActive = true },
                new PaymentVoucherSupplier { SupplierID = 2, SupplierName = "شركة المضادات الحيوية", CurrentBalance = 8750.25m, Phone = "01234568", IsActive = true },
                new PaymentVoucherSupplier { SupplierID = 3, SupplierName = "شركة الفيتامينات والمكملات", CurrentBalance = 12300.75m, Phone = "01234569", IsActive = true },
                new PaymentVoucherSupplier { SupplierID = 4, SupplierName = "شركة أدوية القلب والأوعية", CurrentBalance = 22100.00m, Phone = "01234570", IsActive = true },
                new PaymentVoucherSupplier { SupplierID = 5, SupplierName = "شركة المسكنات والمضادات", CurrentBalance = 6500.30m, Phone = "01234571", IsActive = true },
                new PaymentVoucherSupplier { SupplierID = 6, SupplierName = "شركة أدوية الأطفال", CurrentBalance = 9800.80m, Phone = "01234572", IsActive = true }
            };
        }

        private void LoadSamplePayments()
        {
            _samplePayments = new List<PaymentVoucherData>
            {
                new PaymentVoucherData { PaymentID = 1001, PaymentNumber = "PAY202401001", PaymentDate = DateTime.Now.AddDays(-1), SupplierID = 1, Amount = 5000.00m, PaymentMethod = "Cash", Description = "دفعة على حساب فاتورة رقم INV001", Status = "Active" },
                new PaymentVoucherData { PaymentID = 1002, PaymentNumber = "PAY202401002", PaymentDate = DateTime.Now.AddDays(-2), SupplierID = 2, Amount = 3500.50m, PaymentMethod = "Bank", BankName = "البنك الأهلي", CheckNumber = "CHK001", Description = "تسوية مستحقات شهر ديسمبر", Status = "Active" },
                new PaymentVoucherData { PaymentID = 1003, PaymentNumber = "PAY202401003", PaymentDate = DateTime.Now.AddDays(-3), SupplierID = 3, Amount = 7200.25m, PaymentMethod = "Card", Description = "دفعة مقدمة لطلبية جديدة", Status = "Active" },
                new PaymentVoucherData { PaymentID = 1004, PaymentNumber = "PAY202401004", PaymentDate = DateTime.Now.AddDays(-5), SupplierID = 4, Amount = 12000.00m, PaymentMethod = "Bank", BankName = "بنك التسليف", CheckNumber = "CHK002", Description = "تسوية كاملة للمستحقات المتأخرة", Status = "Active" },
                new PaymentVoucherData { PaymentID = 1005, PaymentNumber = "PAY202401005", PaymentDate = DateTime.Now.AddDays(-7), SupplierID = 5, Amount = 2800.75m, PaymentMethod = "Cash", Description = "دفعة نقدية عاجلة", Status = "Active" }
            };
        }

        private void LoadPaymentData()
        {
            if (!_paymentId.HasValue) return;

            try
            {
                var paymentObj = FinancialManager.GetPaymentVoucher(_paymentId.Value);
                if (paymentObj != null)
                {
                    var payment = paymentObj as PaymentVoucher;
                    if (payment != null)
                    {
                        txtPaymentNumber.Text = payment.PaymentNumber;
                        dtpPaymentDate.Value = payment.PaymentDate;
                        cmbSupplier.SelectedValue = payment.SupplierID;
                        txtAmount.Text = payment.Amount.ToString("F2");
                        cmbPaymentMethod.SelectedValue = payment.PaymentMethod;
                        txtBankName.Text = payment.BankName ?? "";
                        txtCheckNumber.Text = payment.CheckNumber ?? "";
                        if (payment.CheckDate.HasValue)
                            dtpCheckDate.Value = payment.CheckDate.Value;
                        txtDescription.Text = payment.Description ?? "";
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات السند: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Event Handlers

        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateData()) return;

                var payment = CreatePaymentObject();
                bool success;

                if (_isEditMode)
                {
                    payment.PaymentID = _paymentId.Value;
                    success = FinancialManager.UpdatePaymentVoucher(payment);
                }
                else
                {
                    success = FinancialManager.AddPaymentVoucher(payment);
                }

                if (success)
                {
                    MessageBox.Show("تم حفظ سند الصرف بنجاح", "نجح", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("فشل في حفظ سند الصرف", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ السند: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void BtnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                if (_paymentId.HasValue)
                {
                    ReportsManager.PrintPaymentVoucher(_paymentId.Value);
                }
                else
                {
                    MessageBox.Show("يجب حفظ السند أولاً قبل الطباعة", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة السند: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CmbSupplier_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                if (cmbSupplier.SelectedValue != null)
                {
                    var supplierId = Convert.ToInt32(cmbSupplier.SelectedValue);

                    // البحث في البيانات التجريبية أولاً
                    var sampleSupplier = _sampleSuppliers?.FirstOrDefault(s => s.SupplierID == supplierId);
                    if (sampleSupplier != null)
                    {
                        lblSupplierBalance.Text = $"الرصيد: {sampleSupplier.CurrentBalance:F2} ر.ي";
                        lblSupplierBalance.ForeColor = sampleSupplier.CurrentBalance > 0 ?
                            Color.FromArgb(231, 76, 60) : Color.FromArgb(46, 204, 113);
                    }
                    else
                    {
                        // محاولة البحث في قاعدة البيانات
                        try
                        {
                            var supplier = SupplierManager.GetSupplier(supplierId);
                            if (supplier != null)
                            {
                                lblSupplierBalance.Text = $"الرصيد: {supplier.CurrentBalance:F2} ر.ي";
                                lblSupplierBalance.ForeColor = supplier.CurrentBalance > 0 ?
                                    Color.FromArgb(231, 76, 60) : Color.FromArgb(46, 204, 113);
                            }
                        }
                        catch
                        {
                            lblSupplierBalance.Text = "الرصيد: غير متاح";
                            lblSupplierBalance.ForeColor = Color.FromArgb(149, 165, 166);
                        }
                    }
                }
                else
                {
                    lblSupplierBalance.Text = "الرصيد: 0.00 ر.ي";
                    lblSupplierBalance.ForeColor = Color.FromArgb(52, 152, 219);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تحديث رصيد المورد: {ex.Message}");
                lblSupplierBalance.Text = "الرصيد: خطأ";
                lblSupplierBalance.ForeColor = Color.FromArgb(231, 76, 60);
            }
        }

        private void CmbPaymentMethod_SelectedIndexChanged(object sender, EventArgs e)
        {
            bool isBankPayment = cmbPaymentMethod.SelectedValue?.ToString() == "Bank";
            
            txtBankName.Enabled = isBankPayment;
            txtCheckNumber.Enabled = isBankPayment;
            dtpCheckDate.Enabled = isBankPayment;
            
            if (!isBankPayment)
            {
                txtBankName.Clear();
                txtCheckNumber.Clear();
                dtpCheckDate.Value = DateTime.Now;
            }
        }

        #endregion

        #region Helper Methods

        private bool ValidateData()
        {
            if (cmbSupplier.SelectedValue == null)
            {
                MessageBox.Show("يرجى اختيار المورد", "تحقق من البيانات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbSupplier.Focus();
                return false;
            }

            if (!decimal.TryParse(txtAmount.Text, out decimal amount) || amount <= 0)
            {
                MessageBox.Show("يرجى إدخال مبلغ صحيح", "تحقق من البيانات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtAmount.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtDescription.Text))
            {
                MessageBox.Show("يرجى إدخال وصف للسند", "تحقق من البيانات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtDescription.Focus();
                return false;
            }

            return true;
        }

        private PaymentVoucher CreatePaymentObject()
        {
            return new PaymentVoucher
            {
                PaymentNumber = txtPaymentNumber.Text.Trim(),
                PaymentDate = dtpPaymentDate.Value.Date,
                FiscalYearID = FinancialManager.GetCurrentFiscalYearId(),
                SupplierID = Convert.ToInt32(cmbSupplier.SelectedValue),
                Amount = decimal.Parse(txtAmount.Text),
                PaymentMethod = cmbPaymentMethod.SelectedValue.ToString(),
                BankName = string.IsNullOrWhiteSpace(txtBankName.Text) ? null : txtBankName.Text.Trim(),
                CheckNumber = string.IsNullOrWhiteSpace(txtCheckNumber.Text) ? null : txtCheckNumber.Text.Trim(),
                CheckDate = dtpCheckDate.Enabled ? (DateTime?)dtpCheckDate.Value.Date : null,
                Description = txtDescription.Text.Trim(),
                Status = "Active",
                CreatedBy = UserManager.CurrentUser.UserID
            };
        }

        private string GeneratePaymentNumber()
        {
            try
            {
                return FinancialManager.GeneratePaymentNumber();
            }
            catch
            {
                // في حالة عدم توفر قاعدة البيانات، إنشاء رقم تجريبي
                return $"PAY{DateTime.Now:yyyyMM}{DateTime.Now.Millisecond:D3}";
            }
        }

        #endregion
    }

    #region Payment Voucher Models

    /// <summary>
    /// نموذج مورد لسند الصرف
    /// </summary>
    public class PaymentVoucherSupplier
    {
        public int SupplierID { get; set; }
        public string SupplierName { get; set; }
        public decimal CurrentBalance { get; set; }
        public string Phone { get; set; }
        public bool IsActive { get; set; }
    }

    /// <summary>
    /// نموذج بيانات سند الصرف
    /// </summary>
    public class PaymentVoucherData
    {
        public int PaymentID { get; set; }
        public string PaymentNumber { get; set; }
        public DateTime PaymentDate { get; set; }
        public int SupplierID { get; set; }
        public decimal Amount { get; set; }
        public string PaymentMethod { get; set; }
        public string BankName { get; set; }
        public string CheckNumber { get; set; }
        public DateTime? CheckDate { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
    }

    #endregion
}
