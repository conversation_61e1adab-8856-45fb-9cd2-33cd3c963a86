using System;
using System.Drawing;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using Currency = PharmacyManagement.Models.Currency;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نافذة إضافة/تعديل العملة - Currency Add/Edit Form
    /// </summary>
    public partial class CurrencyAddEditForm : Form
    {
        #region Fields - الحقول

        private Currency _currency;
        private bool _isEditMode;
        private int _currencyId;

        #endregion

        #region Constructor - المنشئ

        /// <summary>
        /// منشئ لإضافة عملة جديدة
        /// </summary>
        public CurrencyAddEditForm()
        {
            InitializeComponent();
            _isEditMode = false;
            SetupForm();
        }

        /// <summary>
        /// منشئ لتعديل عملة موجودة
        /// </summary>
        /// <param name="currency">العملة المراد تعديلها</param>
        public CurrencyAddEditForm(Currency currency)
        {
            InitializeComponent();
            _currency = currency;
            _currencyId = currency.CurrencyID;
            _isEditMode = true;
            SetupForm();
            LoadCurrencyData();
        }

        #endregion

        #region Form Setup - إعداد النافذة

        /// <summary>
        /// إعداد النافذة
        /// </summary>
        private void SetupForm()
        {
            this.Text = _isEditMode ? "تعديل العملة" : "إضافة عملة جديدة";
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.ShowInTaskbar = false;

            ApplyFlatDesign();
        }

        /// <summary>
        /// تطبيق التصميم المسطح
        /// </summary>
        private void ApplyFlatDesign()
        {
            this.BackColor = Color.White;
            
            foreach (Control control in this.Controls)
            {
                if (control is Button button)
                {
                    button.FlatStyle = FlatStyle.Flat;
                    button.FlatAppearance.BorderSize = 0;
                }
                else if (control is TextBox textBox)
                {
                    textBox.BorderStyle = BorderStyle.FixedSingle;
                }
            }
        }

        /// <summary>
        /// تحميل بيانات العملة للتعديل
        /// </summary>
        private void LoadCurrencyData()
        {
            if (_currency != null)
            {
                txtCurrencyCode.Text = _currency.CurrencyCode;
                txtCurrencyName.Text = _currency.CurrencyName;
                txtCurrencySymbol.Text = _currency.CurrencySymbol;
                numExchangeRate.Value = _currency.ExchangeRate;
                chkIsBaseCurrency.Checked = _currency.IsBaseCurrency;
                chkIsActive.Checked = _currency.IsActive;

                // منع تعديل كود العملة في وضع التعديل
                txtCurrencyCode.ReadOnly = true;
                txtCurrencyCode.BackColor = Color.LightGray;
            }
        }

        #endregion

        #region Event Handlers - معالجات الأحداث

        /// <summary>
        /// حفظ العملة
        /// </summary>
        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                var currency = new Models.Currency
                {
                    CurrencyCode = txtCurrencyCode.Text.Trim().ToUpper(),
                    CurrencyName = txtCurrencyName.Text.Trim(),
                    CurrencySymbol = txtCurrencySymbol.Text.Trim(),
                    ExchangeRate = numExchangeRate.Value,
                    IsBaseCurrency = chkIsBaseCurrency.Checked,
                    IsActive = chkIsActive.Checked
                };

                // Fix CurrencyManager ambiguity and missing _currencyId
                bool success;
                if (_isEditMode)
                {
                    currency.CurrencyID = _currencyId;
                    success = Classes.CurrencyManager.UpdateCurrency(currency);
                }
                else
                {
                    success = Classes.CurrencyManager.AddNewCurrency(currency);
                }

                if (success)
                {
                    string message = _isEditMode ? "تم تحديث العملة بنجاح" : "تم إضافة العملة بنجاح";
                    MessageBox.Show(message, "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    string message = _isEditMode ? "فشل في تحديث العملة" : "فشل في إضافة العملة";
                    MessageBox.Show(message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إلغاء العملية
        /// </summary>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        #endregion

        #region Validation - التحقق

        /// <summary>
        /// التحقق من صحة البيانات المدخلة
        /// </summary>
        /// <returns>صحة البيانات</returns>
        private bool ValidateInput()
        {
            // التحقق من كود العملة
            if (string.IsNullOrWhiteSpace(txtCurrencyCode.Text))
            {
                MessageBox.Show("يرجى إدخال كود العملة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCurrencyCode.Focus();
                return false;
            }

            if (txtCurrencyCode.Text.Trim().Length != 3)
            {
                MessageBox.Show("كود العملة يجب أن يكون 3 أحرف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCurrencyCode.Focus();
                return false;
            }

            // التحقق من اسم العملة
            if (string.IsNullOrWhiteSpace(txtCurrencyName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم العملة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCurrencyName.Focus();
                return false;
            }

            // التحقق من رمز العملة
            if (string.IsNullOrWhiteSpace(txtCurrencySymbol.Text))
            {
                MessageBox.Show("يرجى إدخال رمز العملة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCurrencySymbol.Focus();
                return false;
            }

            // التحقق من سعر الصرف
            if (numExchangeRate.Value <= 0)
            {
                MessageBox.Show("سعر الصرف يجب أن يكون أكبر من صفر", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                numExchangeRate.Focus();
                return false;
            }

            return true;
        }

        #endregion
    }
}






