using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Text;
using System.IO;
using System.Windows.Forms;

namespace PharmacyManagement.Classes
{
    /// <summary>
    /// مدير الأيقونات - إنشاء وإدارة أيقونات النظام
    /// </summary>
    public static class IconManager
    {
        #region Constants

        private static readonly string IconsPath = Path.Combine(Application.StartupPath, "images");
        private static readonly Color PrimaryColor = Color.FromArgb(52, 73, 94);
        private static readonly Color SecondaryColor = Color.FromArgb(46, 204, 113);
        private static readonly Color AlertColor = Color.FromArgb(231, 76, 60);
        private static readonly Color WarningColor = Color.FromArgb(241, 196, 15);

        #endregion

        #region Initialization

        /// <summary>
        /// تهيئة مجلد الأيقونات وإنشاء الأيقونات
        /// </summary>
        public static void Initialize()
        {
            try
            {
                // إنشاء مجلد الصور إذا لم يكن موجوداً
                if (!Directory.Exists(IconsPath))
                {
                    Directory.CreateDirectory(IconsPath);
                }

                // إنشاء جميع الأيقونات
                CreateAllIcons();
                
                LogManager.LogInfo("تم إنشاء الأيقونات بنجاح");
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في تهيئة الأيقونات: " + ex.Message);
            }
        }

        #endregion

        #region Icon Creation

        /// <summary>
        /// إنشاء جميع الأيقونات المطلوبة
        /// </summary>
        private static void CreateAllIcons()
        {
            // أيقونات شريط الأدوات
            CreateSaleIcon();
            CreateSearchIcon();
            CreateAlertIcon();
            CreateRefreshIcon();

            // أيقونات القوائم
            CreateDashboardIcon();
            CreateDrugsIcon();
            CreateCustomersIcon();
            CreateSuppliersIcon();
            CreateInventoryIcon();
            CreateReportsIcon();
            CreateFinancialIcon();
            CreateUsersIcon();
            CreateSettingsIcon();

            // أيقونات النظام
            CreatePharmacyLogo();
            CreateAppIcon();
        }

        /// <summary>
        /// إنشاء أيقونة البيع السريع
        /// </summary>
        private static void CreateSaleIcon()
        {
            var icon = CreateCustomIcon("sale", SecondaryColor, 32);
            SaveIcon(icon, "sale_icon.png");
        }

        /// <summary>
        /// إنشاء أيقونة البحث السريع
        /// </summary>
        private static void CreateSearchIcon()
        {
            var icon = CreateCustomIcon("search", PrimaryColor, 32);
            SaveIcon(icon, "search_icon.png");
        }

        /// <summary>
        /// إنشاء أيقونة التنبيهات
        /// </summary>
        private static void CreateAlertIcon()
        {
            var icon = CreateCustomIcon("alert", AlertColor, 32);
            SaveIcon(icon, "alert_icon.png");
        }

        /// <summary>
        /// إنشاء أيقونة التحديث
        /// </summary>
        private static void CreateRefreshIcon()
        {
            var icon = CreateCustomIcon("refresh", PrimaryColor, 32);
            SaveIcon(icon, "refresh_icon.png");
        }

        /// <summary>
        /// إنشاء أيقونة لوحة المعلومات
        /// </summary>
        private static void CreateDashboardIcon()
        {
            var icon = CreateCustomIcon("dashboard", PrimaryColor, 32);
            SaveIcon(icon, "dashboard_icon.png");
        }

        /// <summary>
        /// إنشاء أيقونة الأدوية
        /// </summary>
        private static void CreateDrugsIcon()
        {
            var icon = CreateCustomIcon("drugs", SecondaryColor, 32);
            SaveIcon(icon, "drugs_icon.png");
        }

        /// <summary>
        /// إنشاء أيقونة العملاء
        /// </summary>
        private static void CreateCustomersIcon()
        {
            var icon = CreateCustomIcon("customers", PrimaryColor, 32);
            SaveIcon(icon, "customers_icon.png");
        }

        /// <summary>
        /// إنشاء أيقونة الموردين
        /// </summary>
        private static void CreateSuppliersIcon()
        {
            var icon = CreateCustomIcon("suppliers", PrimaryColor, 32);
            SaveIcon(icon, "suppliers_icon.png");
        }

        /// <summary>
        /// إنشاء أيقونة المخزون
        /// </summary>
        private static void CreateInventoryIcon()
        {
            var icon = CreateIconWithText("📦", WarningColor, 32);
            SaveIcon(icon, "inventory_icon.png");
        }

        /// <summary>
        /// إنشاء أيقونة التقارير
        /// </summary>
        private static void CreateReportsIcon()
        {
            var icon = CreateIconWithText("📋", PrimaryColor, 32);
            SaveIcon(icon, "reports_icon.png");
        }

        /// <summary>
        /// إنشاء أيقونة المالية
        /// </summary>
        private static void CreateFinancialIcon()
        {
            var icon = CreateIconWithText("💳", SecondaryColor, 32);
            SaveIcon(icon, "financial_icon.png");
        }

        /// <summary>
        /// إنشاء أيقونة المستخدمين
        /// </summary>
        private static void CreateUsersIcon()
        {
            var icon = CreateIconWithText("👤", PrimaryColor, 32);
            SaveIcon(icon, "users_icon.png");
        }









        /// <summary>
        /// إنشاء أيقونة الإعدادات
        /// </summary>
        private static void CreateSettingsIcon()
        {
            var icon = CreateCustomIcon("settings", PrimaryColor, 32);
            SaveIcon(icon, "settings_icon.png");
        }

        /// <summary>
        /// إنشاء شعار الصيدلية
        /// </summary>
        private static void CreatePharmacyLogo()
        {
            var logo = CreatePharmacyLogoImage();
            SaveIcon(logo, "pharmacy_logo.png");
        }

        /// <summary>
        /// إنشاء أيقونة التطبيق
        /// </summary>
        private static void CreateAppIcon()
        {
            var icon = CreateIconWithText("🏥", SecondaryColor, 64);
            SaveIcon(icon, "app_icon.png");
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// إنشاء أيقونة مع نص
        /// </summary>
        private static Bitmap CreateIconWithText(string text, Color color, int size)
        {
            var bitmap = new Bitmap(size, size);
            using (var graphics = Graphics.FromImage(bitmap))
            {
                graphics.SmoothingMode = SmoothingMode.AntiAlias;
                graphics.TextRenderingHint = TextRenderingHint.AntiAlias;

                // خلفية شفافة
                graphics.Clear(Color.Transparent);

                // رسم دائرة خلفية
                using (var brush = new SolidBrush(Color.FromArgb(240, color)))
                {
                    graphics.FillEllipse(brush, 1, 1, size - 2, size - 2);
                }

                // رسم إطار
                using (var pen = new Pen(color, 2))
                {
                    graphics.DrawEllipse(pen, 1, 1, size - 2, size - 2);
                }

                // رسم النص/الرمز
                using (var font = new Font("Segoe UI", size * 0.5f, FontStyle.Bold))
                using (var brush = new SolidBrush(color))
                {
                    var textSize = graphics.MeasureString(text, font);
                    var x = (size - textSize.Width) / 2;
                    var y = (size - textSize.Height) / 2;
                    graphics.DrawString(text, font, brush, x, y);
                }
            }
            return bitmap;
        }

        /// <summary>
        /// إنشاء أيقونة مخصصة برسم
        /// </summary>
        private static Bitmap CreateCustomIcon(string iconType, Color color, int size)
        {
            var bitmap = new Bitmap(size, size);
            using (var graphics = Graphics.FromImage(bitmap))
            {
                graphics.SmoothingMode = SmoothingMode.AntiAlias;
                graphics.Clear(Color.Transparent);

                switch (iconType.ToLower())
                {
                    case "sale":
                        DrawSaleIcon(graphics, color, size);
                        break;
                    case "search":
                        DrawSearchIcon(graphics, color, size);
                        break;
                    case "alert":
                        DrawAlertIcon(graphics, color, size);
                        break;
                    case "refresh":
                        DrawRefreshIcon(graphics, color, size);
                        break;
                    case "dashboard":
                        DrawDashboardIcon(graphics, color, size);
                        break;
                    case "drugs":
                        DrawDrugsIcon(graphics, color, size);
                        break;
                    case "customers":
                        DrawCustomersIcon(graphics, color, size);
                        break;
                    case "suppliers":
                        DrawSuppliersIcon(graphics, color, size);
                        break;
                    case "inventory":
                        DrawInventoryIcon(graphics, color, size);
                        break;
                    case "reports":
                        DrawReportsIcon(graphics, color, size);
                        break;
                    case "financial":
                        DrawFinancialIcon(graphics, color, size);
                        break;
                    case "users":
                        DrawUsersIcon(graphics, color, size);
                        break;
                    case "settings":
                        DrawSettingsIcon(graphics, color, size);
                        break;
                    default:
                        DrawDefaultIcon(graphics, color, size);
                        break;
                }
            }
            return bitmap;
        }

        /// <summary>
        /// إنشاء صورة شعار الصيدلية
        /// </summary>
        private static Bitmap CreatePharmacyLogoImage()
        {
            var bitmap = new Bitmap(128, 128);
            using (var graphics = Graphics.FromImage(bitmap))
            {
                graphics.SmoothingMode = SmoothingMode.AntiAlias;
                graphics.TextRenderingHint = TextRenderingHint.AntiAlias;

                // خلفية بيضاء
                graphics.Clear(Color.White);

                // رسم دائرة خضراء
                using (var brush = new SolidBrush(SecondaryColor))
                {
                    graphics.FillEllipse(brush, 10, 10, 108, 108);
                }

                // رسم صليب أبيض
                using (var brush = new SolidBrush(Color.White))
                {
                    // الخط العمودي
                    graphics.FillRectangle(brush, 54, 30, 20, 68);
                    // الخط الأفقي
                    graphics.FillRectangle(brush, 40, 54, 48, 20);
                }

                // إضافة نص
                using (var font = new Font("Arial", 12, FontStyle.Bold))
                using (var brush = new SolidBrush(PrimaryColor))
                {
                    var text = "صيدلية";
                    var textSize = graphics.MeasureString(text, font);
                    var x = (128 - textSize.Width) / 2;
                    graphics.DrawString(text, font, brush, x, 100);
                }
            }
            return bitmap;
        }

        /// <summary>
        /// حفظ الأيقونة
        /// </summary>
        private static void SaveIcon(Bitmap icon, string fileName)
        {
            try
            {
                var filePath = Path.Combine(IconsPath, fileName);
                icon.Save(filePath, System.Drawing.Imaging.ImageFormat.Png);
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في حفظ الأيقونة " + fileName + ": " + ex.Message);
            }
        }

        /// <summary>
        /// الحصول على مسار الأيقونة
        /// </summary>
        public static string GetIconPath(string iconName)
        {
            return Path.Combine(IconsPath, iconName);
        }

        /// <summary>
        /// تحميل أيقونة
        /// </summary>
        public static Image LoadIcon(string iconName)
        {
            try
            {
                var iconPath = GetIconPath(iconName);
                if (File.Exists(iconPath))
                {
                    return Image.FromFile(iconPath);
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في تحميل الأيقونة " + iconName + ": " + ex.Message);
            }
            return null;
        }

        #endregion

        #region Icon Drawing Methods

        /// <summary>
        /// رسم أيقونة البيع
        /// </summary>
        private static void DrawSaleIcon(Graphics graphics, Color color, int size)
        {
            var center = size / 2f;
            var radius = size * 0.35f;

            // رسم دائرة العملة
            using (var brush = new SolidBrush(Color.FromArgb(100, color)))
            {
                graphics.FillEllipse(brush, center - radius, center - radius, radius * 2, radius * 2);
            }

            using (var pen = new Pen(color, 3))
            {
                graphics.DrawEllipse(pen, center - radius, center - radius, radius * 2, radius * 2);
            }

            // رسم رمز العملة
            using (var font = new Font("Arial", size * 0.3f, FontStyle.Bold))
            using (var brush = new SolidBrush(color))
            {
                var text = "$";
                var textSize = graphics.MeasureString(text, font);
                graphics.DrawString(text, font, brush,
                    center - textSize.Width / 2, center - textSize.Height / 2);
            }
        }

        /// <summary>
        /// رسم أيقونة البحث
        /// </summary>
        private static void DrawSearchIcon(Graphics graphics, Color color, int size)
        {
            var center = size / 2f;
            var radius = size * 0.25f;

            // رسم دائرة العدسة
            using (var pen = new Pen(color, 3))
            {
                graphics.DrawEllipse(pen, center - radius, center - radius, radius * 2, radius * 2);
            }

            // رسم مقبض العدسة
            var handleStart = new PointF(center + radius * 0.7f, center + radius * 0.7f);
            var handleEnd = new PointF(center + radius * 1.3f, center + radius * 1.3f);

            using (var pen = new Pen(color, 3))
            {
                pen.StartCap = LineCap.Round;
                pen.EndCap = LineCap.Round;
                graphics.DrawLine(pen, handleStart, handleEnd);
            }
        }

        /// <summary>
        /// رسم أيقونة التنبيه
        /// </summary>
        private static void DrawAlertIcon(Graphics graphics, Color color, int size)
        {
            var center = size / 2f;
            var bellWidth = size * 0.5f;
            var bellHeight = size * 0.4f;

            // رسم الجرس
            var bellRect = new RectangleF(center - bellWidth / 2, center - bellHeight / 2, bellWidth, bellHeight);

            using (var brush = new SolidBrush(Color.FromArgb(100, color)))
            {
                graphics.FillEllipse(brush, bellRect);
            }

            using (var pen = new Pen(color, 2))
            {
                graphics.DrawEllipse(pen, bellRect);
            }

            // رسم المقبض العلوي
            using (var pen = new Pen(color, 2))
            {
                graphics.DrawLine(pen, center - 3, center - bellHeight / 2 - 5,
                                      center + 3, center - bellHeight / 2 - 5);
            }

            // رسم النقطة السفلية
            using (var brush = new SolidBrush(color))
            {
                graphics.FillEllipse(brush, center - 2, center + bellHeight / 2 + 3, 4, 4);
            }
        }

        /// <summary>
        /// رسم أيقونة التحديث
        /// </summary>
        private static void DrawRefreshIcon(Graphics graphics, Color color, int size)
        {
            var center = size / 2f;
            var radius = size * 0.3f;

            using (var pen = new Pen(color, 3))
            {
                pen.StartCap = LineCap.Round;
                pen.EndCap = LineCap.Round;

                // رسم القوس
                graphics.DrawArc(pen, center - radius, center - radius, radius * 2, radius * 2, -45, 270);

                // رسم السهم
                var arrowSize = size * 0.1f;
                var arrowX = center + radius * 0.7f;
                var arrowY = center - radius * 0.7f;

                var arrowPoints = new PointF[]
                {
                    new PointF(arrowX, arrowY),
                    new PointF(arrowX - arrowSize, arrowY - arrowSize),
                    new PointF(arrowX - arrowSize, arrowY + arrowSize)
                };

                using (var brush = new SolidBrush(color))
                {
                    graphics.FillPolygon(brush, arrowPoints);
                }
            }
        }

        /// <summary>
        /// رسم أيقونة لوحة المعلومات
        /// </summary>
        private static void DrawDashboardIcon(Graphics graphics, Color color, int size)
        {
            var margin = size * 0.15f;
            var barWidth = (size - margin * 4) / 3;
            var maxHeight = size * 0.6f;

            // رسم الأعمدة البيانية
            var heights = new float[] { maxHeight * 0.6f, maxHeight, maxHeight * 0.8f };

            for (int i = 0; i < 3; i++)
            {
                var x = margin + i * (barWidth + margin);
                var y = size - margin - heights[i];

                using (var brush = new SolidBrush(Color.FromArgb(150, color)))
                {
                    graphics.FillRectangle(brush, x, y, barWidth, heights[i]);
                }

                using (var pen = new Pen(color, 1))
                {
                    graphics.DrawRectangle(pen, x, y, barWidth, heights[i]);
                }
            }
        }

        /// <summary>
        /// رسم أيقونة الأدوية
        /// </summary>
        private static void DrawDrugsIcon(Graphics graphics, Color color, int size)
        {
            var center = size / 2f;
            var pillWidth = size * 0.6f;
            var pillHeight = size * 0.3f;

            // رسم الحبة
            var pillRect = new RectangleF(center - pillWidth / 2, center - pillHeight / 2, pillWidth, pillHeight);

            using (var brush = new SolidBrush(Color.FromArgb(150, color)))
            {
                graphics.FillEllipse(brush, pillRect);
            }

            using (var pen = new Pen(color, 2))
            {
                graphics.DrawEllipse(pen, pillRect);
            }

            // رسم الخط الفاصل
            using (var pen = new Pen(color, 2))
            {
                graphics.DrawLine(pen, center, center - pillHeight / 2, center, center + pillHeight / 2);
            }
        }

        /// <summary>
        /// رسم أيقونة العملاء
        /// </summary>
        private static void DrawCustomersIcon(Graphics graphics, Color color, int size)
        {
            var center = size / 2f;
            var headRadius = size * 0.15f;
            var bodyWidth = size * 0.25f;
            var bodyHeight = size * 0.3f;

            // رسم الرأس
            using (var brush = new SolidBrush(Color.FromArgb(150, color)))
            {
                graphics.FillEllipse(brush, center - headRadius, center - headRadius - bodyHeight / 2,
                                   headRadius * 2, headRadius * 2);
            }

            using (var pen = new Pen(color, 2))
            {
                graphics.DrawEllipse(pen, center - headRadius, center - headRadius - bodyHeight / 2,
                                   headRadius * 2, headRadius * 2);
            }

            // رسم الجسم
            using (var brush = new SolidBrush(Color.FromArgb(150, color)))
            {
                graphics.FillEllipse(brush, center - bodyWidth / 2, center, bodyWidth, bodyHeight);
            }

            using (var pen = new Pen(color, 2))
            {
                graphics.DrawEllipse(pen, center - bodyWidth / 2, center, bodyWidth, bodyHeight);
            }
        }

        /// <summary>
        /// رسم أيقونة الموردين
        /// </summary>
        private static void DrawSuppliersIcon(Graphics graphics, Color color, int size)
        {
            var margin = size * 0.1f;
            var buildingWidth = size - margin * 2;
            var buildingHeight = size * 0.7f;

            // رسم المبنى
            var buildingRect = new RectangleF(margin, size - margin - buildingHeight, buildingWidth, buildingHeight);

            using (var brush = new SolidBrush(Color.FromArgb(150, color)))
            {
                graphics.FillRectangle(brush, buildingRect);
            }

            using (var pen = new Pen(color, 2))
            {
                var rect = new Rectangle((int)buildingRect.X, (int)buildingRect.Y, (int)buildingRect.Width, (int)buildingRect.Height);
                graphics.DrawRectangle(pen, rect);
            }

            // رسم النوافذ
            var windowSize = size * 0.08f;
            var windowMargin = size * 0.05f;

            for (int row = 0; row < 2; row++)
            {
                for (int col = 0; col < 3; col++)
                {
                    var x = margin + windowMargin + col * (windowSize + windowMargin);
                    var y = size - margin - buildingHeight + windowMargin + row * (windowSize + windowMargin);

                    using (var brush = new SolidBrush(color))
                    {
                        graphics.FillRectangle(brush, x, y, windowSize, windowSize);
                    }
                }
            }
        }

        /// <summary>
        /// رسم أيقونة المخزون
        /// </summary>
        private static void DrawInventoryIcon(Graphics graphics, Color color, int size)
        {
            var margin = size * 0.15f;
            var boxSize = (size - margin * 3) / 2;

            // رسم صناديق المخزون
            for (int row = 0; row < 2; row++)
            {
                for (int col = 0; col < 2; col++)
                {
                    var x = margin + col * (boxSize + margin);
                    var y = margin + row * (boxSize + margin);

                    using (var brush = new SolidBrush(Color.FromArgb(150, color)))
                    {
                        graphics.FillRectangle(brush, x, y, boxSize, boxSize);
                    }

                    using (var pen = new Pen(color, 2))
                    {
                        graphics.DrawRectangle(pen, x, y, boxSize, boxSize);
                    }
                }
            }
        }

        /// <summary>
        /// رسم أيقونة التقارير
        /// </summary>
        private static void DrawReportsIcon(Graphics graphics, Color color, int size)
        {
            var margin = size * 0.15f;
            var docWidth = size - margin * 2;
            var docHeight = size * 0.7f;

            // رسم الورقة
            using (var brush = new SolidBrush(Color.FromArgb(150, color)))
            {
                graphics.FillRectangle(brush, margin, margin, docWidth, docHeight);
            }

            using (var pen = new Pen(color, 2))
            {
                graphics.DrawRectangle(pen, margin, margin, docWidth, docHeight);
            }

            // رسم خطوط النص
            var lineHeight = size * 0.08f;
            var lineMargin = size * 0.25f;

            using (var pen = new Pen(color, 1))
            {
                for (int i = 0; i < 4; i++)
                {
                    var y = margin + lineMargin + i * lineHeight;
                    graphics.DrawLine(pen, margin + lineMargin, y,
                                    margin + docWidth - lineMargin, y);
                }
            }
        }

        /// <summary>
        /// رسم أيقونة المالية
        /// </summary>
        private static void DrawFinancialIcon(Graphics graphics, Color color, int size)
        {
            var center = size / 2f;
            var cardWidth = size * 0.6f;
            var cardHeight = size * 0.4f;

            // رسم البطاقة المصرفية
            var cardRect = new RectangleF(center - cardWidth / 2, center - cardHeight / 2, cardWidth, cardHeight);

            using (var brush = new SolidBrush(Color.FromArgb(150, color)))
            {
                graphics.FillRoundedRectangle(brush, cardRect, 5);
            }

            using (var pen = new Pen(color, 2))
            {
                graphics.DrawRoundedRectangle(pen, cardRect, 5);
            }

            // رسم الشريط المغناطيسي
            using (var brush = new SolidBrush(color))
            {
                graphics.FillRectangle(brush, cardRect.X, cardRect.Y + cardHeight * 0.2f,
                                     cardWidth, cardHeight * 0.15f);
            }
        }

        /// <summary>
        /// رسم أيقونة المستخدمين
        /// </summary>
        private static void DrawUsersIcon(Graphics graphics, Color color, int size)
        {
            var center = size / 2f;
            var headRadius = size * 0.12f;
            var bodyWidth = size * 0.2f;
            var bodyHeight = size * 0.25f;

            // رسم المستخدم الأول
            DrawSingleUser(graphics, color, center - size * 0.15f, center, headRadius, bodyWidth, bodyHeight);

            // رسم المستخدم الثاني (متداخل)
            DrawSingleUser(graphics, Color.FromArgb(180, color), center + size * 0.15f, center, headRadius, bodyWidth, bodyHeight);
        }

        /// <summary>
        /// رسم مستخدم واحد
        /// </summary>
        private static void DrawSingleUser(Graphics graphics, Color color, float centerX, float centerY, float headRadius, float bodyWidth, float bodyHeight)
        {
            // رسم الرأس
            using (var brush = new SolidBrush(Color.FromArgb(150, color)))
            {
                graphics.FillEllipse(brush, centerX - headRadius, centerY - headRadius - bodyHeight / 2,
                                   headRadius * 2, headRadius * 2);
            }

            using (var pen = new Pen(color, 1))
            {
                graphics.DrawEllipse(pen, centerX - headRadius, centerY - headRadius - bodyHeight / 2,
                                   headRadius * 2, headRadius * 2);
            }

            // رسم الجسم
            using (var brush = new SolidBrush(Color.FromArgb(150, color)))
            {
                graphics.FillEllipse(brush, centerX - bodyWidth / 2, centerY, bodyWidth, bodyHeight);
            }

            using (var pen = new Pen(color, 1))
            {
                graphics.DrawEllipse(pen, centerX - bodyWidth / 2, centerY, bodyWidth, bodyHeight);
            }
        }

        /// <summary>
        /// رسم أيقونة الإعدادات
        /// </summary>
        private static void DrawSettingsIcon(Graphics graphics, Color color, int size)
        {
            var center = size / 2f;
            var outerRadius = size * 0.35f;
            var innerRadius = size * 0.15f;
            var teethCount = 8;

            // رسم الترس
            var points = new List<PointF>();

            for (int i = 0; i < teethCount * 2; i++)
            {
                var angle = i * Math.PI / teethCount;
                var radius = (i % 2 == 0) ? outerRadius : outerRadius * 0.8f;
                var x = center + (float)(Math.Cos(angle) * radius);
                var y = center + (float)(Math.Sin(angle) * radius);
                points.Add(new PointF(x, y));
            }

            using (var brush = new SolidBrush(Color.FromArgb(150, color)))
            {
                graphics.FillPolygon(brush, points.ToArray());
            }

            using (var pen = new Pen(color, 2))
            {
                graphics.DrawPolygon(pen, points.ToArray());
            }

            // رسم الدائرة الداخلية
            using (var brush = new SolidBrush(Color.White))
            {
                graphics.FillEllipse(brush, center - innerRadius, center - innerRadius,
                                   innerRadius * 2, innerRadius * 2);
            }

            using (var pen = new Pen(color, 2))
            {
                graphics.DrawEllipse(pen, center - innerRadius, center - innerRadius,
                                   innerRadius * 2, innerRadius * 2);
            }
        }

        /// <summary>
        /// رسم أيقونة افتراضية
        /// </summary>
        private static void DrawDefaultIcon(Graphics graphics, Color color, int size)
        {
            var center = size / 2f;
            var radius = size * 0.3f;

            using (var brush = new SolidBrush(Color.FromArgb(150, color)))
            {
                graphics.FillEllipse(brush, center - radius, center - radius, radius * 2, radius * 2);
            }

            using (var pen = new Pen(color, 2))
            {
                graphics.DrawEllipse(pen, center - radius, center - radius, radius * 2, radius * 2);
            }

            using (var font = new Font("Arial", size * 0.3f, FontStyle.Bold))
            using (var brush = new SolidBrush(color))
            {
                var text = "?";
                var textSize = graphics.MeasureString(text, font);
                graphics.DrawString(text, font, brush,
                    center - textSize.Width / 2, center - textSize.Height / 2);
            }
        }

        #endregion

        #region Helper Extensions

        /// <summary>
        /// رسم مستطيل مدور
        /// </summary>
        private static void FillRoundedRectangle(this Graphics graphics, Brush brush, RectangleF rect, float radius)
        {
            using (var path = new GraphicsPath())
            {
                path.AddArc(rect.X, rect.Y, radius * 2, radius * 2, 180, 90);
                path.AddArc(rect.Right - radius * 2, rect.Y, radius * 2, radius * 2, 270, 90);
                path.AddArc(rect.Right - radius * 2, rect.Bottom - radius * 2, radius * 2, radius * 2, 0, 90);
                path.AddArc(rect.X, rect.Bottom - radius * 2, radius * 2, radius * 2, 90, 90);
                path.CloseFigure();
                graphics.FillPath(brush, path);
            }
        }

        /// <summary>
        /// رسم إطار مستطيل مدور
        /// </summary>
        private static void DrawRoundedRectangle(this Graphics graphics, Pen pen, RectangleF rect, float radius)
        {
            using (var path = new GraphicsPath())
            {
                path.AddArc(rect.X, rect.Y, radius * 2, radius * 2, 180, 90);
                path.AddArc(rect.Right - radius * 2, rect.Y, radius * 2, radius * 2, 270, 90);
                path.AddArc(rect.Right - radius * 2, rect.Bottom - radius * 2, radius * 2, radius * 2, 0, 90);
                path.AddArc(rect.X, rect.Bottom - radius * 2, radius * 2, radius * 2, 90, 90);
                path.CloseFigure();
                graphics.DrawPath(pen, path);
            }
        }

        #endregion
    }
}
