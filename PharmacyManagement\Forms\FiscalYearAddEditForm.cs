using System;
using System.Drawing;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نموذج إضافة/تعديل السنة المالية - Fiscal Year Add/Edit Form
    /// </summary>
    public partial class FiscalYearAddEditForm : Form
    {
        #region Fields

        private int? _fiscalYearId;
        private bool _isEditMode;

        #endregion

        #region Constructor

        public FiscalYearAddEditForm(int? fiscalYearId = null)
        {
            InitializeComponent();
            _fiscalYearId = fiscalYearId;
            _isEditMode = fiscalYearId.HasValue;
            
            SetupForm();
            
            if (_isEditMode)
                LoadFiscalYearData();
            else
                SetDefaultValues();
        }

        #endregion

        #region Setup Methods

        private void SetupForm()
        {
            this.Text = _isEditMode ? "تعديل السنة المالية" : "إضافة سنة مالية جديدة";
            this.StartPosition = FormStartPosition.CenterParent;
            
            // إعداد التصميم المسطح
            SetupFlatDesign();
            
            // إعداد الأحداث
            SetupEvents();
        }

        private void SetupFlatDesign()
        {
            // إعدادات النموذج
            this.BackColor = Color.White;
            this.Font = new Font("Segoe UI", 9F);
            
            // إعداد الأزرار
            btnSave.FlatStyle = FlatStyle.Flat;
            btnSave.FlatAppearance.BorderSize = 1;
            btnSave.FlatAppearance.BorderColor = Color.FromArgb(46, 204, 113);
            btnSave.BackColor = Color.FromArgb(46, 204, 113);
            btnSave.ForeColor = Color.White;
            btnSave.Font = new Font("Segoe UI", 9F, FontStyle.Bold);

            btnCancel.FlatStyle = FlatStyle.Flat;
            btnCancel.FlatAppearance.BorderSize = 1;
            btnCancel.FlatAppearance.BorderColor = Color.FromArgb(231, 76, 60);
            btnCancel.BackColor = Color.FromArgb(231, 76, 60);
            btnCancel.ForeColor = Color.White;
            btnCancel.Font = new Font("Segoe UI", 9F, FontStyle.Bold);

            // إعداد الحقول
            txtYearName.BorderStyle = BorderStyle.FixedSingle;
            txtYearName.Font = new Font("Segoe UI", 10F);
            
            dtpStartDate.Font = new Font("Segoe UI", 10F);
            dtpEndDate.Font = new Font("Segoe UI", 10F);
        }

        private void SetupEvents()
        {
            // ربط الأحداث
            btnSave.Click += BtnSave_Click;
            btnCancel.Click += BtnCancel_Click;
            dtpStartDate.ValueChanged += DtpStartDate_ValueChanged;
        }

        #endregion

        #region Load Data Methods

        private void SetDefaultValues()
        {
            var currentYear = DateTime.Now.Year;
            txtYearName.Text = currentYear.ToString();
            dtpStartDate.Value = new DateTime(currentYear, 1, 1);
            dtpEndDate.Value = new DateTime(currentYear, 12, 31);
            chkIsActive.Checked = false;
        }

        private void LoadFiscalYearData()
        {
            if (!_fiscalYearId.HasValue) return;

            try
            {
                var fiscalYear = FiscalYearManager.GetFiscalYear(_fiscalYearId.Value);
                if (fiscalYear != null)
                {
                    txtYearName.Text = fiscalYear.YearName;
                    dtpStartDate.Value = fiscalYear.StartDate;
                    dtpEndDate.Value = fiscalYear.EndDate;
                    chkIsActive.Checked = fiscalYear.IsActive;
                    
                    // إذا كانت السنة مقفلة، منع التعديل
                    if (fiscalYear.IsClosed)
                    {
                        txtYearName.ReadOnly = true;
                        dtpStartDate.Enabled = false;
                        dtpEndDate.Enabled = false;
                        chkIsActive.Enabled = false;
                        btnSave.Enabled = false;
                        
                        MessageBox.Show("هذه السنة المالية مقفلة ولا يمكن تعديلها", "تنبيه",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات السنة المالية: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Event Handlers

        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateData()) return;

                var fiscalYear = CreateFiscalYearObject();
                bool success;

                if (_isEditMode)
                {
                    fiscalYear.FiscalYearID = _fiscalYearId.Value;
                    success = FiscalYearManager.UpdateFiscalYear(fiscalYear);
                }
                else
                {
                    var newId = FiscalYearManager.AddFiscalYear(fiscalYear);
                    success = newId > 0;
                }

                if (success)
                {
                    MessageBox.Show("تم حفظ السنة المالية بنجاح", "نجح", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("فشل في حفظ السنة المالية", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ السنة المالية: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void DtpStartDate_ValueChanged(object sender, EventArgs e)
        {
            // تحديث تاريخ النهاية تلقائياً
            if (dtpStartDate.Value.Year != dtpEndDate.Value.Year)
            {
                dtpEndDate.Value = new DateTime(dtpStartDate.Value.Year, 12, 31);
            }
            
            // تحديث اسم السنة تلقائياً
            if (string.IsNullOrEmpty(txtYearName.Text) || txtYearName.Text == dtpStartDate.Value.AddYears(-1).Year.ToString())
            {
                txtYearName.Text = dtpStartDate.Value.Year.ToString();
            }
        }

        #endregion

        #region Helper Methods

        private bool ValidateData()
        {
            // التحقق من اسم السنة
            if (string.IsNullOrWhiteSpace(txtYearName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم السنة المالية", "تحقق من البيانات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtYearName.Focus();
                return false;
            }

            // التحقق من التواريخ
            if (dtpStartDate.Value >= dtpEndDate.Value)
            {
                MessageBox.Show("تاريخ بداية السنة المالية يجب أن يكون قبل تاريخ النهاية", "تحقق من البيانات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                dtpStartDate.Focus();
                return false;
            }

            // التحقق من أن السنة المالية لا تقل عن 3 أشهر
            var duration = dtpEndDate.Value - dtpStartDate.Value;
            if (duration.TotalDays < 90)
            {
                MessageBox.Show("مدة السنة المالية يجب أن تكون 3 أشهر على الأقل", "تحقق من البيانات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                dtpEndDate.Focus();
                return false;
            }

            // التحقق من أن السنة المالية لا تزيد عن سنتين
            if (duration.TotalDays > 730)
            {
                MessageBox.Show("مدة السنة المالية يجب أن لا تزيد عن سنتين", "تحقق من البيانات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                dtpEndDate.Focus();
                return false;
            }

            return true;
        }

        private FiscalYear CreateFiscalYearObject()
        {
            return new FiscalYear
            {
                YearName = txtYearName.Text.Trim(),
                StartDate = dtpStartDate.Value.Date,
                EndDate = dtpEndDate.Value.Date,
                IsActive = chkIsActive.Checked,
                IsClosed = false,
                CreatedBy = UserManager.CurrentUser?.UserID
            };
        }

        #endregion
    }
}
