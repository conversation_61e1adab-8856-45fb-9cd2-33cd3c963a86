using System;
using System.Drawing;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نافذة إضافة/تعديل الموردين - Supplier Add/Edit Form
    /// </summary>
    public partial class SupplierAddEditForm : Form
    {
        #region Fields - الحقول

        private Supplier _currentSupplier;
        private bool _isEditMode;
        
       
        #endregion

        #region Constructors - المنشئات

        /// <summary>
        /// منشئ لإضافة مورد جديد
        /// </summary>
        public SupplierAddEditForm()
        {
            InitializeComponent();
            _isEditMode = false;
            _currentSupplier = new Supplier();
            SetupForm();
        }

        /// <summary>
        /// منشئ لتعديل مورد موجود
        /// </summary>
        /// <param name="supplier">المورد المراد تعديله</param>
        public SupplierAddEditForm(Supplier supplier)
        {
            InitializeComponent();
            _isEditMode = true;
            _currentSupplier = supplier;
            SetupForm();
            LoadSupplierData();
        }

        #endregion

        #region Form Setup - إعداد النافذة

        /// <summary>
        /// إعداد النافذة بدلاً من Designer
        /// </summary>
 
        /// <summary>
        /// إعداد النافذة
        /// </summary>
        private void SetupForm()
        {
            try
            {
                this.Text = _isEditMode ? "تعديل مورد" : "إضافة مورد جديد";
                lblTitle.Text = _isEditMode ? "✏️ تعديل مورد" : "➕ إضافة مورد جديد";

                // إذا كان في وضع الإضافة، قم بتوليد كود المورد تلقائياً
                if (!_isEditMode)
                {
                    GenerateSupplierCode();
                    // تعيين القيم الافتراضية
                    chkIsActive.Checked = true;
                    txtCreditLimit.Text = "0";
                }

                LogManager.LogInfo($"تم فتح نافذة {(_isEditMode ? "تعديل" : "إضافة")} مورد");
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في إعداد نافذة المورد: {ex.Message}");
                MessageBox.Show("حدث خطأ في إعداد النافذة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// توليد كود المورد تلقائياً
        /// </summary>
        private void GenerateSupplierCode()
        {
            try
            {
                string newCode = SupplierManager.GenerateSupplierCode();
                txtSupplierCode.Text = newCode;
                _currentSupplier.SupplierCode = newCode;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في توليد كود المورد: {ex.Message}");
                // في حالة الفشل، استخدم كود افتراضي
                string defaultCode = "SUP" + DateTime.Now.ToString("yyyyMMddHHmmss");
                txtSupplierCode.Text = defaultCode;
                _currentSupplier.SupplierCode = defaultCode;
            }
        }

        /// <summary>
        /// تحميل بيانات المورد للتعديل
        /// </summary>
        private void LoadSupplierData()
        {
            try
            {
                if (_currentSupplier != null)
                {
                    txtSupplierCode.Text = _currentSupplier.SupplierCode;
                    txtSupplierName.Text = _currentSupplier.SupplierName;
                    txtContactPerson.Text = _currentSupplier.ContactPerson;
                    txtPhone.Text = _currentSupplier.Phone;
                    txtEmail.Text = _currentSupplier.Email;
                    txtAddress.Text = _currentSupplier.Address;
                    txtTaxNumber.Text = _currentSupplier.TaxNumber;
                    txtCreditLimit.Text = _currentSupplier.CreditLimit.ToString();
                    chkIsActive.Checked = _currentSupplier.IsActive;
                    txtNotes.Text = _currentSupplier.Notes;
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تحميل بيانات المورد: {ex.Message}");
                MessageBox.Show("حدث خطأ في تحميل بيانات المورد", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Event Handlers - معالجات الأحداث

        /// <summary>
        /// حفظ المورد
        /// </summary>
        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (ValidateInput())
                {
                    SaveSupplier();
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في حفظ المورد: {ex.Message}");
                MessageBox.Show("حدث خطأ في حفظ المورد", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إلغاء العملية
        /// </summary>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        #endregion

        #region Private Methods - الطرق الخاصة

        /// <summary>
        /// التحقق من صحة البيانات المدخلة
        /// </summary>
        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtSupplierCode.Text))
            {
                MessageBox.Show("يرجى إدخال كود المورد", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtSupplierCode.Focus();
                return false;
            }

            // التحقق من تكرار كود المورد
            if (!_isEditMode || txtSupplierCode.Text.Trim() != _currentSupplier.SupplierCode)
            {
                if (SupplierManager.IsSupplierCodeExists(txtSupplierCode.Text.Trim()))
                {
                    MessageBox.Show("كود المورد موجود مسبقاً، يرجى اختيار كود آخر", "تنبيه",
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtSupplierCode.Focus();
                    return false;
                }
            }

            if (string.IsNullOrWhiteSpace(txtSupplierName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المورد", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtSupplierName.Focus();
                return false;
            }

            if (!string.IsNullOrWhiteSpace(txtEmail.Text) && !IsValidEmail(txtEmail.Text))
            {
                MessageBox.Show("يرجى إدخال بريد إلكتروني صحيح", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtEmail.Focus();
                return false;
            }

            if (!decimal.TryParse(txtCreditLimit.Text, out decimal creditLimit) || creditLimit < 0)
            {
                MessageBox.Show("يرجى إدخال حد ائتمان صحيح", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCreditLimit.Focus();
                return false;
            }

            return true;
        }

        /// <summary>
        /// التحقق من صحة البريد الإلكتروني
        /// </summary>
        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// حفظ بيانات المورد
        /// </summary>
        private void SaveSupplier()
        {
            // تحديث بيانات المورد من الحقول
            _currentSupplier.SupplierCode = txtSupplierCode.Text.Trim();
            _currentSupplier.SupplierName = txtSupplierName.Text.Trim();
            _currentSupplier.ContactPerson = txtContactPerson.Text.Trim();
            _currentSupplier.Phone = txtPhone.Text.Trim();
            _currentSupplier.Mobile = ""; // حقل غير موجود في النموذج
            _currentSupplier.Email = txtEmail.Text.Trim();
            _currentSupplier.Address = txtAddress.Text.Trim();
            _currentSupplier.TaxNumber = txtTaxNumber.Text.Trim();
            _currentSupplier.CreditLimit = decimal.TryParse(txtCreditLimit.Text, out decimal credit) ? credit : 0;
            _currentSupplier.IsActive = chkIsActive.Checked;

            if (!_isEditMode)
            {
                _currentSupplier.CreatedDate = DateTime.Now;
                _currentSupplier.CurrentBalance = 0; // رصيد ابتدائي صفر للموردين الجدد
            }
            _currentSupplier.ModifiedDate = DateTime.Now;

            // حفظ في قاعدة البيانات
            bool success = false;
            if (_isEditMode)
            {
                success = SupplierManager.UpdateSupplier(_currentSupplier);
                if (success)
                {
                    MessageBox.Show("تم تعديل المورد بنجاح", "نجح",
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                    LogManager.LogInfo($"تم تعديل المورد: {_currentSupplier.SupplierName}");
                }
                else
                {
                    MessageBox.Show("فشل في تعديل المورد", "خطأ",
                                  MessageBoxButtons.OK, MessageBoxIcon.Error);
                    throw new Exception("فشل في تعديل المورد في قاعدة البيانات");
                }
            }
            else
            {
                int newSupplierId = SupplierManager.AddSupplier(_currentSupplier);
                if (newSupplierId > 0)
                {
                    _currentSupplier.SupplierID = newSupplierId;
                    success = true;
                    MessageBox.Show("تم إضافة المورد بنجاح", "نجح",
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                    LogManager.LogInfo($"تم إضافة المورد: {_currentSupplier.SupplierName} بالمعرف: {newSupplierId}");
                }
                else
                {
                    MessageBox.Show("فشل في إضافة المورد", "خطأ",
                                  MessageBoxButtons.OK, MessageBoxIcon.Error);
                    throw new Exception("فشل في إضافة المورد إلى قاعدة البيانات");
                }
            }
        }

        #endregion

        #region Properties - الخصائص

        /// <summary>
        /// المورد الحالي
        /// </summary>
        public Supplier CurrentSupplier
        {
            get { return _currentSupplier; }
        }

        #endregion
    }
}
