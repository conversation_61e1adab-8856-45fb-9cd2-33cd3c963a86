using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using PharmacyManagement.Models;

namespace PharmacyManagement.Classes
{
    /// <summary>
    /// مدير المخزون الموحد - Unified Inventory Manager
    /// يدمج وظائف InventoryManager و StockManager في كلاس واحد
    /// </summary>
    public static class UnifiedInventoryManager
    {
        #region Inventory Operations - عمليات المخزون

        /// <summary>
        /// الحصول على جميع عناصر المخزون
        /// </summary>
        /// <returns>قائمة عناصر المخزون</returns>
        public static List<InventoryItem> GetAllInventoryItems()
        {
            var items = new List<InventoryItem>();
            try
            {
                string query = @"
                    SELECT 
                        i.InventoryID,
                        i.DrugID,
                        d.DrugCode,
                        d.DrugName,
                        d.ScientificName,
                        d.Barcode,
                        dc.CategoryName,
                        m.ManufacturerName,
                        i.WarehouseID,
                        w.<PERSON>house<PERSON>,
                        i.BatchNumber,
                        i.Quantity,
                        i.ReservedQuantity,
                        (i.Quantity - i.ReservedQuantity) as AvailableQuantity,
                        i.ExpiryDate,
                        i.ManufacturingDate,
                        i.PurchasePrice,
                        i.SalePrice,
                        d.MinStock,
                        d.MaxStock,
                        d.Unit,
                        i.CreatedDate,
                        i.UpdatedDate
                    FROM Inventory i
                    INNER JOIN Drugs d ON i.DrugID = d.DrugID
                    LEFT JOIN DrugCategories dc ON d.CategoryID = dc.CategoryID
                    LEFT JOIN Manufacturers m ON d.ManufacturerID = m.ManufacturerID
                    INNER JOIN Warehouses w ON i.WarehouseID = w.WarehouseID
                    WHERE d.IsActive = 1
                    ORDER BY d.DrugName, i.ExpiryDate";

                using (var reader = DatabaseHelper.ExecuteReader(query))
                {
                    while (reader.Read())
                    {
                        items.Add(new InventoryItem
                        {
                            InventoryID = Convert.ToInt32(reader["InventoryID"]),
                            DrugID = Convert.ToInt32(reader["DrugID"]),
                            DrugCode = reader["DrugCode"].ToString(),
                            DrugName = reader["DrugName"].ToString(),
                            ScientificName = reader["ScientificName"] != DBNull.Value ? reader["ScientificName"].ToString() : null,
                            Barcode = reader["Barcode"] != DBNull.Value ? reader["Barcode"].ToString() : null,
                            CategoryName = reader["CategoryName"] != DBNull.Value ? reader["CategoryName"].ToString() : null,
                            ManufacturerName = reader["ManufacturerName"] != DBNull.Value ? reader["ManufacturerName"].ToString() : null,
                            WarehouseID = Convert.ToInt32(reader["WarehouseID"]),
                            WarehouseName = reader["WarehouseName"].ToString(),
                            BatchNumber = reader["BatchNumber"] != DBNull.Value ? reader["BatchNumber"].ToString() : null,
                            Quantity = Convert.ToDecimal(reader["Quantity"]),
                            ReservedQuantity = Convert.ToDecimal(reader["ReservedQuantity"]),
                            AvailableQuantity = Convert.ToDecimal(reader["AvailableQuantity"]),
                            ExpiryDate = reader["ExpiryDate"] != DBNull.Value ? (DateTime?)Convert.ToDateTime(reader["ExpiryDate"]) : null,
                            ManufacturingDate = reader["ManufacturingDate"] != DBNull.Value ? (DateTime?)Convert.ToDateTime(reader["ManufacturingDate"]) : null,
                            PurchasePrice = Convert.ToDecimal(reader["PurchasePrice"]),
                            SalePrice = Convert.ToDecimal(reader["SalePrice"]),
                            MinStock = Convert.ToDecimal(reader["MinStock"]),
                            MaxStock = Convert.ToDecimal(reader["MaxStock"]),
                            Unit = reader["Unit"].ToString(),
                            CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                            UpdatedDate = Convert.ToDateTime(reader["UpdatedDate"])
                        });
                    }
                }

                LogManager.LogInfo("تم استرجاع " + items.Count.ToString() + " عنصر مخزون");
                return items;
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في استرجاع عناصر المخزون: " + ex.Message);
                return items;
            }
        }

        /// <summary>
        /// الحصول على مخزون دواء معين
        /// </summary>
        /// <param name="drugId">معرف الدواء</param>
        /// <returns>قائمة مخزون الدواء</returns>
        public static List<InventoryItem> GetDrugInventory(int drugId)
        {
            var items = new List<InventoryItem>();
            try
            {
                string query = @"
                    SELECT 
                        i.InventoryID, i.DrugID, i.WarehouseID, w.WarehouseName,
                        i.BatchNumber, i.Quantity, i.ReservedQuantity,
                        (i.Quantity - i.ReservedQuantity) as AvailableQuantity,
                        i.ExpiryDate, i.PurchasePrice, i.SalePrice,
                        i.CreatedDate, i.UpdatedDate
                    FROM Inventory i
                    INNER JOIN Warehouses w ON i.WarehouseID = w.WarehouseID
                    WHERE i.DrugID = @DrugID
                    ORDER BY i.ExpiryDate";

                var parameters = new[] { DatabaseHelper.CreateParameter("@DrugID", drugId) };

                using (var reader = DatabaseHelper.ExecuteReader(query, parameters))
                {
                    while (reader.Read())
                    {
                        items.Add(new InventoryItem
                        {
                            InventoryID = Convert.ToInt32(reader["InventoryID"]),
                            DrugID = drugId,
                            WarehouseID = Convert.ToInt32(reader["WarehouseID"]),
                            WarehouseName = reader["WarehouseName"].ToString(),
                            BatchNumber = reader["BatchNumber"] != DBNull.Value ? reader["BatchNumber"].ToString() : null,
                            Quantity = Convert.ToDecimal(reader["Quantity"]),
                            ReservedQuantity = Convert.ToDecimal(reader["ReservedQuantity"]),
                            AvailableQuantity = Convert.ToDecimal(reader["AvailableQuantity"]),
                            ExpiryDate = reader["ExpiryDate"] != DBNull.Value ? (DateTime?)Convert.ToDateTime(reader["ExpiryDate"]) : null,
                            PurchasePrice = Convert.ToDecimal(reader["PurchasePrice"]),
                            SalePrice = Convert.ToDecimal(reader["SalePrice"]),
                            CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                            UpdatedDate = Convert.ToDateTime(reader["UpdatedDate"])
                        });
                    }
                }

                return items;
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في استرجاع مخزون الدواء " + drugId.ToString() + ": " + ex.Message);
                return items;
            }
        }

        #endregion

        #region Stock Movement Operations - عمليات حركة المخزون

        /// <summary>
        /// إضافة حركة مخزون
        /// </summary>
        /// <param name="drugId">معرف الدواء</param>
        /// <param name="warehouseId">معرف المخزن</param>
        /// <param name="movementType">نوع الحركة</param>
        /// <param name="quantity">الكمية</param>
        /// <param name="referenceNumber">رقم المرجع</param>
        /// <param name="notes">ملاحظات</param>
        /// <returns>معرف الحركة الجديدة</returns>
        public static int AddStockMovement(int drugId, int warehouseId, string movementType, 
            int quantity, string referenceNumber = null, string notes = null)
        {
            try
            {
                string query = @"
                    INSERT INTO StockMovements 
                    (DrugID, WarehouseID, MovementType, Quantity, UnitCost, TotalCost,
                     ReferenceNumber, ReferenceType, Notes, UserID, MovementDate)
                    VALUES 
                    (@DrugID, @WarehouseID, @MovementType, @Quantity, @UnitCost, @TotalCost,
                     @ReferenceNumber, @ReferenceType, @Notes, @UserID, @MovementDate);
                    SELECT SCOPE_IDENTITY();";

                var parameters = new[]
                {
                    DatabaseHelper.CreateParameter("@DrugID", drugId),
                    DatabaseHelper.CreateParameter("@WarehouseID", warehouseId),
                    DatabaseHelper.CreateParameter("@MovementType", movementType),
                    DatabaseHelper.CreateParameter("@Quantity", quantity),
                    DatabaseHelper.CreateParameter("@UnitCost", 0), // سيتم تحديثه لاحقاً
                    DatabaseHelper.CreateParameter("@TotalCost", 0), // سيتم تحديثه لاحقاً
                    DatabaseHelper.CreateParameter("@ReferenceNumber", referenceNumber),
                    DatabaseHelper.CreateParameter("@ReferenceType", "Manual"),
                    DatabaseHelper.CreateParameter("@Notes", notes),
                    DatabaseHelper.CreateParameter("@UserID", UserManager.CurrentUser?.UserID),
                    DatabaseHelper.CreateParameter("@MovementDate", DateTime.Now)
                };

                var result = DatabaseHelper.ExecuteScalar(query, parameters);
                int movementId = Convert.ToInt32(result);

                if (movementId > 0)
                {
                    // تحديث المخزون
                    UpdateInventoryQuantity(drugId, warehouseId, quantity, movementType);
                    
                    LogManager.LogInfo("تم إضافة حركة مخزون: الدواء " + drugId.ToString() + ", النوع " + movementType + ", الكمية " + quantity.ToString());
                }

                return movementId;
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في إضافة حركة المخزون: " + ex.Message);
                return -1;
            }
        }

        /// <summary>
        /// تحديث كمية المخزون
        /// </summary>
        /// <param name="drugId">معرف الدواء</param>
        /// <param name="warehouseId">معرف المخزن</param>
        /// <param name="quantity">الكمية</param>
        /// <param name="movementType">نوع الحركة</param>
        private static void UpdateInventoryQuantity(int drugId, int warehouseId, int quantity, string movementType)
        {
            try
            {
                string updateQuery;
                
                if (movementType == "دخول" || movementType == "In")
                {
                    updateQuery = @"
                        UPDATE Inventory 
                        SET Quantity = Quantity + @Quantity, UpdatedDate = @UpdatedDate, UpdatedBy = @UpdatedBy
                        WHERE DrugID = @DrugID AND WarehouseID = @WarehouseID";
                }
                else if (movementType == "خروج" || movementType == "Out")
                {
                    updateQuery = @"
                        UPDATE Inventory 
                        SET Quantity = Quantity - @Quantity, UpdatedDate = @UpdatedDate, UpdatedBy = @UpdatedBy
                        WHERE DrugID = @DrugID AND WarehouseID = @WarehouseID";
                }
                else
                {
                    return; // نوع حركة غير مدعوم
                }

                var parameters = new[]
                {
                    DatabaseHelper.CreateParameter("@Quantity", quantity),
                    DatabaseHelper.CreateParameter("@DrugID", drugId),
                    DatabaseHelper.CreateParameter("@WarehouseID", warehouseId),
                    DatabaseHelper.CreateParameter("@UpdatedDate", DateTime.Now),
                    DatabaseHelper.CreateParameter("@UpdatedBy", UserManager.CurrentUser?.UserID)
                };

                DatabaseHelper.ExecuteNonQuery(updateQuery, parameters);
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في تحديث كمية المخزون: " + ex.Message);
            }
        }

        #endregion

        #region Low Stock Alerts - تنبيهات المخزون المنخفض

        /// <summary>
        /// الحصول على الأدوية ذات المخزون المنخفض
        /// </summary>
        /// <returns>قائمة الأدوية ذات المخزون المنخفض</returns>
        public static List<LowStockAlert> GetLowStockAlerts()
        {
            var alerts = new List<LowStockAlert>();
            try
            {
                string query = @"
                    SELECT 
                        d.DrugID, d.DrugCode, d.DrugName,
                        SUM(i.Quantity) as TotalStock,
                        d.MinStock,
                        dc.CategoryName,
                        m.ManufacturerName
                    FROM Drugs d
                    LEFT JOIN Inventory i ON d.DrugID = i.DrugID
                    LEFT JOIN DrugCategories dc ON d.CategoryID = dc.CategoryID
                    LEFT JOIN Manufacturers m ON d.ManufacturerID = m.ManufacturerID
                    WHERE d.IsActive = 1
                    GROUP BY d.DrugID, d.DrugCode, d.DrugName, d.MinStock, dc.CategoryName, m.ManufacturerName
                    HAVING SUM(ISNULL(i.Quantity, 0)) <= d.MinStock
                    ORDER BY SUM(ISNULL(i.Quantity, 0))";

                using (var reader = DatabaseHelper.ExecuteReader(query))
                {
                    while (reader.Read())
                    {
                        alerts.Add(new LowStockAlert
                        {
                            DrugID = Convert.ToInt32(reader["DrugID"]),
                            DrugCode = reader["DrugCode"].ToString(),
                            DrugName = reader["DrugName"].ToString(),
                            CurrentStock = reader["TotalStock"] != DBNull.Value ? Convert.ToInt32(reader["TotalStock"]) : 0,
                            MinStock = Convert.ToInt32(reader["MinStock"]),
                            CategoryName = reader["CategoryName"] != DBNull.Value ? reader["CategoryName"].ToString() : null,
                            ManufacturerName = reader["ManufacturerName"] != DBNull.Value ? reader["ManufacturerName"].ToString() : null
                        });
                    }
                }

                LogManager.LogInfo("تم العثور على " + alerts.Count.ToString() + " تنبيه مخزون منخفض");
                return alerts;
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في الحصول على تنبيهات المخزون المنخفض: " + ex.Message);
                return alerts;
            }
        }

        #endregion

        #region Expiry Alerts - تنبيهات انتهاء الصلاحية

        /// <summary>
        /// الحصول على الأدوية قريبة انتهاء الصلاحية
        /// </summary>
        /// <param name="daysBeforeExpiry">عدد الأيام قبل انتهاء الصلاحية</param>
        /// <returns>قائمة الأدوية قريبة انتهاء الصلاحية</returns>
        public static List<ExpiryAlert> GetExpiryAlerts(int daysBeforeExpiry = 90)
        {
            var alerts = new List<ExpiryAlert>();
            try
            {
                string query = @"
                    SELECT 
                        i.InventoryID, i.DrugID, d.DrugCode, d.DrugName,
                        i.BatchNumber, i.Quantity, i.ExpiryDate,
                        w.WarehouseName, dc.CategoryName
                    FROM Inventory i
                    INNER JOIN Drugs d ON i.DrugID = d.DrugID
                    INNER JOIN Warehouses w ON i.WarehouseID = w.WarehouseID
                    LEFT JOIN DrugCategories dc ON d.CategoryID = dc.CategoryID
                    WHERE i.ExpiryDate IS NOT NULL 
                      AND i.ExpiryDate <= DATEADD(day, @DaysBeforeExpiry, GETDATE())
                      AND i.Quantity > 0
                      AND d.IsActive = 1
                    ORDER BY i.ExpiryDate";

                var parameters = new[] { DatabaseHelper.CreateParameter("@DaysBeforeExpiry", daysBeforeExpiry) };

                using (var reader = DatabaseHelper.ExecuteReader(query, parameters))
                {
                    while (reader.Read())
                    {
                        alerts.Add(new ExpiryAlert
                        {
                            InventoryID = Convert.ToInt32(reader["InventoryID"]),
                            DrugID = Convert.ToInt32(reader["DrugID"]),
                            DrugCode = reader["DrugCode"].ToString(),
                            DrugName = reader["DrugName"].ToString(),
                            BatchNumber = reader["BatchNumber"] != DBNull.Value ? reader["BatchNumber"].ToString() : null,
                            Quantity = Convert.ToInt32(reader["Quantity"]),
                            ExpiryDate = Convert.ToDateTime(reader["ExpiryDate"]),
                            WarehouseName = reader["WarehouseName"].ToString(),
                            CategoryName = reader["CategoryName"] != DBNull.Value ? reader["CategoryName"].ToString() : null,
                            DaysToExpiry = (Convert.ToDateTime(reader["ExpiryDate"]) - DateTime.Now).Days
                        });
                    }
                }

                LogManager.LogInfo("تم العثور على " + alerts.Count.ToString() + " تنبيه انتهاء صلاحية");
                return alerts;
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في الحصول على تنبيهات انتهاء الصلاحية: " + ex.Message);
                return alerts;
            }
        }

        #endregion
    }

    #region Helper Models - النماذج المساعدة

    public class LowStockAlert
    {
        public int DrugID { get; set; }
        public string DrugCode { get; set; }
        public string DrugName { get; set; }
        public int CurrentStock { get; set; }
        public int MinStock { get; set; }
        public string CategoryName { get; set; }
        public string ManufacturerName { get; set; }
    }

    public class ExpiryAlert
    {
        public int InventoryID { get; set; }
        public int DrugID { get; set; }
        public string DrugCode { get; set; }
        public string DrugName { get; set; }
        public string BatchNumber { get; set; }
        public int Quantity { get; set; }
        public DateTime ExpiryDate { get; set; }
        public string WarehouseName { get; set; }
        public string CategoryName { get; set; }
        public int DaysToExpiry { get; set; }
    }

    #endregion
}
