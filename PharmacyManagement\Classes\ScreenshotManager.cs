using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Text;
using System.IO;
using System.Windows.Forms;

namespace PharmacyManagement.Classes
{
    /// <summary>
    /// مدير لقطات الشاشة - إنشاء صور للواجهات
    /// </summary>
    public static class ScreenshotManager
    {
        #region Constants

        private static readonly string ImagesPath = Path.Combine(Application.StartupPath, "images");

        #endregion

        #region Main Interface Screenshot

        /// <summary>
        /// إنشاء صورة للواجهة الأساسية
        /// </summary>
        public static void CreateMainInterfaceImage()
        {
            CreateClassicInterfaceImage();
            CreateModernInterfaceImage();
        }

        /// <summary>
        /// إنشاء صورة للواجهة التقليدية
        /// </summary>
        public static void CreateClassicInterfaceImage()
        {
            try
            {
                // إنشاء صورة بحجم 1200x700
                var bitmap = new Bitmap(1200, 700);
                using (var graphics = Graphics.FromImage(bitmap))
                {
                    graphics.SmoothingMode = SmoothingMode.AntiAlias;
                    graphics.TextRenderingHint = TextRenderingHint.AntiAlias;

                    // رسم الخلفية
                    DrawBackground(graphics, bitmap.Width, bitmap.Height);

                    // رسم شريط القائمة
                    DrawMenuBar(graphics, bitmap.Width);

                    // رسم شريط الأدوات
                    DrawToolBar(graphics, bitmap.Width);

                    // رسم المحتوى الرئيسي
                    DrawMainContent(graphics, bitmap.Width, bitmap.Height);

                    // رسم شريط الحالة
                    DrawStatusBar(graphics, bitmap.Width, bitmap.Height);
                }

                // حفظ الصورة
                var filePath = Path.Combine(ImagesPath, "classic_interface.png");
                bitmap.Save(filePath, System.Drawing.Imaging.ImageFormat.Png);

                LogManager.LogInfo("تم إنشاء صورة الواجهة التقليدية بنجاح");
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في إنشاء صورة الواجهة الأساسية: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء صورة للواجهة الحديثة
        /// </summary>
        public static void CreateModernInterfaceImage()
        {
            try
            {
                // إنشاء صورة بحجم 1400x800
                var bitmap = new Bitmap(1400, 800);
                using (var graphics = Graphics.FromImage(bitmap))
                {
                    graphics.SmoothingMode = SmoothingMode.AntiAlias;
                    graphics.TextRenderingHint = TextRenderingHint.AntiAlias;

                    // رسم الخلفية
                    DrawModernBackground(graphics, bitmap.Width, bitmap.Height);

                    // رسم اللوحة العلوية
                    DrawModernTopPanel(graphics, bitmap.Width);

                    // رسم اللوحة الجانبية
                    DrawModernSidePanel(graphics, bitmap.Width, bitmap.Height);

                    // رسم المحتوى الرئيسي
                    DrawModernMainContent(graphics, bitmap.Width, bitmap.Height);

                    // رسم شريط الحالة
                    DrawModernStatusBar(graphics, bitmap.Width, bitmap.Height);
                }

                // حفظ الصورة
                var filePath = Path.Combine(ImagesPath, "modern_interface.png");
                bitmap.Save(filePath, System.Drawing.Imaging.ImageFormat.Png);

                LogManager.LogInfo("تم إنشاء صورة الواجهة الحديثة بنجاح");
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في إنشاء صورة الواجهة الحديثة: {ex.Message}");
            }
        }

        #endregion

        #region Modern Interface Drawing

        /// <summary>
        /// رسم خلفية الواجهة الحديثة
        /// </summary>
        private static void DrawModernBackground(Graphics graphics, int width, int height)
        {
            using (var brush = new SolidBrush(Color.FromArgb(236, 240, 241)))
            {
                graphics.FillRectangle(brush, 0, 0, width, height);
            }
        }

        /// <summary>
        /// رسم اللوحة العلوية الحديثة
        /// </summary>
        private static void DrawModernTopPanel(Graphics graphics, int width)
        {
            // خلفية اللوحة العلوية
            using (var brush = new SolidBrush(Color.FromArgb(41, 128, 185)))
            {
                graphics.FillRectangle(brush, 0, 0, width, 80);
            }

            // شعار النظام
            using (var font = new Font("Segoe UI", 18F, FontStyle.Bold))
            using (var brush = new SolidBrush(Color.White))
            {
                graphics.DrawString("🏥 نظام إدارة الصيدلية اليمنية", font, brush, 20, 20);
            }

            // معلومات المستخدم
            using (var font = new Font("Segoe UI", 12F))
            using (var brush = new SolidBrush(Color.White))
            {
                graphics.DrawString("مرحباً، أحمد محمد", font, brush, 20, 50);
            }

            // الأزرار السريعة
            var buttons = new[] { "🔄 تحديث", "💰 بيع سريع", "🔍 بحث سريع", "🔔 التنبيهات" };
            var buttonColors = new[] {
                Color.FromArgb(52, 73, 94),
                Color.FromArgb(46, 204, 113),
                Color.FromArgb(46, 204, 113),
                Color.FromArgb(241, 196, 15)
            };

            for (int i = 0; i < buttons.Length; i++)
            {
                var x = width - 400 + (i * 95);
                var y = 22;

                using (var brush = new SolidBrush(buttonColors[i]))
                {
                    graphics.FillRectangle(brush, x, y, 90, 35);
                }

                using (var font = new Font("Segoe UI", 9F, FontStyle.Bold))
                using (var brush = new SolidBrush(Color.White))
                {
                    var textSize = graphics.MeasureString(buttons[i], font);
                    graphics.DrawString(buttons[i], font, brush,
                        x + (90 - textSize.Width) / 2, y + (35 - textSize.Height) / 2);
                }
            }
        }

        /// <summary>
        /// رسم اللوحة الجانبية الحديثة
        /// </summary>
        private static void DrawModernSidePanel(Graphics graphics, int width, int height)
        {
            var sideWidth = 250;
            var sideX = width - sideWidth;

            // خلفية اللوحة الجانبية
            using (var brush = new SolidBrush(Color.FromArgb(52, 73, 94)))
            {
                graphics.FillRectangle(brush, sideX, 80, sideWidth, height - 110);
            }

            // عناصر القائمة
            var menuItems = new[]
            {
                "📊 لوحة المعلومات", "💊 الأدوية", "👥 العملاء", "🏢 الموردين",
                "💰 المبيعات", "📦 المخزون", "📋 التقارير", "💳 المالية",
                "🏪 الفروع", "🏬 المخازن", "👤 المستخدمين", "⚙️ الإعدادات"
            };

            var y = 100;
            using (var font = new Font("Segoe UI", 11F))
            using (var brush = new SolidBrush(Color.White))
            {
                foreach (var item in menuItems)
                {
                    // تمييز العنصر الأول (المحدد)
                    if (item == menuItems[0])
                    {
                        using (var highlightBrush = new SolidBrush(Color.FromArgb(50, 255, 255, 255)))
                        {
                            graphics.FillRectangle(highlightBrush, sideX + 10, y - 5, 230, 40);
                        }
                    }

                    graphics.DrawString(item, font, brush, sideX + 20, y);
                    y += 50;
                }
            }

            // زر تسجيل الخروج
            using (var brush = new SolidBrush(Color.FromArgb(231, 76, 60)))
            {
                graphics.FillRectangle(brush, sideX + 10, y + 20, 230, 40);
            }

            using (var font = new Font("Segoe UI", 11F))
            using (var brush = new SolidBrush(Color.White))
            {
                graphics.DrawString("🚪 تسجيل خروج", font, brush, sideX + 20, y + 30);
            }
        }

        /// <summary>
        /// رسم المحتوى الرئيسي الحديث
        /// </summary>
        private static void DrawModernMainContent(Graphics graphics, int width, int height)
        {
            var contentArea = new Rectangle(20, 100, width - 290, height - 140);

            // خلفية المحتوى
            using (var brush = new SolidBrush(Color.White))
            {
                graphics.FillRectangle(brush, contentArea);
            }

            // رسم لوحة المعلومات الحديثة
            DrawModernDashboard(graphics, contentArea);
        }

        /// <summary>
        /// رسم لوحة المعلومات الحديثة
        /// </summary>
        private static void DrawModernDashboard(Graphics graphics, Rectangle area)
        {
            // عنوان لوحة المعلومات
            using (var font = new Font("Segoe UI", 20F, FontStyle.Bold))
            using (var brush = new SolidBrush(Color.FromArgb(52, 73, 94)))
            {
                graphics.DrawString("📊 لوحة المعلومات", font, brush, area.X + 30, area.Y + 30);
            }

            // البطاقات الحديثة
            DrawModernCards(graphics, area);

            // الرسوم البيانية الحديثة
            DrawModernCharts(graphics, area);
        }

        /// <summary>
        /// رسم البطاقات الحديثة
        /// </summary>
        private static void DrawModernCards(Graphics graphics, Rectangle area)
        {
            var cards = new[]
            {
                new { Title = "مبيعات اليوم", Value = "25,750 ريال", Icon = "💰", Color = Color.FromArgb(46, 204, 113) },
                new { Title = "إجمالي الأدوية", Value = "1,845 دواء", Icon = "💊", Color = Color.FromArgb(52, 152, 219) },
                new { Title = "مخزون منخفض", Value = "12 دواء", Icon = "⚠️", Color = Color.FromArgb(241, 196, 15) },
                new { Title = "العملاء النشطين", Value = "678 عميل", Icon = "👥", Color = Color.FromArgb(155, 89, 182) }
            };

            var cardWidth = 280;
            var cardHeight = 120;
            var startX = area.X + 30;
            var startY = area.Y + 100;

            for (int i = 0; i < cards.Length; i++)
            {
                var card = cards[i];
                var x = startX + (i % 2) * (cardWidth + 30);
                var y = startY + (i / 2) * (cardHeight + 30);

                // رسم البطاقة مع ظل
                using (var shadowBrush = new SolidBrush(Color.FromArgb(50, 0, 0, 0)))
                {
                    graphics.FillRectangle(shadowBrush, x + 3, y + 3, cardWidth, cardHeight);
                }

                using (var brush = new SolidBrush(Color.White))
                {
                    graphics.FillRectangle(brush, x, y, cardWidth, cardHeight);
                }

                // شريط ملون في الأعلى
                using (var brush = new SolidBrush(card.Color))
                {
                    graphics.FillRectangle(brush, x, y, cardWidth, 5);
                }

                // أيقونة البطاقة
                using (var font = new Font("Segoe UI Emoji", 32))
                using (var brush = new SolidBrush(card.Color))
                {
                    graphics.DrawString(card.Icon, font, brush, x + 20, y + 25);
                }

                // عنوان البطاقة
                using (var font = new Font("Segoe UI", 14F, FontStyle.Bold))
                using (var brush = new SolidBrush(Color.FromArgb(52, 73, 94)))
                {
                    graphics.DrawString(card.Title, font, brush, x + 100, y + 30);
                }

                // قيمة البطاقة
                using (var font = new Font("Segoe UI", 18F, FontStyle.Bold))
                using (var brush = new SolidBrush(card.Color))
                {
                    graphics.DrawString(card.Value, font, brush, x + 100, y + 65);
                }
            }
        }

        /// <summary>
        /// رسم الرسوم البيانية الحديثة
        /// </summary>
        private static void DrawModernCharts(Graphics graphics, Rectangle area)
        {
            var chartArea = new Rectangle(area.X + 650, area.Y + 100, 450, 350);

            // خلفية الرسم البياني مع ظل
            using (var shadowBrush = new SolidBrush(Color.FromArgb(50, 0, 0, 0)))
            {
                graphics.FillRectangle(shadowBrush, chartArea.X + 3, chartArea.Y + 3, chartArea.Width, chartArea.Height);
            }

            using (var brush = new SolidBrush(Color.White))
            {
                graphics.FillRectangle(brush, chartArea);
            }

            // عنوان الرسم البياني
            using (var font = new Font("Segoe UI", 16F, FontStyle.Bold))
            using (var brush = new SolidBrush(Color.FromArgb(52, 73, 94)))
            {
                graphics.DrawString("📈 مبيعات الأسبوع", font, brush, chartArea.X + 20, chartArea.Y + 20);
            }

            // رسم أعمدة بيانية حديثة
            var barColors = new[] {
                Color.FromArgb(46, 204, 113),
                Color.FromArgb(52, 152, 219),
                Color.FromArgb(155, 89, 182),
                Color.FromArgb(241, 196, 15),
                Color.FromArgb(230, 126, 34),
                Color.FromArgb(231, 76, 60),
                Color.FromArgb(149, 165, 166)
            };

            var barWidth = 45;
            var startX = chartArea.X + 40;
            var startY = chartArea.Y + 280;

            for (int i = 0; i < 7; i++)
            {
                var height = (int)(Math.Sin(i + 1) * 60 + 120);
                var x = startX + i * (barWidth + 15);
                var y = startY - height;

                // رسم العمود مع تدرج
                using (var brush = new LinearGradientBrush(
                    new Point(x, y), new Point(x, startY),
                    barColors[i], ControlPaint.Dark(barColors[i], 0.3f)))
                {
                    graphics.FillRectangle(brush, x, y, barWidth, height);
                }

                // تسميات الأيام
                using (var font = new Font("Segoe UI", 9F))
                using (var brush = new SolidBrush(Color.FromArgb(52, 73, 94)))
                {
                    var days = new[] { "السبت", "الأحد", "الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة" };
                    var textSize = graphics.MeasureString(days[i], font);
                    graphics.DrawString(days[i], font, brush, x + (barWidth - textSize.Width) / 2, startY + 10);
                }
            }
        }

        /// <summary>
        /// رسم شريط الحالة الحديث
        /// </summary>
        private static void DrawModernStatusBar(Graphics graphics, int width, int height)
        {
            var statusY = height - 30;

            // خلفية شريط الحالة
            using (var brush = new SolidBrush(Color.FromArgb(52, 73, 94)))
            {
                graphics.FillRectangle(brush, 0, statusY, width, 30);
            }

            // معلومات شريط الحالة
            using (var font = new Font("Segoe UI", 10F))
            using (var brush = new SolidBrush(Color.White))
            {
                graphics.DrawString("جاهز", font, brush, 20, statusY + 8);
                graphics.DrawString(DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), font, brush, 200, statusY + 8);

                using (var greenBrush = new SolidBrush(Color.FromArgb(46, 204, 113)))
                {
                    graphics.DrawString("✅ لا توجد تنبيهات", font, greenBrush, 450, statusY + 8);
                }
            }
        }

        #endregion

        #region Classic Interface Drawing

        /// <summary>
        /// رسم الخلفية
        /// </summary>
        private static void DrawBackground(Graphics graphics, int width, int height)
        {
            // خلفية متدرجة
            using (var brush = new LinearGradientBrush(
                new Point(0, 0), 
                new Point(0, height),
                Color.FromArgb(240, 240, 240),
                Color.FromArgb(220, 220, 220)))
            {
                graphics.FillRectangle(brush, 0, 0, width, height);
            }
        }

        /// <summary>
        /// رسم شريط القائمة
        /// </summary>
        private static void DrawMenuBar(Graphics graphics, int width)
        {
            // خلفية شريط القائمة
            using (var brush = new SolidBrush(Color.FromArgb(52, 73, 94)))
            {
                graphics.FillRectangle(brush, 0, 0, width, 27);
            }

            // عناصر القائمة
            var menuItems = new[] { "ملف", "لوحة المعلومات", "البيانات", "المبيعات", "المخزون", "التقارير", "المالية", "أدوات", "إدارة", "مساعدة" };
            var x = width - 20; // البداية من اليمين

            using (var font = new Font("Segoe UI", 10, FontStyle.Regular))
            using (var brush = new SolidBrush(Color.White))
            {
                foreach (var item in menuItems)
                {
                    var textSize = graphics.MeasureString(item, font);
                    x -= (int)textSize.Width + 20;
                    graphics.DrawString(item, font, brush, x, 5);
                }
            }
        }

        /// <summary>
        /// رسم شريط الأدوات
        /// </summary>
        private static void DrawToolBar(Graphics graphics, int width)
        {
            // خلفية شريط الأدوات
            using (var brush = new SolidBrush(Color.FromArgb(230, 230, 230)))
            {
                graphics.FillRectangle(brush, 0, 27, width, 25);
            }

            // أزرار شريط الأدوات
            var toolButtons = new[] { "🔄 تحديث", "🔔 التنبيهات", "🔍 بحث سريع", "💰 بيع سريع" };
            var x = width - 20;

            using (var font = new Font("Segoe UI", 9, FontStyle.Regular))
            using (var brush = new SolidBrush(Color.FromArgb(52, 73, 94)))
            {
                foreach (var button in toolButtons)
                {
                    var textSize = graphics.MeasureString(button, font);
                    x -= (int)textSize.Width + 15;
                    
                    // رسم خلفية الزر
                    using (var buttonBrush = new SolidBrush(Color.FromArgb(200, 200, 200)))
                    {
                        graphics.FillRectangle(buttonBrush, x - 5, 30, (int)textSize.Width + 10, 20);
                    }
                    
                    graphics.DrawString(button, font, brush, x, 32);
                }
            }
        }

        /// <summary>
        /// رسم المحتوى الرئيسي
        /// </summary>
        private static void DrawMainContent(Graphics graphics, int width, int height)
        {
            var contentArea = new Rectangle(10, 60, width - 20, height - 90);

            // خلفية المحتوى
            using (var brush = new SolidBrush(Color.White))
            {
                graphics.FillRectangle(brush, contentArea);
            }

            // إطار المحتوى
            using (var pen = new Pen(Color.FromArgb(200, 200, 200), 1))
            {
                graphics.DrawRectangle(pen, contentArea);
            }

            // رسم لوحة المعلومات المحاكية
            DrawDashboardContent(graphics, contentArea);
        }

        /// <summary>
        /// رسم محتوى لوحة المعلومات
        /// </summary>
        private static void DrawDashboardContent(Graphics graphics, Rectangle area)
        {
            // عنوان لوحة المعلومات
            using (var font = new Font("Segoe UI", 16, FontStyle.Bold))
            using (var brush = new SolidBrush(Color.FromArgb(52, 73, 94)))
            {
                graphics.DrawString("📊 لوحة المعلومات - نظام إدارة الصيدلية اليمنية", font, brush, area.X + 20, area.Y + 20);
            }

            // رسم البطاقات الإحصائية
            DrawStatCards(graphics, area);

            // رسم الرسوم البيانية المحاكية
            DrawCharts(graphics, area);
        }

        /// <summary>
        /// رسم البطاقات الإحصائية
        /// </summary>
        private static void DrawStatCards(Graphics graphics, Rectangle area)
        {
            var cards = new[]
            {
                new { Title = "مبيعات اليوم", Value = "15,750 ريال", Icon = "💰", Color = Color.FromArgb(46, 204, 113) },
                new { Title = "إجمالي الأدوية", Value = "1,245 دواء", Icon = "💊", Color = Color.FromArgb(52, 152, 219) },
                new { Title = "مخزون منخفض", Value = "23 دواء", Icon = "⚠️", Color = Color.FromArgb(231, 76, 60) },
                new { Title = "العملاء النشطين", Value = "456 عميل", Icon = "👥", Color = Color.FromArgb(155, 89, 182) }
            };

            var cardWidth = 250;
            var cardHeight = 100;
            var startX = area.X + 50;
            var startY = area.Y + 80;

            for (int i = 0; i < cards.Length; i++)
            {
                var card = cards[i];
                var x = startX + (i % 2) * (cardWidth + 30);
                var y = startY + (i / 2) * (cardHeight + 20);

                // رسم البطاقة
                using (var brush = new SolidBrush(Color.White))
                {
                    graphics.FillRectangle(brush, x, y, cardWidth, cardHeight);
                }

                // إطار البطاقة
                using (var pen = new Pen(card.Color, 2))
                {
                    graphics.DrawRectangle(pen, x, y, cardWidth, cardHeight);
                }

                // أيقونة البطاقة
                using (var font = new Font("Segoe UI Emoji", 24))
                using (var brush = new SolidBrush(card.Color))
                {
                    graphics.DrawString(card.Icon, font, brush, x + 20, y + 20);
                }

                // عنوان البطاقة
                using (var font = new Font("Segoe UI", 12, FontStyle.Bold))
                using (var brush = new SolidBrush(Color.FromArgb(52, 73, 94)))
                {
                    graphics.DrawString(card.Title, font, brush, x + 80, y + 20);
                }

                // قيمة البطاقة
                using (var font = new Font("Segoe UI", 14, FontStyle.Bold))
                using (var brush = new SolidBrush(card.Color))
                {
                    graphics.DrawString(card.Value, font, brush, x + 80, y + 50);
                }
            }
        }

        /// <summary>
        /// رسم الرسوم البيانية المحاكية
        /// </summary>
        private static void DrawCharts(Graphics graphics, Rectangle area)
        {
            var chartArea = new Rectangle(area.X + 600, area.Y + 80, 500, 300);

            // خلفية الرسم البياني
            using (var brush = new SolidBrush(Color.White))
            {
                graphics.FillRectangle(brush, chartArea);
            }

            // إطار الرسم البياني
            using (var pen = new Pen(Color.FromArgb(200, 200, 200), 1))
            {
                graphics.DrawRectangle(pen, chartArea);
            }

            // عنوان الرسم البياني
            using (var font = new Font("Segoe UI", 12, FontStyle.Bold))
            using (var brush = new SolidBrush(Color.FromArgb(52, 73, 94)))
            {
                graphics.DrawString("📈 مبيعات الأسبوع الماضي", font, brush, chartArea.X + 20, chartArea.Y + 20);
            }

            // رسم أعمدة بيانية محاكية
            var barColors = new[] { 
                Color.FromArgb(46, 204, 113), 
                Color.FromArgb(52, 152, 219), 
                Color.FromArgb(155, 89, 182),
                Color.FromArgb(241, 196, 15),
                Color.FromArgb(230, 126, 34),
                Color.FromArgb(231, 76, 60),
                Color.FromArgb(149, 165, 166)
            };

            var barWidth = 50;
            var maxHeight = 200;
            var startX = chartArea.X + 50;
            var startY = chartArea.Y + 250;

            for (int i = 0; i < 7; i++)
            {
                var height = (int)(Math.Sin(i) * 80 + 120); // ارتفاع عشوائي
                var x = startX + i * (barWidth + 10);
                var y = startY - height;

                using (var brush = new SolidBrush(barColors[i]))
                {
                    graphics.FillRectangle(brush, x, y, barWidth, height);
                }

                // تسميات الأيام
                using (var font = new Font("Segoe UI", 8))
                using (var brush = new SolidBrush(Color.FromArgb(52, 73, 94)))
                {
                    var days = new[] { "السبت", "الأحد", "الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة" };
                    graphics.DrawString(days[i], font, brush, x + 5, startY + 10);
                }
            }
        }

        /// <summary>
        /// رسم شريط الحالة
        /// </summary>
        private static void DrawStatusBar(Graphics graphics, int width, int height)
        {
            var statusY = height - 22;

            // خلفية شريط الحالة
            using (var brush = new SolidBrush(Color.FromArgb(240, 240, 240)))
            {
                graphics.FillRectangle(brush, 0, statusY, width, 22);
            }

            // خط علوي
            using (var pen = new Pen(Color.FromArgb(200, 200, 200), 1))
            {
                graphics.DrawLine(pen, 0, statusY, width, statusY);
            }

            // معلومات شريط الحالة
            using (var font = new Font("Segoe UI", 9))
            using (var brush = new SolidBrush(Color.FromArgb(52, 73, 94)))
            {
                var statusItems = new[]
                {
                    "المستخدم: أحمد محمد",
                    "الدور: مدير",
                    "النوافذ المفتوحة: 3",
                    "قاعدة البيانات: متصلة",
                    "لا توجد تنبيهات"
                };

                var x = 20;
                foreach (var item in statusItems)
                {
                    graphics.DrawString(item, font, brush, x, statusY + 4);
                    x += (int)graphics.MeasureString(item, font).Width + 30;
                }

                // التاريخ والوقت في اليمين
                var dateTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                var dateTimeSize = graphics.MeasureString(dateTime, font);
                graphics.DrawString(dateTime, font, brush, width - dateTimeSize.Width - 20, statusY + 4);
            }
        }

        #endregion
    }
}
