@echo off
echo ========================================
echo    نظام إدارة الصيدلية - إعداد قاعدة البيانات
echo    Pharmacy Management System - Database Setup
echo ========================================
echo.

echo جاري إنشاء قاعدة البيانات...
echo Creating database...
echo.

REM محاولة تشغيل السكريبت باستخدام sqlcmd
sqlcmd -S (localdb)\MSSQLLocalDB -i "..\PharmacyManagement\Database\CreateDatabase.sql"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ تم إنشاء قاعدة البيانات بنجاح!
    echo ✓ Database created successfully!
    echo.
    echo يمكنك الآن تشغيل التطبيق باستخدام:
    echo You can now run the application using:
    echo - Visual Studio
    echo - أو تشغيل الملف التنفيذي مباشرة
    echo.
    echo بيانات تسجيل الدخول الافتراضية:
    echo Default login credentials:
    echo اسم المستخدم / Username: admin
    echo كلمة المرور / Password: admin123
) else (
    echo.
    echo ✗ فشل في إنشاء قاعدة البيانات
    echo ✗ Failed to create database
    echo.
    echo تأكد من:
    echo Please ensure:
    echo 1. تثبيت SQL Server LocalDB
    echo    SQL Server LocalDB is installed
    echo 2. تشغيل الأمر كمدير
    echo    Running as administrator
    echo 3. وجود ملف السكريبت في المكان الصحيح
    echo    Script file exists in correct location
)

echo.
echo اضغط أي مفتاح للخروج...
echo Press any key to exit...
pause >nul
