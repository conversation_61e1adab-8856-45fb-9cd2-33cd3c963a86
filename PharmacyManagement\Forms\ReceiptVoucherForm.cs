using System;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نموذج سند القبض - Receipt Voucher Form
    /// </summary>
    public partial class ReceiptVoucherForm : Form
    {
        #region Fields

        private int? _receiptId;
        private bool _isEditMode;

        #endregion

        #region Constructor

        public ReceiptVoucherForm(int? receiptId = null)
        {
            InitializeComponent();
            _receiptId = receiptId;
            _isEditMode = receiptId.HasValue;
            
            SetupForm();
            LoadData();
            
            if (_isEditMode)
                LoadReceiptData();
        }

        #endregion

        #region Setup Methods

        private void SetupForm()
        {
            this.Text = _isEditMode ? "تعديل سند قبض" : "سند قبض جديد";
            this.WindowState = FormWindowState.Normal;
            this.StartPosition = FormStartPosition.CenterParent;
            
            // إعداد التصميم المسطح
            SetupFlatDesign();
            
            // إعداد التحقق من البيانات
            SetupValidation();
            
            // إعداد الأحداث
            SetupEvents();
        }

        private void SetupFlatDesign()
        {
            // إعدادات النموذج
            this.BackColor = Color.White;
            this.Font = new Font("Segoe UI", 9F);
            
            // إعداد الأزرار
            foreach (Control control in this.Controls)
            {
                if (control is Button btn)
                {
                    btn.FlatStyle = FlatStyle.Flat;
                    btn.FlatAppearance.BorderSize = 1;
                    btn.FlatAppearance.BorderColor = Color.FromArgb(52, 152, 219);
                    btn.BackColor = Color.FromArgb(52, 152, 219);
                    btn.ForeColor = Color.White;
                    btn.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
                }
                else if (control is TextBox txt)
                {
                    txt.BorderStyle = BorderStyle.FixedSingle;
                    txt.Font = new Font("Segoe UI", 10F);
                }
                else if (control is ComboBox cmb)
                {
                    cmb.FlatStyle = FlatStyle.Flat;
                    cmb.Font = new Font("Segoe UI", 10F);
                }
            }
        }

        private void SetupValidation()
        {
            // إعداد التحقق من البيانات
            txtAmount.KeyPress += (s, e) => {
                if (!char.IsControl(e.KeyChar) && !char.IsDigit(e.KeyChar) && e.KeyChar != '.')
                    e.Handled = true;
            };
        }

        private void SetupEvents()
        {
            // ربط الأحداث
            btnSave.Click += BtnSave_Click;
            btnCancel.Click += BtnCancel_Click;
            btnPrint.Click += BtnPrint_Click;
            cmbCustomer.SelectedIndexChanged += CmbCustomer_SelectedIndexChanged;
            cmbPaymentMethod.SelectedIndexChanged += CmbPaymentMethod_SelectedIndexChanged;
        }

        #endregion

        #region Load Data Methods

        private void LoadData()
        {
            try
            {
                // تحميل العملاء
                LoadCustomers();
                
                // تحميل طرق الدفع
                LoadPaymentMethods();
                
                // إعداد القيم الافتراضية
                SetDefaultValues();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadCustomers()
        {
            var customers = CustomerManager.GetAllCustomers()
                .Where(c => c.IsActive)
                .OrderBy(c => c.CustomerName)
                .ToList();

            cmbCustomer.DataSource = customers;
            cmbCustomer.DisplayMember = "CustomerName";
            cmbCustomer.ValueMember = "CustomerID";
            cmbCustomer.SelectedIndex = -1;
        }

        private void LoadPaymentMethods()
        {
            var paymentMethods = new[]
            {
                new { Value = "Cash", Text = "نقدي" },
                new { Value = "Bank", Text = "بنكي" },
                new { Value = "Card", Text = "بطاقة ائتمان" }
            };

            cmbPaymentMethod.DataSource = paymentMethods;
            cmbPaymentMethod.DisplayMember = "Text";
            cmbPaymentMethod.ValueMember = "Value";
            cmbPaymentMethod.SelectedIndex = 0;
        }

        private void SetDefaultValues()
        {
            if (!_isEditMode)
            {
                txtReceiptNumber.Text = GenerateReceiptNumber();
                dtpReceiptDate.Value = DateTime.Now;
                txtAmount.Text = "0.00";
            }
        }

        private void LoadReceiptData()
        {
            if (!_receiptId.HasValue) return;

            try
            {
                var receiptObj = FinancialManager.GetReceiptVoucher(_receiptId.Value);
                if (receiptObj != null)
                {
                    var receiptType = receiptObj.GetType();

                    txtReceiptNumber.Text = receiptType.GetProperty("ReceiptNumber").GetValue(receiptObj, null).ToString();
                    dtpReceiptDate.Value = (DateTime)receiptType.GetProperty("ReceiptDate").GetValue(receiptObj, null);

                    var customerIdProp = receiptType.GetProperty("CustomerID").GetValue(receiptObj, null);
                    if (customerIdProp != null)
                        cmbCustomer.SelectedValue = customerIdProp;

                    txtAmount.Text = receiptType.GetProperty("Amount").GetValue(receiptObj, null).ToString();

                    var paymentMethodProp = receiptType.GetProperty("PaymentMethod").GetValue(receiptObj, null);
                    cmbPaymentMethod.SelectedValue = paymentMethodProp != null ? paymentMethodProp.ToString() : "";

                    var bankNameProp = receiptType.GetProperty("BankName").GetValue(receiptObj, null);
                    txtBankName.Text = bankNameProp != null ? bankNameProp.ToString() : "";

                    var checkNumberProp = receiptType.GetProperty("CheckNumber").GetValue(receiptObj, null);
                    txtCheckNumber.Text = checkNumberProp != null ? checkNumberProp.ToString() : "";

                    var checkDateProp = receiptType.GetProperty("CheckDate").GetValue(receiptObj, null);
                    if (checkDateProp != null)
                        dtpCheckDate.Value = (DateTime)checkDateProp;

                    var descriptionProp = receiptType.GetProperty("Description").GetValue(receiptObj, null);
                    txtDescription.Text = descriptionProp != null ? descriptionProp.ToString() : "";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات السند: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Event Handlers

        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateData()) return;

                var receipt = CreateReceiptObject();
                bool success;

                if (_isEditMode)
                {
                    receipt.ReceiptID = _receiptId.Value;
                    success = FinancialManager.UpdateReceiptVoucher(receipt);
                }
                else
                {
                    success = FinancialManager.AddReceiptVoucher(receipt);
                }

                if (success)
                {
                    MessageBox.Show("تم حفظ سند القبض بنجاح", "نجح", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("فشل في حفظ سند القبض", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ السند: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void BtnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                if (_receiptId.HasValue)
                {
                    ReportsManager.PrintReceiptVoucher(_receiptId.Value);
                }
                else
                {
                    MessageBox.Show("يجب حفظ السند أولاً قبل الطباعة", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة السند: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CmbCustomer_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cmbCustomer.SelectedValue != null)
            {
                var customerId = Convert.ToInt32(cmbCustomer.SelectedValue);
                var customer = CustomerManager.GetCustomer(customerId);
                if (customer != null)
                {
                    lblCustomerBalance.Text = $"الرصيد: {customer.CurrentBalance:F2} ر.ي";
                }
            }
        }

        private void CmbPaymentMethod_SelectedIndexChanged(object sender, EventArgs e)
        {
            bool isBankPayment = cmbPaymentMethod.SelectedValue?.ToString() == "Bank";
            
            txtBankName.Enabled = isBankPayment;
            txtCheckNumber.Enabled = isBankPayment;
            dtpCheckDate.Enabled = isBankPayment;
            
            if (!isBankPayment)
            {
                txtBankName.Clear();
                txtCheckNumber.Clear();
                dtpCheckDate.Value = DateTime.Now;
            }
        }

        #endregion

        #region Helper Methods

        private bool ValidateData()
        {
            if (cmbCustomer.SelectedValue == null)
            {
                MessageBox.Show("يرجى اختيار العميل", "تحقق من البيانات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbCustomer.Focus();
                return false;
            }

            if (!decimal.TryParse(txtAmount.Text, out decimal amount) || amount <= 0)
            {
                MessageBox.Show("يرجى إدخال مبلغ صحيح", "تحقق من البيانات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtAmount.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtDescription.Text))
            {
                MessageBox.Show("يرجى إدخال وصف للسند", "تحقق من البيانات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtDescription.Focus();
                return false;
            }

            return true;
        }

        private ReceiptVoucher CreateReceiptObject()
        {
            return new ReceiptVoucher
            {
                ReceiptNumber = txtReceiptNumber.Text.Trim(),
                ReceiptDate = dtpReceiptDate.Value.Date,
                FiscalYearID = FinancialManager.GetCurrentFiscalYearId(),
                CustomerID = Convert.ToInt32(cmbCustomer.SelectedValue),
                Amount = decimal.Parse(txtAmount.Text),
                PaymentMethod = cmbPaymentMethod.SelectedValue.ToString(),
                BankName = string.IsNullOrWhiteSpace(txtBankName.Text) ? null : txtBankName.Text.Trim(),
                CheckNumber = string.IsNullOrWhiteSpace(txtCheckNumber.Text) ? null : txtCheckNumber.Text.Trim(),
                CheckDate = dtpCheckDate.Enabled ? (DateTime?)dtpCheckDate.Value.Date : null,
                Description = txtDescription.Text.Trim(),
                Status = "Active",
                CreatedBy = UserManager.CurrentUser.UserID
            };
        }

        private string GenerateReceiptNumber()
        {
            return FinancialManager.GenerateReceiptNumber();
        }

        #endregion
    }
}
