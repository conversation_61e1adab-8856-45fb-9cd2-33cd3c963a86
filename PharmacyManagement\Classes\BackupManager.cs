using System;
using System.Data.SqlClient;
using System.IO;
using System.Windows.Forms;

namespace PharmacyManagement.Classes
{
    /// <summary>
    /// مدير النسخ الاحتياطي - Backup Manager
    /// </summary>
    public static class BackupManager
    {
        #region Backup Operations - عمليات النسخ الاحتياطي

        /// <summary>
        /// إنشاء نسخة احتياطية
        /// </summary>
        /// <param name="backupPath">مسار النسخة الاحتياطية</param>
        /// <returns>نجح العملية</returns>
        public static bool CreateBackup(string backupPath)
        {
            try
            {
                string connectionString = DatabaseHelper.GetConnection().ToString ();
                string databaseName = GetDatabaseName(connectionString);
                
                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    
                    string backupQuery = $@"
                        BACKUP DATABASE [{databaseName}] 
                        TO DISK = @BackupPath
                        WITH FORMAT, INIT, 
                        NAME = 'PharmacyManagement-Full Database Backup', 
                        SKIP, NOREWIND, NOUNLOAD, STATS = 10";
                    
                    using (var command = new SqlCommand(backupQuery, connection))
                    {
                        command.Parameters.AddWithValue("@BackupPath", backupPath);
                        command.CommandTimeout = 300; // 5 دقائق
                        command.ExecuteNonQuery();
                    }
                }
                
                // تسجيل العملية
                LogManager.LogInfo($"تم إنشاء نسخة احتياطية: {backupPath}");
                return true;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// استعادة نسخة احتياطية
        /// </summary>
        /// <param name="backupPath">مسار النسخة الاحتياطية</param>
        /// <returns>نجح العملية</returns>
        public static bool RestoreBackup(string backupPath)
        {
            try
            {
                if (!File.Exists(backupPath))
                {
                    throw new FileNotFoundException("ملف النسخة الاحتياطية غير موجود");
                }

                string connectionString = DatabaseHelper.GetConnection().ToString ();
                string databaseName = GetDatabaseName(connectionString);
                
                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    
                    // إغلاق جميع الاتصالات النشطة
                    string killConnectionsQuery = $@"
                        ALTER DATABASE [{databaseName}] SET SINGLE_USER WITH ROLLBACK IMMEDIATE";
                    
                    using (var command = new SqlCommand(killConnectionsQuery, connection))
                    {
                        command.ExecuteNonQuery();
                    }
                    
                    // استعادة النسخة الاحتياطية
                    string restoreQuery = $@"
                        RESTORE DATABASE [{databaseName}] 
                        FROM DISK = @BackupPath
                        WITH REPLACE, STATS = 10";
                    
                    using (var command = new SqlCommand(restoreQuery, connection))
                    {
                        command.Parameters.AddWithValue("@BackupPath", backupPath);
                        command.CommandTimeout = 300; // 5 دقائق
                        command.ExecuteNonQuery();
                    }
                    
                    // إعادة تفعيل الاتصالات المتعددة
                    string enableConnectionsQuery = $@"
                        ALTER DATABASE [{databaseName}] SET MULTI_USER";
                    
                    using (var command = new SqlCommand(enableConnectionsQuery, connection))
                    {
                        command.ExecuteNonQuery();
                    }
                }
                
                // تسجيل العملية
                LogManager.LogInfo($"تم استعادة النسخة الاحتياطية: {backupPath}");
                return true;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في استعادة النسخة الاحتياطية: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// نسخة احتياطية تلقائية
        /// </summary>
        public static void AutoBackup()
        {
            try
            {
                bool autoBackupEnabled = SettingsManager.GetBoolSetting("AutoBackup", true);
                if (!autoBackupEnabled) return;

                int backupInterval = SettingsManager.GetIntSetting("BackupInterval", 24);
                DateTime lastBackup = SettingsManager.GetDateTimeSetting("LastBackupDate", DateTime.MinValue);
                
                if (DateTime.Now.Subtract(lastBackup).TotalHours >= backupInterval)
                {
                    string backupFolder = Path.Combine(Application.StartupPath, "Backups");
                    if (!Directory.Exists(backupFolder))
                    {
                        Directory.CreateDirectory(backupFolder);
                    }
                    
                    string backupFileName = $"AutoBackup_{DateTime.Now:yyyyMMdd_HHmmss}.bak";
                    string backupPath = Path.Combine(backupFolder, backupFileName);
                    
                    if (CreateBackup(backupPath))
                    {
                        SettingsManager.SetSetting("LastBackupDate", DateTime.Now.ToString());
                        
                        // حذف النسخ القديمة (الاحتفاظ بآخر 10 نسخ)
                        CleanupOldBackups(backupFolder, 10);
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في النسخ الاحتياطي التلقائي: {ex.Message}");
            }
        }

        /// <summary>
        /// تنظيف النسخ الاحتياطية القديمة
        /// </summary>
        /// <param name="backupFolder">مجلد النسخ الاحتياطية</param>
        /// <param name="keepCount">عدد النسخ المراد الاحتفاظ بها</param>
        private static void CleanupOldBackups(string backupFolder, int keepCount)
        {
            try
            {
                var backupFiles = Directory.GetFiles(backupFolder, "AutoBackup_*.bak");
                if (backupFiles.Length > keepCount)
                {
                    Array.Sort(backupFiles);
                    for (int i = 0; i < backupFiles.Length - keepCount; i++)
                    {
                        File.Delete(backupFiles[i]);
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تنظيف النسخ الاحتياطية: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على اسم قاعدة البيانات من نص الاتصال
        /// </summary>
        /// <param name="connectionString">نص الاتصال</param>
        /// <returns>اسم قاعدة البيانات</returns>
        private static string GetDatabaseName(string connectionString)
        {
            var builder = new SqlConnectionStringBuilder(connectionString);
            return builder.InitialCatalog;
        }

        #endregion

        #region Backup Information - معلومات النسخ الاحتياطية

        /// <summary>
        /// الحصول على معلومات النسخة الاحتياطية
        /// </summary>
        /// <param name="backupPath">مسار النسخة الاحتياطية</param>
        /// <returns>معلومات النسخة الاحتياطية</returns>
        public static BackupInfo GetBackupInfo(string backupPath)
        {
            try
            {
                if (!File.Exists(backupPath))
                {
                    return null;
                }

                var fileInfo = new FileInfo(backupPath);
                
                return new BackupInfo
                {
                    FileName = fileInfo.Name,
                    FilePath = fileInfo.FullName,
                    FileSize = fileInfo.Length,
                    CreatedDate = fileInfo.CreationTime,
                    ModifiedDate = fileInfo.LastWriteTime
                };
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على معلومات النسخة الاحتياطية: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// التحقق من صحة النسخة الاحتياطية
        /// </summary>
        /// <param name="backupPath">مسار النسخة الاحتياطية</param>
        /// <returns>صحة النسخة الاحتياطية</returns>
        public static bool ValidateBackup(string backupPath)
        {
            try
            {
                string connectionString = DatabaseHelper.GetConnection().ToString ();
                
                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    
                    string validateQuery = @"
                        RESTORE VERIFYONLY 
                        FROM DISK = @BackupPath";
                    
                    using (var command = new SqlCommand(validateQuery, connection))
                    {
                        command.Parameters.AddWithValue("@BackupPath", backupPath);
                        command.CommandTimeout = 60;
                        command.ExecuteNonQuery();
                    }
                }
                
                return true;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في التحقق من النسخة الاحتياطية: {ex.Message}");
                return false;
            }
        }

        #endregion
    }

    /// <summary>
    /// معلومات النسخة الاحتياطية
    /// </summary>
    public class BackupInfo
    {
        public string FileName { get; set; }
        public string FilePath { get; set; }
        public long FileSize { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime ModifiedDate { get; set; }
        
        public string FileSizeFormatted
        {
            get
            {
                if (FileSize < 1024) return $"{FileSize} بايت";
                if (FileSize < 1024 * 1024) return $"{FileSize / 1024:F1} كيلوبايت";
                if (FileSize < 1024 * 1024 * 1024) return $"{FileSize / (1024 * 1024):F1} ميجابايت";
                return $"{FileSize / (1024 * 1024 * 1024):F1} جيجابايت";
            }
        }
    }
}
