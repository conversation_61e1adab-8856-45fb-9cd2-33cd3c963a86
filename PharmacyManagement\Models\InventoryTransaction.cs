using System;

namespace PharmacyManagement.Models
{
    /// <summary>
    /// معاملة المخزون - Inventory Transaction
    /// </summary>
    public class InventoryTransaction
    {
        /// <summary>
        /// معرف المعاملة
        /// </summary>
        public int TransactionID { get; set; }

        /// <summary>
        /// معرف الدواء
        /// </summary>
        public int DrugID { get; set; }

        /// <summary>
        /// نوع المعاملة (In/Out)
        /// </summary>
        public string TransactionType { get; set; }

        /// <summary>
        /// الكمية
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// سعر الوحدة
        /// </summary>
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// المبلغ الإجمالي
        /// </summary>
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// تاريخ المعاملة
        /// </summary>
        public DateTime TransactionDate { get; set; }

        /// <summary>
        /// المرجع
        /// </summary>
        public string Reference { get; set; }

        /// <summary>
        /// ملاحظات
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// معرف المستخدم
        /// </summary>
        public int UserID { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// معرف المستودع
        /// </summary>
        public int WarehouseID { get; set; }

        /// <summary>
        /// رقم الدفعة
        /// </summary>
        public string BatchNumber { get; set; }

        /// <summary>
        /// تاريخ انتهاء الصلاحية
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// منشئ بواسطة
        /// </summary>
        public int CreatedBy { get; set; }
    }
}
