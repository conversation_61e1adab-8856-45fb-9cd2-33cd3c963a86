-- ===================================================================
-- ملف البيانات التجريبية - Sample Data
-- تاريخ الإنشاء: 2025-01-28
-- الغرض: إضافة بيانات تجريبية لاختبار نظام المخزون
-- ===================================================================

USE PharmacyDB
GO

PRINT '📦 بدء إدراج البيانات التجريبية...'
PRINT '📅 التاريخ: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT ''

-- ===================================================================
-- 1. التحقق من وجود البيانات الأساسية
-- ===================================================================

PRINT '🔍 التحقق من البيانات الأساسية...'

-- التحقق من وجود فئات الأدوية
IF NOT EXISTS (SELECT 1 FROM DrugCategories WHERE IsActive = 1)
BEGIN
    PRINT '⚠️ لا توجد فئات أدوية - سيتم إضافة فئات تجريبية'
    
    INSERT INTO DrugCategories (CategoryCode, CategoryName, Description, IsActive, CreatedDate, CreatedBy)
    VALUES 
    ('PAIN', 'مسكنات الألم', 'أدوية تسكين الألم والالتهابات', 1, GETDATE(), 1),
    ('ANTI', 'مضادات حيوية', 'أدوية مضادة للبكتيريا والعدوى', 1, GETDATE(), 1),
    ('CARD', 'أدوية القلب', 'أدوية علاج أمراض القلب والأوعية الدموية', 1, GETDATE(), 1),
    ('DIAB', 'أدوية السكري', 'أدوية علاج مرض السكري', 1, GETDATE(), 1),
    ('RESP', 'أدوية الجهاز التنفسي', 'أدوية علاج أمراض الجهاز التنفسي', 1, GETDATE(), 1),
    ('DERM', 'أدوية الجلدية', 'أدوية علاج الأمراض الجلدية', 1, GETDATE(), 1),
    ('NEUR', 'أدوية الأعصاب', 'أدوية علاج الأمراض العصبية', 1, GETDATE(), 1),
    ('GAST', 'أدوية الجهاز الهضمي', 'أدوية علاج أمراض الجهاز الهضمي', 1, GETDATE(), 1)
    
    PRINT '✅ تم إضافة 8 فئات تجريبية'
END
ELSE
    PRINT '✅ فئات الأدوية موجودة'

-- التحقق من وجود الشركات المصنعة
IF NOT EXISTS (SELECT 1 FROM Manufacturers WHERE IsActive = 1)
BEGIN
    PRINT '⚠️ لا توجد شركات مصنعة - سيتم إضافة شركات تجريبية'
    
    INSERT INTO Manufacturers (ManufacturerCode, ManufacturerName, Country, IsActive, CreatedDate, CreatedBy)
    VALUES 
    ('PFIZER', 'شركة فايزر', 'الولايات المتحدة', 1, GETDATE(), 1),
    ('NOVARTIS', 'شركة نوفارتيس', 'سويسرا', 1, GETDATE(), 1),
    ('ROCHE', 'شركة روش', 'سويسرا', 1, GETDATE(), 1),
    ('SANOFI', 'شركة سانوفي', 'فرنسا', 1, GETDATE(), 1),
    ('GSK', 'شركة جلاكسو سميث كلاين', 'المملكة المتحدة', 1, GETDATE(), 1),
    ('BAYER', 'شركة باير', 'ألمانيا', 1, GETDATE(), 1),
    ('ABBOTT', 'شركة أبوت', 'الولايات المتحدة', 1, GETDATE(), 1),
    ('MERCK', 'شركة مرك', 'الولايات المتحدة', 1, GETDATE(), 1)
    
    PRINT '✅ تم إضافة 8 شركات مصنعة تجريبية'
END
ELSE
    PRINT '✅ الشركات المصنعة موجودة'

PRINT ''

-- ===================================================================
-- 2. إضافة أدوية تجريبية
-- ===================================================================

PRINT '💊 إضافة أدوية تجريبية...'

-- الحصول على معرفات الفئات والشركات
DECLARE @PainCategoryID INT = (SELECT TOP 1 CategoryID FROM DrugCategories WHERE CategoryCode = 'PAIN')
DECLARE @AntiCategoryID INT = (SELECT TOP 1 CategoryID FROM DrugCategories WHERE CategoryCode = 'ANTI')
DECLARE @CardCategoryID INT = (SELECT TOP 1 CategoryID FROM DrugCategories WHERE CategoryCode = 'CARD')
DECLARE @DiabCategoryID INT = (SELECT TOP 1 CategoryID FROM DrugCategories WHERE CategoryCode = 'DIAB')

DECLARE @PfizerID INT = (SELECT TOP 1 ManufacturerID FROM Manufacturers WHERE ManufacturerCode = 'PFIZER')
DECLARE @NovartisID INT = (SELECT TOP 1 ManufacturerID FROM Manufacturers WHERE ManufacturerCode = 'NOVARTIS')
DECLARE @SanofiID INT = (SELECT TOP 1 ManufacturerID FROM Manufacturers WHERE ManufacturerCode = 'SANOFI')
DECLARE @BayerID INT = (SELECT TOP 1 ManufacturerID FROM Manufacturers WHERE ManufacturerCode = 'BAYER')

-- إضافة أدوية تجريبية إذا لم تكن موجودة
IF NOT EXISTS (SELECT 1 FROM Drugs WHERE DrugCode = 'PARA001')
BEGIN
    INSERT INTO Drugs (DrugCode, DrugName, ScientificName, CategoryID, ManufacturerID, 
                      Unit, Strength, Form, PurchasePrice, SalePrice, WholesalePrice,
                      MinStock, MaxStock, ReorderLevel, IsActive, CreatedDate, CreatedBy)
    VALUES 
    -- مسكنات الألم
    ('PARA001', 'باراسيتامول 500 مجم', 'Paracetamol', @PainCategoryID, @PfizerID, 
     'قرص', '500mg', 'أقراص', 0.50, 1.00, 0.75, 100, 1000, 200, 1, GETDATE(), 1),
    
    ('IBUP001', 'إيبوبروفين 400 مجم', 'Ibuprofen', @PainCategoryID, @BayerID,
     'قرص', '400mg', 'أقراص', 0.75, 1.50, 1.00, 50, 500, 100, 1, GETDATE(), 1),
    
    ('ASPI001', 'أسبرين 100 مجم', 'Aspirin', @PainCategoryID, @BayerID,
     'قرص', '100mg', 'أقراص', 0.25, 0.50, 0.35, 200, 2000, 400, 1, GETDATE(), 1),
    
    -- مضادات حيوية
    ('AMOX001', 'أموكسيسيلين 500 مجم', 'Amoxicillin', @AntiCategoryID, @PfizerID,
     'كبسولة', '500mg', 'كبسولات', 2.00, 4.00, 3.00, 30, 300, 60, 1, GETDATE(), 1),
    
    ('AZIT001', 'أزيثرومايسين 250 مجم', 'Azithromycin', @AntiCategoryID, @PfizerID,
     'قرص', '250mg', 'أقراص', 5.00, 10.00, 7.50, 20, 200, 40, 1, GETDATE(), 1),
    
    -- أدوية القلب
    ('ATEN001', 'أتينولول 50 مجم', 'Atenolol', @CardCategoryID, @NovartisID,
     'قرص', '50mg', 'أقراص', 1.50, 3.00, 2.25, 50, 500, 100, 1, GETDATE(), 1),
    
    ('LISIN001', 'ليسينوبريل 10 مجم', 'Lisinopril', @CardCategoryID, @NovartisID,
     'قرص', '10mg', 'أقراص', 2.50, 5.00, 3.75, 40, 400, 80, 1, GETDATE(), 1),
    
    -- أدوية السكري
    ('METF001', 'ميتفورمين 500 مجم', 'Metformin', @DiabCategoryID, @SanofiID,
     'قرص', '500mg', 'أقراص', 1.00, 2.00, 1.50, 100, 1000, 200, 1, GETDATE(), 1),
    
    ('GLIB001', 'جليبنكلاميد 5 مجم', 'Glibenclamide', @DiabCategoryID, @SanofiID,
     'قرص', '5mg', 'أقراص', 1.25, 2.50, 1.85, 60, 600, 120, 1, GETDATE(), 1),
    
    ('INSU001', 'إنسولين سريع المفعول', 'Insulin Rapid', @DiabCategoryID, @NovartisID,
     'قلم', '100IU/ml', 'حقن', 25.00, 50.00, 37.50, 10, 100, 20, 1, GETDATE(), 1)
    
    PRINT '✅ تم إضافة 10 أدوية تجريبية'
END
ELSE
    PRINT '✅ الأدوية التجريبية موجودة'

PRINT ''

-- ===================================================================
-- 3. إضافة مخزون تجريبي
-- ===================================================================

PRINT '📦 إضافة مخزون تجريبي...'

-- إضافة مخزون لكل دواء
DECLARE @DrugID INT
DECLARE drug_cursor CURSOR FOR 
    SELECT DrugID FROM Drugs WHERE IsActive = 1

OPEN drug_cursor
FETCH NEXT FROM drug_cursor INTO @DrugID

WHILE @@FETCH_STATUS = 0
BEGIN
    -- التحقق من عدم وجود مخزون لهذا الدواء
    IF NOT EXISTS (SELECT 1 FROM Inventory WHERE DrugID = @DrugID)
    BEGIN
        -- إضافة 3 دفعات مختلفة لكل دواء
        INSERT INTO Inventory (DrugID, WarehouseID, BatchNumber, Quantity, 
                             ExpiryDate, ManufacturingDate, PurchasePrice, SalePrice, CreatedDate)
        SELECT 
            @DrugID,
            1, -- المستودع الرئيسي
            'BATCH_' + FORMAT(GETDATE(), 'yyyyMMdd') + '_' + CAST(@DrugID AS VARCHAR) + '_1',
            CAST(RAND() * 200 + 50 AS INT), -- كمية عشوائية بين 50-250
            DATEADD(MONTH, CAST(RAND() * 24 + 6 AS INT), GETDATE()), -- تنتهي خلال 6-30 شهر
            DATEADD(MONTH, -CAST(RAND() * 6 AS INT), GETDATE()), -- تم تصنيعها خلال آخر 6 أشهر
            PurchasePrice,
            SalePrice,
            GETDATE()
        FROM Drugs WHERE DrugID = @DrugID
        
        -- دفعة ثانية
        INSERT INTO Inventory (DrugID, WarehouseID, BatchNumber, Quantity, 
                             ExpiryDate, ManufacturingDate, PurchasePrice, SalePrice, CreatedDate)
        SELECT 
            @DrugID,
            1,
            'BATCH_' + FORMAT(GETDATE(), 'yyyyMMdd') + '_' + CAST(@DrugID AS VARCHAR) + '_2',
            CAST(RAND() * 150 + 25 AS INT), -- كمية عشوائية بين 25-175
            DATEADD(MONTH, CAST(RAND() * 18 + 12 AS INT), GETDATE()), -- تنتهي خلال 12-30 شهر
            DATEADD(MONTH, -CAST(RAND() * 3 AS INT), GETDATE()), -- تم تصنيعها خلال آخر 3 أشهر
            PurchasePrice,
            SalePrice,
            GETDATE()
        FROM Drugs WHERE DrugID = @DrugID
        
        -- دفعة ثالثة (قد تكون منتهية الصلاحية)
        INSERT INTO Inventory (DrugID, WarehouseID, BatchNumber, Quantity, 
                             ExpiryDate, ManufacturingDate, PurchasePrice, SalePrice, CreatedDate)
        SELECT 
            @DrugID,
            1,
            'BATCH_' + FORMAT(GETDATE(), 'yyyyMMdd') + '_' + CAST(@DrugID AS VARCHAR) + '_3',
            CAST(RAND() * 50 + 10 AS INT), -- كمية عشوائية بين 10-60
            CASE 
                WHEN RAND() > 0.7 THEN DATEADD(DAY, -CAST(RAND() * 30 AS INT), GETDATE()) -- 30% منتهية الصلاحية
                ELSE DATEADD(MONTH, CAST(RAND() * 6 + 1 AS INT), GETDATE()) -- 70% صالحة
            END,
            DATEADD(MONTH, -CAST(RAND() * 12 AS INT), GETDATE()), -- تم تصنيعها خلال آخر سنة
            PurchasePrice,
            SalePrice,
            GETDATE()
        FROM Drugs WHERE DrugID = @DrugID
    END
    
    FETCH NEXT FROM drug_cursor INTO @DrugID
END

CLOSE drug_cursor
DEALLOCATE drug_cursor

PRINT '✅ تم إضافة مخزون تجريبي لجميع الأدوية'

PRINT ''

-- ===================================================================
-- 4. إضافة حركات مخزون تجريبية
-- ===================================================================

PRINT '🔄 إضافة حركات مخزون تجريبية...'

-- إضافة بعض حركات المخزون التجريبية
INSERT INTO StockMovements (DrugID, WarehouseID, MovementType, Quantity, 
                           ReferenceNumber, ReferenceType, Notes, UserID, MovementDate)
SELECT TOP 20
    d.DrugID,
    1,
    CASE WHEN RAND() > 0.5 THEN 'دخول' ELSE 'خروج' END,
    CAST(RAND() * 50 + 10 AS INT),
    'REF_' + FORMAT(GETDATE(), 'yyyyMMdd') + '_' + CAST(ROW_NUMBER() OVER (ORDER BY d.DrugID) AS VARCHAR),
    CASE WHEN RAND() > 0.5 THEN 'فاتورة_شراء' ELSE 'فاتورة_بيع' END,
    'حركة تجريبية',
    1,
    DATEADD(DAY, -CAST(RAND() * 30 AS INT), GETDATE())
FROM Drugs d
WHERE d.IsActive = 1
ORDER BY NEWID()

PRINT '✅ تم إضافة 20 حركة مخزون تجريبية'

PRINT ''

-- ===================================================================
-- 5. إحصائيات البيانات التجريبية
-- ===================================================================

PRINT '📊 إحصائيات البيانات التجريبية:'

-- عدد الأدوية
SELECT 'الأدوية' as DataType, COUNT(*) as Count
FROM Drugs WHERE IsActive = 1

UNION ALL

-- عدد الفئات
SELECT 'فئات الأدوية' as DataType, COUNT(*) as Count
FROM DrugCategories WHERE IsActive = 1

UNION ALL

-- عدد الشركات المصنعة
SELECT 'الشركات المصنعة' as DataType, COUNT(*) as Count
FROM Manufacturers WHERE IsActive = 1

UNION ALL

-- عدد سجلات المخزون
SELECT 'سجلات المخزون' as DataType, COUNT(*) as Count
FROM Inventory

UNION ALL

-- عدد حركات المخزون
SELECT 'حركات المخزون' as DataType, COUNT(*) as Count
FROM StockMovements

-- إحصائيات المخزون
PRINT ''
PRINT 'إحصائيات المخزون التفصيلية:'

SELECT 
    'إجمالي المخزون' as Description,
    SUM(CurrentStock) as Value,
    'وحدة' as Unit
FROM vw_CurrentStock

UNION ALL

SELECT 
    'قيمة المخزون',
    SUM(CurrentStock * PurchasePrice),
    'ريال'
FROM vw_CurrentStock

UNION ALL

SELECT 
    'أدوية مخزون منخفض',
    COUNT(*),
    'دواء'
FROM vw_LowStockDrugs

UNION ALL

SELECT 
    'أدوية منتهية الصلاحية',
    COUNT(DISTINCT DrugID),
    'دواء'
FROM vw_ExpiredDrugs

PRINT ''
PRINT '✅ تم إدراج جميع البيانات التجريبية بنجاح!'
PRINT '📅 تاريخ الانتهاء: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT ''
PRINT '📝 ملاحظات:'
PRINT '   - تم إضافة 10 أدوية تجريبية'
PRINT '   - تم إضافة 3 دفعات مخزون لكل دواء'
PRINT '   - تم إضافة 20 حركة مخزون تجريبية'
PRINT '   - بعض الأدوية قد تكون منتهية الصلاحية للاختبار'
PRINT '   - يمكن حذف هذه البيانات لاحقاً إذا لزم الأمر'
PRINT ''
PRINT '🎯 قاعدة البيانات جاهزة للاختبار!'
