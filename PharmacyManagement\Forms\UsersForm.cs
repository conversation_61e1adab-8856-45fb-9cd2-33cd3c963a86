using System;
using System.Data;
using System.Drawing;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نافذة إدارة المستخدمين - Users Management Form
    /// </summary>
    public partial class UsersForm : Form
    {
        #region Constructor - المنشئ

        public UsersForm()
        {
            InitializeComponent();
            SetupForm();
            LoadData();
        }

        #endregion

        #region Form Setup - إعداد النافذة

        /// <summary>
        /// إعداد النافذة
        /// </summary>
        private void SetupForm()
        {
            // إعداد DataGridView
            SetupDataGridView();
            
            // إعداد ComboBoxes
            LoadRoles();
            
            // إعداد التصميم المسطح
            ApplyFlatDesign();
        }

        /// <summary>
        /// إعداد DataGridView
        /// </summary>
        private void SetupDataGridView()
        {
            dgvUsers.AutoGenerateColumns = false;
            dgvUsers.AllowUserToAddRows = false;
            dgvUsers.AllowUserToDeleteRows = false;
            dgvUsers.ReadOnly = true;
            dgvUsers.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvUsers.MultiSelect = false;
            
            // تنسيق الألوان
            dgvUsers.BackgroundColor = Color.White;
            dgvUsers.GridColor = Color.FromArgb(189, 195, 199);
            dgvUsers.DefaultCellStyle.SelectionBackColor = Color.FromArgb(52, 152, 219);
            dgvUsers.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvUsers.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 73, 94);
            dgvUsers.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvUsers.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10, FontStyle.Bold);
        }

        /// <summary>
        /// تحميل الأدوار
        /// </summary>
        private void LoadRoles()
        {
            var roles = new[]
            {
                new { Value = "", Text = "جميع الأدوار" },
                new { Value = "Admin", Text = "مدير النظام" },
                new { Value = "Manager", Text = "مدير" },
                new { Value = "Pharmacist", Text = "صيدلي" },
                new { Value = "Cashier", Text = "كاشير" },
                new { Value = "User", Text = "مستخدم" }
            };
            
            cmbRole.DisplayMember = "Text";
            cmbRole.ValueMember = "Value";
            cmbRole.DataSource = roles;
            cmbRole.SelectedIndex = 0;
        }

        /// <summary>
        /// تطبيق التصميم المسطح
        /// </summary>
        private void ApplyFlatDesign()
        {
            // إزالة حدود الأزرار
            foreach (Control control in this.Controls)
            {
                ApplyFlatStyleToControl(control);
            }
        }

        /// <summary>
        /// تطبيق التصميم المسطح على عنصر
        /// </summary>
        private void ApplyFlatStyleToControl(Control control)
        {
            if (control is Button button)
            {
                button.FlatAppearance.BorderSize = 0;
                button.Cursor = Cursors.Hand;
            }
            
            // تطبيق على العناصر الفرعية
            foreach (Control child in control.Controls)
            {
                ApplyFlatStyleToControl(child);
            }
        }

        #endregion

        #region Data Loading - تحميل البيانات

        /// <summary>
        /// تحميل بيانات المستخدمين
        /// </summary>
        private void LoadData()
        {
            try
            {
                var users = UserManager.GetAllUsers();
                dgvUsers.DataSource = users;
                
                // تحديث عدد السجلات
                lblRecordsCount.Text = $"عدد السجلات: {users.Count}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Event Handlers - معالجات الأحداث

        /// <summary>
        /// البحث في المستخدمين
        /// </summary>
        private void btnSearch_Click(object sender, EventArgs e)
        {
            try
            {
                string searchTerm = txtSearch.Text.Trim();
                string role = cmbRole.SelectedValue?.ToString();
                
                var users = string.IsNullOrEmpty(searchTerm) ? UserManager.GetAllUsers() : UserManager.SearchUsers(searchTerm);
                dgvUsers.DataSource = users;
                
                lblRecordsCount.Text = $"عدد السجلات: {users.Count}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إضافة مستخدم جديد
        /// </summary>
        private void btnAdd_Click(object sender, EventArgs e)
        {
            var addForm = new UserAddEditForm();
            if (addForm.ShowDialog() == DialogResult.OK)
            {
                LoadData();
            }
        }

        /// <summary>
        /// تعديل مستخدم
        /// </summary>
        private void btnEdit_Click(object sender, EventArgs e)
        {
            if (dgvUsers.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار مستخدم للتعديل", "تنبيه", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedUser = dgvUsers.SelectedRows[0].DataBoundItem as User;
            if (selectedUser != null)
            {
                var editForm = new UserAddEditForm(selectedUser);
                if (editForm.ShowDialog() == DialogResult.OK)
                {
                    LoadData();
                }
            }
        }

        /// <summary>
        /// حذف مستخدم
        /// </summary>
        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (dgvUsers.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار مستخدم للحذف", "تنبيه", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedUser = dgvUsers.SelectedRows[0].DataBoundItem as User;
            if (selectedUser != null)
            {
                // منع حذف المستخدم الحالي
                if (selectedUser.UserID == UserManager.CurrentUser.UserID)
                {
                    MessageBox.Show("لا يمكن حذف المستخدم الحالي", "تنبيه", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var result = MessageBox.Show("هل أنت متأكد من حذف هذا المستخدم؟", "تأكيد الحذف", 
                                           MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                
                if (result == DialogResult.Yes)
                {
                    if (UserManager.DeleteUser(selectedUser.UserID))
                    {
                        MessageBox.Show("تم حذف المستخدم بنجاح", "نجح", 
                                      MessageBoxButtons.OK, MessageBoxIcon.Information);
                        LoadData();
                    }
                    else
                    {
                        MessageBox.Show("فشل في حذف المستخدم", "خطأ", 
                                      MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        /// <summary>
        /// إعادة تعيين كلمة المرور
        /// </summary>
        //private void btnResetPassword_Click(object sender, EventArgs e)
        //{
        //    if (dgvUsers.SelectedRows.Count == 0)
        //    {
        //        MessageBox.Show("يرجى اختيار مستخدم لإعادة تعيين كلمة المرور", "تنبيه", 
        //                      MessageBoxButtons.OK, MessageBoxIcon.Warning);
        //        return;
        //    }

        //    var selectedUser = dgvUsers.SelectedRows[0].DataBoundItem as User;
        //    if (selectedUser != null)
        //    {
        //        var resetForm = new PasswordResetForm(selectedUser);
        //        if (resetForm.ShowDialog() == DialogResult.OK)
        //        {
        //            MessageBox.Show("تم إعادة تعيين كلمة المرور بنجاح", "نجح", 
        //                          MessageBoxButtons.OK, MessageBoxIcon.Information);
        //        }
        //    }
        //}

        /// <summary>
        /// إدارة الصلاحيات
        /// </summary>
        //private void btnPermissions_Click(object sender, EventArgs e)
        //{
        //    if (dgvUsers.SelectedRows.Count == 0)
        //    {
        //        MessageBox.Show("يرجى اختيار مستخدم لإدارة الصلاحيات", "تنبيه",
        //                      MessageBoxButtons.OK, MessageBoxIcon.Warning);
        //        return;
        //    }

        //    var selectedUser = dgvUsers.SelectedRows[0].DataBoundItem as User;
        //    if (selectedUser != null)
        //    {
        //        var permissionsForm = new UserPermissionsForm(selectedUser);
        //        permissionsForm.ShowDialog();
        //    }
        //}

        /// <summary>
        /// إعادة تعيين كلمة المرور
        /// </summary>
        private void btnResetPassword_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvUsers.SelectedRows.Count == 0)
                {
                    MessageBox.Show("يرجى اختيار مستخدم لإعادة تعيين كلمة المرور", "تنبيه",
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var selectedUser = dgvUsers.SelectedRows[0].DataBoundItem as User;
                if (selectedUser != null)
                {
                    var passwordResetForm = new PasswordResetForm(selectedUser.UserID);
                    if (passwordResetForm.ShowDialog() == DialogResult.OK)
                    {
                        LoadData(); // تحديث البيانات في حالة تغيير كلمة المرور
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعادة تعيين كلمة المرور: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
                LogManager.LogError($"UsersForm.btnResetPassword_Click: {ex.Message}");
            }
        }

        /// <summary>
        /// إدارة صلاحيات المستخدم
        /// </summary>
        private void btnPermissions_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvUsers.SelectedRows.Count == 0)
                {
                    MessageBox.Show("يرجى اختيار مستخدم لإدارة صلاحياته", "تنبيه",
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var selectedUser = dgvUsers.SelectedRows[0].DataBoundItem as User;
                if (selectedUser != null)
                {
                    var permissionsForm = new UserPermissionsForm(selectedUser.UserID);
                    if (permissionsForm.ShowDialog() == DialogResult.OK)
                    {
                        LoadData(); // تحديث البيانات في حالة تغيير الصلاحيات
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إدارة الصلاحيات: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
                LogManager.LogError($"UsersForm.btnPermissions_Click: {ex.Message}");
            }
        }

        /// <summary>
        /// إعادة تعيين البحث
        /// </summary>
        private void btnReset_Click(object sender, EventArgs e)
        {
            txtSearch.Clear();
            cmbRole.SelectedIndex = 0;
            LoadData();
        }

        /// <summary>
        /// البحث عند الكتابة
        /// </summary>
        private void txtSearch_TextChanged(object sender, EventArgs e)
        {
            // البحث التلقائي بعد توقف الكتابة
            searchTimer.Stop();
            searchTimer.Start();
        }

        /// <summary>
        /// البحث التلقائي
        /// </summary>
        private void searchTimer_Tick(object sender, EventArgs e)
        {
            searchTimer.Stop();
            btnSearch_Click(sender, e);
        }

        /// <summary>
        /// تغيير الدور
        /// </summary>
        private void cmbRole_SelectedIndexChanged(object sender, EventArgs e)
        {
            btnSearch_Click(sender, e);
        }

        /// <summary>
        /// النقر المزدوج لتعديل
        /// </summary>
        private void dgvUsers_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                btnEdit_Click(sender, e);
            }
        }

        #endregion
    }
}
