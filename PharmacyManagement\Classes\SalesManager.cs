using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using PharmacyManagement.Models;

namespace PharmacyManagement.Classes
{
    /// <summary>
    /// مدير المبيعات - Sales Manager
    /// </summary>
    public static class SalesManager
    {
        #region Sales Operations - عمليات المبيعات

        /// <summary>
        /// إنشاء فاتورة مبيعات جديدة
        /// </summary>
        /// <param name="invoice">بيانات الفاتورة</param>
        /// <returns>معرف الفاتورة</returns>
        public static int CreateSalesInvoice(Invoice invoice)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // إدراج الفاتورة
                            string invoiceQuery = @"
                                INSERT INTO SalesInvoices (InvoiceNumber, InvoiceDate, CustomerID,
                                                         UserID, BranchID, WarehouseID, SubTotal,
                                                         DiscountAmount, TaxAmount, NetAmount, PaidAmount,
                                                         PaymentMethod, PaymentStatus, Notes,
                                                         CreatedDate, CreatedBy)
                                VALUES (@InvoiceNumber, @InvoiceDate, @CustomerID,
                                       @UserID, @BranchID, @WarehouseID, @SubTotal,
                                       @DiscountAmount, @TaxAmount, @NetAmount, @PaidAmount,
                                       @PaymentMethod, @PaymentStatus, @Notes,
                                       @CreatedDate, @CreatedBy);
                                SELECT SCOPE_IDENTITY();";

                            int invoiceId;
                            using (var command = new SqlCommand(invoiceQuery, connection, transaction))
                            {
                                command.Parameters.AddWithValue("@InvoiceNumber", invoice.InvoiceNumber);
                                command.Parameters.AddWithValue("@InvoiceDate", invoice.InvoiceDate);
                                command.Parameters.AddWithValue("@CustomerID", invoice.CustomerID ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@UserID", invoice.UserID > 0 ? invoice.UserID : (invoice.CreatedBy > 0 ? invoice.CreatedBy : 1));
                                command.Parameters.AddWithValue("@BranchID", 1); // قيمة افتراضية
                                command.Parameters.AddWithValue("@WarehouseID", 1); // قيمة افتراضية
                                command.Parameters.AddWithValue("@SubTotal", invoice.SubTotal);
                                command.Parameters.AddWithValue("@DiscountAmount", invoice.DiscountAmount);
                                command.Parameters.AddWithValue("@TaxAmount", invoice.TaxAmount);
                                command.Parameters.AddWithValue("@NetAmount", invoice.TotalAmount);
                                command.Parameters.AddWithValue("@PaidAmount", invoice.PaidAmount);
                                command.Parameters.AddWithValue("@PaymentMethod", invoice.PaymentMethod ?? "نقد");
                                command.Parameters.AddWithValue("@PaymentStatus", invoice.PaymentStatus ?? "مدفوع");
                                command.Parameters.AddWithValue("@Notes", invoice.Notes ?? "");
                                command.Parameters.AddWithValue("@CreatedDate", DateTime.Now);
                                command.Parameters.AddWithValue("@CreatedBy", invoice.CreatedBy > 0 ? invoice.CreatedBy : 1);

                                var result = command.ExecuteScalar();
                                invoiceId = Convert.ToInt32(result);
                                System.Diagnostics.Debug.WriteLine($"تم إنشاء فاتورة برقم ID: {invoiceId}");
                            }

                            // إدراج تفاصيل الفاتورة
                            var details = invoice.Details ?? invoice.InvoiceDetails ?? new List<InvoiceDetail>();
                            foreach (var detail in details)
                            {
                                string detailQuery = @"
                                    INSERT INTO SalesInvoiceDetails (InvoiceID, DrugID, BatchNumber, Quantity,
                                                                    UnitPrice, DiscountPercent, DiscountAmount,
                                                                    TaxPercent, TaxAmount, TotalPrice, ExpiryDate)
                                    VALUES (@InvoiceID, @DrugID, @BatchNumber, @Quantity,
                                           @UnitPrice, @DiscountPercent, @DiscountAmount,
                                           @TaxPercent, @TaxAmount, @TotalPrice, @ExpiryDate)";

                                using (var command = new SqlCommand(detailQuery, connection, transaction))
                                {
                                    command.Parameters.AddWithValue("@InvoiceID", invoiceId);
                                    command.Parameters.AddWithValue("@DrugID", detail.DrugID);
                                    command.Parameters.AddWithValue("@BatchNumber", detail.BatchNumber ?? (object)DBNull.Value);
                                    command.Parameters.AddWithValue("@Quantity", detail.Quantity);
                                    command.Parameters.AddWithValue("@UnitPrice", detail.UnitPrice);
                                    command.Parameters.AddWithValue("@DiscountPercent", detail.DiscountPercent);
                                    command.Parameters.AddWithValue("@DiscountAmount", detail.DiscountAmount);
                                    command.Parameters.AddWithValue("@TaxPercent", 0); // قيمة افتراضية
                                    command.Parameters.AddWithValue("@TaxAmount", 0); // قيمة افتراضية
                                    command.Parameters.AddWithValue("@TotalPrice", detail.TotalPrice);
                                    command.Parameters.AddWithValue("@ExpiryDate", detail.ExpiryDate ?? (object)DBNull.Value);

                                    command.ExecuteNonQuery();
                                    System.Diagnostics.Debug.WriteLine($"تم حفظ تفصيل: الدواء {detail.DrugID}, الكمية {detail.Quantity}");
                                }

                                // تحديث المخزون
                                var inventoryTransaction = new InventoryTransaction
                                {
                                    DrugID = detail.DrugID,
                                    TransactionType = "Out",
                                    Quantity = detail.Quantity,
                                    UnitPrice = detail.UnitPrice,
                                    TotalAmount = detail.TotalPrice,
                                    TransactionDate = invoice.InvoiceDate,
                                    Reference = "فاتورة مبيعات " + invoice.InvoiceNumber,
                                    Notes = "مبيعات",
                                    CreatedBy = invoice.UserID > 0 ? invoice.UserID : 1
                                };

                                // تحديث المخزون - خصم الكمية المباعة
                                UpdateDrugStock(detail.DrugID, -detail.Quantity, connection, transaction);
                            }

                            transaction.Commit();
                            LogManager.LogInfo("تم إنشاء فاتورة مبيعات: " + invoice.InvoiceNumber);
                            return invoiceId;
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            System.Diagnostics.Debug.WriteLine($"خطأ في حفظ الفاتورة: {ex.Message}");
                            System.Diagnostics.Debug.WriteLine($"Stack Trace: {ex.StackTrace}");
                            LogManager.LogError($"خطأ في حفظ فاتورة المبيعات: {ex.Message}");
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في إنشاء فاتورة المبيعات: " + ex.Message);
                throw;
            }
        }

        /// <summary>
        /// تحديث مخزون الدواء
        /// </summary>
        /// <param name="drugId">معرف الدواء</param>
        /// <param name="quantityChange">التغيير في الكمية (سالب للخصم، موجب للإضافة)</param>
        /// <param name="connection">الاتصال بقاعدة البيانات</param>
        /// <param name="transaction">المعاملة</param>
        private static void UpdateDrugStock(int drugId, int quantityChange, SqlConnection connection, SqlTransaction transaction)
        {
            try
            {
                // البحث عن أقدم دفعة متاحة للدواء
                string selectQuery = @"
                    SELECT TOP 1 InventoryID, Quantity
                    FROM Inventory
                    WHERE DrugID = @DrugID
                    AND Quantity > 0
                    AND (ExpiryDate > GETDATE() OR ExpiryDate IS NULL)
                    ORDER BY ExpiryDate ASC, InventoryID ASC";

                using (var selectCommand = new SqlCommand(selectQuery, connection, transaction))
                {
                    selectCommand.Parameters.AddWithValue("@DrugID", drugId);

                    using (var reader = selectCommand.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            int inventoryId = Convert.ToInt32(reader["InventoryID"]);
                            int currentQuantity = Convert.ToInt32(reader["Quantity"]);
                            reader.Close();

                            // حساب الكمية الجديدة
                            int newQuantity = currentQuantity + quantityChange; // quantityChange سالب للمبيعات

                            if (newQuantity >= 0)
                            {
                                // تحديث الكمية
                                string updateQuery = @"
                                    UPDATE Inventory
                                    SET Quantity = @NewQuantity,
                                        UpdatedDate = GETDATE()
                                    WHERE InventoryID = @InventoryID";

                                using (var updateCommand = new SqlCommand(updateQuery, connection, transaction))
                                {
                                    updateCommand.Parameters.AddWithValue("@NewQuantity", newQuantity);
                                    updateCommand.Parameters.AddWithValue("@InventoryID", inventoryId);
                                    updateCommand.ExecuteNonQuery();
                                }

                                System.Diagnostics.Debug.WriteLine($"تم تحديث مخزون الدواء {drugId}: {currentQuantity} -> {newQuantity}");
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"تحذير: الكمية المطلوبة ({Math.Abs(quantityChange)}) أكبر من المتاح ({currentQuantity}) للدواء {drugId}");
                            }
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"تحذير: لا توجد كمية متاحة للدواء {drugId}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث المخزون للدواء {drugId}: {ex.Message}");
                throw; // إعادة إلقاء الخطأ لإلغاء المعاملة
            }
        }

        /// <summary>
        /// الحصول على فواتير المبيعات
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <returns>قائمة فواتير المبيعات</returns>
        public static List<Invoice> GetSalesInvoices(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var invoices = new List<Invoice>();
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT
                            i.InvoiceID,
                            i.InvoiceNumber,
                            i.InvoiceDate,
                            i.CustomerID,
                            c.CustomerName,
                            i.SubTotal,
                            i.DiscountAmount,
                            i.TaxAmount,
                            i.NetAmount as TotalAmount,
                            i.PaidAmount,
                            i.PaymentMethod,
                            i.PaymentStatus,
                            i.Notes,
                            i.CreatedBy,
                            i.CreatedDate
                        FROM SalesInvoices i
                        LEFT JOIN Customers c ON i.CustomerID = c.CustomerID";

                    if (fromDate.HasValue)
                        query += " AND i.InvoiceDate >= @FromDate";
                    
                    if (toDate.HasValue)
                        query += " AND i.InvoiceDate <= @ToDate";

                    query += " ORDER BY i.InvoiceDate DESC";

                    using (var command = new SqlCommand(query, connection))
                    {
                        if (fromDate.HasValue)
                            command.Parameters.AddWithValue("@FromDate", fromDate.Value);
                        
                        if (toDate.HasValue)
                            command.Parameters.AddWithValue("@ToDate", toDate.Value);

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                invoices.Add(new Invoice
                                {
                                    InvoiceID = Convert.ToInt32(reader["InvoiceID"]),
                                    InvoiceNumber = reader["InvoiceNumber"].ToString(),
                                    InvoiceDate = Convert.ToDateTime(reader["InvoiceDate"]),
                                    InvoiceType = reader["InvoiceType"].ToString(),
                                    CustomerID = reader["CustomerID"] != DBNull.Value ? Convert.ToInt32(reader["CustomerID"]) : (int?)null,
                                    CustomerName = reader["CustomerName"].ToString(),
                                    SubTotal = Convert.ToDecimal(reader["SubTotal"]),
                                    DiscountAmount = Convert.ToDecimal(reader["DiscountAmount"]),
                                    TaxAmount = Convert.ToDecimal(reader["TaxAmount"]),
                                    TotalAmount = Convert.ToDecimal(reader["TotalAmount"]),
                                    PaymentMethod = reader["PaymentMethod"].ToString(),
                                    PaymentStatus = reader["PaymentStatus"].ToString(),
                                    Notes = reader["Notes"].ToString(),
                                    CreatedBy = Convert.ToInt32(reader["CreatedBy"]),
                                    CreatedByName = reader["CreatedByName"].ToString(),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"])
                                });
                            }
                        }
                    }
                }
                LogManager.LogInfo("تم الحصول على " + invoices.Count.ToString() + " فاتورة مبيعات");
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في الحصول على فواتير المبيعات: " + ex.Message);
                throw;
            }
            return invoices;
        }

        /// <summary>
        /// الحصول على تفاصيل فاتورة مبيعات
        /// </summary>
        /// <param name="invoiceID">معرف الفاتورة</param>
        /// <returns>تفاصيل الفاتورة</returns>
        public static List<InvoiceDetail> GetInvoiceDetails(int invoiceID)
        {
            var details = new List<InvoiceDetail>();
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT 
                            id.InvoiceDetailID,
                            id.InvoiceID,
                            id.DrugID,
                            d.DrugName,
                            d.DrugCode,
                            id.Quantity,
                            id.UnitPrice,
                            id.DiscountAmount,
                            id.TotalPrice
                        FROM SalesInvoiceDetails id
                        INNER JOIN Drugs d ON id.DrugID = d.DrugID
                        WHERE id.InvoiceID = @InvoiceID
                        ORDER BY id.InvoiceDetailID";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@InvoiceID", invoiceID);
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                details.Add(new InvoiceDetail
                                {
                                    InvoiceDetailID = Convert.ToInt32(reader["InvoiceDetailID"]),
                                    InvoiceID = Convert.ToInt32(reader["InvoiceID"]),
                                    DrugID = Convert.ToInt32(reader["DrugID"]),
                                    DrugName = reader["DrugName"].ToString(),
                                    DrugCode = reader["DrugCode"].ToString(),
                                    Quantity = Convert.ToInt32(reader["Quantity"]),
                                    UnitPrice = Convert.ToDecimal(reader["UnitPrice"]),
                                    DiscountAmount = Convert.ToDecimal(reader["DiscountAmount"]),
                                    TotalAmount = Convert.ToDecimal(reader["TotalPrice"])
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في الحصول على تفاصيل الفاتورة: " + ex.Message);
                throw;
            }
            return details;
        }

        #endregion

        #region Sales Statistics - إحصائيات المبيعات

        /// <summary>
        /// الحصول على إحصائيات المبيعات
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <returns>إحصائيات المبيعات</returns>
        public static SalesStatistics GetSalesStatistics(DateTime fromDate, DateTime toDate)
        {
            var stats = new SalesStatistics();
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    
                    // إجمالي المبيعات
                    string totalSalesQuery = @"
                        SELECT 
                            COUNT(*) as TotalInvoices,
                            ISNULL(SUM(NetAmount), 0) as TotalSales,
                            ISNULL(AVG(NetAmount), 0) as AverageSale
                        FROM SalesInvoices
                        WHERE InvoiceDate BETWEEN @FromDate AND @ToDate";

                    using (var command = new SqlCommand(totalSalesQuery, connection))
                    {
                        command.Parameters.AddWithValue("@FromDate", fromDate);
                        command.Parameters.AddWithValue("@ToDate", toDate);
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                stats.TotalInvoices = Convert.ToInt32(reader["TotalInvoices"]);
                                stats.TotalSales = Convert.ToDecimal(reader["TotalSales"]);
                                stats.AverageSale = Convert.ToDecimal(reader["AverageSale"]);
                            }
                        }
                    }
                    
                    // أفضل الأدوية مبيعاً
                    string topDrugsQuery = @"
                        SELECT TOP 10
                            d.DrugName,
                            SUM(id.Quantity) as TotalQuantity,
                            SUM(id.TotalPrice) as TotalAmount
                        FROM SalesInvoiceDetails id
                        INNER JOIN SalesInvoices i ON id.InvoiceID = i.InvoiceID
                        INNER JOIN Drugs d ON id.DrugID = d.DrugID
                        WHERE i.InvoiceDate BETWEEN @FromDate AND @ToDate
                        GROUP BY d.DrugID, d.DrugName
                        ORDER BY SUM(id.TotalPrice) DESC";

                    using (var command = new SqlCommand(topDrugsQuery, connection))
                    {
                        command.Parameters.AddWithValue("@FromDate", fromDate);
                        command.Parameters.AddWithValue("@ToDate", toDate);
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                stats.TopSellingDrugs.Add(new TopSellingDrug
                                {
                                    DrugName = reader["DrugName"].ToString(),
                                    TotalQuantitySold = Convert.ToInt32(reader["TotalQuantity"]),
                                    TotalSalesAmount = Convert.ToDecimal(reader["TotalAmount"])
                                });
                            }
                        }
                    }
                }
                
                LogManager.LogInfo("تم الحصول على إحصائيات المبيعات من " + fromDate.ToString("yyyy-MM-dd") + " إلى " + toDate.ToString("yyyy-MM-dd"));
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في الحصول على إحصائيات المبيعات: " + ex.Message);
                throw;
            }
            return stats;
        }

        /// <summary>
        /// الحصول على المبيعات اليومية
        /// </summary>
        /// <param name="date">التاريخ</param>
        /// <returns>مبيعات اليوم</returns>
        public static DailySales GetDailySales(DateTime date)
        {
            var dailySales = new DailySales { Date = date };
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT 
                            COUNT(*) as InvoiceCount,
                            ISNULL(SUM(NetAmount), 0) as TotalAmount,
                            ISNULL(SUM(DiscountAmount), 0) as TotalDiscount,
                            ISNULL(SUM(TaxAmount), 0) as TotalTax
                        FROM SalesInvoices
                        WHERE CAST(InvoiceDate as DATE) = CAST(@Date as DATE)";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@Date", date);
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                dailySales.InvoiceCount = Convert.ToInt32(reader["InvoiceCount"]);
                                dailySales.TotalAmount = Convert.ToDecimal(reader["TotalAmount"]);
                                dailySales.TotalDiscount = Convert.ToDecimal(reader["TotalDiscount"]);
                                dailySales.TotalTax = Convert.ToDecimal(reader["TotalTax"]);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في الحصول على المبيعات اليومية: " + ex.Message);
                throw;
            }
            return dailySales;
        }

        #endregion

        #region Helper Methods - الطرق المساعدة

        /// <summary>
        /// توليد رقم فاتورة جديد
        /// </summary>
        /// <returns>رقم الفاتورة</returns>
        public static string GenerateInvoiceNumber()
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT COUNT(*)
                        FROM SalesInvoices
                        WHERE YEAR(InvoiceDate) = YEAR(GETDATE())";
                    
                    using (var command = new SqlCommand(query, connection))
                    {
                        int count = Convert.ToInt32(command.ExecuteScalar());
                        return "INV-" + DateTime.Now.Year.ToString() + "-" + (count + 1).ToString("D6");
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في توليد رقم الفاتورة: " + ex.Message);
                return "INV-" + DateTime.Now.Year.ToString() + "-" + DateTime.Now.Ticks.ToString().Substring(10);
            }
        }

        /// <summary>
        /// التحقق من توفر المخزون
        /// </summary>
        /// <param name="drugID">معرف الدواء</param>
        /// <param name="requiredQuantity">الكمية المطلوبة</param>
        /// <returns>true إذا كان المخزون متوفر</returns>
        public static bool CheckStockAvailability(int drugID, int requiredQuantity)
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT ISNULL(SUM(CASE WHEN TransactionType = 'In' THEN Quantity ELSE -Quantity END), 0)
                        FROM InventoryTransactions 
                        WHERE DrugID = @DrugID";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@DrugID", drugID);
                        int currentStock = Convert.ToInt32(command.ExecuteScalar());
                        return currentStock >= requiredQuantity;
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في التحقق من توفر المخزون: " + ex.Message);
                return false;
            }
        }

        /// <summary>
        /// الحصول على عدد فواتير اليوم
        /// </summary>
        /// <returns>عدد الفواتير</returns>
        public static int GetTodayInvoicesCount()
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT COUNT(*)
                        FROM Invoices
                        WHERE CAST(InvoiceDate AS DATE) = CAST(GETDATE() AS DATE)
                        AND IsActive = 1";

                    using (var command = new SqlCommand(query, connection))
                    {
                        var result = command.ExecuteScalar();
                        return result != null ? Convert.ToInt32(result) : 0;
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في الحصول على عدد فواتير اليوم: " + ex.Message);
                return 0;
            }
        }

        /// <summary>
        /// الحصول على مبيعات اليوم
        /// </summary>
        /// <returns>مبيعات اليوم</returns>
        public static decimal GetTodaySales()
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT ISNULL(SUM(NetAmount), 0)
                        FROM SalesInvoices
                        WHERE CAST(InvoiceDate AS DATE) = CAST(GETDATE() AS DATE)";

                    using (var command = new SqlCommand(query, connection))
                    {
                        var result = command.ExecuteScalar();
                        return result != null ? Convert.ToDecimal(result) : 0;
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في الحصول على مبيعات اليوم: " + ex.Message);
                return 0;
            }
        }

        /// <summary>
        /// الحصول على مبيعات الشهر
        /// </summary>
        /// <returns>مبيعات الشهر</returns>
        public static decimal GetMonthlySales()
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT ISNULL(SUM(NetAmount), 0)
                        FROM SalesInvoices
                        WHERE MONTH(InvoiceDate) = MONTH(GETDATE())
                        AND YEAR(InvoiceDate) = YEAR(GETDATE())";

                    using (var command = new SqlCommand(query, connection))
                    {
                        var result = command.ExecuteScalar();
                        return result != null ? Convert.ToDecimal(result) : 0;
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في الحصول على مبيعات الشهر: " + ex.Message);
                return 0;
            }
        }

        #endregion
    }

    #region Sales Models - نماذج المبيعات

    /// <summary>
    /// نموذج إحصائيات المبيعات
    /// </summary>
    public class SalesStatistics
    {
        public int TotalInvoices { get; set; }
        public decimal TotalSales { get; set; }
        public decimal AverageSale { get; set; }
        public List<TopSellingDrug> TopSellingDrugs { get; set; }
    }

    // تم حذف فئة TopSellingDrug من هنا لتجنب التعارض
    // استخدم TopSellingDrug من SalesModels.cs

    /// <summary>
    /// نموذج المبيعات اليومية
    /// </summary>
    public class DailySales
    {
        public DateTime Date { get; set; }
        public int InvoiceCount { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal TotalDiscount { get; set; }
        public decimal TotalTax { get; set; }
    }

    #endregion
}
