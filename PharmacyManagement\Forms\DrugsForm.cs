using System;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نافذة إدارة الأدوية - Drugs Management Form
    /// </summary>
    public partial class DrugsForm : Form
    {
        #region Constructor - المنشئ

        public DrugsForm()
        {
            InitializeComponent();
            SetupForm();
            LoadData();
        }

        #endregion

        #region Form Setup - إعداد النافذة

        /// <summary>
        /// إعداد النافذة
        /// </summary>
        private void SetupForm()
        {
            // إعداد DataGridView
            SetupDataGridView();
            
            // إعداد ComboBoxes
            LoadCategories();
            LoadManufacturers();
            
            // إعداد التصميم المسطح
            ApplyFlatDesign();
        }

        /// <summary>
        /// إعداد DataGridView
        /// </summary>
        private void SetupDataGridView()
        {
            dgvDrugs.AutoGenerateColumns = false;
            dgvDrugs.AllowUserToAddRows = false;
            dgvDrugs.AllowUserToDeleteRows = false;
            dgvDrugs.ReadOnly = true;
            dgvDrugs.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvDrugs.MultiSelect = false;
            
            // تنسيق الألوان
            dgvDrugs.BackgroundColor = Color.White;
            dgvDrugs.GridColor = Color.FromArgb(189, 195, 199);
            dgvDrugs.DefaultCellStyle.SelectionBackColor = Color.FromArgb(52, 152, 219);
            dgvDrugs.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvDrugs.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 73, 94);
            dgvDrugs.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvDrugs.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10, FontStyle.Bold);
        }

        /// <summary>
        /// تطبيق التصميم المسطح
        /// </summary>
        private void ApplyFlatDesign()
        {
            // إزالة حدود الأزرار
            foreach (Control control in this.Controls)
            {
                if (control is Button button)
                {
                    button.FlatAppearance.BorderSize = 0;
                    button.Cursor = Cursors.Hand;
                }
            }
        }

        #endregion

        #region Data Loading - تحميل البيانات

        /// <summary>
        /// تحميل بيانات الأدوية
        /// </summary>
        private void LoadData()
        {
            try
            {
                var drugs = DrugManager.SearchDrugsWithStock();
                dgvDrugs.DataSource = drugs;
                
                // تحديث عدد السجلات
                lblRecordsCount.Text = $"عدد السجلات: {drugs.Count}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحميل فئات الأدوية
        /// </summary>
        private void LoadCategories()
        {
            try
            {
                string query = "SELECT CategoryID, CategoryName FROM DrugCategories ORDER BY CategoryName";
                var dataTable = DatabaseHelper.ExecuteQuery(query);
                
                cmbCategory.DisplayMember = "CategoryName";
                cmbCategory.ValueMember = "CategoryID";
                cmbCategory.DataSource = dataTable;
                cmbCategory.SelectedIndex = -1;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الفئات: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل الشركات المصنعة
        /// </summary>
        private void LoadManufacturers()
        {
            try
            {
                string query = "SELECT ManufacturerID, ManufacturerName FROM Manufacturers ORDER BY ManufacturerName";
                var dataTable = DatabaseHelper.ExecuteQuery(query);
                
                cmbManufacturer.DisplayMember = "ManufacturerName";
                cmbManufacturer.ValueMember = "ManufacturerID";
                cmbManufacturer.DataSource = dataTable;
                cmbManufacturer.SelectedIndex = -1;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الشركات: {ex.Message}");
            }
        }

        #endregion

        #region Event Handlers - معالجات الأحداث

        /// <summary>
        /// البحث في الأدوية
        /// </summary>
        private void btnSearch_Click(object sender, EventArgs e)
        {
            try
            {
                string searchTerm = txtSearch.Text.Trim();
                int? categoryId = cmbCategory.SelectedValue as int?;
                int? manufacturerId = cmbManufacturer.SelectedValue as int?;
                
                var drugs = DrugManager.SearchDrugsWithStock(searchTerm, categoryId, manufacturerId);
                dgvDrugs.DataSource = drugs;
                
                lblRecordsCount.Text = $"عدد السجلات: {drugs.Count}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إضافة دواء جديد
        /// </summary>
        private void btnAdd_Click(object sender, EventArgs e)
        {
            var addForm = new DrugAddEditForm();
            if (addForm.ShowDialog() == DialogResult.OK)
            {
                LoadData();
            }
        }

        /// <summary>
        /// تعديل دواء
        /// </summary>
        private void btnEdit_Click(object sender, EventArgs e)
        {
            if (dgvDrugs.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار دواء للتعديل", "تنبيه", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedDrug = dgvDrugs.SelectedRows[0].DataBoundItem as Drug;
            if (selectedDrug != null)
            {
                var editForm = new DrugAddEditForm(selectedDrug);
                if (editForm.ShowDialog() == DialogResult.OK)
                {
                    LoadData();
                }
            }
        }

        /// <summary>
        /// حذف دواء
        /// </summary>
        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (dgvDrugs.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار دواء للحذف", "تنبيه", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show("هل أنت متأكد من حذف هذا الدواء؟", "تأكيد الحذف", 
                                       MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            
            if (result == DialogResult.Yes)
            {
                var selectedDrug = dgvDrugs.SelectedRows[0].DataBoundItem as Drug;
                if (selectedDrug != null)
                {
                    if (DrugManager.DeleteDrug(selectedDrug.DrugID))
                    {
                        MessageBox.Show("تم حذف الدواء بنجاح", "نجح", 
                                      MessageBoxButtons.OK, MessageBoxIcon.Information);
                        LoadData();
                    }
                    else
                    {
                        MessageBox.Show("فشل في حذف الدواء", "خطأ", 
                                      MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        /// <summary>
        /// تحديث المخزون
        /// </summary>
        private void btnUpdateStock_Click(object sender, EventArgs e)
        {
            if (dgvDrugs.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار دواء لتحديث المخزون", "تنبيه", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedDrug = dgvDrugs.SelectedRows[0].DataBoundItem as Drug;
            if (selectedDrug != null)
            {
                var stockForm = new StockUpdateForm(selectedDrug);
                if (stockForm.ShowDialog() == DialogResult.OK)
                {
                    LoadData();
                }
            }
        }

        /// <summary>
        /// إعادة تعيين البحث
        /// </summary>
        private void btnReset_Click(object sender, EventArgs e)
        {
            txtSearch.Clear();
            cmbCategory.SelectedIndex = -1;
            cmbManufacturer.SelectedIndex = -1;
            LoadData();
        }

        /// <summary>
        /// البحث عند الكتابة
        /// </summary>
        private void txtSearch_TextChanged(object sender, EventArgs e)
        {
            // البحث التلقائي بعد توقف الكتابة
            searchTimer.Stop();
            searchTimer.Start();
        }

        /// <summary>
        /// البحث التلقائي
        /// </summary>
        private void searchTimer_Tick(object sender, EventArgs e)
        {
            searchTimer.Stop();
            btnSearch_Click(sender, e);
        }

        /// <summary>
        /// النقر المزدوج لتعديل
        /// </summary>
        private void dgvDrugs_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                btnEdit_Click(sender, e);
            }
        }

        #endregion
    }
}
