using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using CurrencyManager = PharmacyManagement.Classes.CurrencyManager;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نافذة تحديث أسعار الصرف - Exchange Rate Update Form
    /// </summary>
    public partial class ExchangeRateUpdateForm : Form
    {
        #region Constructor - المنشئ

        public ExchangeRateUpdateForm()
        {
            InitializeComponent();
            SetupForm();
            LoadCurrencies();
        }

        #endregion

        #region Form Setup - إعداد النافذة

        /// <summary>
        /// إعداد النافذة
        /// </summary>
        private void SetupForm()
        {
            this.Text = "تحديث أسعار الصرف";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.ShowInTaskbar = false;

            SetupDataGridView();
            ApplyFlatDesign();
        }

        /// <summary>
        /// إعداد DataGridView
        /// </summary>
        private void SetupDataGridView()
        {
            dgvCurrencies.AutoGenerateColumns = false;
            dgvCurrencies.AllowUserToAddRows = false;
            dgvCurrencies.AllowUserToDeleteRows = false;
            dgvCurrencies.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvCurrencies.MultiSelect = false;
            dgvCurrencies.ReadOnly = false;

            // السماح بتعديل عمود سعر الصرف فقط
            foreach (DataGridViewColumn column in dgvCurrencies.Columns)
            {
                if (column.Name == "ExchangeRate")
                    column.ReadOnly = false;
                else
                    column.ReadOnly = true;
            }
        }

        /// <summary>
        /// تطبيق التصميم المسطح
        /// </summary>
        private void ApplyFlatDesign()
        {
            this.BackColor = Color.White;
            
            foreach (Control control in this.Controls)
            {
                if (control is Button button)
                {
                    button.FlatStyle = FlatStyle.Flat;
                    button.FlatAppearance.BorderSize = 0;
                }
            }
        }

        /// <summary>
        /// تحميل العملات
        /// </summary>
        private void LoadCurrencies()
        {
            try
            {
                var currencies = CurrencyManager.GetAllCurrencies()
                    .Where(c => c.IsActive && !c.IsBaseCurrency)
                    .ToList();

                dgvCurrencies.DataSource = currencies;
                // lblTotalCurrencies.Text = $"إجمالي العملات: {currencies.Count}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل العملات: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Event Handlers - معالجات الأحداث

        /// <summary>
        /// حفظ التحديثات
        /// </summary>
        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                bool hasChanges = false;
                var currencies = dgvCurrencies.DataSource as System.Collections.Generic.List<Models.Currency>;

                if (currencies != null)
                {
                    foreach (var currency in currencies)
                    {
                        // البحث عن الصف المقابل في الجدول
                        foreach (DataGridViewRow row in dgvCurrencies.Rows)
                        {
                            if (row.DataBoundItem == currency)
                            {
                                decimal newRate = Convert.ToDecimal(row.Cells["ExchangeRate"].Value);
                                if (newRate != currency.ExchangeRate && newRate > 0)
                                {
                                    if (CurrencyManager.UpdateExchangeRate(currency.CurrencyCode, newRate))
                                    {
                                        hasChanges = true;
                                    }
                                }
                                break;
                            }
                        }
                    }
                }

                if (hasChanges)
                {
                    MessageBox.Show("تم تحديث أسعار الصرف بنجاح", "نجح", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("لم يتم إجراء أي تغييرات", "تنبيه", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ التحديثات: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إلغاء العملية
        /// </summary>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// تحديث تلقائي للأسعار (محاكاة)
        /// </summary>
        private void btnAutoUpdate_Click(object sender, EventArgs e)
        {
            try
            {
                var result = MessageBox.Show("هل تريد تحديث الأسعار تلقائ�؟\nسيتم تطبيق تغييرات عشوائية صغيرة على الأسعار.", 
                                           "تأكيد التحديث التلقائي", 
                                           MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    var random = new Random();
                    bool hasUpdates = false;

                    foreach (DataGridViewRow row in dgvCurrencies.Rows)
                    {
                        if (row.DataBoundItem != null)
                        {
                            decimal currentRate = Convert.ToDecimal(row.Cells["ExchangeRate"].Value);
                            // تغيير عشوائي بين -5% و +5%
                            double changePercent = (random.NextDouble() - 0.5) * 0.1; // -5% to +5%
                            decimal newRate = currentRate * (1 + (decimal)changePercent);
                            newRate = Math.Round(newRate, 4);

                            row.Cells["ExchangeRate"].Value = newRate;
                            hasUpdates = true;
                        }
                    }

                    if (hasUpdates)
                    {
                        MessageBox.Show("تم تحديث الأسعار تلقائ�. اضغط حفظ لتطبيق التغييرات.", "تم التحديث", 
                                      MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التحديث التلقائي: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحديث البيانات
        /// </summary>
        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadCurrencies();
        }

        #endregion

        #region Validation - التحقق

        /// <summary>
        /// التحقق من صحة البيانات عند تعديل الخلية
        /// </summary>
        private void dgvCurrencies_CellValidating(object sender, DataGridViewCellValidatingEventArgs e)
        {
            if (dgvCurrencies.Columns[e.ColumnIndex].Name == "ExchangeRate")
            {
                if (!decimal.TryParse(e.FormattedValue.ToString(), out decimal rate) || rate <= 0)
                {
                    e.Cancel = true;
                    MessageBox.Show("يرجى إدخال قيمة صحيحة أكبر من صفر", "خطأ في البيانات", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
        }

        #endregion
    }
}


