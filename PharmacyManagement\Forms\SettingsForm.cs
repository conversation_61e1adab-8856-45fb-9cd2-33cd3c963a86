using System;
using System.Drawing;
using System.Windows.Forms;
using PharmacyManagement.Classes;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نافذة الإعدادات - Settings Form
    /// </summary>
    public partial class SettingsForm : Form
    {
        #region Constructor - المنشئ

        public SettingsForm()
        {
            InitializeComponent();
            SetupForm();
            LoadSettings();
        }

        #endregion

        #region Form Setup - إعداد النافذة

        /// <summary>
        /// إعداد النافذة
        /// </summary>
        private void SetupForm()
        {
            // إعداد التصميم المسطح
            ApplyFlatDesign();
        }

        /// <summary>
        /// تطبيق التصميم المسطح
        /// </summary>
        private void ApplyFlatDesign()
        {
            // إزالة حدود الأزرار
            foreach (Control control in this.Controls)
            {
                ApplyFlatStyleToControl(control);
            }
        }

        /// <summary>
        /// تطبيق التصميم المسطح على عنصر
        /// </summary>
        private void ApplyFlatStyleToControl(Control control)
        {
            if (control is Button button)
            {
                button.FlatAppearance.BorderSize = 0;
                button.Cursor = Cursors.Hand;
            }
            
            // تطبيق على العناصر الفرعية
            foreach (Control child in control.Controls)
            {
                ApplyFlatStyleToControl(child);
            }
        }

        #endregion

        #region Settings Loading - تحميل الإعدادات

        /// <summary>
        /// تحميل الإعدادات
        /// </summary>
        private void LoadSettings()
        {
            try
            {
                // إعدادات عامة
                txtPharmacyName.Text = SettingsManager.GetSetting("PharmacyName", "صيدلية الشفاء");
                txtPharmacyAddress.Text = SettingsManager.GetSetting("PharmacyAddress", "");
                txtPharmacyPhone.Text = SettingsManager.GetSetting("PharmacyPhone", "");
                txtPharmacyEmail.Text = SettingsManager.GetSetting("PharmacyEmail", "");
                txtLicenseNumber.Text = SettingsManager.GetSetting("LicenseNumber", "");
                
                // إعدادات النظام
                chkAutoBackup.Checked = SettingsManager.GetBoolSetting("AutoBackup", true);
                numBackupInterval.Value = SettingsManager.GetIntSetting("BackupInterval", 24);
                chkLowStockAlert.Checked = SettingsManager.GetBoolSetting("LowStockAlert", true);
                numLowStockThreshold.Value = SettingsManager.GetIntSetting("LowStockThreshold", 10);
                
                // إعدادات الأمان
                chkPasswordExpiry.Checked = SettingsManager.GetBoolSetting("PasswordExpiry", false);
                numPasswordDays.Value = SettingsManager.GetIntSetting("PasswordExpiryDays", 90);
                chkLoginAttempts.Checked = SettingsManager.GetBoolSetting("LimitLoginAttempts", true);
                numMaxAttempts.Value = SettingsManager.GetIntSetting("MaxLoginAttempts", 3);
                
                // إعدادات الطباعة
                chkAutoPrint.Checked = SettingsManager.GetBoolSetting("AutoPrintInvoice", false);
                txtReceiptHeader.Text = SettingsManager.GetSetting("ReceiptHeader", "");
                txtReceiptFooter.Text = SettingsManager.GetSetting("ReceiptFooter", "شكراً لزيارتكم");
                
                // إعدادات الضرائب
                chkEnableTax.Checked = SettingsManager.GetBoolSetting("EnableTax", false);
                numTaxRate.Value = SettingsManager.GetDecimalSetting("TaxRate", 0);
                txtTaxNumber.Text = SettingsManager.GetSetting("TaxNumber", "");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإعدادات: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Event Handlers - معالجات الأحداث

        /// <summary>
        /// حفظ الإعدادات
        /// </summary>
        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                // إعدادات عامة
                SettingsManager.SetSetting("PharmacyName", txtPharmacyName.Text);
                SettingsManager.SetSetting("PharmacyAddress", txtPharmacyAddress.Text);
                SettingsManager.SetSetting("PharmacyPhone", txtPharmacyPhone.Text);
                SettingsManager.SetSetting("PharmacyEmail", txtPharmacyEmail.Text);
                SettingsManager.SetSetting("LicenseNumber", txtLicenseNumber.Text);
                
                // إعدادات النظام
                SettingsManager.SetSetting("AutoBackup", chkAutoBackup.Checked.ToString());
                SettingsManager.SetSetting("BackupInterval", numBackupInterval.Value.ToString());
                SettingsManager.SetSetting("LowStockAlert", chkLowStockAlert.Checked.ToString());
                SettingsManager.SetSetting("LowStockThreshold", numLowStockThreshold.Value.ToString());
                
                // إعدادات الأمان
                SettingsManager.SetSetting("PasswordExpiry", chkPasswordExpiry.Checked.ToString());
                SettingsManager.SetSetting("PasswordExpiryDays", numPasswordDays.Value.ToString());
                SettingsManager.SetSetting("LimitLoginAttempts", chkLoginAttempts.Checked.ToString());
                SettingsManager.SetSetting("MaxLoginAttempts", numMaxAttempts.Value.ToString());
                
                // إعدادات الطباعة
                SettingsManager.SetSetting("AutoPrintInvoice", chkAutoPrint.Checked.ToString());
                SettingsManager.SetSetting("ReceiptHeader", txtReceiptHeader.Text);
                SettingsManager.SetSetting("ReceiptFooter", txtReceiptFooter.Text);
                
                // إعدادات الضرائب
                SettingsManager.SetSetting("EnableTax", chkEnableTax.Checked.ToString());
                SettingsManager.SetSetting("TaxRate", numTaxRate.Value.ToString());
                SettingsManager.SetSetting("TaxNumber", txtTaxNumber.Text);

                // إعدادات العملات
                if (cmbBaseCurrency.SelectedValue != null)
                {
                    SettingsManager.SetSetting("BaseCurrency", cmbBaseCurrency.SelectedValue.ToString());
                }
                SettingsManager.SetSetting("EnableMultiCurrency", chkEnableMultiCurrency.Checked.ToString());
                
                MessageBox.Show("تم حفظ الإعدادات بنجاح", "نجح", 
                              MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                this.DialogResult = DialogResult.OK;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إلغاء التغييرات
        /// </summary>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// إعادة تعيين الإعدادات للافتراضية
        /// </summary>
        private void btnReset_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show("هل تريد إعادة تعيين جميع الإعدادات للقيم الافتراضية؟", 
                                       "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            
            if (result == DialogResult.Yes)
            {
                try
                {
                    SettingsManager.ResetToDefaults();
                    LoadSettings();
                    MessageBox.Show("تم إعادة تعيين الإعدادات بنجاح", "نجح", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في إعادة تعيين الإعدادات: {ex.Message}", "خطأ", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        /// <summary>
        /// نسخ احتياطي يدوي
        /// </summary>
        private void btnBackupNow_Click(object sender, EventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "Backup Files|*.bak",
                    Title = "حفظ النسخة الاحتياطية",
                    FileName = $"PharmacyBackup_{DateTime.Now:yyyyMMdd_HHmmss}.bak"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    BackupManager.CreateBackup(saveDialog.FileName);
                    MessageBox.Show("تم إنشاء النسخة الاحتياطية بنجاح", "نجح", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// استعادة من نسخة احتياطية
        /// </summary>
        private void btnRestore_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show("تحذير: سيتم استبدال جميع البيانات الحالية. هل تريد المتابعة؟", 
                                       "تحذير", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
            
            if (result == DialogResult.Yes)
            {
                try
                {
                    var openDialog = new OpenFileDialog
                    {
                        Filter = "Backup Files|*.bak",
                        Title = "اختر النسخة الاحتياطية للاستعادة"
                    };

                    if (openDialog.ShowDialog() == DialogResult.OK)
                    {
                        BackupManager.RestoreBackup(openDialog.FileName);
                        MessageBox.Show("تم استعادة النسخة الاحتياطية بنجاح. سيتم إعادة تشغيل النظام.", 
                                      "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        
                        Application.Restart();
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في استعادة النسخة الاحتياطية: {ex.Message}", "خطأ", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        /// <summary>
        /// اختبار الاتصال بقاعدة البيانات
        /// </summary>
        private void btnTestConnection_Click(object sender, EventArgs e)
        {
            try
            {
                if (DatabaseHelper.TestConnection())
                {
                    MessageBox.Show("الاتصال بقاعدة البيانات ناجح", "نجح", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("فشل الاتصال بقاعدة البيانات", "خطأ", 
                                  MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختبار الاتصال: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحسين قاعدة البيانات
        /// </summary>
        //private void btnOptimizeDatabase_Click(object sender, EventArgs e)
        //{
        //    try
        //    {
        //        DatabaseHelper.OptimizeDatabase();
        //        MessageBox.Show("تم تحسين قاعدة البيانات بنجاح", "نجح", 
        //                      MessageBoxButtons.OK, MessageBoxIcon.Information);
        //    }
        //    catch (Exception ex)
        //    {
        //        MessageBox.Show($"خطأ في تحسين قاعدة البيانات: {ex.Message}", "خطأ", 
        //                      MessageBoxButtons.OK, MessageBoxIcon.Error);
        //    }
        //}

        ///// <summary>
        /// تحسين قاعدة البيانات
        /// </summary>
        private void btnOptimizeDatabase_Click(object sender, EventArgs e)
        {
            try
            {
                var result = MessageBox.Show("هل تريد تحسين قاعدة البيانات؟ قد يستغرق هذا بعض الوقت.",
                                           "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // تعطيل الزر أثناء العملية
                    btnOptimizeDatabase.Enabled = false;
                    btnOptimizeDatabase.Text = "جاري التحسين...";

                    // تحسين قاعدة البيانات
                    DatabaseHelper.OptimizeDatabase();

                    MessageBox.Show("تم تحسين قاعدة البيانات بنجاح", "نجح",
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحسين قاعدة البيانات: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
                LogManager.LogError($"SettingsForm.btnOptimizeDatabase_Click: {ex.Message}");
            }
            finally
            {
                // إعادة تفعيل الزر
                btnOptimizeDatabase.Enabled = true;
                btnOptimizeDatabase.Text = "⚡ تحسين قاعدة البيانات";
            }
        }

        #endregion
    }
}
