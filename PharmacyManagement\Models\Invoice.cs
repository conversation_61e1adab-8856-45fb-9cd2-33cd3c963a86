using System;
using System.Collections.Generic;
using System.Linq;

namespace PharmacyManagement.Models
{
    /// <summary>
    /// نموذج الفاتورة - Invoice Model
    /// يمثل بيانات الفواتير في النظام
    /// </summary>
    public class Invoice
    {
        #region Properties - الخصائص

        /// <summary>
        /// معرف الفاتورة الفريد
        /// </summary>
        public int InvoiceID { get; set; }

        /// <summary>
        /// رقم الفاتورة
        /// </summary>
        public string InvoiceNumber { get; set; }

        /// <summary>
        /// تاريخ الفاتورة
        /// </summary>
        public DateTime InvoiceDate { get; set; }

        /// <summary>
        /// معرف العميل
        /// </summary>
        public int? CustomerID { get; set; }

        /// <summary>
        /// اسم العميل
        /// </summary>
        public string CustomerName { get; set; }

        /// <summary>
        /// معرف المستخدم
        /// </summary>
        public int UserID { get; set; }

        /// <summary>
        /// طريقة الدفع
        /// </summary>
        public string PaymentMethod { get; set; }

        /// <summary>
        /// حالة الدفع
        /// </summary>
        public string PaymentStatus { get; set; }

        /// <summary>
        /// اسم المستخدم
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// المبلغ الصافي
        /// </summary>
        public decimal NetAmount { get; set; }

        /// <summary>
        /// معرف الفرع
        /// </summary>
        public int BranchID { get; set; }

        /// <summary>
        /// تفاصيل الفاتورة
        /// </summary>
        public List<InvoiceDetail> InvoiceDetails { get; set; }

        /// <summary>
        /// نوع الفاتورة (Sale, Purchase, Return)
        /// </summary>
        public string InvoiceType { get; set; }

        /// <summary>
        /// نوع الدفع (Cash, Credit, Card)
        /// </summary>
        public string PaymentType { get; set; }

        /// <summary>
        /// المجموع الفرعي
        /// </summary>
        public decimal SubTotal { get; set; }

        /// <summary>
        /// مبلغ الخصم
        /// </summary>
        public decimal DiscountAmount { get; set; }

        /// <summary>
        /// نسبة الخصم
        /// </summary>
        public decimal DiscountPercent { get; set; }

        /// <summary>
        /// مبلغ الضريبة
        /// </summary>
        public decimal TaxAmount { get; set; }

        /// <summary>
        /// المجموع الكلي
        /// </summary>
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// المبلغ المدفوع
        /// </summary>
        public decimal PaidAmount { get; set; }

        /// <summary>
        /// المبلغ المتبقي
        /// </summary>
        public decimal RemainingAmount { get; set; }

        /// <summary>
        /// حالة الفاتورة (Pending, Paid, Cancelled)
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// ملاحظات
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// معرف المستخدم الذي أنشأ الفاتورة
        /// </summary>
        public int? CreatedBy { get; set; }

        /// <summary>
        /// اسم المستخدم الذي أنشأ الفاتورة
        /// </summary>
        public string CreatedByName { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// تفاصيل الفاتورة
        /// </summary>
        public List<InvoiceDetail> Details { get; set; }

        #endregion

        #region Calculated Properties - الخصائص المحسوبة

        /// <summary>
        /// عدد الأصناف في الفاتورة
        /// </summary>
        public int ItemsCount
        {
            get { return InvoiceDetails != null ? InvoiceDetails.Count : 0; }
        }

        /// <summary>
        /// إجمالي الكمية
        /// </summary>
        public int TotalQuantity
        {
            get
            {
                if (InvoiceDetails == null) return 0;
                int total = 0;
                foreach (var detail in InvoiceDetails)
                {
                    total += detail.Quantity;
                }
                return total;
            }
        }

        /// <summary>
        /// المبلغ المتبقي للدفع
        /// </summary>
        public decimal Balance
        {
            get { return TotalAmount - PaidAmount; }
        }

        /// <summary>
        /// هل الفاتورة مدفوعة بالكامل
        /// </summary>
        public bool IsFullyPaid
        {
            get { return Balance <= 0; }
        }

        /// <summary>
        /// نوع الفاتورة بالعربية
        /// </summary>
        public string InvoiceTypeArabic
        {
            get
            {
                if (InvoiceType == null) return "";
                switch (InvoiceType.ToLower())
                {
                    case "sale": return "مبيعات";
                    case "purchase": return "مشتريات";
                    case "return": return "مرتجعات";
                    default: return InvoiceType;
                }
            }
        }

        /// <summary>
        /// نوع الدفع بالعربية
        /// </summary>
        public string PaymentTypeArabic
        {
            get
            {
                if (PaymentType == null) return "";
                switch (PaymentType.ToLower())
                {
                    case "cash": return "نقدي";
                    case "credit": return "آجل";
                    case "card": return "بطاقة";
                    default: return PaymentType;
                }
            }
        }

        /// <summary>
        /// حالة الفاتورة بالعربية
        /// </summary>
        public string StatusArabic
        {
            get
            {
                if (Status == null) return "";
                switch (Status.ToLower())
                {
                    case "pending": return "معلقة";
                    case "paid": return "مدفوعة";
                    case "cancelled": return "ملغية";
                    default: return Status;
                }
            }
        }

        #endregion

        #region Constructors - المنشئات

        /// <summary>
        /// منشئ افتراضي
        /// </summary>
        public Invoice()
        {
            InvoiceDate = DateTime.Now;
            CreatedDate = DateTime.Now;
            InvoiceType = "Sale";
            PaymentType = "Cash";
            Status = "Pending";
            Details = new List<InvoiceDetail>();
            SubTotal = 0;
            DiscountAmount = 0;
            DiscountPercent = 0;
            TaxAmount = 0;
            TotalAmount = 0;
            PaidAmount = 0;
            RemainingAmount = 0;
        }

        /// <summary>
        /// منشئ مع المعاملات الأساسية
        /// </summary>
        /// <param name="invoiceNumber">رقم الفاتورة</param>
        /// <param name="customerID">معرف العميل</param>
        /// <param name="invoiceType">نوع الفاتورة</param>
        public Invoice(string invoiceNumber, int? customerID, string invoiceType)
        {
            InvoiceNumber = invoiceNumber;
            CustomerID = customerID;
            InvoiceType = invoiceType;
            InvoiceDate = DateTime.Now;
            CreatedDate = DateTime.Now;
            PaymentType = "Cash";
            Status = "Pending";
            Details = new List<InvoiceDetail>();
            SubTotal = 0;
            DiscountAmount = 0;
            DiscountPercent = 0;
            TaxAmount = 0;
            TotalAmount = 0;
            PaidAmount = 0;
            RemainingAmount = 0;
        }

        #endregion

        #region Methods - الطرق

        /// <summary>
        /// التحقق من صحة بيانات الفاتورة
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(InvoiceNumber) &&
                   !string.IsNullOrWhiteSpace(InvoiceType) &&
                   !string.IsNullOrWhiteSpace(PaymentType) &&
                   TotalAmount >= 0 &&
                   PaidAmount >= 0 &&
                   Details != null && Details.Count > 0;
        }

        /// <summary>
        /// حساب إجماليات الفاتورة
        /// </summary>
        public void CalculateTotals()
        {
            if (Details == null || Details.Count == 0)
            {
                SubTotal = 0;
                TotalAmount = 0;
                RemainingAmount = 0;
                return;
            }

            // حساب المجموع الفرعي
            SubTotal = Details.Sum(d => d.TotalPrice);

            // حساب الخصم
            if (DiscountPercent > 0)
                DiscountAmount = SubTotal * (DiscountPercent / 100);

            // حساب المجموع بعد الخصم
            var afterDiscount = SubTotal - DiscountAmount;

            // حساب الضريبة (إذا كانت موجودة)
            // TaxAmount = afterDiscount * (TaxRate / 100);

            // حساب المجموع الكلي
            TotalAmount = afterDiscount + TaxAmount;

            // حساب المبلغ المتبقي
            RemainingAmount = TotalAmount - PaidAmount;
        }

        /// <summary>
        /// إضافة صنف للفاتورة
        /// </summary>
        /// <param name="detail">تفاصيل الصنف</param>
        public void AddDetail(InvoiceDetail detail)
        {
            if (Details == null)
                Details = new List<InvoiceDetail>();

            Details.Add(detail);
            CalculateTotals();
        }

        /// <summary>
        /// حذف صنف من الفاتورة
        /// </summary>
        /// <param name="detail">تفاصيل الصنف</param>
        public void RemoveDetail(InvoiceDetail detail)
        {
            if (Details != null)
            {
                Details.Remove(detail);
                CalculateTotals();
            }
        }

        /// <summary>
        /// إرجاع تمثيل نصي للفاتورة
        /// </summary>
        /// <returns>رقم الفاتورة وتاريخها</returns>
        public override string ToString()
        {
            return "فاتورة رقم: " + InvoiceNumber + " - " + InvoiceDate.ToString("yyyy/MM/dd");
        }

        #endregion
    }
}
