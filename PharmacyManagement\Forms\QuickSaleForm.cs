using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نموذج البيع السريع المحسن - Enhanced Quick Sale Form
    /// تصميم مسطح حديث مع ميزات متقدمة للبيع السريع
    /// </summary>
    public partial class QuickSaleForm : Form
    {
        #region Fields

        private BindingSource _itemsBindingSource;
        private decimal _totalAmount = 0;
        private List<QuickSaleItem> _saleItems;
        private bool _hasChanges = false;

        #endregion

        #region Constructor

        public QuickSaleForm()
        {
            InitializeComponent();
            SetupForm();
            SetupDataGrid();
            LoadSampleData();
            LoadCustomers();
        }

        #endregion

        #region Setup Methods

        private void SetupForm()
        {
            // إعداد النموذج الأساسي
            this.Text = "🛒 البيع السريع";
            this.Size = new Size(1000, 660);
            this.StartPosition = FormStartPosition.CenterParent;
            this.BackColor = Color.White;
            this.Font = new Font("Segoe UI", 9F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // إعداد التصميم المسطح
            SetupFlatDesign();

            // إعداد الأحداث
            SetupEvents();

            // إعداد البيانات الأولية
            _saleItems = new List<QuickSaleItem>();
            dtpInvoiceDate.Value = DateTime.Now;

            // تحديث الإجمالي
            UpdateTotal();
        }

        private void SetupFlatDesign()
        {
            // ألوان الأزرار المحسنة
            btnAddItem.BackColor = Color.FromArgb(46, 204, 113);
            btnAddItem.ForeColor = Color.White;
            btnAddItem.FlatStyle = FlatStyle.Flat;
            btnAddItem.FlatAppearance.BorderSize = 0;
            btnAddItem.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            btnAddItem.Cursor = Cursors.Hand;

            btnRemoveItem.BackColor = Color.FromArgb(231, 76, 60);
            btnRemoveItem.ForeColor = Color.White;
            btnRemoveItem.FlatStyle = FlatStyle.Flat;
            btnRemoveItem.FlatAppearance.BorderSize = 0;
            btnRemoveItem.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            btnRemoveItem.Cursor = Cursors.Hand;

            btnSave.BackColor = Color.FromArgb(52, 152, 219);
            btnSave.ForeColor = Color.White;
            btnSave.FlatStyle = FlatStyle.Flat;
            btnSave.FlatAppearance.BorderSize = 0;
            btnSave.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            btnSave.Cursor = Cursors.Hand;

            btnCancel.BackColor = Color.FromArgb(149, 165, 166);
            btnCancel.ForeColor = Color.White;
            btnCancel.FlatStyle = FlatStyle.Flat;
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            btnCancel.Cursor = Cursors.Hand;

            btnPrint.BackColor = Color.FromArgb(155, 89, 182);
            btnPrint.ForeColor = Color.White;
            btnPrint.FlatStyle = FlatStyle.Flat;
            btnPrint.FlatAppearance.BorderSize = 0;
            btnPrint.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            btnPrint.Cursor = Cursors.Hand;

            // تنسيق حقول الإدخال
            txtDrugSearch.BorderStyle = BorderStyle.FixedSingle;
            txtDrugSearch.Font = new Font("Segoe UI", 10F);

            txtQuantity.BorderStyle = BorderStyle.FixedSingle;
            txtQuantity.Font = new Font("Segoe UI", 10F);

            // تنسيق القوائم المنسدلة
            cmbCustomer.FlatStyle = FlatStyle.Flat;
            cmbCustomer.Font = new Font("Segoe UI", 10F);
        }

        private void SetupDataGrid()
        {
            // إعداد شبكة العناصر المحسنة
            dgvItems.AutoGenerateColumns = false;
            dgvItems.AllowUserToAddRows = false;
            dgvItems.AllowUserToDeleteRows = false;
            dgvItems.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvItems.MultiSelect = true;
            dgvItems.BackgroundColor = Color.White;
            dgvItems.BorderStyle = BorderStyle.FixedSingle;
            dgvItems.RowHeadersVisible = false;
            dgvItems.EnableHeadersVisualStyles = false;
            dgvItems.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dgvItems.ColumnHeadersHeight = 40;
            dgvItems.RowTemplate.Height = 35;

            // تنسيق الرأس
            dgvItems.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 73, 94);
            dgvItems.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvItems.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            dgvItems.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;

            // تنسيق الخلايا
            dgvItems.DefaultCellStyle.Font = new Font("Segoe UI", 10F);
            dgvItems.DefaultCellStyle.SelectionBackColor = Color.FromArgb(52, 152, 219);
            dgvItems.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvItems.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);
            dgvItems.GridColor = Color.FromArgb(189, 195, 199);

            // إضافة الأعمدة المحسنة
            dgvItems.Columns.Clear();

            // عمود اسم الدواء
            dgvItems.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "DrugName",
                HeaderText = "اسم الدواء",
                DataPropertyName = "DrugName",
                FillWeight = 40,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Alignment = DataGridViewContentAlignment.MiddleRight,
                    Font = new Font("Segoe UI", 10F, FontStyle.Bold)
                }
            });

            // عمود الكمية
            dgvItems.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Quantity",
                HeaderText = "الكمية",
                DataPropertyName = "Quantity",
                FillWeight = 15,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    BackColor = Color.FromArgb(255, 248, 220)
                }
            });

            // عمود سعر الوحدة
            dgvItems.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "UnitPrice",
                HeaderText = "سعر الوحدة",
                DataPropertyName = "UnitPrice",
                FillWeight = 20,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Alignment = DataGridViewContentAlignment.MiddleRight,
                    Format = "F2"
                }
            });

            // عمود الإجمالي
            dgvItems.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TotalPrice",
                HeaderText = "الإجمالي",
                DataPropertyName = "TotalPrice",
                FillWeight = 20,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Alignment = DataGridViewContentAlignment.MiddleRight,
                    Format = "F2",
                    Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                    ForeColor = Color.FromArgb(52, 73, 94)
                }
            });

            // عمود الملاحظات
            dgvItems.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Notes",
                HeaderText = "ملاحظات",
                DataPropertyName = "Notes",
                FillWeight = 25,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Alignment = DataGridViewContentAlignment.MiddleRight
                }
            });

            // إعداد مصدر البيانات
            _itemsBindingSource = new BindingSource();
            dgvItems.DataSource = _itemsBindingSource;

            // إعداد الأحداث
            dgvItems.CellValueChanged += DgvItems_CellValueChanged;
            dgvItems.CellFormatting += DgvItems_CellFormatting;
        }

        private void SetupEvents()
        {
            btnAddItem.Click += BtnAddItem_Click;
            btnRemoveItem.Click += BtnRemoveItem_Click;
            btnSave.Click += BtnSave_Click;
            btnCancel.Click += BtnCancel_Click;
            btnPrint.Click += BtnPrint_Click;

            txtDrugSearch.KeyPress += TxtDrugSearch_KeyPress;
            txtQuantity.KeyPress += TxtQuantity_KeyPress;
            txtQuantity.TextChanged += TxtQuantity_TextChanged;

            dgvItems.SelectionChanged += DgvItems_SelectionChanged;
        }

        private void LoadSampleData()
        {
            try
            {
                // إنشاء بيانات تجريبية للبيع السريع
                _saleItems = new List<QuickSaleItem>();

                // تحميل البيانات في الشبكة
                _itemsBindingSource.DataSource = _saleItems;

                Console.WriteLine("تم إعداد نموذج البيع السريع بنجاح");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات التجريبية: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Load Data Methods

        private void LoadCustomers()
        {
            try
            {
                // إنشاء قائمة عملاء تجريبية
                var sampleCustomers = new List<QuickSaleCustomer>
                {
                    new QuickSaleCustomer { CustomerID = 0, CustomerName = "💰 عميل نقدي" },
                    new QuickSaleCustomer { CustomerID = 1, CustomerName = "أحمد محمد علي" },
                    new QuickSaleCustomer { CustomerID = 2, CustomerName = "فاطمة حسن أحمد" },
                    new QuickSaleCustomer { CustomerID = 3, CustomerName = "محمد عبدالله صالح" },
                    new QuickSaleCustomer { CustomerID = 4, CustomerName = "عائشة علي محمد" },
                    new QuickSaleCustomer { CustomerID = 5, CustomerName = "يوسف إبراهيم حسن" }
                };

                cmbCustomer.DataSource = sampleCustomers;
                cmbCustomer.DisplayMember = "CustomerName";
                cmbCustomer.ValueMember = "CustomerID";
                cmbCustomer.SelectedIndex = 0;

                Console.WriteLine($"تم تحميل {sampleCustomers.Count} عميل تجريبي");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل العملاء: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Event Handlers

        private void BtnAddItem_Click(object sender, EventArgs e)
        {
            try
            {
                if (ValidateItem())
                {
                    var drugName = txtDrugSearch.Text.Trim();
                    var quantity = Convert.ToInt32(txtQuantity.Text);

                    // محاكاة البحث عن الدواء
                    var sampleDrug = GetSampleDrug(drugName);
                    if (sampleDrug != null)
                    {
                        var totalPrice = quantity * sampleDrug.UnitPrice;

                        var item = new QuickSaleItem
                        {
                            DrugName = sampleDrug.DrugName,
                            Quantity = quantity,
                            UnitPrice = sampleDrug.UnitPrice,
                            TotalPrice = totalPrice,
                            Notes = "تم إضافته يدوياً"
                        };

                        _saleItems.Add(item);
                        _itemsBindingSource.ResetBindings(false);
                        _hasChanges = true;

                        // مسح الحقول
                        txtDrugSearch.Clear();
                        txtQuantity.Clear();
                        txtDrugSearch.Focus();

                        UpdateTotal();

                        MessageBox.Show($"تم إضافة {drugName} بكمية {quantity} ✅", "نجح",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        // إضافة دواء جديد
                        var newItem = new QuickSaleItem
                        {
                            DrugName = drugName,
                            Quantity = quantity,
                            UnitPrice = 10.00m, // سعر افتراضي
                            TotalPrice = quantity * 10.00m,
                            Notes = "دواء جديد - يحتاج تحديد السعر"
                        };

                        _saleItems.Add(newItem);
                        _itemsBindingSource.ResetBindings(false);
                        _hasChanges = true;

                        // مسح الحقول
                        txtDrugSearch.Clear();
                        txtQuantity.Clear();
                        txtDrugSearch.Focus();

                        UpdateTotal();

                        MessageBox.Show($"تم إضافة دواء جديد: {drugName} ✅", "نجح",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة العنصر: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnRemoveItem_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvItems.SelectedRows.Count > 0)
                {
                    var selectedIndices = dgvItems.SelectedRows.Cast<DataGridViewRow>()
                        .Select(row => row.Index)
                        .OrderByDescending(i => i)
                        .ToList();

                    var result = MessageBox.Show($"هل تريد حذف {selectedIndices.Count} عنصر محدد؟",
                        "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        foreach (var index in selectedIndices)
                        {
                            if (index < _saleItems.Count)
                            {
                                _saleItems.RemoveAt(index);
                            }
                        }

                        _itemsBindingSource.ResetBindings(false);
                        _hasChanges = true;
                        UpdateTotal();

                        MessageBox.Show($"تم حذف {selectedIndices.Count} عنصر بنجاح ✅", "نجح",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
                else
                {
                    MessageBox.Show("يرجى تحديد عنصر واحد على الأقل للحذف", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف العنصر: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (ValidateInvoice())
                {
                    // محاكاة حفظ الفاتورة
                    var invoiceNumber = GenerateInvoiceNumber();
                    var customerName = cmbCustomer.Text;
                    var itemCount = _saleItems.Count;

                    _hasChanges = false;

                    var summary = $"📋 تم حفظ الفاتورة بنجاح\n" +
                                 $"{'=',-40}\n\n" +
                                 $"رقم الفاتورة: {invoiceNumber}\n" +
                                 $"العميل: {customerName}\n" +
                                 $"التاريخ: {dtpInvoiceDate.Value:yyyy/MM/dd}\n" +
                                 $"عدد الأصناف: {itemCount}\n" +
                                 $"الإجمالي: {_totalAmount:F2} ريال";

                    MessageBox.Show(summary, "تم حفظ الفاتورة ✅",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // طباعة الفاتورة
                    var result = MessageBox.Show("هل تريد طباعة الفاتورة؟", "طباعة",
                        MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        BtnPrint_Click(sender, e);
                    }

                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الفاتورة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                var invoiceDetails = "🖨️ فاتورة البيع السريع\n" +
                                   $"{'=',-50}\n\n" +
                                   $"رقم الفاتورة: {GenerateInvoiceNumber()}\n" +
                                   $"التاريخ: {dtpInvoiceDate.Value:yyyy/MM/dd HH:mm}\n" +
                                   $"العميل: {cmbCustomer.Text}\n\n" +
                                   $"تفاصيل الأصناف:\n" +
                                   $"{'-',-50}\n";

                for (int i = 0; i < _saleItems.Count; i++)
                {
                    var item = _saleItems[i];
                    invoiceDetails += $"{i + 1}. {item.DrugName}\n" +
                                    $"   الكمية: {item.Quantity} | السعر: {item.UnitPrice:F2} | الإجمالي: {item.TotalPrice:F2}\n\n";
                }

                invoiceDetails += $"{'-',-50}\n" +
                                $"إجمالي الفاتورة: {_totalAmount:F2} ريال\n" +
                                $"عدد الأصناف: {_saleItems.Count}\n\n" +
                                $"شكراً لتعاملكم معنا! 🙏";

                MessageBox.Show(invoiceDetails, "طباعة الفاتورة",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة الفاتورة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            if (_hasChanges)
            {
                var result = MessageBox.Show("هناك تغييرات غير محفوظة. هل تريد الخروج؟",
                    "تأكيد الإغلاق", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.No)
                    return;
            }

            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void TxtDrugSearch_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                txtQuantity.Focus();
                e.Handled = true;
            }
        }

        private void TxtQuantity_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                BtnAddItem_Click(sender, e);
                e.Handled = true;
            }
            else if (!char.IsDigit(e.KeyChar) && e.KeyChar != (char)Keys.Back)
            {
                e.Handled = true;
            }
        }

        private void TxtQuantity_TextChanged(object sender, EventArgs e)
        {
            // تحديث السعر المتوقع
            if (!string.IsNullOrWhiteSpace(txtDrugSearch.Text) && !string.IsNullOrWhiteSpace(txtQuantity.Text))
            {
                var sampleDrug = GetSampleDrug(txtDrugSearch.Text);
                if (sampleDrug != null && int.TryParse(txtQuantity.Text, out int quantity))
                {
                    var expectedTotal = quantity * sampleDrug.UnitPrice;
                    lblExpectedTotal.Text = $"المتوقع: {expectedTotal:F2} ريال";
                }
                else if (int.TryParse(txtQuantity.Text, out int qty))
                {
                    var expectedTotal = qty * 10.00m; // سعر افتراضي
                    lblExpectedTotal.Text = $"المتوقع: {expectedTotal:F2} ريال (تقديري)";
                }
            }
            else
            {
                lblExpectedTotal.Text = "المتوقع: 0.00 ريال";
            }
        }

        private void DgvItems_SelectionChanged(object sender, EventArgs e)
        {
            btnRemoveItem.Enabled = dgvItems.SelectedRows.Count > 0;
        }

        private void DgvItems_CellValueChanged(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                if (e.RowIndex >= 0 && e.RowIndex < _saleItems.Count)
                {
                    var item = _saleItems[e.RowIndex];
                    var columnName = dgvItems.Columns[e.ColumnIndex].Name;

                    if (columnName == "Quantity")
                    {
                        if (int.TryParse(dgvItems.Rows[e.RowIndex].Cells[e.ColumnIndex].Value?.ToString(), out int newQuantity))
                        {
                            item.Quantity = newQuantity;
                            item.TotalPrice = item.Quantity * item.UnitPrice;
                            dgvItems.Rows[e.RowIndex].Cells["TotalPrice"].Value = item.TotalPrice;
                            _hasChanges = true;
                            UpdateTotal();
                        }
                    }
                    else if (columnName == "Notes")
                    {
                        item.Notes = dgvItems.Rows[e.RowIndex].Cells[e.ColumnIndex].Value?.ToString() ?? "";
                        _hasChanges = true;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تحديث الخلية: {ex.Message}");
            }
        }

        private void DgvItems_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            try
            {
                if (e.RowIndex >= 0 && e.RowIndex < _saleItems.Count)
                {
                    var item = _saleItems[e.RowIndex];
                    var row = dgvItems.Rows[e.RowIndex];

                    // تلوين الصفوف بالتناوب
                    if (e.RowIndex % 2 == 0)
                    {
                        row.DefaultCellStyle.BackColor = Color.White;
                    }
                    else
                    {
                        row.DefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);
                    }

                    // تنسيق خاص للإجمالي
                    if (e.ColumnIndex == dgvItems.Columns["TotalPrice"]?.Index)
                    {
                        e.CellStyle.ForeColor = Color.FromArgb(52, 73, 94);
                        e.CellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تنسيق الخلية: {ex.Message}");
            }
        }

        #endregion

        #region Helper Methods

        private bool ValidateItem()
        {
            if (string.IsNullOrWhiteSpace(txtDrugSearch.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الدواء", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtDrugSearch.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtQuantity.Text) || !int.TryParse(txtQuantity.Text, out int quantity) || quantity <= 0)
            {
                MessageBox.Show("يرجى إدخال كمية صحيحة", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtQuantity.Focus();
                return false;
            }

            return true;
        }

        private bool ValidateInvoice()
        {
            if (_saleItems == null || _saleItems.Count == 0)
            {
                MessageBox.Show("يرجى إضافة عناصر للفاتورة", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        private QuickSaleDrug GetSampleDrug(string searchTerm)
        {
            // قائمة أدوية تجريبية
            var sampleDrugs = new List<QuickSaleDrug>
            {
                new QuickSaleDrug { DrugName = "باراسيتامول 500 مجم", UnitPrice = 2.50m },
                new QuickSaleDrug { DrugName = "أموكسيسيلين 250 مجم", UnitPrice = 8.75m },
                new QuickSaleDrug { DrugName = "إيبوبروفين 400 مجم", UnitPrice = 3.25m },
                new QuickSaleDrug { DrugName = "أسبرين 100 مجم", UnitPrice = 1.50m },
                new QuickSaleDrug { DrugName = "فيتامين د 1000 وحدة", UnitPrice = 12.00m },
                new QuickSaleDrug { DrugName = "أوميجا 3 كبسولات", UnitPrice = 15.50m },
                new QuickSaleDrug { DrugName = "كالسيوم + مغنيسيوم", UnitPrice = 9.25m },
                new QuickSaleDrug { DrugName = "لوراتادين 10 مجم", UnitPrice = 4.75m },
                new QuickSaleDrug { DrugName = "سيتريزين 10 مجم", UnitPrice = 3.50m },
                new QuickSaleDrug { DrugName = "ديكلوفيناك 50 مجم", UnitPrice = 2.75m }
            };

            return sampleDrugs.FirstOrDefault(d =>
                d.DrugName.Contains(searchTerm) ||
                searchTerm.Contains(d.DrugName.Split(' ')[0]));
        }

        private string GenerateInvoiceNumber()
        {
            return "INV" + DateTime.Now.ToString("yyyyMM") + DateTime.Now.Millisecond.ToString("D3");
        }

        private void UpdateTotal()
        {
            _totalAmount = _saleItems != null ? _saleItems.Sum(i => i.TotalPrice) : 0;
            var itemCount = _saleItems != null ? _saleItems.Count : 0;

            lblTotal.Text = "الإجمالي: " + _totalAmount.ToString("F2") + " ريال";
            lblItemCount.Text = "عدد الأصناف: " + itemCount.ToString();

            btnSave.Enabled = _totalAmount > 0;
        }

        #endregion

        private void QuickSaleForm_Load(object sender, EventArgs e)
        {
            // تحديث إضافي عند تحميل النموذج
            UpdateTotal();
        }
    }

    #region Helper Models

    /// <summary>
    /// عنصر البيع السريع
    /// </summary>
    public class QuickSaleItem
    {
        public string DrugName { get; set; }
        public int Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalPrice { get; set; }
        public string Notes { get; set; }
    }

    /// <summary>
    /// دواء البيع السريع
    /// </summary>
    public class QuickSaleDrug
    {
        public string DrugName { get; set; }
        public decimal UnitPrice { get; set; }
    }

    /// <summary>
    /// عميل البيع السريع
    /// </summary>
    public class QuickSaleCustomer
    {
        public int CustomerID { get; set; }
        public string CustomerName { get; set; }
    }

    #endregion
}
