using System;
using System.IO;
using System.Windows.Forms;
using PharmacyManagement.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement
{
    /// <summary>
    /// نقطة دخول التطبيق الرئيسية
    /// Main entry point for the Pharmacy Management System
    /// </summary>
    internal static class Program
    {
        /// <summary>
        /// نقطة الدخول الرئيسية للتطبيق
        /// The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            try
            {
                // إنشاء المجلدات الضرورية
                CreateRequiredDirectories();

                // تهيئة نظام السجلات
                LogManager.Initialize();
                LogManager.LogInfo("=== بدء تشغيل نظام إدارة الصيدلية ===");

                // تهيئة الأيقونات
                IconManager.Initialize();

                // إنشاء صورة الواجهة الأساسية
                ScreenshotManager.CreateMainInterfaceImage();

                // إنشاء قاعدة البيانات إذا لم تكن موجودة
                LogManager.LogInfo("فحص قاعدة البيانات...");
                if (!DatabaseHelper.CreateDatabaseIfNotExists())
                {
                    var result = MessageBox.Show(
                        "فشل في إنشاء أو الاتصال بقاعدة البيانات.\nهل تريد المتابعة بدون قاعدة بيانات؟",
                        "تحذير قاعدة البيانات",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Warning);

                    if (result == DialogResult.No)
                    {
                        LogManager.LogInfo("تم إلغاء تشغيل التطبيق بواسطة المستخدم");
                        return;
                    }
                }

                // إنشاء مستخدم افتراضي للاختبار
                CreateDefaultUser();

                // إنشاء العميل العام الافتراضي
                CreateDefaultCustomer();

                // تشغيل الواجهة الرئيسية
                LogManager.LogInfo("تشغيل الواجهة الرئيسية...");

                // فحص نوع الواجهة المفضل
                bool useModernInterface = GetInterfacePreference();

                if (useModernInterface)
                {
                    LogManager.LogInfo("تشغيل الواجهة الحديثة...");

                    LoginForm loginForm = new LoginForm();

                    if (loginForm.ShowDialog() == DialogResult.OK)
                    {
                        Application.Run(new ModernMainForm());
                    }

                    //Application.Run(new ModernMainForm());
                }
                else
                {
                    LogManager.LogInfo("تشغيل الواجهة التقليدية...");
                    LoginForm loginForm = new LoginForm();

                    if (loginForm.ShowDialog() == DialogResult.OK)
                    {
                        Application.Run(new ModernMainForm());
                    }
                    //Application.Run(new ModernMainForm()); // Use ModernMainForm instead of MainForm
                }


              

            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تشغيل التطبيق: {ex.Message}");
                MessageBox.Show($"خطأ في تشغيل التطبيق: {ex.Message}",
                              "خطأ",
                              MessageBoxButtons.OK,
                              MessageBoxIcon.Error);
            }
            finally
            {
                LogManager.LogInfo("إنهاء تشغيل نظام إدارة الصيدلية");
            }


            // للاستخدام مع نموذج تسجيل الدخول:
          
        }

        /// <summary>
        /// إنشاء المجلدات الضرورية
        /// </summary>
        private static void CreateRequiredDirectories()
        {
            try
            {
                var directories = new[]
                {
                    "Data",
                    "Logs",
                    "Backup",
                    "Reports",
                    "Database"
                };

                foreach (var dir in directories)
                {
                    var path = Path.Combine(Application.StartupPath, dir);
                    if (!Directory.Exists(path))
                    {
                        Directory.CreateDirectory(path);
                        LogManager.LogInfo($"تم إنشاء المجلد: {path}");
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في إنشاء المجلدات: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء مستخدم افتراضي للاختبار
        /// </summary>
        private static void CreateDefaultUser()
        {
            try
            {
                // إنشاء مستخدم افتراضي للاختبار
                var defaultUser = new User
                {
                    UserID = 1,
                    Username = "admin",
                    FullName = "مدير النظام",
                    Email = "<EMAIL>",
                    Phone = "777777777",
                    Role = "Admin",
                    BranchID = 1,
                    IsActive = true
                };

                UserManager.SetCurrentUser(defaultUser);
                LogManager.LogInfo("تم إنشاء المستخدم الافتراضي للاختبار");
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في إنشاء المستخدم الافتراضي: " + ex.Message);
            }
        }

        /// <summary>
        /// إنشاء العميل العام الافتراضي
        /// </summary>
        private static void CreateDefaultCustomer()
        {
            try
            {
                // التأكد من وجود العميل العام
                int defaultCustomerId = CustomerManager.EnsureDefaultCustomer();
                LogManager.LogInfo($"تم التأكد من وجود العميل العام برقم: {defaultCustomerId}");
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في إنشاء العميل العام: " + ex.Message);
            }
        }

        /// <summary>
        /// الحصول على تفضيل نوع الواجهة
        /// </summary>
        private static bool GetInterfacePreference()
        {
            try
            {
                // فحص الإعدادات المحفوظة
                string savedPreference = SettingsManager.GetUserSetting("UseModernInterface");

                if (!string.IsNullOrEmpty(savedPreference))
                {
                    return Convert.ToBoolean(savedPreference);
                }

                // إذا لم توجد إعدادات محفوظة، اعرض نافذة الاختيار
                using (var selectionForm = new InterfaceSelectionForm())
                {
                    if (selectionForm.ShowDialog() == DialogResult.OK)
                    {
                        return selectionForm.UseModernInterface;
                    }
                }

                // افتراضي: الواجهة التقليدية
                return false;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تحديد نوع الواجهة: {ex.Message}");
                return false; // افتراضي: الواجهة التقليدية
            }
        }
    }
}

