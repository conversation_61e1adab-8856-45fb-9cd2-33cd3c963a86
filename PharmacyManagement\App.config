<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <startup> 
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8"/>
    </startup>
    
    <connectionStrings>
        <!-- اتصال قاعدة البيانات المحلية -->
        <add name="PharmacyDB2" connectionString="Data Source=(LocalDB)\MSSQLLocalDB;AttachDbFilename=|DataDirectory|\PharmacyDB.mdf;Integrated Security=True;Connect Timeout=30" providerName="System.Data.SqlClient"/>
        
        <!-- اتصال SQL Server Express -->
        <add name="PharmacyDB" connectionString="Server=.\SQLEXPRESS;Database=PharmacyDB;Integrated Security=true;" providerName="System.Data.SqlClient"/>
    </connectionStrings>
    
    <appSettings>
        <!-- إعدادات التطبيق -->
        <add key="CompanyName" value="صيدلية الشفاء"/>
        <add key="CompanyAddress" value="عدن - اليمن"/>
        <add key="CompanyPhone" value="+967-*********"/>
        <add key="CurrencySymbol" value="ر.ي"/>
        <add key="BackupPath" value=".\Backup\"/>
        <add key="ReportsPath" value=".\Reports\"/>
        <add key="LowStockThreshold" value="10"/>
        <add key="ExpiryWarningDays" value="30"/>
    </appSettings>
</configuration>
