using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using PharmacyManagement.Models;

namespace PharmacyManagement.Classes
{
    /// <summary>
    /// مدير العملاء المحسن - Enhanced Customer Manager
    /// تم إزالة جميع التكرارات وتحسين الأداء
    /// </summary>
    public static class CustomerManager
    {
        #region Customer Management - إدارة العملاء

        /// <summary>
        /// إضافة عميل جديد
        /// </summary>
        /// <param name="customer">بيانات العميل</param>
        /// <returns>معرف العميل الجديد</returns>
        public static int AddCustomer(Customer customer)
        {
            try
            {
                // التحقق من صحة البيانات
                ValidateCustomerData(customer);

                // التحقق من عدم تكرار كود العميل
                if (IsCustomerCodeExists(customer.CustomerCode))
                {
                    throw new Exception("كود العميل موجود مسبقاً");
                }

                string query = @"
                    INSERT INTO Customers (CustomerCode, CustomerName, Phone, Mobile, Email, Address,
                                         DateOfBirth, Gender, CustomerType, CreditLimit,
                                         IsActive, CreatedDate, CreatedBy)
                    VALUES (@CustomerCode, @CustomerName, @Phone, @Mobile, @Email, @Address,
                           @DateOfBirth, @Gender, @CustomerType, @CreditLimit,
                           @IsActive, @CreatedDate, @CreatedBy);
                    SELECT SCOPE_IDENTITY();";

                var parameters = new[]
                {
                    DatabaseHelper.CreateParameter("@CustomerCode", customer.CustomerCode),
                    DatabaseHelper.CreateParameter("@CustomerName", customer.CustomerName),
                    DatabaseHelper.CreateParameter("@Phone", customer.Phone ?? ""),
                    DatabaseHelper.CreateParameter("@Mobile", customer.Mobile ?? ""),
                    DatabaseHelper.CreateParameter("@Email", customer.Email ?? ""),
                    DatabaseHelper.CreateParameter("@Address", customer.Address ?? ""),
                    DatabaseHelper.CreateParameter("@DateOfBirth", customer.BirthDate),
                    DatabaseHelper.CreateParameter("@Gender", customer.Gender ?? ""),
                    DatabaseHelper.CreateParameter("@CustomerType", customer.CustomerType ?? "عادي"),
                    DatabaseHelper.CreateParameter("@CreditLimit", customer.CreditLimit),
                    DatabaseHelper.CreateParameter("@IsActive", customer.IsActive),
                    DatabaseHelper.CreateParameter("@CreatedDate", DateTime.Now),
                    DatabaseHelper.CreateParameter("@CreatedBy", UserManager.CurrentUser?.UserID)
                };

                var result = DatabaseHelper.ExecuteScalar(query, parameters);
                int customerId = Convert.ToInt32(result);

                if (customerId > 0)
                {
                    LogManager.LogInfo($"تم إضافة عميل جديد: {customer.CustomerCode} - {customer.CustomerName}");
                }

                return customerId;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في إضافة العميل: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// تحديث بيانات عميل
        /// </summary>
        /// <param name="customer">بيانات العميل المحدثة</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public static bool UpdateCustomer(Customer customer)
        {
            try
            {
                // التحقق من صحة البيانات
                ValidateCustomerData(customer);

                // التحقق من عدم تكرار كود العميل
                if (IsCustomerCodeExists(customer.CustomerCode, customer.CustomerID))
                {
                    throw new Exception("كود العميل موجود مسبقاً");
                }

                string query = @"
                    UPDATE Customers SET
                        CustomerCode = @CustomerCode,
                        CustomerName = @CustomerName,
                        Phone = @Phone,
                        Mobile = @Mobile,
                        Email = @Email,
                        Address = @Address,
                        DateOfBirth = @DateOfBirth,
                        Gender = @Gender,
                        CustomerType = @CustomerType,
                        CreditLimit = @CreditLimit,
                        IsActive = @IsActive,
                        ModifiedDate = @ModifiedDate,
                        ModifiedBy = @ModifiedBy
                    WHERE CustomerID = @CustomerID";

                var parameters = new[]
                {
                    DatabaseHelper.CreateParameter("@CustomerID", customer.CustomerID),
                    DatabaseHelper.CreateParameter("@CustomerCode", customer.CustomerCode),
                    DatabaseHelper.CreateParameter("@CustomerName", customer.CustomerName),
                    DatabaseHelper.CreateParameter("@Phone", customer.Phone ?? ""),
                    DatabaseHelper.CreateParameter("@Mobile", customer.Mobile ?? ""),
                    DatabaseHelper.CreateParameter("@Email", customer.Email ?? ""),
                    DatabaseHelper.CreateParameter("@Address", customer.Address ?? ""),
                    DatabaseHelper.CreateParameter("@DateOfBirth", customer.BirthDate),
                    DatabaseHelper.CreateParameter("@Gender", customer.Gender ?? ""),
                    DatabaseHelper.CreateParameter("@CustomerType", customer.CustomerType ?? "عادي"),
                    DatabaseHelper.CreateParameter("@CreditLimit", customer.CreditLimit),
                    DatabaseHelper.CreateParameter("@IsActive", customer.IsActive),
                    DatabaseHelper.CreateParameter("@ModifiedDate", DateTime.Now),
                    DatabaseHelper.CreateParameter("@ModifiedBy", UserManager.CurrentUser?.UserID)
                };

                int rowsAffected = DatabaseHelper.ExecuteNonQuery(query, parameters);

                if (rowsAffected > 0)
                {
                    LogManager.LogInfo($"تم تحديث العميل: {customer.CustomerCode} - {customer.CustomerName}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تحديث العميل: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// حذف عميل
        /// </summary>
        /// <param name="customerId">معرف العميل</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public static bool DeleteCustomer(int customerId)
        {
            try
            {
                // التحقق من عدم وجود معاملات مرتبطة
                if (HasTransactions(customerId))
                {
                    throw new Exception("لا يمكن حذف العميل لوجود معاملات مرتبطة به");
                }

                string query = "DELETE FROM Customers WHERE CustomerID = @CustomerID";
                var parameters = new[] { DatabaseHelper.CreateParameter("@CustomerID", customerId) };

                int rowsAffected = DatabaseHelper.ExecuteNonQuery(query, parameters);

                if (rowsAffected > 0)
                {
                    LogManager.LogInfo($"تم حذف العميل رقم: {customerId}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في حذف العميل: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Data Retrieval - استرجاع البيانات

        /// <summary>
        /// الحصول على عميل بالمعرف
        /// </summary>
        /// <param name="customerId">معرف العميل</param>
        /// <returns>بيانات العميل</returns>
        public static Customer GetCustomer(int customerId)
        {
            try
            {
                string query = @"
                    SELECT c.*, 
                           ISNULL(c.CreditLimit, 0) as CreditLimit,
                           ISNULL(c.CurrentBalance, 0) as CurrentBalance,
                           ISNULL((SELECT SUM(TotalAmount) FROM SalesInvoices WHERE CustomerID = c.CustomerID), 0) as TotalPurchases
                    FROM Customers c
                    WHERE c.CustomerID = @CustomerID";

                var parameters = new[] { DatabaseHelper.CreateParameter("@CustomerID", customerId) };

                using (var reader = DatabaseHelper.ExecuteReader(query, parameters))
                {
                    if (reader.Read())
                    {
                        return MapCustomerFromReader(reader);
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على العميل {customerId}: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// الحصول على جميع العملاء
        /// </summary>
        /// <returns>قائمة جميع العملاء</returns>
        public static List<Customer> GetAllCustomers()
        {
            try
            {
                string query = @"
                    SELECT c.CustomerID, c.CustomerCode, c.CustomerName, c.Phone, c.Mobile,
                           c.Email, c.Address, c.DateOfBirth, c.Gender, c.CustomerType,
                           ISNULL(c.CreditLimit, 0) as CreditLimit,
                           ISNULL(c.CurrentBalance, 0) as CurrentBalance,
                           c.IsActive, c.CreatedDate, c.ModifiedDate, c.CreatedBy,
                           0 as TotalPurchases
                    FROM Customers c
                    ORDER BY c.CustomerCode";

                var customers = ExecuteCustomerQuery(query);
                LogManager.LogInfo($"تم استرجاع {customers.Count} عميل");
                return customers;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على جميع العملاء: {ex.Message}");
                return new List<Customer>();
            }
        }

        /// <summary>
        /// الحصول على العملاء النشطين فقط
        /// </summary>
        /// <returns>قائمة العملاء النشطين</returns>
        public static List<Customer> GetActiveCustomers()
        {
            try
            {
                string query = @"
                    SELECT c.CustomerID, c.CustomerCode, c.CustomerName, c.Phone, c.Mobile,
                           c.Email, c.Address, c.DateOfBirth, c.Gender, c.CustomerType,
                           ISNULL(c.CreditLimit, 0) as CreditLimit,
                           ISNULL(c.CurrentBalance, 0) as CurrentBalance,
                           c.IsActive, c.CreatedDate, c.ModifiedDate, c.CreatedBy,
                           0 as TotalPurchases
                    FROM Customers c
                    WHERE c.IsActive = 1
                    ORDER BY c.CustomerCode";

                var customers = ExecuteCustomerQuery(query);
                LogManager.LogInfo($"تم استرجاع {customers.Count} عميل نشط");
                return customers;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على العملاء النشطين: {ex.Message}");
                return new List<Customer>();
            }
        }

        /// <summary>
        /// البحث في العملاء
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <returns>قائمة العملاء المطابقة للبحث</returns>
        public static List<Customer> SearchCustomers(string searchTerm)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                {
                    return GetActiveCustomers();
                }

                string query = @"
                    SELECT c.CustomerID, c.CustomerCode, c.CustomerName, c.Phone, c.Mobile,
                           c.Email, c.Address, c.DateOfBirth, c.Gender, c.CustomerType,
                           ISNULL(c.CreditLimit, 0) as CreditLimit,
                           ISNULL(c.CurrentBalance, 0) as CurrentBalance,
                           c.IsActive, c.CreatedDate, c.ModifiedDate, c.CreatedBy,
                           0 as TotalPurchases
                    FROM Customers c
                    WHERE (c.CustomerCode LIKE @SearchTerm OR
                           c.CustomerName LIKE @SearchTerm OR
                           c.Phone LIKE @SearchTerm OR
                           c.Mobile LIKE @SearchTerm OR
                           c.Email LIKE @SearchTerm OR
                           c.Address LIKE @SearchTerm)
                    AND c.IsActive = 1
                    ORDER BY c.CustomerCode";

                var parameters = new[] { DatabaseHelper.CreateParameter("@SearchTerm", $"%{searchTerm}%") };
                var customers = ExecuteCustomerQuery(query, parameters);
                
                LogManager.LogInfo($"تم العثور على {customers.Count} عميل مطابق للبحث: {searchTerm}");
                return customers;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في البحث في العملاء: {ex.Message}");
                LogManager.LogError($"Stack Trace: {ex.StackTrace}");
                LogManager.LogError($"Search Term: {searchTerm}");
                return new List<Customer>();
            }
        }

        /// <summary>
        /// الحصول على العملاء حسب النوع
        /// </summary>
        /// <param name="customerType">نوع العميل</param>
        /// <returns>قائمة العملاء من النوع المحدد</returns>
        public static List<Customer> GetCustomersByType(string customerType)
        {
            try
            {
                string query = @"
                    SELECT c.*, 
                           ISNULL(c.CreditLimit, 0) as CreditLimit,
                           ISNULL(c.CurrentBalance, 0) as CurrentBalance,
                           ISNULL((SELECT SUM(TotalAmount) FROM SalesInvoices WHERE CustomerID = c.CustomerID), 0) as TotalPurchases
                    FROM Customers c
                    WHERE c.CustomerType = @CustomerType AND c.IsActive = 1
                    ORDER BY c.CustomerCode";

                var parameters = new[] { DatabaseHelper.CreateParameter("@CustomerType", customerType) };
                var customers = ExecuteCustomerQuery(query, parameters);
                
                LogManager.LogInfo($"تم العثور على {customers.Count} عميل من نوع: {customerType}");
                return customers;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على العملاء حسب النوع: {ex.Message}");
                return new List<Customer>();
            }
        }

        #endregion

        #region Default Customer - العميل الافتراضي

        /// <summary>
        /// إنشاء أو الحصول على العميل العام الافتراضي
        /// </summary>
        /// <returns>معرف العميل العام</returns>
        public static int EnsureDefaultCustomer()
        {
            try
            {
                // البحث عن العميل العام
                string checkQuery = "SELECT CustomerID FROM Customers WHERE CustomerCode = 'GENERAL'";
                var existingId = DatabaseHelper.ExecuteScalar(checkQuery);

                if (existingId != null && existingId != DBNull.Value)
                {
                    return Convert.ToInt32(existingId);
                }

                // إنشاء العميل العام إذا لم يكن موجوداً
                var generalCustomer = new Customer
                {
                    CustomerCode = "GENERAL",
                    CustomerName = "عميل عام",
                    Phone = "************",
                    Mobile = "************",
                    Email = "<EMAIL>",
                    Address = "عنوان عام",
                    CustomerType = "عادي",
                    CreditLimit = 0,
                    CurrentBalance = 0,
                    IsActive = true,
                    RegistrationDate = DateTime.Now
                };

                int customerId = AddCustomer(generalCustomer);
                LogManager.LogInfo($"تم إنشاء العميل العام برقم: {customerId}");
                return customerId;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في إنشاء العميل العام: {ex.Message}");
                return 1; // قيمة افتراضية في حالة الخطأ
            }
        }

        /// <summary>
        /// الحصول على معرف العميل العام
        /// </summary>
        /// <returns>معرف العميل العام</returns>
        public static int GetDefaultCustomerId()
        {
            try
            {
                string query = "SELECT CustomerID FROM Customers WHERE CustomerCode = 'GENERAL'";
                var result = DatabaseHelper.ExecuteScalar(query);

                if (result != null && result != DBNull.Value)
                {
                    return Convert.ToInt32(result);
                }

                // إنشاء العميل العام إذا لم يكن موجوداً
                return EnsureDefaultCustomer();
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في الحصول على العميل العام: {ex.Message}");
                return 1; // قيمة افتراضية
            }
        }

        /// <summary>
        /// التحقق من وجود العميل العام
        /// </summary>
        /// <returns>true إذا كان العميل العام موجود</returns>
        public static bool IsDefaultCustomerExists()
        {
            try
            {
                string query = "SELECT COUNT(*) FROM Customers WHERE CustomerCode = 'GENERAL'";
                var result = DatabaseHelper.ExecuteScalar(query);
                return Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في التحقق من العميل العام: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Auto Code Generation - إنشاء الكود التلقائي

        /// <summary>
        /// إنشاء كود عميل جديد تلقائياً
        /// </summary>
        /// <returns>كود العميل الجديد</returns>
        public static string GenerateCustomerCode()
        {
            try
            {
                string query = @"
                    SELECT TOP 1 CustomerCode
                    FROM Customers
                    WHERE CustomerCode LIKE 'C%'
                    AND ISNUMERIC(SUBSTRING(CustomerCode, 2, LEN(CustomerCode)-1)) = 1
                    ORDER BY CAST(SUBSTRING(CustomerCode, 2, LEN(CustomerCode)-1) AS INT) DESC";

                var result = DatabaseHelper.ExecuteScalar(query);

                if (result != null && result != DBNull.Value)
                {
                    string lastCode = result.ToString();
                    // استخراج الرقم من الكود (مثل C001 -> 001)
                    string numberPart = lastCode.Substring(1);
                    if (int.TryParse(numberPart, out int lastNumber))
                    {
                        int newNumber = lastNumber + 1;
                        return $"C{newNumber:D3}"; // C001, C002, C003...
                    }
                }

                // إذا لم توجد أكواد سابقة، ابدأ من C001
                return "C001";
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في إنشاء كود العميل: {ex.Message}");
                // في حالة الخطأ، استخدم التاريخ والوقت
                return $"C{DateTime.Now:yyyyMMddHHmmss}".Substring(0, 10);
            }
        }

        /// <summary>
        /// التحقق من توفر كود العميل
        /// </summary>
        /// <param name="customerCode">كود العميل</param>
        /// <returns>true إذا كان الكود متاحاً</returns>
        public static bool IsCustomerCodeAvailable(string customerCode)
        {
            try
            {
                string query = "SELECT COUNT(*) FROM Customers WHERE CustomerCode = @CustomerCode";
                var parameters = new[] { DatabaseHelper.CreateParameter("@CustomerCode", customerCode) };
                var result = DatabaseHelper.ExecuteScalar(query, parameters);
                return Convert.ToInt32(result) == 0;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في التحقق من كود العميل: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إنشاء كود عميل فريد
        /// </summary>
        /// <returns>كود عميل فريد</returns>
        public static string GenerateUniqueCustomerCode()
        {
            string code = GenerateCustomerCode();
            int attempts = 0;

            // تأكد من أن الكود فريد (حتى 100 محاولة)
            while (!IsCustomerCodeAvailable(code) && attempts < 100)
            {
                attempts++;
                // إذا كان الكود موجود، أضف رقم عشوائي
                code = GenerateCustomerCode() + attempts.ToString("D2");
            }

            return code;
        }

        /// <summary>
        /// إنشاء كود عميل بناءً على الاسم
        /// </summary>
        /// <param name="customerName">اسم العميل</param>
        /// <returns>كود العميل المقترح</returns>
        public static string GenerateCustomerCodeFromName(string customerName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(customerName))
                    return GenerateUniqueCustomerCode();

                // استخراج الأحرف الأولى من الاسم
                string[] nameParts = customerName.Trim().Split(' ');
                string initials = "";

                foreach (string part in nameParts)
                {
                    if (!string.IsNullOrWhiteSpace(part))
                    {
                        initials += part[0].ToString().ToUpper();
                        if (initials.Length >= 3) break; // حد أقصى 3 أحرف
                    }
                }

                // إذا كان أقل من 3 أحرف، أضف أرقام
                while (initials.Length < 3)
                {
                    initials += "0";
                }

                // إضافة رقم تسلسلي
                string baseCode = initials;
                string code = baseCode + "001";
                int counter = 1;

                // البحث عن كود متاح
                while (!IsCustomerCodeAvailable(code) && counter < 1000)
                {
                    counter++;
                    code = baseCode + counter.ToString("D3");
                }

                return code;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في إنشاء كود من الاسم: {ex.Message}");
                return GenerateUniqueCustomerCode();
            }
        }

        #endregion

        #region Utility Methods - الطرق المساعدة

        /// <summary>
        /// توليد كود عميل جديد تلقائياً
        /// </summary>
        /// <param name="customerType">نوع العميل</param>
        /// <returns>كود العميل الجديد</returns>
        public static string GenerateCustomerCode(string customerType = "Regular")
        {
            try
            {
                string prefix;
                switch (customerType?.ToUpper())
                {
                    case "VIP":
                        prefix = "VIP";
                        break;
                    case "WHOLESALE":
                        prefix = "WS";
                        break;
                    case "CORPORATE":
                        prefix = "CORP";
                        break;
                    default:
                        prefix = "CUST";
                        break;
                }

                string query = @"
                    SELECT ISNULL(MAX(CAST(SUBSTRING(CustomerCode, LEN(@Prefix) + 1, 10) AS INT)), 0)
                    FROM Customers
                    WHERE CustomerCode LIKE @Pattern";

                var parameters = new[]
                {
                    DatabaseHelper.CreateParameter("@Prefix", prefix),
                    DatabaseHelper.CreateParameter("@Pattern", prefix + "%")
                };

                var result = DatabaseHelper.ExecuteScalar(query, parameters);
                int nextNumber = Convert.ToInt32(result) + 1;

                string newCode = $"{prefix}{nextNumber:D4}";

                LogManager.LogInfo($"تم توليد كود عميل جديد: {newCode}");
                return newCode;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في توليد كود العميل: {ex.Message}");
                return $"CUST{DateTime.Now.Ticks.ToString().Substring(10)}";
            }
        }

        /// <summary>
        /// التحقق من وجود كود العميل
        /// </summary>
        /// <param name="customerCode">كود العميل</param>
        /// <param name="excludeCustomerId">معرف العميل المستثنى (للتحديث)</param>
        /// <returns>true إذا كان الكود موجود</returns>
        public static bool IsCustomerCodeExists(string customerCode, int? excludeCustomerId = null)
        {
            try
            {
                string query = "SELECT COUNT(*) FROM Customers WHERE CustomerCode = @CustomerCode";
                var parameters = new List<SqlParameter>
                {
                    DatabaseHelper.CreateParameter("@CustomerCode", customerCode)
                };

                if (excludeCustomerId.HasValue)
                {
                    query += " AND CustomerID != @ExcludeCustomerID";
                    parameters.Add(DatabaseHelper.CreateParameter("@ExcludeCustomerID", excludeCustomerId.Value));
                }

                var result = DatabaseHelper.ExecuteScalar(query, parameters.ToArray());
                return Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في التحقق من كود العميل: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تحديث رصيد العميل
        /// </summary>
        /// <param name="customerId">معرف العميل</param>
        /// <param name="amount">المبلغ (موجب للزيادة، سالب للتقليل)</param>
        /// <param name="description">وصف العملية</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public static bool UpdateCustomerBalance(int customerId, decimal amount, string description)
        {
            try
            {
                string query = @"
                    UPDATE Customers
                    SET CurrentBalance = ISNULL(CurrentBalance, 0) + @Amount,
                        ModifiedDate = @ModifiedDate,
                        ModifiedBy = @ModifiedBy
                    WHERE CustomerID = @CustomerID";

                var parameters = new[]
                {
                    DatabaseHelper.CreateParameter("@CustomerID", customerId),
                    DatabaseHelper.CreateParameter("@Amount", amount),
                    DatabaseHelper.CreateParameter("@ModifiedDate", DateTime.Now),
                    DatabaseHelper.CreateParameter("@ModifiedBy", UserManager.CurrentUser?.UserID)
                };

                int rowsAffected = DatabaseHelper.ExecuteNonQuery(query, parameters);

                if (rowsAffected > 0)
                {
                    LogManager.LogInfo($"تم تحديث رصيد العميل {customerId}: {amount:C} - {description}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تحديث رصيد العميل: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Private Helper Methods - الطرق المساعدة الخاصة

        /// <summary>
        /// التحقق من صحة بيانات العميل
        /// </summary>
        /// <param name="customer">بيانات العميل</param>
        private static void ValidateCustomerData(Customer customer)
        {
            if (customer == null)
                throw new ArgumentNullException(nameof(customer), "بيانات العميل مطلوبة");

            if (string.IsNullOrWhiteSpace(customer.CustomerCode))
                throw new ArgumentException("كود العميل مطلوب", nameof(customer.CustomerCode));

            if (string.IsNullOrWhiteSpace(customer.CustomerName))
                throw new ArgumentException("اسم العميل مطلوب", nameof(customer.CustomerName));

            if (customer.CustomerCode.Length > 20)
                throw new ArgumentException("كود العميل يجب أن يكون أقل من 20 حرف", nameof(customer.CustomerCode));

            if (customer.CustomerName.Length > 200)
                throw new ArgumentException("اسم العميل يجب أن يكون أقل من 200 حرف", nameof(customer.CustomerName));

            if (customer.CreditLimit < 0)
                throw new ArgumentException("حد الائتمان لا يمكن أن يكون سالب", nameof(customer.CreditLimit));
        }

        /// <summary>
        /// التحقق من وجود معاملات مرتبطة بالعميل
        /// </summary>
        /// <param name="customerId">معرف العميل</param>
        /// <returns>true إذا كان يوجد معاملات مرتبطة</returns>
        private static bool HasTransactions(int customerId)
        {
            try
            {
                string query = @"
                    SELECT COUNT(*) FROM (
                        SELECT 1 FROM SalesInvoices WHERE CustomerID = @CustomerID
                        UNION ALL
                        SELECT 1 FROM CustomerPayments WHERE CustomerID = @CustomerID
                        UNION ALL
                        SELECT 1 FROM Vouchers WHERE CustomerID = @CustomerID
                    ) AS Transactions";

                var parameters = new[] { DatabaseHelper.CreateParameter("@CustomerID", customerId) };
                var result = DatabaseHelper.ExecuteScalar(query, parameters);
                return Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في التحقق من المعاملات المرتبطة: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تنفيذ استعلام العملاء وإرجاع النتائج
        /// </summary>
        /// <param name="query">الاستعلام</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>قائمة العملاء</returns>
        private static List<Customer> ExecuteCustomerQuery(string query, SqlParameter[] parameters = null)
        {
            var customers = new List<Customer>();
            try
            {
                using (var reader = DatabaseHelper.ExecuteReader(query, parameters))
                {
                    while (reader.Read())
                    {
                        customers.Add(MapCustomerFromReader(reader));
                    }
                }
                return customers;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في تنفيذ استعلام العملاء: {ex.Message}");
                LogManager.LogError($"Stack Trace: {ex.StackTrace}");
                LogManager.LogError($"Query: {query}");
                return customers;
            }
        }

        /// <summary>
        /// تحويل بيانات القارئ إلى كائن عميل
        /// </summary>
        /// <param name="reader">قارئ البيانات</param>
        /// <returns>كائن العميل</returns>
        private static Customer MapCustomerFromReader(SqlDataReader reader)
        {
            return new Customer
            {
                CustomerID = Convert.ToInt32(reader["CustomerID"]),
                CustomerCode = reader["CustomerCode"]?.ToString() ?? "",
                CustomerName = reader["CustomerName"]?.ToString() ?? "",
                Phone = reader["Phone"] == DBNull.Value ? null : reader["Phone"].ToString(),
                Mobile = reader["Mobile"] == DBNull.Value ? null : reader["Mobile"].ToString(),
                Email = reader["Email"] == DBNull.Value ? null : reader["Email"].ToString(),
                Address = reader["Address"] == DBNull.Value ? null : reader["Address"].ToString(),
                BirthDate = reader["DateOfBirth"] == DBNull.Value ? null : (DateTime?)Convert.ToDateTime(reader["DateOfBirth"]),
                Gender = reader["Gender"] == DBNull.Value ? null : reader["Gender"].ToString(),
                CustomerType = reader["CustomerType"] == DBNull.Value ? "عادي" : reader["CustomerType"].ToString(),
                CreditLimit = reader["CreditLimit"] == DBNull.Value ? 0 : Convert.ToDecimal(reader["CreditLimit"]),
                CurrentBalance = reader["CurrentBalance"] == DBNull.Value ? 0 : Convert.ToDecimal(reader["CurrentBalance"]),
                IsActive = Convert.ToBoolean(reader["IsActive"]),
                RegistrationDate = Convert.ToDateTime(reader["CreatedDate"]),
                ModifiedDate = reader["ModifiedDate"] == DBNull.Value ? null : (DateTime?)Convert.ToDateTime(reader["ModifiedDate"]),
                TotalPurchases = reader["TotalPurchases"] == DBNull.Value ? 0 : Convert.ToDecimal(reader["TotalPurchases"]),
                CreatedBy = reader["CreatedBy"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["CreatedBy"])
            };
        }

        /// <summary>
        /// فحص وجود عمود في SqlDataReader
        /// </summary>
        private static bool HasColumn(SqlDataReader reader, string columnName)
        {
            for (int i = 0; i < reader.FieldCount; i++)
            {
                if (reader.GetName(i).Equals(columnName, StringComparison.OrdinalIgnoreCase))
                    return true;
            }
            return false;
        }

        /// <summary>
        /// دالة اختبار للبحث المبسط
        /// </summary>
        public static List<Customer> TestSearchCustomers(string searchTerm)
        {
            var customers = new List<Customer>();
            try
            {
                LogManager.LogInfo($"بدء البحث عن: {searchTerm}");

                string query = @"
                    SELECT CustomerID, CustomerCode, CustomerName, Phone, Mobile, Email, Address,
                           IsActive, CreatedDate, ModifiedDate, CreatedBy
                    FROM Customers
                    WHERE CustomerName LIKE @SearchTerm AND IsActive = 1
                    ORDER BY CustomerCode";

                var parameters = new[] { DatabaseHelper.CreateParameter("@SearchTerm", $"%{searchTerm}%") };

                LogManager.LogInfo($"تنفيذ الاستعلام: {query}");

                using (var reader = DatabaseHelper.ExecuteReader(query, parameters))
                {
                    while (reader.Read())
                    {
                        customers.Add(new Customer
                        {
                            CustomerID = Convert.ToInt32(reader["CustomerID"]),
                            CustomerCode = reader["CustomerCode"].ToString(),
                            CustomerName = reader["CustomerName"].ToString(),
                            Phone = reader["Phone"] == DBNull.Value ? null : reader["Phone"].ToString(),
                            Mobile = reader["Mobile"] == DBNull.Value ? null : reader["Mobile"].ToString(),
                            Email = reader["Email"] == DBNull.Value ? null : reader["Email"].ToString(),
                            Address = reader["Address"] == DBNull.Value ? null : reader["Address"].ToString(),
                            IsActive = Convert.ToBoolean(reader["IsActive"]),
                            RegistrationDate = Convert.ToDateTime(reader["CreatedDate"]),
                            ModifiedDate = reader["ModifiedDate"] == DBNull.Value ? null : (DateTime?)Convert.ToDateTime(reader["ModifiedDate"]),
                            CreatedBy = reader["CreatedBy"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["CreatedBy"]),
                            CustomerType = "عادي",
                            CreditLimit = 0,
                            CurrentBalance = 0,
                            TotalPurchases = 0
                        });
                    }
                }

                LogManager.LogInfo($"تم العثور على {customers.Count} عميل");
                return customers;
            }
            catch (Exception ex)
            {
                LogManager.LogError($"خطأ في البحث التجريبي: {ex.Message}");
                LogManager.LogError($"Stack Trace: {ex.StackTrace}");
                return customers;
            }
        }

        #endregion

        #region Statistics and Reports - الإحصائيات والتقارير

        /// <summary>
        /// الحصول على إحصائيات العملاء
        /// </summary>
        /// <returns>إحصائيات العملاء</returns>
        public static CustomerStatistics GetCustomerStatistics()
        {
            try
            {
                string query = @"
                    SELECT
                        COUNT(*) as TotalCustomers,
                        SUM(CASE WHEN IsActive = 1 THEN 1 ELSE 0 END) as ActiveCustomers,
                        SUM(CASE WHEN IsActive = 0 THEN 1 ELSE 0 END) as InactiveCustomers,
                        COUNT(DISTINCT CustomerType) as CustomerTypes,
                        ISNULL(SUM(CurrentBalance), 0) as TotalBalance,
                        ISNULL(SUM(CreditLimit), 0) as TotalCreditLimit,
                        ISNULL(AVG(CreditLimit), 0) as AverageCreditLimit
                    FROM Customers";

                using (var reader = DatabaseHelper.ExecuteReader(query))
                {
                    if (reader.Read())
                    {
                        return new CustomerStatistics
                        {
                            TotalCustomers = Convert.ToInt32(reader["TotalCustomers"]),
                            ActiveCustomers = Convert.ToInt32(reader["ActiveCustomers"]),
                            InactiveCustomers = Convert.ToInt32(reader["InactiveCustomers"]),
                            CustomerTypes = Convert.ToInt32(reader["CustomerTypes"]),
                            TotalBalance = Convert.ToDecimal(reader["TotalBalance"]),
                            TotalCreditLimit = Convert.ToDecimal(reader["TotalCreditLimit"]),
                            AverageCreditLimit = Convert.ToDecimal(reader["AverageCreditLimit"])
                        };
                    }
                }

                return new CustomerStatistics();
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في الحصول على إحصائيات العملاء: " + ex.Message);
                return new CustomerStatistics();
            }
        }

        /// <summary>
        /// الحصول على أفضل العملاء
        /// </summary>
        /// <param name="topCount">عدد العملاء المطلوب</param>
        /// <returns>قائمة أفضل العملاء</returns>
        public static List<Customer> GetTopCustomers(int topCount = 10)
        {
            try
            {
                string query = @"
                    SELECT TOP " + topCount.ToString() + @" c.*,
                           ISNULL(c.CreditLimit, 0) as CreditLimit,
                           ISNULL(c.CurrentBalance, 0) as CurrentBalance,
                           ISNULL((SELECT SUM(TotalAmount) FROM SalesInvoices WHERE CustomerID = c.CustomerID), 0) as TotalPurchases
                    FROM Customers c
                    WHERE c.IsActive = 1
                    ORDER BY (SELECT SUM(TotalAmount) FROM SalesInvoices WHERE CustomerID = c.CustomerID) DESC";

                return ExecuteCustomerQuery(query);
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في الحصول على أفضل العملاء: " + ex.Message);
                return new List<Customer>();
            }
        }

        /// <summary>
        /// الحصول على إجمالي عدد العملاء
        /// </summary>
        /// <returns>عدد العملاء</returns>
        public static int GetTotalCustomersCount()
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = "SELECT COUNT(*) FROM Customers WHERE IsActive = 1";

                    using (var command = new SqlCommand(query, connection))
                    {
                        var result = command.ExecuteScalar();
                        return result != null ? Convert.ToInt32(result) : 0;
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في حساب عدد العملاء: " + ex.Message);
                return 0;
            }
        }

        /// <summary>
        /// الحصول على إجمالي رصيد العملاء
        /// </summary>
        /// <returns>إجمالي الرصيد</returns>
        public static decimal GetTotalCustomerBalance()
        {
            try
            {
                using (var connection = DatabaseHelper.GetConnection())
                {
                    connection.Open();
                    string query = "SELECT ISNULL(SUM(CurrentBalance), 0) FROM Customers WHERE IsActive = 1";

                    using (var command = new SqlCommand(query, connection))
                    {
                        var result = command.ExecuteScalar();
                        return result != null ? Convert.ToDecimal(result) : 0;
                    }
                }
            }
            catch (Exception ex)
            {
                LogManager.LogError("خطأ في حساب إجمالي رصيد العملاء: " + ex.Message);
                return 0;
            }
        }

        #endregion
    }

    #region Helper Models - النماذج المساعدة

    /// <summary>
    /// إحصائيات العملاء
    /// </summary>
    public class CustomerStatistics
    {
        public int TotalCustomers { get; set; }
        public int ActiveCustomers { get; set; }
        public int InactiveCustomers { get; set; }
        public int CustomerTypes { get; set; }
        public decimal TotalBalance { get; set; }
        public decimal TotalCreditLimit { get; set; }
        public decimal AverageCreditLimit { get; set; }
    }

    #endregion
}
