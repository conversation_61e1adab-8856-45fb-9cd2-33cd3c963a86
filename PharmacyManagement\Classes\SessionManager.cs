using System;
using System.Collections.Generic;

namespace PharmacyManagement.Classes
{
    /// <summary>
    /// مدير الجلسات
    /// </summary>
    public static class SessionManager
    {
        private static Dictionary<string, object> _sessionData = new Dictionary<string, object>();

        /// <summary>
        /// تعيين قيمة في الجلسة
        /// </summary>
        /// <param name="key">المفتاح</param>
        /// <param name="value">القيمة</param>
        public static void SetValue(string key, object value)
        {
            try
            {
                if (_sessionData.ContainsKey(key))
                {
                    _sessionData[key] = value;
                }
                else
                {
                    _sessionData.Add(key, value);
                }
            }
            catch (Exception ex)
            {
                // LogManager.LogError("خطأ في تعيين قيمة الجلسة: " + ex.Message);
            }
        }

        /// <summary>
        /// الحصول على قيمة من الجلسة
        /// </summary>
        /// <param name="key">المفتاح</param>
        /// <returns>القيمة</returns>
        public static object GetValue(string key)
        {
            try
            {
                if (_sessionData.ContainsKey(key))
                {
                    return _sessionData[key];
                }
                return null;
            }
            catch (Exception ex)
            {
                // LogManager.LogError("خطأ في الحصول على قيمة الجلسة: " + ex.Message);
                return null;
            }
        }

        /// <summary>
        /// مسح الجلسة
        /// </summary>
        public static void Clear()
        {
            try
            {
                _sessionData.Clear();
            }
            catch (Exception ex)
            {
                // LogManager.LogError("خطأ في مسح الجلسة: " + ex.Message);
            }
        }
    }
}
