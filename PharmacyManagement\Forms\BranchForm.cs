using System;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نموذج إدارة الفروع - Branch Management Form
    /// </summary>
    public partial class BranchForm : Form
    {
        #region Fields

        private BindingSource _bindingSource;
        private int? _selectedBranchId;

        #endregion

        #region Constructor

        public BranchForm()
        {
            InitializeComponent();
            SetupForm();
            LoadData();
        }

        #endregion

        #region Setup Methods

        private void SetupForm()
        {
            this.Text = "إدارة الفروع";
            this.WindowState = FormWindowState.Maximized;
            
            // إعداد التصميم المسطح
            SetupFlatDesign();
            
            // إعداد الشبكة
            SetupDataGrid();
            
            // إعداد الأحداث
            SetupEvents();
        }

        private void SetupFlatDesign()
        {
            // إعدادات النموذج
            this.BackColor = Color.White;
            this.Font = new Font("Segoe UI", 9F);
            
            // إعداد الأزرار
            foreach (Control control in this.Controls)
            {
                if (control is Button btn)
                {
                    btn.FlatStyle = FlatStyle.Flat;
                    btn.FlatAppearance.BorderSize = 1;
                    btn.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
                }
                else if (control is TextBox txt)
                {
                    txt.BorderStyle = BorderStyle.FixedSingle;
                    txt.Font = new Font("Segoe UI", 10F);
                }
            }

            // ألوان الأزرار
            btnAdd.BackColor = Color.FromArgb(46, 204, 113);
            btnAdd.ForeColor = Color.White;
            btnEdit.BackColor = Color.FromArgb(52, 152, 219);
            btnEdit.ForeColor = Color.White;
            btnDelete.BackColor = Color.FromArgb(231, 76, 60);
            btnDelete.ForeColor = Color.White;
            btnSearch.BackColor = Color.FromArgb(155, 89, 182);
            btnSearch.ForeColor = Color.White;
            btnViewWarehouses.BackColor = Color.FromArgb(26, 188, 156);
            btnViewWarehouses.ForeColor = Color.White;
        }

        private void SetupDataGrid()
        {
            // إعداد الشبكة
            dgvBranches.AutoGenerateColumns = false;
            dgvBranches.AllowUserToAddRows = false;
            dgvBranches.AllowUserToDeleteRows = false;
            dgvBranches.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvBranches.MultiSelect = false;
            dgvBranches.BackgroundColor = Color.White;
            dgvBranches.BorderStyle = BorderStyle.FixedSingle;
            dgvBranches.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 152, 219);
            dgvBranches.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvBranches.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            dgvBranches.DefaultCellStyle.Font = new Font("Segoe UI", 9F);
            dgvBranches.RowHeadersVisible = false;
            dgvBranches.EnableHeadersVisualStyles = false;

            // إضافة الأعمدة
            dgvBranches.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "BranchCode",
                HeaderText = "كود الفرع",
                DataPropertyName = "BranchCode",
                Width = 100
            });

            dgvBranches.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "BranchName",
                HeaderText = "اسم الفرع",
                DataPropertyName = "BranchName",
                Width = 200
            });

            dgvBranches.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "City",
                HeaderText = "المدينة",
                DataPropertyName = "City",
                Width = 120
            });

            dgvBranches.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Region",
                HeaderText = "المنطقة",
                DataPropertyName = "Region",
                Width = 120
            });

            dgvBranches.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ManagerName",
                HeaderText = "المدير",
                DataPropertyName = "ManagerName",
                Width = 150
            });

            dgvBranches.Columns.Add(new DataGridViewCheckBoxColumn
            {
                Name = "IsMainBranch",
                HeaderText = "فرع رئيسي",
                DataPropertyName = "IsMainBranch",
                Width = 100
            });

            dgvBranches.Columns.Add(new DataGridViewCheckBoxColumn
            {
                Name = "IsActive",
                HeaderText = "نشط",
                DataPropertyName = "IsActive",
                Width = 80
            });

            dgvBranches.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Phone",
                HeaderText = "الهاتف",
                DataPropertyName = "Phone",
                Width = 120
            });

            dgvBranches.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "OpeningHours",
                HeaderText = "ساعات العمل",
                DataPropertyName = "OpeningHours",
                Width = 150
            });

            // إعداد مصدر البيانات
            _bindingSource = new BindingSource();
            dgvBranches.DataSource = _bindingSource;
        }

        private void SetupEvents()
        {
            // ربط الأحداث
            btnAdd.Click += BtnAdd_Click;
            btnEdit.Click += BtnEdit_Click;
            btnDelete.Click += BtnDelete_Click;
            btnSearch.Click += BtnSearch_Click;
            btnRefresh.Click += BtnRefresh_Click;
            btnViewWarehouses.Click += BtnViewWarehouses_Click;
            
            dgvBranches.SelectionChanged += DgvBranches_SelectionChanged;
            dgvBranches.CellFormatting += DgvBranches_CellFormatting;
            dgvBranches.CellDoubleClick += DgvBranches_CellDoubleClick;
            
            txtSearch.KeyPress += TxtSearch_KeyPress;
        }

        #endregion

        #region Load Data Methods

        private void LoadData()
        {
            try
            {
                var branches = BranchManager.GetAllBranches();
                _bindingSource.DataSource = branches;
                
                UpdateButtonStates();
                UpdateStatusLabel();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Event Handlers

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                using (var addForm = new BranchAddEditForm())
                {
                    if (addForm.ShowDialog() == DialogResult.OK)
                    {
                        LoadData();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة الفرع: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            try
            {
                if (_selectedBranchId.HasValue)
                {
                    using (var editForm = new BranchAddEditForm(_selectedBranchId.Value))
                    {
                        if (editForm.ShowDialog() == DialogResult.OK)
                        {
                            LoadData();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعديل الفرع: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            try
            {
                if (_selectedBranchId.HasValue)
                {
                    var result = MessageBox.Show("هل تريد حذف الفرع المحدد؟", "تأكيد الحذف",
                        MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    
                    if (result == DialogResult.Yes)
                    {
                        if (BranchManager.DeleteBranch(_selectedBranchId.Value))
                        {
                            MessageBox.Show("تم حذف الفرع بنجاح", "نجح",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                            LoadData();
                        }
                        else
                        {
                            MessageBox.Show("فشل في حذف الفرع", "خطأ",
                                MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف الفرع: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnSearch_Click(object sender, EventArgs e)
        {
            PerformSearch();
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            txtSearch.Clear();
            LoadData();
        }

        private void BtnViewWarehouses_Click(object sender, EventArgs e)
        {
            try
            {
                if (_selectedBranchId.HasValue)
                {
                    using (var warehouseForm = new WarehouseForm(_selectedBranchId.Value))
                    {
                        warehouseForm.ShowDialog();
                    }
                }
                else
                {
                    MessageBox.Show("يرجى اختيار فرع أولاً", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض المخازن: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DgvBranches_SelectionChanged(object sender, EventArgs e)
        {
            if (dgvBranches.SelectedRows.Count > 0)
            {
                var selectedRow = dgvBranches.SelectedRows[0];
                if (selectedRow.DataBoundItem is Branch branch)
                {
                    _selectedBranchId = branch.BranchID;
                    UpdateButtonStates();
                }
            }
            else
            {
                _selectedBranchId = null;
                UpdateButtonStates();
            }
        }

        private void DgvBranches_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (dgvBranches.Rows[e.RowIndex].DataBoundItem is Branch branch)
            {
                // تلوين الصف حسب الحالة
                if (branch.IsMainBranch)
                {
                    dgvBranches.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.LightGreen;
                    dgvBranches.Rows[e.RowIndex].DefaultCellStyle.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
                }
                else if (!branch.IsActive)
                {
                    dgvBranches.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.LightGray;
                    dgvBranches.Rows[e.RowIndex].DefaultCellStyle.ForeColor = Color.DarkGray;
                }
                else
                {
                    dgvBranches.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.White;
                }
            }
        }

        private void DgvBranches_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                BtnEdit_Click(sender, e);
            }
        }

        private void TxtSearch_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                PerformSearch();
                e.Handled = true;
            }
        }

        #endregion

        #region Helper Methods

        private void UpdateButtonStates()
        {
            bool hasSelection = _selectedBranchId.HasValue;
            
            btnEdit.Enabled = hasSelection;
            btnDelete.Enabled = hasSelection;
            btnViewWarehouses.Enabled = hasSelection;

            if (hasSelection && dgvBranches.SelectedRows.Count > 0)
            {
                var selectedRow = dgvBranches.SelectedRows[0];
                if (selectedRow.DataBoundItem is Branch branch)
                {
                    // لا يمكن حذف الفرع الرئيسي
                    btnDelete.Enabled = !branch.IsMainBranch;
                }
            }
        }

        private void UpdateStatusLabel()
        {
            var totalBranches = _bindingSource.Count;
            var activeBranches = 0;
            var mainBranch = 0;

            foreach (Branch branch in _bindingSource)
            {
                if (branch.IsActive)
                    activeBranches++;
                if (branch.IsMainBranch)
                    mainBranch++;
            }

            lblStatus.Text = $"إجمالي الفروع: {totalBranches} | نشط: {activeBranches} | رئيسي: {mainBranch}";
        }

        private void PerformSearch()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtSearch.Text))
                {
                    LoadData();
                    return;
                }

                var searchResults = BranchManager.SearchBranches(txtSearch.Text.Trim());
                _bindingSource.DataSource = searchResults;
                UpdateStatusLabel();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion
    }
}
