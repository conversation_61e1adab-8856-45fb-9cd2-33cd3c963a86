using System;

namespace PharmacyManagement.Models
{
    /// <summary>
    /// نموذج الدواء - Drug Model
    /// يمثل بيانات الأدوية في النظام
    /// </summary>
    public class Drug
    {
        #region Properties - الخصائص

        /// <summary>
        /// معرف الدواء الفريد
        /// </summary>
        public int DrugID { get; set; }

        /// <summary>
        /// كود الدواء
        /// </summary>
        public string DrugCode { get; set; }

        /// <summary>
        /// الاسم التجاري للدواء
        /// </summary>
        public string DrugName { get; set; }

        /// <summary>
        /// الاسم التجاري للدواء (مرادف لـ DrugName للتوافق مع الكود الموجود)
        /// </summary>
        public string TradeName
        {
            get { return DrugName; }
            set { DrugName = value; }
        }

        /// <summary>
        /// الاسم العلمي للدواء
        /// </summary>
        public string ScientificName { get; set; }

        /// <summary>
        /// الباركود
        /// </summary>
        public string Barcode { get; set; }

        /// <summary>
        /// معرف فئة الدواء
        /// </summary>
        public int? CategoryID { get; set; }

        /// <summary>
        /// اسم فئة الدواء
        /// </summary>
        public string CategoryName { get; set; }

        /// <summary>
        /// معرف الشركة المصنعة
        /// </summary>
        public int? ManufacturerID { get; set; }

        /// <summary>
        /// اسم الشركة المصنعة
        /// </summary>
        public string ManufacturerName { get; set; }

        /// <summary>
        /// اسم الدواء
        /// </summary>
       // public string DrugName { get; set; }

        /// <summary>
        /// كمية المخزون
        /// </summary>
        public decimal StockQuantity { get; set; }

        /// <summary>
        /// تاريخ انتهاء الصلاحية
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// الاسم العام
        /// </summary>
        public string GenericName { get; set; }

        /// <summary>
        /// الشركة المصنعة (اسم بديل)
        /// </summary>
        public string Manufacturer
        {
            get { return ManufacturerName; }
            set { ManufacturerName = value; }
        }

        /// <summary>
        /// الفئة (اسم بديل)
        /// </summary>
        public string Category
        {
            get { return CategoryName; }
            set { CategoryName = value; }
        }

        /// <summary>
        /// سعر الوحدة
        /// </summary>
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// الشكل الدوائي (قرص، كبسولة، شراب، حقن)
        /// </summary>
        public string DosageForm { get; set; }

        /// <summary>
        /// قوة الدواء/الجرعة
        /// </summary>
        public string Strength { get; set; }

        /// <summary>
        /// حجم العبوة
        /// </summary>
        public int PackSize { get; set; }

        /// <summary>
        /// الوحدة (قرص، كبسولة، مل)
        /// </summary>
        public string Unit { get; set; }

        /// <summary>
        /// سعر الشراء
        /// </summary>
        public decimal PurchasePrice { get; set; }

        /// <summary>
        /// سعر البيع
        /// </summary>
        public decimal SalePrice { get; set; }

        /// <summary>
        /// الحد الأدنى للمخزون
        /// </summary>
        public int MinStockLevel { get; set; }

        /// <summary>
        /// الحد الأقصى للمخزون
        /// </summary>
        public int MaxStockLevel { get; set; }

        /// <summary>
        /// المخزون الحالي
        /// </summary>
        public int CurrentStock { get; set; }

        /// <summary>
        /// موقع التخزين
        /// </summary>
        public string Location { get; set; }

        /// <summary>
        /// هل يحتاج وصفة طبية
        /// </summary>
        public bool RequiresPrescription { get; set; }

        /// <summary>
        /// حالة الدواء (نشط/غير نشط)
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// ملاحظات
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// تاريخ الإضافة
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// معرف المستخدم الذي أضاف الدواء
        /// </summary>
        public int? CreatedBy { get; set; }

        #endregion

        #region Calculated Properties - الخصائص المحسوبة

        /// <summary>
        /// هامش الربح
        /// </summary>
        public decimal ProfitMargin
        {
            get
            {
                if (PurchasePrice == 0) return 0;
                return ((SalePrice - PurchasePrice) / PurchasePrice) * 100;
            }
        }

        /// <summary>
        /// قيمة المخزون الحالي
        /// </summary>
        public decimal StockValue
        {
            get { return CurrentStock * PurchasePrice; }
        }

        /// <summary>
        /// حالة المخزون
        /// </summary>
        public string StockStatus
        {
            get
            {
                if (CurrentStock == 0) return "نفد المخزون";
                if (CurrentStock <= MinStockLevel) return "مخزون منخفض";
                if (CurrentStock >= MaxStockLevel) return "مخزون مرتفع";
                return "مخزون طبيعي";
            }
        }

        /// <summary>
        /// الاسم الكامل للدواء
        /// </summary>
        public string FullName
        {
            get
            {
                string name = DrugName;
                if (!string.IsNullOrEmpty(Strength))
                    name += " " + Strength;
                if (!string.IsNullOrEmpty(DosageForm))
                    name += " (" + DosageForm + ")";
                return name;
            }
        }

        #endregion

        #region Constructors - المنشئات

        /// <summary>
        /// منشئ افتراضي
        /// </summary>
        public Drug()
        {
            IsActive = true;
            CreatedDate = DateTime.Now;
            MinStockLevel = 10;
            MaxStockLevel = 1000;
            CurrentStock = 0;
            RequiresPrescription = false;
        }

        /// <summary>
        /// منشئ مع المعاملات الأساسية
        /// </summary>
        public Drug(string drugCode, string drugName, string scientificName,
                   decimal purchasePrice, decimal salePrice)
        {
            DrugCode = drugCode;
            DrugName = drugName;
            ScientificName = scientificName;
            PurchasePrice = purchasePrice;
            SalePrice = salePrice;
            IsActive = true;
            CreatedDate = DateTime.Now;
            MinStockLevel = 10;
            MaxStockLevel = 1000;
            CurrentStock = 0;
            RequiresPrescription = false;
        }

        #endregion

        #region Methods - الطرق

        /// <summary>
        /// التحقق من صحة بيانات الدواء
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(DrugCode) &&
                   !string.IsNullOrWhiteSpace(DrugName) &&
                   PurchasePrice >= 0 &&
                   SalePrice >= 0 &&
                   MinStockLevel >= 0 &&
                   MaxStockLevel >= MinStockLevel;
        }

        /// <summary>
        /// التحقق من حاجة الدواء لإعادة الطلب
        /// </summary>
        /// <returns>true إذا كان المخزون أقل من الحد الأدنى</returns>
        public bool NeedsReorder()
        {
            return CurrentStock <= MinStockLevel;
        }

        /// <summary>
        /// إرجاع تمثيل نصي للدواء
        /// </summary>
        /// <returns>الاسم التجاري والقوة</returns>
        public override string ToString()
        {
            return FullName;
        }

        #endregion
    }
}
