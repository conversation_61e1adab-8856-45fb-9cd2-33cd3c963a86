using System;
using System.Data;
using System.Drawing;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نافذة إدارة العملاء - Customers Management Form
    /// </summary>
    public partial class CustomersForm : Form
    {
        #region Constructor - المنشئ

        public CustomersForm()
        {
            InitializeComponent();
            SetupForm();
            LoadData();
        }

        #endregion

        #region Form Setup - إعداد النافذة

        /// <summary>
        /// إعداد النافذة
        /// </summary>
        private void SetupForm()
        {
            // إعداد DataGridView
            SetupDataGridView();
            
            // إعداد التصميم المسطح
            ApplyFlatDesign();
        }

        /// <summary>
        /// إعداد DataGridView
        /// </summary>
        private void SetupDataGridView()
        {
            dgvCustomers.AutoGenerateColumns = false;
            dgvCustomers.AllowUserToAddRows = false;
            dgvCustomers.AllowUserToDeleteRows = false;
            dgvCustomers.ReadOnly = true;
            dgvCustomers.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvCustomers.MultiSelect = false;
            
            // تنسيق الألوان
            dgvCustomers.BackgroundColor = Color.White;
            dgvCustomers.GridColor = Color.FromArgb(189, 195, 199);
            dgvCustomers.DefaultCellStyle.SelectionBackColor = Color.FromArgb(52, 152, 219);
            dgvCustomers.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvCustomers.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 73, 94);
            dgvCustomers.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvCustomers.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10, FontStyle.Bold);
        }

        /// <summary>
        /// تطبيق التصميم المسطح
        /// </summary>
        private void ApplyFlatDesign()
        {
            // إزالة حدود الأزرار
            foreach (Control control in this.Controls)
            {
                if (control is Button button)
                {
                    button.FlatAppearance.BorderSize = 0;
                    button.Cursor = Cursors.Hand;
                }
            }
        }

        #endregion

        #region Data Loading - تحميل البيانات

        /// <summary>
        /// تحميل بيانات العملاء
        /// </summary>
        private void LoadData()
        {
            try
            {
                var customers = CustomerManager.GetAllCustomers();
                dgvCustomers.DataSource = customers;
                
                // تحديث عدد السجلات
                lblRecordsCount.Text = $"عدد السجلات: {customers.Count}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Event Handlers - معالجات الأحداث

        /// <summary>
        /// البحث في العملاء
        /// </summary>
        private void btnSearch_Click(object sender, EventArgs e)
        {
            try
            {
                string searchTerm = txtSearch.Text.Trim();
                var customers = CustomerManager.SearchCustomers(searchTerm);
                dgvCustomers.DataSource = customers;
                
                lblRecordsCount.Text = $"عدد السجلات: {customers.Count}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إضافة عميل جديد
        /// </summary>
        private void btnAdd_Click(object sender, EventArgs e)
        {
            var addForm = new CustomerAddEditForm();
            if (addForm.ShowDialog() == DialogResult.OK)
            {
                LoadData();
            }
        }

        /// <summary>
        /// تعديل عميل
        /// </summary>
        private void btnEdit_Click(object sender, EventArgs e)
        {
            if (dgvCustomers.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار عميل للتعديل", "تنبيه", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedCustomer = dgvCustomers.SelectedRows[0].DataBoundItem as Customer;
            if (selectedCustomer != null)
            {
                var editForm = new CustomerAddEditForm(selectedCustomer);
                if (editForm.ShowDialog() == DialogResult.OK)
                {
                    LoadData();
                }
            }
        }

        /// <summary>
        /// حذف عميل
        /// </summary>
        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (dgvCustomers.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار عميل للحذف", "تنبيه", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show("هل أنت متأكد من حذف هذا العميل؟", "تأكيد الحذف", 
                                       MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            
            if (result == DialogResult.Yes)
            {
                var selectedCustomer = dgvCustomers.SelectedRows[0].DataBoundItem as Customer;
                if (selectedCustomer != null)
                {
                    if (CustomerManager.DeleteCustomer(selectedCustomer.CustomerID))
                    {
                        MessageBox.Show("تم حذف العميل بنجاح", "نجح", 
                                      MessageBoxButtons.OK, MessageBoxIcon.Information);
                        LoadData();
                    }
                    else
                    {
                        MessageBox.Show("فشل في حذف العميل", "خطأ", 
                                      MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        /// <summary>
        /// عرض تاريخ العميل
        /// </summary>
        private void btnHistory_Click(object sender, EventArgs e)
        {
            if (dgvCustomers.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار عميل لعرض التاريخ", "تنبيه", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedCustomer = dgvCustomers.SelectedRows[0].DataBoundItem as Customer;
            if (selectedCustomer != null)
            {
                var historyForm = new CustomerHistoryForm(selectedCustomer);
                historyForm.ShowDialog();
            }
        }

        /// <summary>
        /// إعادة تعيين البحث
        /// </summary>
        private void btnReset_Click(object sender, EventArgs e)
        {
            txtSearch.Clear();
            LoadData();
        }

        /// <summary>
        /// البحث عند الكتابة
        /// </summary>
        private void txtSearch_TextChanged(object sender, EventArgs e)
        {
            // البحث التلقائي بعد توقف الكتابة
            searchTimer.Stop();
            searchTimer.Start();
        }

        /// <summary>
        /// البحث التلقائي
        /// </summary>
        private void searchTimer_Tick(object sender, EventArgs e)
        {
            searchTimer.Stop();
            btnSearch_Click(sender, e);
        }

        /// <summary>
        /// النقر المزدوج لتعديل
        /// </summary>
        private void dgvCustomers_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                btnEdit_Click(sender, e);
            }
        }

        #endregion
    }
}
