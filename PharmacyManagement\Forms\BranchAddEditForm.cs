using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PharmacyManagement.Classes;
using PharmacyManagement.Models;

namespace PharmacyManagement.Forms
{
    /// <summary>
    /// نموذج إضافة/تعديل الفرع - Branch Add/Edit Form
    /// </summary>
    public partial class BranchAddEditForm : Form
    {
        #region Fields

        private int? _branchId;
        private Branch _currentBranch;
        private bool _isEditMode;

        #endregion

        #region Constructors

        /// <summary>
        /// منشئ لإضافة فرع جديد
        /// </summary>
        public BranchAddEditForm()
        {
            InitializeComponent();
            _isEditMode = false;
            SetupForm();
            LoadManagers();
        }

        /// <summary>
        /// منشئ لتعديل فرع موجود
        /// </summary>
        /// <param name="branchId">معرف الفرع</param>
        public BranchAddEditForm(int branchId)
        {
            InitializeComponent();
            _branchId = branchId;
            _isEditMode = true;
            SetupForm();
            LoadManagers();
            LoadBranchData();
        }

        #endregion

        #region Setup Methods

        private void SetupForm()
        {
            this.Text = _isEditMode ? "تعديل الفرع" : "إضافة فرع جديد";
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Size = new Size(600, 700);
            this.BackColor = Color.White;
            this.Font = new Font("Segoe UI", 9F);

            // إعداد التصميم المسطح
            SetupFlatDesign();
            
            // إعداد التحقق من صحة البيانات
            SetupValidation();
            
            // إعداد الأحداث
            SetupEvents();
            
            // توليد كود الفرع إذا كان جديد
            if (!_isEditMode)
            {
                txtBranchCode.Text = BranchManager.GenerateBranchCode();
            }
        }

        private void SetupFlatDesign()
        {
            // إعداد الأزرار
            btnSave.BackColor = Color.FromArgb(46, 204, 113);
            btnSave.ForeColor = Color.White;
            btnSave.FlatStyle = FlatStyle.Flat;
            btnSave.FlatAppearance.BorderSize = 0;
            btnSave.Font = new Font("Segoe UI", 10F, FontStyle.Bold);

            btnCancel.BackColor = Color.FromArgb(231, 76, 60);
            btnCancel.ForeColor = Color.White;
            btnCancel.FlatStyle = FlatStyle.Flat;
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Font = new Font("Segoe UI", 10F, FontStyle.Bold);

            // إعداد مربعات النص
            foreach (Control control in this.Controls)
            {
                if (control is TextBox textBox)
                {
                    textBox.BorderStyle = BorderStyle.FixedSingle;
                    textBox.Font = new Font("Segoe UI", 10F);
                    textBox.Height = 30;
                }
                else if (control is ComboBox comboBox)
                {
                    comboBox.FlatStyle = FlatStyle.Flat;
                    comboBox.Font = new Font("Segoe UI", 10F);
                    comboBox.Height = 30;
                }
                else if (control is CheckBox checkBox)
                {
                    checkBox.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
                    checkBox.ForeColor = Color.FromArgb(52, 73, 94);
                }
                else if (control is Label label && label.Name.StartsWith("lbl"))
                {
                    label.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
                    label.ForeColor = Color.FromArgb(52, 73, 94);
                }
            }
        }

        private void SetupValidation()
        {
            // إعداد التحقق من صحة البيانات
            txtBranchName.Leave += ValidateBranchName;
            txtBranchCode.Leave += ValidateBranchCode;
            txtPhone.Leave += ValidatePhone;
            txtEmail.Leave += ValidateEmail;
        }

        private void SetupEvents()
        {
            btnSave.Click += BtnSave_Click;
            btnCancel.Click += BtnCancel_Click;
            chkIsMainBranch.CheckedChanged += ChkIsMainBranch_CheckedChanged;
        }

        #endregion

        #region Load Data Methods

        private void LoadManagers()
        {
            try
            {
                // تحميل قائمة المديرين
                var managers = UserManager.GetUsersByRole("Manager");
                cmbManager.DataSource = managers;
                cmbManager.DisplayMember = "FullName";
                cmbManager.ValueMember = "UserID";
                cmbManager.SelectedIndex = -1;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المديرين: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadBranchData()
        {
            try
            {
                if (_branchId.HasValue)
                {
                    _currentBranch = BranchManager.GetBranch(_branchId.Value);
                    if (_currentBranch != null)
                    {
                        // تعبئة البيانات
                        txtBranchCode.Text = _currentBranch.BranchCode;
                        txtBranchName.Text = _currentBranch.BranchName;
                        txtAddress.Text = _currentBranch.Address;
                        txtPhone.Text = _currentBranch.Phone;
                        txtEmail.Text = _currentBranch.Email;
                        txtCity.Text = _currentBranch.City;
                        txtRegion.Text = _currentBranch.Region;
                        txtOpeningHours.Text = _currentBranch.OpeningHours;
                        txtLicenseNumber.Text = _currentBranch.LicenseNumber;
                        txtTaxNumber.Text = _currentBranch.TaxNumber;
                        chkIsMainBranch.Checked = _currentBranch.IsMainBranch;
                        chkIsActive.Checked = _currentBranch.IsActive;

                        // تحديد المدير
                        if (_currentBranch.ManagerID.HasValue)
                        {
                            cmbManager.SelectedValue = _currentBranch.ManagerID.Value;
                        }

                        // منع تعديل كود الفرع
                        txtBranchCode.ReadOnly = true;
                        
                        // منع إلغاء تفعيل الفرع الرئيسي
                        if (_currentBranch.IsMainBranch)
                        {
                            chkIsMainBranch.Enabled = false;
                            chkIsActive.Enabled = false;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الفرع: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Event Handlers

        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (ValidateForm())
                {
                    var branch = CreateBranchFromForm();
                    
                    if (_isEditMode)
                    {
                        branch.BranchID = _branchId.Value;
                        if (BranchManager.UpdateBranch(branch))
                        {
                            MessageBox.Show("تم تحديث الفرع بنجاح", "نجح",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                            this.DialogResult = DialogResult.OK;
                            this.Close();
                        }
                        else
                        {
                            MessageBox.Show("فشل في تحديث الفرع", "خطأ",
                                MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    else
                    {
                        int branchId = BranchManager.AddBranch(branch);
                        if (branchId > 0)
                        {
                            MessageBox.Show("تم إضافة الفرع بنجاح", "نجح",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                            this.DialogResult = DialogResult.OK;
                            this.Close();
                        }
                        else
                        {
                            MessageBox.Show("فشل في إضافة الفرع", "خطأ",
                                MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الفرع: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void ChkIsMainBranch_CheckedChanged(object sender, EventArgs e)
        {
            if (chkIsMainBranch.Checked)
            {
                var result = MessageBox.Show(
                    "تحديد هذا الفرع كفرع رئيسي سيؤدي إلى إلغاء تفعيل الفرع الرئيسي الحالي.\nهل تريد المتابعة؟",
                    "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                
                if (result == DialogResult.No)
                {
                    chkIsMainBranch.Checked = false;
                }
                else
                {
                    // إجبار تفعيل الفرع إذا كان رئيسي
                    chkIsActive.Checked = true;
                    chkIsActive.Enabled = false;
                }
            }
            else
            {
                chkIsActive.Enabled = true;
            }
        }

        #endregion

        #region Validation Methods

        private bool ValidateForm()
        {
            var errors = new System.Text.StringBuilder();

            if (string.IsNullOrWhiteSpace(txtBranchCode.Text))
                errors.AppendLine("• كود الفرع مطلوب");

            if (string.IsNullOrWhiteSpace(txtBranchName.Text))
                errors.AppendLine("• اسم الفرع مطلوب");

            if (string.IsNullOrWhiteSpace(txtCity.Text))
                errors.AppendLine("• المدينة مطلوبة");

            if (!string.IsNullOrWhiteSpace(txtEmail.Text) && !IsValidEmail(txtEmail.Text))
                errors.AppendLine("• البريد الإلكتروني غير صحيح");

            if (!string.IsNullOrWhiteSpace(txtPhone.Text) && !IsValidPhone(txtPhone.Text))
                errors.AppendLine("• رقم الهاتف غير صحيح");

            if (errors.Length > 0)
            {
                MessageBox.Show($"يرجى تصحيح الأخطاء التالية:\n\n{errors}", "أخطاء في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        private void ValidateBranchName(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtBranchName.Text))
            {
                txtBranchName.BackColor = Color.FromArgb(255, 235, 235);
                toolTip.SetToolTip(txtBranchName, "اسم الفرع مطلوب");
            }
            else
            {
                txtBranchName.BackColor = Color.White;
                toolTip.SetToolTip(txtBranchName, "");
            }
        }

        private void ValidateBranchCode(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtBranchCode.Text))
            {
                txtBranchCode.BackColor = Color.FromArgb(255, 235, 235);
                toolTip.SetToolTip(txtBranchCode, "كود الفرع مطلوب");
            }
            else
            {
                txtBranchCode.BackColor = Color.White;
                toolTip.SetToolTip(txtBranchCode, "");
            }
        }

        private void ValidatePhone(object sender, EventArgs e)
        {
            if (!string.IsNullOrWhiteSpace(txtPhone.Text) && !IsValidPhone(txtPhone.Text))
            {
                txtPhone.BackColor = Color.FromArgb(255, 235, 235);
                toolTip.SetToolTip(txtPhone, "رقم الهاتف غير صحيح");
            }
            else
            {
                txtPhone.BackColor = Color.White;
                toolTip.SetToolTip(txtPhone, "");
            }
        }

        private void ValidateEmail(object sender, EventArgs e)
        {
            if (!string.IsNullOrWhiteSpace(txtEmail.Text) && !IsValidEmail(txtEmail.Text))
            {
                txtEmail.BackColor = Color.FromArgb(255, 235, 235);
                toolTip.SetToolTip(txtEmail, "البريد الإلكتروني غير صحيح");
            }
            else
            {
                txtEmail.BackColor = Color.White;
                toolTip.SetToolTip(txtEmail, "");
            }
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        private bool IsValidPhone(string phone)
        {
            // التحقق من رقم الهاتف اليمني
            var cleanPhone = phone.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "");
            return cleanPhone.Length >= 9 && cleanPhone.All(c => char.IsDigit(c) || c == '+');
        }

        #endregion

        #region Helper Methods

        private Branch CreateBranchFromForm()
        {
            return new Branch
            {
                BranchCode = txtBranchCode.Text.Trim(),
                BranchName = txtBranchName.Text.Trim(),
                Address = txtAddress.Text.Trim(),
                Phone = txtPhone.Text.Trim(),
                Email = txtEmail.Text.Trim(),
                City = txtCity.Text.Trim(),
                Region = txtRegion.Text.Trim(),
                ManagerID = cmbManager.SelectedValue as int?,
                IsMainBranch = chkIsMainBranch.Checked,
                IsActive = chkIsActive.Checked,
                OpeningHours = txtOpeningHours.Text.Trim(),
                LicenseNumber = txtLicenseNumber.Text.Trim(),
                TaxNumber = txtTaxNumber.Text.Trim(),
                CreatedBy = UserManager.CurrentUser?.UserID
            };
        }

        #endregion
    }
}
